{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "noisecancellationimpl.js", "sourceRoot": "", "sources": ["../../../lib/media/track/noisecancellationimpl.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIb,IAAA,uEAAuF;AACvF,IAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAEtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuDG,CACH,IAAA,wBAAA;IAKE,SAAA,sBAAY,SAAyB,EAAE,aAA+B;QACpE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAMD,OAAA,cAAA,CAAI,sBAAA,SAAA,EAAA,QAAM,EAAA;QAJV;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAChC,CAAC;;;OAAA;IAMD,OAAA,cAAA,CAAI,sBAAA,SAAA,EAAA,aAAW,EAAA;QAJf;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAMD,OAAA,cAAA,CAAI,sBAAA,SAAA,EAAA,WAAS,EAAA;QAJb;;;WAGG,MACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACrC,CAAC;;;OAAA;IAED;;;;;OAKG,CACH,sBAAA,SAAA,CAAA,MAAM,GAAN;QACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAI,IAAI,CAAC,MAAM,GAAA,4DAA4D,CAAC,CAAC;SAC7F;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACH,sBAAA,SAAA,CAAA,OAAO,GAAP;QACE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACG,sBAAA,SAAA,CAAA,cAAc,GAApB,SAAqB,SAA0C;;;;;;wBACvD,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;wBACxD,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;wBAEf,OAAA;4BAAA,EAAA,OAAA;4BAAM,SAAS,EAAE;yBAAA,CAAA;;wBAAzB,KAAK,GAAG,GAAA,IAAA,EAAiB;wBAC/B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;wBAEH,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC;yBAAA,CAAA;;wBAArD,cAAc,GAAG,GAAA,IAAA,EAAoC;wBAC3D,IAAI,mBAAmB,EAAE;4BACvB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;yBAC1B,MAAM;4BACL,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;yBAC3B;wBACD,OAAA;4BAAA,EAAA,QAAA;4BAAO,cAAc;yBAAA,CAAC;;;;KACvB;IAED;;OAEG,CACH,sBAAA,SAAA,CAAA,kBAAkB,GAAlB;QACE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAGD;;OAEG,CACH,sBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AA9FD,IA8FC;AA9FY,QAAA,qBAAA,GAAA,sBAAqB;AAiGlC,SAAsB,sBAAsB,CAC1C,gBAAkC,EAClC,wBAAkD,EAClD,GAAe;;;;;;;;;;;;oBAGK,OAAA;wBAAA,EAAA,OAAA;wBAAM,2BAAA,qCAAqC,CAAC,wBAAwB,EAAE,GAAG,CAAC;qBAAA,CAAA;;oBAAtF,SAAS,GAAG,GAAA,IAAA,EAA0E;oBACtF,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;oBACjD,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;oBACjF,OAAA;wBAAA,EAAA,QAAA;wBAAO;4BAAE,UAAU,EAAA,UAAA;4BAAE,iBAAiB,EAAA,iBAAA;wBAAA,CAAE;qBAAA,CAAC;;;oBAEzC,4FAA4F;oBAC5F,GAAG,CAAC,IAAI,CAAC,wEAAsE,IAAI,CAAC,CAAC;oBACrF,OAAA;wBAAA,EAAA,QAAA;wBAAO;4BAAE,UAAU,EAAE,gBAAgB;wBAAA,CAAE;qBAAA,CAAC;;;;;;;;CAE3C;AAfD,QAAA,sBAAA,GAAA,uBAeC", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/media/track/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC7C,IAAA,KAAkC,OAAO,CAAC,YAAY,CAAC,oFAArD,cAAc,GAAA,GAAA,cAAA,EAAE,WAAW,GAAA,GAAA,WAA0B,CAAC;AAC9D,IAAM,iBAAiB,GAAG,OAAO,CAAC,sBAAsB,CAAC,6EAAC,iBAAiB,CAAC;AAC5E,IAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAEtC,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;;GAKG,CACH,IAAA,QAAA,SAAA,MAAA;IAAoB,UAAA,OAAA,QAAY;IAC9B;;;;;OAKG,CACH,SAAA,MAAY,EAAE,EAAE,IAAI,EAAE,OAAO;QAA7B,IAAA,QAAA,IAAA,CAgCC;QA/BC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,IAAI,EAAE,EAAE;YACR,GAAG,EAAE,IAAI;YACT,QAAQ,EAAE,iBAAiB;SAC5B,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAAC;QAER,IAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAElC,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,GACnB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,KAAI,CAAC,GACpC,IAAI,GAAG,CAAC,OAAO,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAE1D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE,UAAU;aACpB;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;SACF,CAAC,CAAC;;IACL,CAAC;IAED,MAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IACH,OAAA,KAAC;AAAD,CAAC,AA5CD,CAAoB,YAAY,GA4C/B;AAED;;;GAGG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH;;;;;;GAMG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "file": "mediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/mediatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,6EAAA,KAA3C,CAA4C;AACjD,IAAA,WAAW,GAAK,OAAO,CAAC,cAAc,CAAC,mFAAA,WAA5B,CAA6B;AAE1C,IAAA,KAAoC,OAAO,CAAC,YAAY,CAAC,oFAAvD,YAAY,GAAA,GAAA,YAAA,EAAE,eAAe,GAAA,GAAA,eAA0B,CAAC;AAChE,IAAM,0BAA0B,GAAG,OAAO,CAAC,uCAAuC,CAAC,CAAC;AACpF,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5B;;;;;;;;;;;;;;;;GAgBG,CACH,IAAA,aAAA,SAAA,MAAA;IAAyB,UAAA,YAAA,QAAK;IAC5B;;;;OAIG,CACH,SAAA,WAAY,qBAAqB,EAAE,OAAO;QAA1C,IAAA,QAAA,IAAA,CA+EC;QA9EC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,mCAAmC,EAAE,KAAK,EAAE,IACvC,OAAO,QAAQ,KAAK,QAAQ,IAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU,IAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,qBAAqB,CAAC,EAAE,EAAE,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;QACrE,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,WAAW,EAAA,WAAA;SACZ,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,0DAA0D,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;SACnG;QAED,wBAAA,EAA0B,CAC1B,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,UAAU,EAAE;gBACV,GAAG,EAAA;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;gBACD,GAAG,EAAA,SAAC,UAAU;oBACZ,SAAS,GAAG,UAAU,CAAC;gBACzB,CAAC;aACF;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,OAAO,CAAC,mCAAmC;aACnD;YACD,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,yBAAyB,IACnC,OAAO,CAAC,mCAAmC;aACjD;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,OAAO,CAAC,WAAW;aAC3B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,OAAO,CAAC,mBAAmB;aACnC;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YACD,gBAAgB,EAAE;gBAChB,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,iBAAiB,IAAI,qBAAqB,CAAC,KAAK,CAAC;gBAC/D,CAAC;aACF;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,WAAW,EAAE,CAAC;;IACrB,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,MAAM,GAAN;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAChC;QACD,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,WAAW,GAAX;QACE,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;YACnE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,OAAO;gBAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;SACJ,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAChC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,SAAS,OAAO;gBAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YACvC,CAAC,CAAC;SACH;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEhE,oFAAoF;YACpF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C,MAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;aAChC;YACD,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED,WAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;QAAT,IAAA,QAAA,IAAA,CAgBC;QAfC,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC1B,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;SAC9B,MAAM,IAAI,CAAC,EAAE,EAAE;YACd,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QACxD,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9D,IAAM,uBAAuB,GAAG,IAAI,CAAC,oCAAoC,GACrE;gBAAM,OAAA,8BAA8B,CAAC,EAAE,EAAE,KAAI,CAAC,IAAI,CAAC;YAA7C,CAA6C,GACnD,IAAI,CAAC;YACT,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,gBAAgB,CAAC,EAAE,EAAE,uBAAuB,CAAC,CAAC,CAAC;SACtE;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;;;OAOG,CACH,WAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,EAAE,EAAE,gBAA+D;QAA/D,IAAA,qBAAA,KAAA,GAAA;YAAA,mBAAmB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB;QAAA;QACzE,IAAI,WAAW,GAAG,EAAE,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,CAAC,WAAW,YAAY,IAAI,CAAC,YAAY,CAAC,EAAE;YAC/C,WAAW,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;SACvC;QAED,IAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,KAAK,OAAO,GAC/C,gBAAgB,GAChB,gBAAgB,CAAC;QAErB,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,SAAA,KAAK;YACpC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAEvC,2DAA2D;QAC3D,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;YAC3F,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;SAC3B;QAED,4FAA4F;QAC5F,8FAA8F;QAC9F,EAAE,CAAC,SAAS,GAAG,WAAW,CAAC;QAC3B,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC;QACnB,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAC3B;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,cAAc,GAAd,SAAe,QAAQ;QACrB,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,IAAI,KAAK,CAAC,kCAAgC,QAAU,CAAC,CAAC;SAC7D;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,+BAA+B,GAA/B;QAAA,IAAA,QAAA,IAAA,CAGC;QAFC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACvE,IAAI,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,SAAA,EAAE;YAAI,OAAA,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAAhB,CAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,cAAc,GAAd;QACE,OAAO,OAAO,QAAQ,KAAK,WAAW,GAClC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GACjC,IAAI,CAAC;IACX,CAAC;IAED,WAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;QACP,IAAI,GAAG,CAAC;QAER,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC1B,GAAG,GAAG;gBAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;aAAC,CAAC;SACjC,MAAM,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SACtC,MAAM;YACL,GAAG,GAAG;gBAAC,EAAE;aAAC,CAAC;SACZ;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,QAAQ;QACtB,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,cAAc,GAAd,SAAe,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9B,OAAO,EAAE,CAAC;SACX;QACD,IAAM,WAAW,GAAG,EAAE,CAAC,SAAS,CAAC;QACjC,IAAI,WAAW,YAAY,IAAI,CAAC,YAAY,EAAE;YAC5C,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvE;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;YACjG,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC7D,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SAC1B;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,uBAAuB,GAAvB;QACE,IAAM,GAAG,GAAG,EAAE,CAAC;QAEf,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAA,EAAE;YAC1B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AA5SD,CAAyB,KAAK,GA4S7B;AAED;;;;;;GAMG,CACH,SAAS,8BAA8B,CAAC,EAAE,EAAE,GAAG;IAC7C,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACrC,GAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAExC,4EAA4E;IAC5E,8EAA8E;IAC9E,2EAA2E;IAC3E,OAAO,CAAC,IAAI,CAAC;QACX,YAAY,CAAC,QAAQ,EAAE,kBAAkB,CAAC;QAC1C,eAAe,CAAC,IAAI,CAAC;KACtB,CAAC,CAAC,IAAI,CAAC;QACN,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;YAC1C,yEAAyE;YACzE,mEAAmE;YACnE,EAAE;YACF,sDAAsD;YACtD,EAAE;YACF,0BAA0B,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;gBACpD,GAAG,CAAC,IAAI,CAAC,qCAAmC,GAAG,GAAA,WAAW,CAAC,CAAC;gBAC5D,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC,IAAI,CAAC;gBACN,GAAG,CAAC,IAAI,CAAC,iDAA+C,GAAG,GAAA,WAAW,CAAC,CAAC;gBACxE,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;gBACZ,GAAG,CAAC,IAAI,CAAC,iDAA+C,GAAG,GAAA,YAAY,EAAE;oBAAE,KAAK,EAAA,KAAA;oBAAE,EAAE,EAAA,EAAA;gBAAA,CAAE,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG,CACH,SAAS,gBAAgB,CAAC,EAAE,EAAE,uBAA8B;IAA9B,IAAA,4BAAA,KAAA,GAAA;QAAA,0BAAA,IAA8B;IAAA;IAC1D,IAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC;IAC3B,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC;IAEzB,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAEhC,EAAE,CAAC,KAAK,GAAG;QACT,mBAAmB,GAAG,IAAI,CAAC;QAC3B,OAAO,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG;QACR,mBAAmB,GAAG,KAAK,CAAC;QAC5B,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAM,OAAO,GAAG,uBAAuB,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,EAAE;YACxB,uBAAuB,EAAE,CAAC;SAC3B;IACH,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAET,IAAI,OAAO,EAAE;QACX,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACvC;IAED,OAAO;QACL,mBAAmB,EAAA;YACjB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QACD,MAAM,EAAA;YACJ,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;YACrB,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC;YACnB,IAAI,OAAO,EAAE;gBACX,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC1C;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "file": "audiotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/audiotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C;;;;;;;;;;;;;;;GAeG,CACH,IAAA,aAAA,SAAA,MAAA;IAAyB,UAAA,YAAA,QAAU;IACjC;;;;OAIG,CACH,SAAA,WAAY,qBAAqB,EAAE,OAAO;eACxC,OAAA,IAAA,CAAA,IAAA,EAAM,qBAAqB,EAAE,OAAO,CAAC,IAAA,IAAA;IACvC,CAAC;IAED;;;;;;;;;;;;;;MAcE,CAAA;;;;;;;;;;;;;;;;;;;;;;MAsBA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;OAyBC,CACH,WAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;MAME,CAAA;;;;;;;;MAQA,CAAA;;;;;;;;OAQC,CACH,WAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AAtGD,CAAyB,UAAU,GAsGlC;AAED;;;;GAIG,CAEH;;;;GAIG,CAEH;;;;;GAKG,CAEH,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC", "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "file": "transceiver.js", "sourceRoot": "", "sources": ["../../../lib/media/track/transceiver.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAEtD;;;;;GAKG,CACH,IAAA,wBAAA,SAAA,MAAA;IAAoC,UAAA,uBAAA,QAAgB;IAClD;;;;OAIG,CACH,SAAA,sBAAY,EAAE,EAAE,gBAAgB;QAAhC,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,EAAE,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAA,IAAA,CAyBjC;QAxBC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,MAAM,EAAE;gBACN,KAAK,EAAE,gBAAgB;gBACvB,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;gBAC7B,CAAC;aACF;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAChC,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;;IACL,CAAC;IAED,sBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAClB,OAAA,SAAA,CAAM,IAAI,CAAA,IAAA,CAAA,IAAA,CAAE,CAAC;IACf,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAtCD,CAAoC,gBAAgB,GAsCnD;AAED,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "file": "sender.js", "sourceRoot": "", "sources": ["../../../lib/media/track/sender.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAEvD;;;;GAIG,CACH,IAAA,mBAAA,SAAA,MAAA;IAA+B,UAAA,kBAAA,QAAqB;IAClD;;;OAGG,CACH,SAAA,iBAAY,gBAAgB;QAA5B,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,IAAA,IAAA,CAgC7C;QA/BC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,CAAC;oBACb;wBAAC,MAAM;wBAAE;4BAAM,OAAA,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC;wBAAnB,CAAmB;qBAAC;oBACnC;wBAAC,QAAQ;wBAAE;4BAAM,OAAA,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC;wBAArB,CAAqB;qBAAC;iBACxC,CAAC;aACH;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,+BAA+B,EAAE;gBAC/B,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7B,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC3B,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,6BAA6B,EAAE,CAAC;;IACvC,CAAC;IAED;;OAEG,CACH,iBAAA,SAAA,CAAA,6BAA6B,GAA7B,SAA8B,gBAA8B;QAA9B,IAAA,qBAAA,KAAA,GAAA;YAAA,mBAAmB,IAAI,CAAC,MAAM;QAAA;QACpD,IAAA,KAA6D,IAAI,EAA1C,kBAAkB,GAAA,GAAA,mBAAA,EAAU,KAAK,GAAA,GAAA,MAAS,CAAC;QACxE,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,kBAAkB,CAAC,OAAO,CAAC,SAAC,SAAS,EAAE,KAAK;gBAAK,OAAA,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC;YAAnD,CAAmD,CAAC,CAAC;SACvG,MAAM;YACL,kBAAkB,CAAC,OAAO,CAAC,SAAC,SAAS,EAAE,KAAK;gBAC1C,gBAAgB,CAAC,OAAK,KAAO,CAAC,GAAG,SAAS,CAAC;YAC7C,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,KAAK,KAAK,gBAAgB,EAAE;YAC9B,kBAAkB,CAAC,OAAO,CAAC,SAAC,SAAS,EAAE,KAAK;gBAAK,OAAA,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC;YAA3C,CAA2C,CAAC,CAAC;YAC9F,IAAI,KAAK,CAAC,KAAK,KAAK,gBAAgB,CAAC,KAAK,EAAE;gBAC1C,IAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACrF,SAAS,EAAE,CAAC;aACb;SACF;IACH,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,KAAK,GAAL;QACE,IAAM,KAAK,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG,CACH,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,KAAK;QACf,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,mBAAmB,GAAnB,SAAoB,gBAAgB;QAApC,IAAA,QAAA,IAAA,CAWC;QAVC,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAA,KAAK;YACjC,OAAO,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,SAAA,MAAM;YAC1B,OAAO,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACX,KAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YACrD,KAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACH,iBAAA,SAAA,CAAA,SAAS,GAAT,SAAU,MAAM,EAAE,qBAAqB;QACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,MAAM;QACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,SAAS;QACxB,yGAAyG;QACnG,IAAA,KAAA,OAA0B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,MAAM,EAAE,CAAC,EAAA,EAAA,EAAlF,qBAAqB,GAAA,EAAA,CAAA,EAA6D,CAAC;QAC1F,OAAO,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAC5G,CAAC;IAED,iBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,MAAM,EAAE,gBAAgB;QAAtC,IAAA,QAAA,IAAA,CAOC;QANC,OAAO,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAA,kBAAkB;YAClE,wDAAwD;YACxD,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAO,CAAC,CAAC,CAAC;YAC5C,KAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AA9ID,CAA+B,qBAAqB,GA8InD;AAED;;;GAGG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "file": "localmediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localmediatrack.js"], "names": [], "mappings": "AAAA,oBAAA,EAAsB,CACtB,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,cAAc,CAAC,mFAAA,YAA5B,CAA6B;AACzC,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,6EAAA,KAA3C,CAA4C;AAEnD,IAAA,KAAuD,OAAO,CAAC,YAAY,CAAC,oFAA1E,UAAU,GAAA,GAAA,UAAA,EAAE,KAAK,GAAA,GAAA,KAAA,EAAE,eAAe,GAAA,GAAA,eAAA,EAAE,YAAY,GAAA,GAAA,YAA0B,CAAC;AAC7D,IAAA,cAAc,GAAO,OAAO,CAAC,sBAAsB,CAAC,6EAAA,UAAA,CAAA,cAAtC,CAAuC;AAC3E,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AACrF,IAAM,0BAA0B,GAAG,OAAO,CAAC,uCAAuC,CAAC,CAAC;AACpF,IAAM,wBAAwB,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC5E,IAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAE7C,SAAS,oBAAoB,CAAC,iBAAiB;IAC7C;;;;;;;;;OASG,CACH,OAAA,SAAA,MAAA;QAAqC,UAAA,iBAAA,QAAiB;QACpD;;;;WAIG,CACH,SAAA,gBAAY,gBAAgB,EAAE,OAAO;YAArC,IAAA,QAAA,IAAA,CAwFC;YAvFC,IAAM,0BAA0B,GAAG,KAAK,EAAE,IACrC,OAAO,QAAQ,KAAK,QAAQ,IAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU,IAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC;YAElD,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBACtB,YAAY,EAAA,YAAA;gBACZ,4BAA4B,EAAE,KAAK;gBACnC,0BAA0B,EAAA,0BAAA;gBAC1B,wBAAwB,EAAA,wBAAA;aACzB,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACxD,IAAA,IAAI,GAAK,gBAAgB,CAAA,IAArB,CAAsB;YAElC,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,gBAAgB,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;YAEjC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;gBAC5B,YAAY,EAAE;oBACZ,KAAK,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,GACpC,OAAO,CAAC,IAAI,CAAC,GACb,CAAA,CAAE;oBACN,QAAQ,EAAE,IAAI;iBACf;gBACD,aAAa,EAAE;oBACb,KAAK,EAAE,OAAO,CAAC,YAAY;iBAC5B;gBACD,yBAAyB,EAAE;oBACzB,KAAK,EAAE,OAAO,CAAC,wBAAwB;iBACxC;gBACD,mBAAmB,EAAE;oBACnB,KAAK,EAAE,IAAI,GAAG,CAAC;wBACb;4BAAC,OAAO;4BAAE;gCAAM,OAAA,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAI,CAAC;4BAAxB,CAAwB;yBAAC;wBACzC;4BAAC,SAAS;4BAAE;gCAAM,OAAA,KAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAI,CAAC;4BAA1B,CAA0B;yBAAC;qBAC9C,CAAC;iBACH;gBACD,2BAA2B,EAAE;oBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;iBAC1C;gBACD,kCAAkC,EAAE;oBAClC,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,IAAI;iBACf;gBACD,6BAA6B,EAAE;oBAC7B,KAAK,EAAE,OAAO,CAAC,4BAA4B;iBAC5C;gBACD,kBAAkB,EAAE;oBAClB,KAAK,EAAE,OAAO,CAAC,iBAAiB,IAAI,IAAI;iBACzC;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,gBAAgB;iBACxB;gBACD,EAAE,EAAE;oBACF,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,gBAAgB,CAAC,EAAE;iBAC3B;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,gBAAgB,CAAC,OAAO,CAAC;oBAClC,CAAC;iBACF;gBACD,OAAO,EAAE;oBACP,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,gBAAgB,CAAC,KAAK,CAAC;oBAChC,CAAC;iBACF;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,gBAAgB,CAAC,UAAU,KAAK,OAAO,CAAC;oBACjD,CAAC;iBACF;aACF,CAAC,CAAC;YAEH,qGAAqG;YACrG,6FAA6F;YAC7F,IAAI,KAAI,CAAC,2BAA2B,EAAE;gBACpC,KAAI,CAAC,kCAAkC,GAAG,+BAA+B,CAAC,KAAI,CAAC,CAAC;aACjF;YAED,KAAI,CAAC,wBAAwB,EAAE,CAAC;;QAClC,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,IAAI,GAAJ;YAAA,IAAA,QAAA,IAAA,CAQC;YAPC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,OAAO;aACR;YACD,OAAA,SAAA,CAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAC,SAAS,EAAE,KAAK;gBAAK,OAAA,KAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC;YAAlD,CAAkD,CAAC,CAAC;YAC3G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,WAAW,GAAX;YACE,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;aAC1B;YACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACjC;YACD,OAAA,SAAA,CAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,WAAW;;YACnB,IAAA,KAKF,IAAI,EAJS,YAAY,GAAA,GAAA,aAAA,EACA,wBAAwB,GAAA,GAAA,yBAAA,EAC7C,GAAG,GAAA,GAAA,IAAA,EACW,IAAI,GAAA,GAAA,gBAAA,CAAA,IAClB,CAAC;YAET,GAAG,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAC9C,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAEvC,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;gBACnC,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,KAAK;aACb,EAAA,CAAA,KAAA,CAAA,GAAI,EAAA,CAAC,IAAI,CAAA,GAAG,WAAW,EAAA,EAAA,EAAG,CAAC;YAE5B,IAAM,UAAU,GAAG,IAAI,CAAC,kCAAkC,GACtD,wBAAwB,CAAC,GAAG,EAAE,YAAY,EAAE,cAAc,CAAC,GAC3D,YAAY,CAAC,cAAc,CAAC,CAAC;YAEjC,OAAO,UAAU,CAAC,IAAI,CAAC,SAAA,WAAW;gBAChC,OAAO,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,wBAAwB,GAAxB;YAAA,IAAA,QAAA,IAAA,CAIC;YAHC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAC,SAAS,EAAE,KAAK;gBAAK,OAAA,KAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;YAAtC,CAAsC,CAAC,CAAC;YAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,WAAW;YAApB,IAAA,QAAA,IAAA,CAmBC;YAlBS,IAAM,GAAG,GAAK,IAAI,CAAA,IAAT,CAAU;YAC3B,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC;YAE/C,oEAAoE;YACpE,0EAA0E;YAC1E,yEAAyE;YACzE,yBAAyB;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;gBAClD,GAAG,CAAC,KAAK,CAAC,4CAA4C,EAAE;oBAAE,KAAK,EAAA,KAAA;oBAAE,WAAW,EAAA,WAAA;gBAAA,CAAE,CAAC,CAAC;gBAChF,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC,IAAI,CAAC,SAAA,mBAAmB;gBACzB,GAAG,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBAC7C,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;gBACpD,KAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,WAAW,CAAC,CAAC;gBACnD,OAAO,KAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,oBAAoB,GAApB,SAAqB,gBAAgB;YAArC,IAAA,QAAA,IAAA,CAkBC;YAjBC,+DAA+D;YAC/D,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAEzD,sEAAsE;YACtE,+CAA+C;YAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,6EAA6E;YAC7E,iEAAiE;YACjE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;gBACtD,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;gBACtE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBAAE,KAAK,EAAA,KAAA;oBAAE,gBAAgB,EAAA,gBAAA;gBAAA,CAAE,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACP,KAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,KAAI,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,SAAA,EAAE;oBAAI,OAAA,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAAhB,CAAgB,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG,CACH,gBAAA,SAAA,CAAA,KAAK,GAAL;YACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAO;YACZ,OAAO,GAAG,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YACxD,IAAI,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAA,QAAQ,CAAC,CAAC;gBAClD,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aACnD;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gBAAA,SAAA,CAAA,OAAO,GAAP;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,gBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,WAAW;YAAnB,IAAA,QAAA,IAAA,CAmBC;YAlBS,IAAA,IAAI,GAAK,IAAI,CAAA,IAAT,CAAU;YACtB,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,yBAAyB,IACrE,WAAS,UAAU,CAAC,IAAI,CAAC,GAAA,+CAA+C,CAAA,IACxE,oBAAkB,UAAU,CAAC,IAAI,CAAC,GAAA,QAAQ,CAAA,CAAC,CAAC,CAAC;aAClD;YACD,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;gBAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;aAChD;YACD,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACpC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;oBACxB,KAAI,CAAC,kCAAkC,GAAG,+BAA+B,CAAC,KAAI,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC;aACJ;YACD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,gBAAA,SAAA,CAAA,IAAI,GAAJ;YACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;gBAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;aAChD;YACD,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QACH,OAAA,eAAC;IAAD,CAAC,AAlQM,CAA8B,iBAAiB,GAkQpD;AACJ,CAAC;AAED;;;;;GAKG,CACH,SAAS,+BAA+B,CAAC,eAAe;IAEpD,IAAM,GAAG,GAGP,eAAe,CAAA,IAHR,EACT,IAAI,GAEF,eAAe,CAAA,IAFb,EACgB,iBAAiB,GACnC,eAAe,CAAA,kBADoB,CACnB;IAEpB,IAAM,aAAa,GAAG;QACpB,KAAK,EAAE,iBAAiB;QACxB,KAAK,EAAE,iBAAiB;KACzB,CAAC,IAAI,CAAC,CAAC;IAER,IAAM,yBAAyB,GAAG;QAAM,OAAA,iBAAiB,GACrD,iBAAiB,CAAC,WAAW,GAC7B,eAAe,CAAC,gBAAgB;IAFI,CAEJ,CAAC;IAE/B,IAAU,EAAE,GAAK,eAAe,CAAA,QAApB,CAAqB;IACvC,IAAI,gBAAgB,GAAG,yBAAyB,EAAE,CAAC;IACnD,IAAI,qBAAqB,GAAG,IAAI,CAAC;IAEjC,SAAS,YAAY;QACnB,mEAAmE;QACnE,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;YAAM,OAAA,aAAa,CAAC,EAAE,CAAC;QAAjB,CAAiB,CAAC,CAAC,IAAI,CAAC,SAAA,QAAQ;YAC1D,IAAI,QAAQ,EAAE;gBACZ,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9B,MAAM;gBACL,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aAClC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;YACZ,GAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,iEAAiE;YACjE,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACnC,EAAE,CAAC,KAAK,EAAE,CAAC;aACZ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,oBAAoB;QAEzB,IAAA,kCAAkC,GAEhC,eAAe,CAAA,kCAFiB,EAClC,SAAS,GACP,eAAe,CAAA,SADR,CACS;QAEpB,IAAM,sBAAsB,GAAG,SAAS,IAAI,CAAC,CAAC,kCAAkC,CAAC;QACzE,IAAA,KAAK,GAAK,yBAAyB,EAAE,CAAA,KAAhC,CAAiC;QAE9C,oDAAoD;QACpD,kCAAkC;QAClC,+CAA+C;QAC/C,0EAA0E;QAC1E,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,OAAO,QAAQ,CAAC,eAAe,KAAK,SAAS,IACxC,CAAC,qBAAqB,IACtB,CAAC,KAAK,IAAI,sBAAsB,IAAI,YAAY,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,YAAY;QACnB,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,YAAY,CAAC,gBAAgB,EAAE,QAAQ,CAAC;YACxC,eAAe,CAAC,EAAE,CAAC;SACpB,CAAC,CAAC,IAAI,CAAC;YAAM,OAAA,oBAAoB,EAAE;QAAtB,CAAsB,CAAC,CAAC,IAAI,CAAC,SAAA,eAAe;YACxD,IAAI,eAAe,IAAI,CAAC,qBAAqB,EAAE;gBAC7C,qBAAqB,GAAG,KAAK,EAAE,CAAC;gBAChC,eAAe,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACjC,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC9B,+BAA+B,EAAE,CAAC;oBAClC,gBAAgB,GAAG,yBAAyB,EAAE,CAAC;oBAC/C,4BAA4B,EAAE,CAAC;oBAC/B,qBAAqB,CAAC,OAAO,EAAE,CAAC;oBAChC,qBAAqB,GAAG,IAAI,CAAC;gBAC/B,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;oBACZ,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;aACJ;YAED,4EAA4E;YAC5E,2EAA2E;YAC3E,6CAA6C;YAC7C,IAAM,OAAO,GAAG,AAAC,qBAAqB,IAAI,qBAAqB,CAAC,OAAO,CAAC,GAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC9F,OAAO,OAAO,CAAC,OAAO,CAAC;gBAAM,OAAA,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC;YAAhD,CAAgD,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,EAAE;YACT,GAAG,CAAC,KAAK,CAAC,4BAA0B,EAAE,CAAC,OAAS,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,MAAM;QACL,IAAM,GAAG,GAAW,eAAe,CAAA,IAA1B,EAAE,IAAI,GAAK,eAAe,CAAA,IAApB,CAAqB;QAC5C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QAE/C,0EAA0E;QAC1E,2EAA2E;QAC3E,2EAA2E;QAC3E,8EAA8E;QAC9E,wDAAwD;QACxD,EAAE;QACF,sDAAsD;QACtD,EAAE;QACF,0BAA0B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,SAAS,4BAA4B;QACnC,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACzD,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAClD,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC3D,MAAM;YACL,gBAAgB,CAAC,OAAO,GAAG,YAAY,CAAC;YACxC,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;YACjC,gBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC;SAC1C;IACH,CAAC;IAED,SAAS,+BAA+B;QACtC,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;YACxC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC5D,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACrD,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC9D,MAAM;YACL,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;SAClC;IACH,CAAC;IAED,yEAAyE;IACzE,oEAAoE;IACpE,qEAAqE;IACrE,0DAA0D;IAC1D,IAAI,kBAAkB,GAAG,SAAA,SAAS;QAChC,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC,CAAC;IACF,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;IACpE,4BAA4B,EAAE,CAAC;IAE/B,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QACrE,+BAA+B,EAAE,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "file": "localaudiotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localaudiotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,6EAAA,KAA3C,CAA4C;AACzD,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAC1D,IAAA,WAAW,GAAK,OAAO,CAAC,mBAAmB,CAAC,mFAAA,WAAjC,CAAkC;AACrD,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE1D,IAAM,oBAAoB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;;;;;;;;;;;;;;;;;;;GAuBG,CACH,IAAA,kBAAA,SAAA,MAAA;IAA8B,UAAA,iBAAA,QAAoB;IAChD;;;;OAIG,CACH,SAAA,gBAAY,gBAAgB,EAAE,OAAO;QAArC,IAAA,QAAA,IAAA,CAkEC;QAjEC,IAAM,iBAAiB,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,iBAAiB,KAAI,IAAI,CAAC;QAC7D,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,gBAAgB,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;QAEzB,IAAM,GAAG,GAAK,KAAI,CAAA,IAAT,CAAU;QACnB,IAAA,KAAmC,gBAAgB,CAAA,KAArB,EAAvB,kBAAkB,GAAA,OAAA,KAAA,IAAG,EAAE,GAAA,EAAA,CAAsB;QACtD,IAAA,KAAmE,gBAAgB,CAAC,WAAW,EAAE,EAA/F,KAAA,GAAA,QAA8B,EAApB,eAAe,GAAA,OAAA,KAAA,IAAG,EAAE,GAAA,EAAA,EAAE,KAAA,GAAA,OAA4B,EAAnB,cAAc,GAAA,OAAA,KAAA,IAAG,EAAE,GAAA,EAAmC,CAAC;QAExG,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,yBAAyB,EAAE;gBACzB,KAAK,EAAE;oBAAE,QAAQ,EAAE,eAAe;oBAAE,OAAO,EAAE,cAAc;oBAAE,KAAK,EAAE,kBAAkB;gBAAA,CAAE;gBACxF,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,gBAAgB,CAAA,KAAK,UAAU,GAClD,OAAO,CAAC,gBAAgB,GACxB,SAAS,CAAC,YAAY,CAAC,gBAAgB;aAC5C;YACD,yBAAyB,EAAE;gBACzB,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,iBAAiB,CAAC,IACnC,KAAI,CAAC,6BAA6B,IAClC,OAAO,SAAS,KAAK,QAAQ,IAC7B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ,IAC1C,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,KAAK,UAAU,IAC7D,CAAC,OAAO,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,gBAAgB,CAAA,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,KAAK,UAAU,CAAC,GACnH,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,wBAAwB,KAAI,MAAM,GAC3C,QAAQ;aACb;YACD,eAAe,EAAE;gBACf,KAAK,EAAE;oBACL,KAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,SAAA,WAAW;wBACvC,wGAAwG;wBACxG,0GAA0G;wBAC1G,2GAA2G;wBAC3G,kCAAkC;wBAClC,IAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,SAAC,EAAkB;gCAAhB,QAAQ,GAAA,GAAA,QAAA,EAAE,IAAI,GAAA,GAAA,IAAA;4BAC1D,OAAO,IAAI,KAAK,YAAY,IAAI,QAAQ,KAAK,SAAS,CAAC;wBACzD,CAAC,CAAC,CAAC;wBAEH,IAAI,iBAAiB,IAAI;4BAAC,UAAU;4BAAE,SAAS;yBAAC,CAAC,IAAI,CAAC,SAAA,IAAI;4BACxD,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,KAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;wBAC1E,CAAC,CAAC,EAAE;4BACF,GAAG,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;4BACnE,GAAG,CAAC,KAAK,CAAC,2BAAwB,KAAI,CAAC,yBAAyB,CAAC,QAAQ,GAAA,aAAS,KAAI,CAAC,yBAAyB,CAAC,KAAK,GAAA,IAAG,CAAC,CAAC;4BAC3H,GAAG,CAAC,KAAK,CAAC,2BAAwB,iBAAiB,CAAC,QAAQ,GAAA,aAAS,iBAAiB,CAAC,KAAK,GAAA,IAAG,CAAC,CAAC;4BACjG,KAAI,CAAC,yBAAyB,GAAG,iBAAiB,CAAC;4BACnD,KAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,SAAA,KAAK;gCAAI,OAAA,GAAG,CAAC,IAAI,CAAC,wBAAsB,KAAK,CAAC,OAAS,CAAC;4BAA/C,CAA+C,CAAC,CAAC;yBAC9F;oBACH,CAAC,EAAE,SAAA,KAAK;wBACN,GAAG,CAAC,IAAI,CAAC,uCAAqC,KAAK,CAAC,OAAS,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB;gBACxB,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAI,CAAC,yBAAyB,CAAC,CAAC;QACvE,KAAI,CAAC,kCAAkC,EAAE,CAAC;;IAC5C,CAAC;IAED,gBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,EAAE,GAAA,GAAG,CAAC;IAC9D,CAAC;IAED,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;QACP,EAAE,GAAG,OAAA,SAAA,CAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC;QAChB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,OAAO,OAAA,SAAA,CAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,kCAAkC,GAAlC;QAAA,IAAA,QAAA,IAAA,CAsCC;QArCO,IAAA,KAAgG,IAAI,EAApF,WAAW,GAAA,GAAA,YAAA,EAA6B,wBAAwB,GAAA,GAAA,yBAAA,EAAQ,GAAG,GAAA,GAAA,IAAS,CAAC;QAC3G,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACrG,IAAA,QAAQ,GAAK,gBAAgB,CAAC,WAAW,EAAE,CAAA,QAAnC,CAAoC;QAEpD,IAAM,4CAA4C,GAAG,SAAA,iBAAiB;YACpE,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,SAAS,CAAC;QAC3E,CAAC,CAAC;QAEF,IAAM,4BAA4B,GAAG,AAAC,SAAS,iCAAiC,CAAC,kBAAuB;YAAvB,IAAA,uBAAA,KAAA,GAAA;gBAAA,qBAAA,CAAA,CAAuB;YAAA;YACtG,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;gBAC1C,OAAO,4CAA4C,CAAC,kBAAkB,CAAC,CAAC;aACzE,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC5C,OAAO,kBAAkB,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;aAC/E,MAAM,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBACnC,OAAO,iCAAiC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACpE,MAAM,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBACnC,OAAO,iCAAiC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACpE;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEzB,IAAI,wBAAwB,KAAK,MAAM,IAAI,4BAA4B,EAAE;YACvE,IAAI,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBAC9C,GAAG,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;gBAC5E,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9E,IAAI,CAAC,oCAAoC,GAAG;oBAC1C,GAAG,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;oBAClG,SAAS,CAAC,YAAY,CAAC,mBAAmB,CAAC,cAAc,EAAE,KAAI,CAAC,eAAe,CAAC,CAAC;oBACjF,KAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;gBACnD,CAAC,CAAC;aACH;SACF,MAAM;YACL,GAAG,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,oCAAoC,EAAE;gBAC7C,IAAI,CAAC,oCAAoC,EAAE,CAAC;aAC7C;SACF;IACH,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,WAAW;QAA3B,IAAA,QAAA,IAAA,CASC;QARC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC3C,OAAO,OAAA,SAAA,CAAM,eAAe,CAAC,IAAI,CAAC,KAAI,EAAE,WAAW,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,OAAA,SAAA,CAAM,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,qBAAqB,GAArB;QAAA,IAAA,QAAA,IAAA,CAUC;QATC,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,IAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,WAAW,EAAE;YAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,QAAQ;QAAA,CAAE,CAAC,CAAC;QACjH,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC;YAC3C,oGAAoG;YACpG,qGAAqG;YACrG,oDAAoD;YACpD,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,KAAI,CAAC,kCAAkC,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACH,gBAAA,SAAA,CAAA,oBAAoB,GAApB,SAAqB,gBAAgB;QAArC,IAAA,QAAA,IAAA,CAeC;QAdO,IAAA,KAAmC,IAAI,EAA/B,GAAG,GAAA,GAAA,IAAA,EAAE,iBAAiB,GAAA,GAAA,iBAAS,CAAC;QAC9C,IAAI,OAAO,GAAG,OAAA,SAAA,CAAM,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAEtE,IAAI,WAAW,EAAE,IAAI,CAAC,CAAC,iBAAiB,EAAE;YACxC,GAAG,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;YAClF,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;gBAAM,OAAA,iBAAiB,CAAC,KAAI,CAAC,QAAQ,CAAC;YAAhC,CAAgC,CAAC,CAAC,IAAI,CAAC,SAAA,QAAQ;gBAC1E,GAAG,CAAC,KAAK,CAAC,oBAAA,CAAkB,QAAQ,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,YAAY,CAAE,CAAC,CAAC;gBACtF,OAAO,QAAQ,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC;oBAC7D,OAAO,OAAA,SAAA,CAAM,oBAAoB,CAAC,IAAI,CAAC,KAAI,EAAE,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC9E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACH,gBAAA,SAAA,CAAA,OAAO,GAAP;QACE,OAAO,OAAA,SAAA,CAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;MAIE,CAAA;;;;;;;;OAQC,CACH,gBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG,CACH,gBAAA,SAAA,CAAA,OAAO,GAAP;QACE,OAAO,OAAA,SAAA,CAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG,CACH,gBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,oCAAoC,EAAE;YAC7C,IAAI,CAAC,oCAAoC,EAAE,CAAC;SAC7C;QACD,OAAO,OAAA,SAAA,CAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AA1QD,CAA8B,oBAAoB,GA0QjD;AAED;;;;;GAKG,CAEH;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;;GAMG,CAEH;;;;;;;;GAQG,CAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 2042, "column": 0}, "map": {"version": 3, "file": "localaudiotrack.js", "sourceRoot": "", "sources": ["../../../../lib/media/track/es5/localaudiotrack.js"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,4EAA4E;AAC5E,4EAA4E;AAC5E,YAAY,CAAC;AAEb,IAAM,QAAQ,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACrD,IAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE3D,SAAS,eAAe,CAAC,gBAAgB,EAAE,OAAO;IAChD,IAAM,KAAK,GAAG,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IACxD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,QAAQ,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;AAEhD,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "file": "capturevideoframes.js", "sourceRoot": "", "sources": ["../../../lib/media/track/capturevideoframes.js"], "names": [], "mappings": "AAAA,iFAAA,EAAmF,CACnF,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,kBAAkB,GAAK,OAAO,CAAC,sBAAsB,CAAC,6EAAA,kBAApC,CAAqC;AAE/D,SAAS,6BAA6B,CAAC,OAAO,EAAE,iBAAiB;IACzD,IAAA,KAAA,OAAU,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,EAAA,EAAA,EAA3C,KAAK,GAAA,EAAA,CAAA,EAAsC,CAAC;IAC3C,IAAA,KAAmC,KAAK,CAAC,WAAW,EAAE,CAAA,SAAxB,EAA9B,SAAS,GAAA,OAAA,KAAA,IAAG,kBAAkB,GAAA,EAAA,CAAyB;IAC/D,IAAI,cAAc,CAAC;IAEnB,IAAM,QAAQ,GAAG,IAAI,cAAc,CAAC;QAClC,KAAK,EAAA,SAAC,UAAU;YACd,cAAc,GAAG,WAAW,CAC1B;gBAAM,OAAA,UAAU,CAAC,OAAO,EAAE;YAApB,CAAoB,EAC1B,IAAI,GAAG,SAAS,CACjB,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;IAEH,IAAM,WAAW,GAAG,IAAI,eAAe,CAAC;QACtC,SAAS,EAAA;YACP,OAAO,iBAAiB,EAAE,CAAC;QAC7B,CAAC;KACF,CAAC,CAAC;IAEH,QAAQ,CACL,WAAW,CAAC,WAAW,CAAC,CACxB,MAAM,CAAC,IAAI,cAAc,EAAE,CAAC,CAC5B,IAAI,CAAC,YAAmB,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,aAAa,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,mCAAmC,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc;IAC/E,IAAA,KAAA,OAAU,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,EAAA,EAAA,EAA3C,KAAK,GAAA,EAAA,CAAA,EAAsC,CAAC;IAC3C,IAAA,QAAQ,GAAK,IAAI,yBAAyB,CAAC;QAAE,KAAK,EAAA,KAAA;IAAA,CAAE,CAAC,CAAA,QAA7C,CAA8C;IAC9D,IAAM,SAAS,GAAG,IAAI,yBAAyB,CAAC;QAAE,IAAI,EAAE,OAAO;IAAA,CAAE,CAAC,CAAC;IACnE,IAAI,UAAU,GAAG,KAAK,CAAC;IAEvB,IAAM,WAAW,GAAG,IAAI,eAAe,CAAC;QACtC,SAAS,EAAA,SAAC,UAAU,EAAE,UAAU;YAC9B,IAAM,OAAO,GAAG,cAAc,KAAK,YAAY,GAC3C,iBAAiB,CAAC,UAAU,CAAC,GAC7B,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAClC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7B,OAAO,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,UAAU,EAAE;oBACd,UAAU,CAAC,SAAS,EAAE,CAAC;iBACxB;YACH,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;IAEH,QAAQ,CACL,WAAW,CAAC,WAAW,CAAC,CACxB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAC1B,IAAI,CAAC,YAAmB,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,OAAO,yBAAyB,KAAK,UAAU,IAAI,OAAO,yBAAyB,KAAK,UAAU,GAC/G,mCAAmC,GACnC,6BAA6B,CAAC", "debugId": null}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "file": "videoprocessoreventobserver.js", "sourceRoot": "", "sources": ["../../../lib/media/track/videoprocessoreventobserver.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,QAAQ,CAAC,sDAAA,YAAtB,CAAuB;AACnC,IAAA,yCAAyC,GAAK,OAAO,CAAC,sBAAsB,CAAC,6EAAA,yCAApC,CAAqC;AAEtF;;;;;GAKG,CACH,IAAA,8BAAA,SAAA,MAAA;IAA0C,UAAA,6BAAA,QAAY;IAEpD;;;OAGG,CACH,SAAA,4BAAY,GAAG;QAAf,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAkDR;QAhDC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,KAAK,EAAE,SAAA,IAAI;YACjB,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACrC,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,KAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACjB,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YAChB,IAAM,IAAI,GAAG,KAAI,CAAC,aAAa,EAAE,CAAC;YAClC,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACf,KAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,MAAM,EAAE,SAAA,OAAO;YACrB,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;gBAAE,OAAO,EAAA,OAAA;YAAA,CAAE,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YAAM,OAAA,KAAI,CAAC,eAAe,EAAE;QAAtB,CAAsB,CAAC,CAAC;;IACjD,CAAC;IAED;;OAEG,CACH,4BAAA,SAAA,CAAA,aAAa,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO,CAAA,CAAE,CAAC;SACX;QAEK,IAAA,KAQF,IAAI,CAAC,cAAc,EAPrB,SAAS,GAAA,GAAA,SAAA,EACT,aAAa,GAAA,GAAA,aAAA,EACb,YAAY,GAAA,GAAA,YAAA,EACZ,cAAc,GAAA,GAAA,cAAA,EACd,kBAAkB,GAAA,GAAA,kBAAA,EAClB,oBAAoB,GAAA,GAAA,oBAAA,EACpB,4BAA4B,GAAA,GAAA,4BACP,CAAC;QACxB,IAAM,IAAI,GAAG;YAAE,aAAa,EAAA,aAAA;YAAE,YAAY,EAAA,YAAA;YAAE,cAAc,EAAA,cAAA;YAAE,kBAAkB,EAAA,kBAAA;YAAE,oBAAoB,EAAA,oBAAA;YAAE,4BAA4B,EAAA,4BAAA;QAAA,CAAE,CAAC;QACrI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,gBAAgB,CAAC;QAEhD;YAAC,YAAY;YAAE,kBAAkB;YAAE,UAAU;YAAE,SAAS;YAAE,eAAe;YAAE,gBAAgB;YAAE,UAAU;YAAE,SAAS;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAC9H,IAAM,GAAG,GAAG,SAAS,CAAC,MAAI,IAAM,CAAC,CAAC;YAClC,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aAClB;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG,CACH,4BAAA,SAAA,CAAA,eAAe,GAAf;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxC,OAAO;SACR;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,EAAE;YACxC,OAAO;SACR;QAED,IAAM,KAAK,GAAG;YAAE,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,sBAAsB,CAAC;QAAA,CAAE,CAAC;QAC7E;YAAC,mBAAmB;YAAE,uBAAuB;YAAE,uBAAuB;YAAE,mBAAmB;YAAE,mBAAmB;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAC5H,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,GAAG,GAAG,IAAI,CAAC,qBAAqB,GAAG,yCAAyC,EAAE;YAChF,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;QACjC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,SAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,SAAA,IAAI;gBAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpB;gBACD,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,AAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC;QAEP,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG,CACH,4BAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,IAAI,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAkB,IAAM,EAAE,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAAE,IAAI,EAAA,IAAA;YAAE,IAAI,EAAA,IAAA;QAAA,CAAE,CAAC,CAAC;IACrC,CAAC;IACH,OAAA,2BAAC;AAAD,CAAC,AArJD,CAA0C,YAAY,GAqJrD;AAED,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC", "debugId": null}}, {"offset": {"line": 2321, "column": 0}, "map": {"version": 3, "file": "videotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/videotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,kBAAkB,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC3D,IAAM,2BAA2B,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AACrE,IAAA,YAAY,GAAK,OAAO,CAAC,mBAAmB,CAAC,mFAAA,YAAjC,CAAkC;AAC9C,IAAA,kBAAkB,GAAK,OAAO,CAAC,sBAAsB,CAAC,6EAAA,kBAApC,CAAqC;AAE/D;;;;;;;;;;;;;;;;;;;;GAoBG,CACH,IAAA,aAAA,SAAA,MAAA;IAAyB,UAAA,YAAA,QAAU;IACjC;;;;OAIG,CACH,SAAA,WAAY,qBAAqB,EAAE,OAAO;QAA1C,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,qBAAqB,EAAE,OAAO,CAAC,IAAA,IAAA,CA+CtC;QA9CC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,CAAA,CAAE;gBACT,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,YAAO,CAAC;gBACf,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,IAAI;iBACb;aACF;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;QAEnH,OAAO,KAAI,CAAC;IACd,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,wBAAwB,GAAxB,SAAyB,YAAoB;QAApB,IAAA,iBAAA,KAAA,GAAA;YAAA,eAAA,KAAoB;QAAA;QAC3C,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAC5B,IAAI,OAAO,GAAG,EAAE,CAAC;QACX,IAAA,KAA0B,IAAI,CAAC,gBAAgB,EAA7C,OAAO,GAAA,GAAA,OAAA,EAAE,UAAU,GAAA,GAAA,UAA0B,CAAC;QAEtD,IAAI,CAAC,OAAO,EAAE;YACZ,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,8BAA8B,CAAC;SAC1C;QACD,IAAI,UAAU,KAAK,OAAO,EAAE;YAC1B,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,2BAA2B,CAAC;SACvC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,8BAA8B,CAAC;SAC1C;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5C,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,gEAAgE,CAAC;SAC5E;QAED,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QACD,OAAO;YAAE,gBAAgB,EAAA,gBAAA;YAAE,OAAO,EAAA,OAAA;QAAA,CAAE,CAAC;IACvC,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,cAAc,GAAd;QAAA,IAAA,QAAA,IAAA,CAgFC;QA/EC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;YAC/E,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,gBAAgB,EAAE;YACrD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACvE,OAAO;SACR;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAElC,IAAA,oBAAoB,GAAK,IAAI,CAAC,iBAAiB,CAAA,oBAA3B,CAA4B;QAExD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;YACxB,IAAM,OAAO,GAAG,SAAA,UAAU;gBACxB,IAAM,WAAW,GAAG,KAAI,CAAC,wBAAwB,EAAE,CAAC;gBACpD,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;oBACjC,IAAI,UAAU,EAAE;wBACd,UAAU,CAAC,KAAK,EAAE,CAAC;qBACpB;oBACD,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,KAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC/D,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;oBACrE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBACK,IAAA,KAA4B,KAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAA7D,KAAA,GAAA,KAAS,EAAT,KAAK,GAAA,OAAA,KAAA,IAAG,CAAC,GAAA,EAAA,EAAE,KAAA,GAAA,MAAU,EAAV,MAAM,GAAA,OAAA,KAAA,IAAG,CAAC,GAAA,EAAwC,CAAC;gBACtE,mDAAmD;gBACnD,iCAAiC;gBACjC,IAAI,KAAI,CAAC,YAAY,IAAI,KAAI,CAAC,YAAY,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC1D,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;oBAChC,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;iBACnC;gBACD,IAAI,KAAI,CAAC,WAAW,EAAE;oBACpB,IAAI,KAAI,CAAC,WAAW,CAAC,KAAK,KAAK,KAAK,EAAE;wBACpC,KAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;wBAC/B,KAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;qBAClC;oBACD,KAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,CACzC,KAAI,CAAC,QAAQ,EACb,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,CACP,CAAC;iBACH;gBACD,IAAM,KAAK,GAAG,UAAU,IAAI,CAC1B;oBAAC,OAAO;oBAAE,YAAY;iBAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC,GAClD,KAAI,CAAC,QAAQ,GACb,KAAI,CAAC,WAAW,CACrB,CAAC;gBACF,IAAI,MAAM,GAAG,IAAI,CAAC;gBAElB,IAAI;oBACF,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC;iBAChE,CAAC,OAAO,EAAE,EAAE;oBACX,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE,EAAE,CAAC,CAAC;iBACvE;gBACD,OAAO,CAAC,AAAC,MAAM,YAAY,OAAO,CAAC,CAAC,CAAC,AAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CACpE,IAAI,CAAC;oBACJ,IAAI,KAAI,CAAC,YAAY,EAAE;wBACrB,IAAI,OAAO,KAAI,CAAC,cAAc,CAAC,YAAY,KAAK,UAAU,EAAE;4BAC1D,KAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;yBACpC;wBACD,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC5C;gBACH,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YACF,KAAI,CAAC,YAAY,GAAG,kBAAkB,CACpC,KAAI,CAAC,QAAQ,EACb,OAAO,EACP,oBAAoB,CACrB,CAAC;QACJ,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;YAAI,OAAA,KAAI,CAAC,IAAI,CAAC,KAAK,CAC/B,gCAAgC,EAChC;gBAAE,KAAK,EAAA,KAAA;gBAAE,KAAK,EAAE,KAAI;YAAA,CAAE,CACvB;QAHiB,CAGjB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,WAAW,GAAX;QAAA,IAAA,QAAA,IAAA,CAoBC;QAnBC,OAAA,SAAA,CAAM,WAAW,CAAA,IAAA,CAAA,IAAA,CAAE,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG;gBAC/B,IAAI,iBAAiB,CAAC,KAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1C,KAAI,CAAC,UAAU,CAAC,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACjD,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;iBACpD;YACH,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;gBACvB,IAAI,iBAAiB,CAAC,KAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1C,KAAI,CAAC,UAAU,CAAC,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACjD,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;oBACnD,IAAI,KAAI,CAAC,SAAS,EAAE;wBAClB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAI,CAAC,UAAU,CAAC,CAAC;wBACxD,KAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,KAAI,CAAC,CAAC;qBAChD;iBACF;YACH,CAAC,CAAC;SACH;IACH,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,iBAAiB,GAAjB;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,SAAS,EAAE;YACb,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;SAChD;IACH,CAAC;IAED;;OAEG,CACH,WAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAO;QACZ,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC/C,OAAO,OAAA,SAAA,CAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG,CACH,WAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,SAAS,EAAE,OAAO;QAA/B,IAAA,QAAA,IAAA,CAgGC;QA/FC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,UAAU,EAAE;YAC9D,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,SAAS,CAAC,CAAC;QAEtE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG;gBACpB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC5C,oGAAoG;gBACpG,4FAA4F;gBAC5F,wFAAwF;gBACxF,IAAI,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE;oBAC7B,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC;oBAClG,KAAI,CAAC,iBAAiB,EAAE,CAAC;iBAC1B;YACH,CAAC,CAAC;YACF,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;gBAC1C,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aACvE,MAAM;gBACL,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjE;SACF;QAED,IAAI,CAAC,iBAAiB,GAAG,OAAO,IAAI,CAAA,CAAE,CAAC;QACnC,IAAA,KAAyD,IAAI,CAAC,iBAAiB,EAA7E,oBAAoB,GAAA,GAAA,oBAAA,EAAE,4BAA4B,GAAA,GAAA,4BAA2B,CAAC;QACpF,IAAI,OAAO,eAAe,KAAK,WAAW,IAAI,oBAAoB,KAAK,iBAAiB,EAAE;YACxF,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;QACD,IAAI,oBAAoB,IACnB,oBAAoB,KAAK,YAAY,IACrC,oBAAoB,KAAK,OAAO,IAChC,oBAAoB,KAAK,QAAQ,IACjC,oBAAoB,KAAK,iBAAiB,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,qCAAmC,oBAAsB,CAAC,CAAC;SAC5E;QACD,IAAI,CAAC,oBAAoB,EAAE;YACzB,oBAAoB,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC;SAC9F;QAEK,IAAA,KAA4D,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAA7F,KAAA,GAAA,KAAS,EAAT,KAAK,GAAA,OAAA,KAAA,IAAG,CAAC,GAAA,EAAA,EAAE,KAAA,GAAA,MAAU,EAAV,MAAM,GAAA,OAAA,KAAA,IAAG,CAAC,GAAA,EAAA,EAAE,KAAA,GAAA,SAA8B,EAA9B,SAAS,GAAA,OAAA,KAAA,IAAG,kBAAkB,GAAA,EAAwC,CAAC;QACtG,IAAI,oBAAoB,KAAK,iBAAiB,EAAE;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SACvD;QACD,IAAI,oBAAoB,KAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;SAClC;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,wFAAwF;QACxF,6FAA6F;QAC7F,uFAAuF;QACvF,4BAA4B,GAAG,4BAA4B,IAAI,IAAI,CAAC;QAEpE,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,8CAA4C,4BAA4B,GAAA,GAAG,CAAC,CAAC;SAC9F;QAED,sGAAsG;QACtG,kHAAkH;QAClH,8DAA8D;QAC9D,IAAM,SAAS,GAAG,OAAO,6BAA6B,KAAK,WAAW,IAAI,6BAA6B,CAAC,SAAS,IAC/G,2BAA2B;QAC3B,OAAO,6BAA6B,CAAC,SAAS,CAAC,YAAY,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE7F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QAC5D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,EAAE;YACvC,SAAS,EAAA,SAAA;YACT,aAAa,EAAE,MAAM;YACrB,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,SAAS;YACzB,kBAAkB,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAChE,oBAAoB,EAAA,oBAAA;YACpB,4BAA4B,EAAA,4BAAA;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;MAcE,CAAA;;;;;;;;;;;;;;;;;;;;;;MAsBA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;OAyBC,CACH,WAAA,SAAA,CAAA,MAAM,GAAN;QACE,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;MAME,CAAA;;;;;;;;MAQA,CAAA;;;;;;;;OAQC,CACH,WAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG,CACH,WAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,SAAS;QACvB,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAC7E;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,YAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzE,IAAI,CAAC,iBAAiB,GAAG,CAAA,CAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AAveD,CAAyB,UAAU,GAuelC;AAED,UAAU,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;AAEpD,SAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI;IACpC,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,IAC5C,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC;AACpD,CAAC;AAED;;;;;;;GAOG,CAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG,CAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG,CAEH;;;;GAIG,CAEH;;;;GAIG,CAEH;;;;GAIG,CAEH;;;;;GAKG,CAEH,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC", "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "file": "localvideotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localvideotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,6EAAA,KAA3C,CAA4C;AACzD,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC1D,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C,IAAM,oBAAoB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;;;;;;;;;;;;;;;;GAoBG,CACH,IAAA,kBAAA,SAAA,MAAA;IAA8B,UAAA,iBAAA,QAAoB;IAChD;;;;OAIG,CACH,SAAA,gBAAY,gBAAgB,EAAE,OAAO;QAArC,IAAA,QAAA,IAAA,CA2BC;QA1BC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,0BAA0B,EAAE,KAAK,EAAE,IAC9B,OAAO,QAAQ,KAAK,WAAW,IAC/B,OAAO,QAAQ,CAAC,aAAa,KAAK,UAAU;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,gBAAgB,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;QAEjC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B,GACrC,0BAA0B,GAC1B,IAAI;aACT;YACD,kCAAkC,EAAE;gBAClC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,0EAA0E;QAC1E,gFAAgF;QAChF,0CAA0C;QAC1C,IAAI,KAAI,CAAC,2BAA2B,EAAE;YACpC,KAAI,CAAC,kCAAkC,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;SAC5F;;IACH,CAAC;IAED,gBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,EAAE,GAAA,GAAG,CAAC;IAC9D,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,wBAAwB,GAAxB;QACE,OAAO,OAAA,SAAA,CAAM,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,OAAO,OAAA,SAAA,CAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,0BAA0B,GAA1B,SAA2B,YAAY;QAAvC,IAAA,QAAA,IAAA,CAUC;QATC,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,IAAM,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAE/E,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAC3D,KAAK,CAAC,SAAA,KAAK;YAAI,OAAA,KAAI,CAAC,IAAI,CAAC,IAAI,CAC5B,4DAA4D,EAAE;gBAAE,KAAK,EAAA,KAAA;gBAAE,gBAAgB,EAAA,gBAAA;YAAA,CAAE,CAAC;QAD5E,CAC4E,CAAC,CAC5F,IAAI,CAAC;YACJ,KAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;QAClE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG,CACH,gBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAChE,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kFAAkF,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzH,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAEtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG,CACH,gBAAA,SAAA,CAAA,eAAe,GAAf;QAAA,IAAA,QAAA,IAAA,CASC;QARC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACpE,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;QACnG,IAAI,CAAC,0BAA0B,EAAE,CAC9B,IAAI,CAAC;YAAM,OAAA,KAAI,CAAC,+BAA+B,EAAE;QAAtC,CAAsC,CAAC,CAAC;QAEtD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACH,gBAAA,SAAA,CAAA,OAAO,GAAP;QACE,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;SACrC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;MAKE,CAAA;;;;;;;;;OASC,CACH,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAc;QAAd,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,IAAc;QAAA;QACnB,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;YAEtC,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kFAAkF,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACzH,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;aACvC;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG,CACH,gBAAA,SAAA,CAAA,OAAO,GAAP;QAAA,IAAA,QAAA,IAAA,CAmBC;QAlBC,IAAI,IAAI,CAAC,kCAAkC,EAAE;YAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;SAChD;QAED,IAAM,OAAO,GAAG,OAAA,SAAA,CAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC;gBACX,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;YACpC,OAAO,CAAC,OAAO,CAAC;gBACd,KAAI,CAAC,kCAAkC,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACH,gBAAA,SAAA,CAAA,IAAI,GAAJ;QACE,IAAI,IAAI,CAAC,kCAAkC,EAAE;YAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;SAChD;QACD,OAAO,OAAA,SAAA,CAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAtPD,CAA8B,oBAAoB,GAsPjD;AAED;;;;;;;GAOG,CACH,SAAS,0BAA0B,CAAC,eAAe,EAAE,GAAG;IAC9C,IAAM,GAAG,GAAK,eAAe,CAAA,IAApB,CAAqB;IAChC,IAAU,EAAE,GAAuB,eAAe,CAAA,QAAtC,EAAE,gBAAgB,GAAK,eAAe,CAAA,gBAApB,CAAqB;IAEzD,SAAS,QAAQ;QACf,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;YAC9B,OAAO;SACR;QACD,GAAG,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEtC,mEAAmE;QACnE,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;YAAM,OAAA,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC;QAA1B,CAA0B,CAAC,CAAC,IAAI,CAAC,SAAA,QAAQ;YAC5D,IAAI,CAAC,QAAQ,EAAE;gBACb,GAAG,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBAC9D,OAAO;aACR;YACD,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAEzC,oEAAoE;YACpE,uEAAuE;YACvE,qCAAqC;YACrC,eAAe,CAAC,KAAK,EAAE,CAAC;YAExB,+BAA+B;YAC/B,6CAA6C;YAC7C,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,KAAK;YACZ,GAAG,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,0EAA0E;YAC1E,kCAAkC;YAClC,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC;YAC9B,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACvD,EAAE,CAAC,KAAK,EAAE,CAAC;aACZ;YAED,4BAA4B;YAC5B,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACzD,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;YACpD,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;gBACrC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aACvD,MAAM;gBACL,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,0BAA0B;IAC1B,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;QACrC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KACvD,MAAM;QACL,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC;KACtC;IAED,OAAO;QACL,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;YACxC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC1D,MAAM;YACL,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;SAClC;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG,CAEH;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;;GAMG,CAEH;;;;;;;;GAQG,CAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 3274, "column": 0}, "map": {"version": 3, "file": "localvideotrack.js", "sourceRoot": "", "sources": ["../../../../lib/media/track/es5/localvideotrack.js"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,4EAA4E;AAC5E,4EAA4E;AAC5E,YAAY,CAAC;AAEb,IAAM,QAAQ,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAErD,IAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE3D,SAAS,eAAe,CAAC,gBAAgB,EAAE,OAAO;IAChD,IAAM,KAAK,GAAG,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IACxD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,QAAQ,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;AAEhD,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "file": "localdatatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localdatatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAM,sBAAsB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG,CACH,IAAA,iBAAA,SAAA,MAAA;IAA6B,UAAA,gBAAA,QAAK;IAChC;;;OAGG,CACH,SAAA,eAAY,OAAO;QAAnB,IAAA,QAAA,IAAA,CA0CC;QAzCC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,eAAe,EAAE,sBAAsB;YACvC,iBAAiB,EAAE,IAAI;YACvB,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,IAAI;SACd,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAChD,IAAM,eAAe,GAAG,IAAI,eAAe,CACzC,OAAO,CAAC,iBAAiB,EACzB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnB,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;QAE3C,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,eAAe;aACvB;YACD,EAAE,EAAE;gBACF,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,eAAe,CAAC,EAAE;aAC1B;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,iBAAiB;aACjC;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,cAAc;aAC9B;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,OAAO;aACvB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,iBAAiB,KAAK,IAAI,IACpC,OAAO,CAAC,cAAc,KAAK,IAAI;aACrC;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;;OAIG,CACH,eAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,IAAI;QACP,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AAzDD,CAA6B,KAAK,GAyDjC;AAED;;;;;;;;;;;;;GAaG,CAEH,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC", "debugId": null}}, {"offset": {"line": 3433, "column": 0}, "map": {"version": 3, "file": "localdatatrack.js", "sourceRoot": "", "sources": ["../../../../lib/media/track/es5/localdatatrack.js"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,4EAA4E;AAC5E,4EAA4E;AAC5E,YAAY,CAAC;AAEb,IAAM,QAAQ,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAErD,IAAM,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAEzD,SAAS,cAAc,CAAC,OAAO;IAC7B,IAAM,KAAK,GAAG,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;IACvD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,QAAQ,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;AAE9C,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC", "debugId": null}}, {"offset": {"line": 3451, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lib/media/track/es5/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,MAAM,CAAC,OAAO,GAAG;IACf,eAAe,EAAE,OAAO,CAAC,mBAAmB,CAAC;IAC7C,eAAe,EAAE,OAAO,CAAC,mBAAmB,CAAC;IAC7C,cAAc,EAAE,OAAO,CAAC,kBAAkB,CAAC;CAC5C,CAAC", "debugId": null}}, {"offset": {"line": 3462, "column": 0}, "map": {"version": 3, "file": "trackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/trackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC7C,IAAA,KAAkC,OAAO,CAAC,YAAY,CAAC,oFAArD,cAAc,GAAA,GAAA,cAAA,EAAE,WAAW,GAAA,GAAA,WAA0B,CAAC;AACtD,IAAA,iBAAiB,GAAK,OAAO,CAAC,sBAAsB,CAAC,6EAAA,iBAApC,CAAqC;AAC9D,IAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACtC,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;;;;GAOG,CACH,IAAA,mBAAA,SAAA,MAAA;IAA+B,UAAA,kBAAA,QAAY;IACzC;;;;;OAKG,CACH,SAAA,iBAAY,SAAS,EAAE,QAAQ,EAAE,OAAO;QAAxC,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAwBR;QAtBC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,iBAAiB;SAC5B,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEnD,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,WAAW,EAAE;gBACX,KAAK,EAAE,UAAU,EAAE;aACpB;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC;aACtH;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,QAAQ;aAChB;SACF,CAAC,CAAC;;IACL,CAAC;IAED,iBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IACrE,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAzCD,CAA+B,YAAY,GAyC1C;AAED;;;GAGG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 3559, "column": 0}, "map": {"version": 3, "file": "localtrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localtrackpublication.js"], "names": [], "mappings": "AAAA,oBAAA,EAAsB,CACtB,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACjD,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,8EAApD,CAAC,GAAA,GAAA,UAAA,EAAE,aAAa,GAAA,GAAA,aAAoC,CAAC;AAEzE;;;;;;;;;;;GAWG,CACH,IAAA,wBAAA,SAAA,MAAA;IAAoC,UAAA,uBAAA,QAAgB;IAClD;;;;;;;;;OASG,CACH,SAAA,sBAAY,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;QAAhD,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,IAAA,IAAA,CA+C1C;QA7CC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE;oBAAC,IAAA,OAAA,EAAA,CAAO;wBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;wBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;oBAAK,OAAA,KAAI,CAAC,IAAI,CAAA,KAAA,CAAT,KAAI,EAAA,cAAA;wBACtB,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB;qBAAA,EAAA,OAChD,IAAI;gBAFW,CAGnB;aACF;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE;oBAAM,OAAA,KAAI,CAAC,IAAI,CAAC,KAAI,CAAC,cAAc,GACtC,cAAc,GACd,eAAe,CAAC;gBAFP,CAEO;aACrB;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;gBAClE,CAAC;aACF;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,IAAI;aAClB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,SAAS,CAAC,eAAe,CAAC;gBACnC,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK;aACb;SACF,CAAC,CAAC;QAEH;YAAC,UAAU;YAAE,SAAS;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAClC,OAAA,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC;QAAtC,CAAsC,CAAC,CAAC;QAE1C;YAAC,SAAS;YAAE,iBAAiB;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YACzC,OAAA,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,KAAI,CAAC,qBAAqB,CAAC;QAA9C,CAA8C,CAAC,CAAC;;IACpD,CAAC;IAED,sBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,6BAA2B,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC1E,CAAC;IAED;;;;;OAKG,CACH,sBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAQ;QAClB,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACH,sBAAA,SAAA,CAAA,SAAS,GAAT;QAAA,IAAA,QAAA,IAAA,CASC;QARC;YAAC,UAAU;YAAE,SAAS;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YAClC,OAAA,KAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC;QAAvD,CAAuD,CAAC,CAAC;QAE3D;YAAC,SAAS;YAAE,iBAAiB;SAAC,CAAC,OAAO,CAAC,SAAA,IAAI;YACzC,OAAA,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,qBAAqB,CAAC;QAAhE,CAAgE,CAAC,CAAC;QAEpE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAhGD,CAAoC,gBAAgB,GAgGnD;AAED;;;;;GAKG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "file": "localaudiotrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localaudiotrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAEjE;;;;;;GAMG,CACH,IAAA,6BAAA,SAAA,MAAA;IAAyC,UAAA,4BAAA,QAAqB;IAC5D;;;;;;;;OAQG,CACH,SAAA,2BAAY,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;eAC9C,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC7C,CAAC;IAED,2BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,kCAAgC,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC/E,CAAC;IACH,OAAA,0BAAC;AAAD,CAAC,AAjBD,CAAyC,qBAAqB,GAiB7D;AAED,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 3794, "column": 0}, "map": {"version": 3, "file": "localdatatrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localdatatrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAEjE;;;;;;GAMG,CACH,IAAA,4BAAA,SAAA,MAAA;IAAwC,UAAA,2BAAA,QAAqB;IAC3D;;;;;;;;OAQG,CACH,SAAA,0BAAY,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;eAC9C,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC7C,CAAC;IAED,0BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,iCAA+B,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC9E,CAAC;IACH,OAAA,yBAAC;AAAD,CAAC,AAjBD,CAAwC,qBAAqB,GAiB5D;AAED,MAAM,CAAC,OAAO,GAAG,yBAAyB,CAAC", "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "file": "localvideotrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localvideotrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAEjE;;;;;;GAMG,CACH,IAAA,6BAAA,SAAA,MAAA;IAAyC,UAAA,4BAAA,QAAqB;IAC5D;;;;;;;;OAQG,CACH,SAAA,2BAAY,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;eAC9C,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC7C,CAAC;IAED,2BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,kCAAgC,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC/E,CAAC;IACH,OAAA,0BAAC;AAAD,CAAC,AAjBD,CAAyC,qBAAqB,GAiB7D;AAED,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 3898, "column": 0}, "map": {"version": 3, "file": "remotemediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotemediatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,8EAApD,CAAC,GAAA,GAAA,UAAA,EAAE,aAAa,GAAA,GAAA,aAAoC,CAAC;AACjE,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,6EAAA,KAA3C,CAA4C;AACzD,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAErF,SAAS,qBAAqB,CAAC,iBAAiB;IAC9C;;;;;;;;;;;OAWG,CACH,OAAA,SAAA,MAAA;QAAsC,UAAA,kBAAA,QAAiB;QACrD;;;;;;;;;;WAUG,CACH,SAAA,iBAAY,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO;YAAlG,IAAA,QAAA,IAAA,CAiEC;YAhEC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBACtB,6GAA6G;gBAC7G,2GAA2G;gBAC3G,yCAAyC;gBACzC,yBAAyB,EAAE,KAAK,EAAE,IAC7B,OAAO,QAAQ,KAAK,QAAQ,IAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU,IAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;aAClD,EAAE,OAAO,CAAC,CAAC;YAEZ,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,kBAAkB,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;YAEnC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;gBAC5B,UAAU,EAAE;oBACV,KAAK,EAAE,SAAS;oBAChB,QAAQ,EAAE,IAAI;iBACf;gBACD,cAAc,EAAE;oBACd,KAAK,EAAE,aAAa;oBACpB,QAAQ,EAAE,IAAI;iBACf;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,WAAW;iBACnB;gBACD,cAAc,EAAE;oBACd,KAAK,EAAE,SAAA,UAAU;wBACf,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;wBACrD,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC5B,CAAC;iBACF;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,IAAI,CAAC,UAAU,CAAC;oBACzB,CAAC;iBACF;gBACD,aAAa,EAAE;oBACb,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,IAAI,CAAC,cAAc,CAAC;oBAC7B,CAAC;iBACF;gBACD,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI;oBAChB,GAAG,EAAA;wBACD,OAAO,IAAI,CAAC,SAAS,CAAC;oBACxB,CAAC;iBACF;gBACD,GAAG,EAAE;oBACH,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,GAAG;iBACX;gBACD,0BAA0B,EAAE;oBAC1B,KAAK,EAAE,OAAO,CAAC,yBAAyB;iBACzC;gBACD,iCAAiC,EAAE;oBACjC,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;;QACL,CAAC;QAED;;;;;;;WAOG,CACH,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAQ;YAClB,IAAM,cAAc,GAAA,cAAA;gBAAI,IAAI;aAAA,EAAA,OAAK,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAC,CAAC;YAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACtC,mCAAmC;gBACnC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;aACnD;YACD,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;gBAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;aAC7B;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;WAGG,CACH,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,SAAS;YACnB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;gBACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC3D;QACH,CAAC;QAED;;;WAGG,CACH,iBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,aAAa;YAC3B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;gBACzC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;aAC/D;QACH,CAAC;QAED,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;YACP,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,EAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,IAAI,EAAE;gBAC1C,4DAA4D;gBAC5D,6DAA6D;gBAC7D,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACrC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC;iBACpC;gBAED,4EAA4E;gBAC5E,kFAAkF;gBAClF,mEAAmE;gBACnE,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,cAAc,EAAE,CAAC;iBACvB;aACF;YACD,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACnC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,iCAAiC,IAC1E,6BAA6B,CAAC,IAAI,CAAC,CAAC;aAC1C;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;YACP,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,EAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;gBAChC,2DAA2D;gBAC3D,0DAA0D;gBAC1D,+DAA+D;gBAC/D,6CAA6C;gBAC7C,wCAAwC;gBACxC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;gBACtC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;iBACrC;gBAED,IAAI,IAAI,CAAC,iCAAiC,EAAE;oBAC1C,2BAA2B;oBAC3B,IAAI,CAAC,iCAAiC,EAAE,CAAC;oBACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;iBAC/C;aACF;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACH,OAAA,gBAAC;IAAD,CAAC,AAzKM,CAA+B,iBAAiB,GAyKrD;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,gBAAgB;IAC7C,IAAM,GAAG,GAAW,gBAAgB,CAAA,IAA3B,EAAE,IAAI,GAAK,gBAAgB,CAAA,IAArB,CAAsB;IAE7C,SAAS,mBAAmB,CAAC,SAAS;QACpC,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,SAAA,EAAE;YACtC,IAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC/C,IAAM,qBAAqB,GAAG,EAAE,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/E,IAAI,qBAAqB,EAAE;gBACzB,GAAG,CAAC,IAAI,CAAC,mCAAiC,IAAI,GAAA,WAAW,CAAC,CAAC;gBAC3D,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC1B,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;gBACjD,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;oBACb,GAAG,CAAC,IAAI,CAAC,+CAA6C,IAAI,GAAA,WAAW,CAAC,CAAC;oBACvE,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;oBAC1B,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,GAAG;oBACV,GAAG,CAAC,IAAI,CAAC,+CAA6C,IAAI,GAAA,YAAY,EAAE;wBAAE,GAAG,EAAA,GAAA;wBAAE,EAAE,EAAA,EAAA;wBAAE,gBAAgB,EAAA,gBAAA;oBAAA,CAAE,CAAC,CAAC;gBACzG,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yEAAyE;IACzE,0FAA0F;IAC1F,+EAA+E;IAC/E,0DAA0D;IAC1D,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACrE,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;;;;GAQG,CAEH,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 4186, "column": 0}, "map": {"version": 3, "file": "remoteaudiotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remoteaudiotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE5D,IAAM,qBAAqB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAEhE;;;;;;;;;;;;;GAaG,CACH,IAAA,mBAAA,SAAA,MAAA;IAA+B,UAAA,kBAAA,QAAqB;IAClD;;;;;;;;;;OAUG,CACH,SAAA,iBAAY,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO;eAChG,OAAA,IAAA,CAAA,IAAA,EAAM,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,IAAA,IAAA;IAC/F,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,GAAG,GAAA,GAAG,CAAC;IAChE,CAAC;IAED;;OAEG,CACH,iBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,CAAE,CAAC;QACf,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,sEAAsE;YACtE,gCAAgC;YAChC,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C,MAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;aAChC;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED;;;;;;OAMG,CACH,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAQ;QAClB,OAAO,OAAA,SAAA,CAAM,WAAW,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AA/CD,CAA+B,qBAAqB,GA+CnD;AAED;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "file": "remotetrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotetrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;GAsBG,CACH,IAAA,yBAAA,SAAA,MAAA;IAAqC,UAAA,wBAAA,QAAgB;IACnD;;;;;OAKG,CACH,SAAA,uBAAY,SAAS,EAAE,OAAO;QAA9B,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,IAAA,IAAA,CA4E9C;QA1EC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,CAAC;aACF;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,SAAS,CAAC,SAAS,CAAC;gBAC7B,CAAC;aACF;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,SAAS,CAAC,IAAI;aACtB;YACD,eAAe,EAAE;gBACf,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,SAAS,CAAC,QAAQ,CAAC;gBAC5B,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;QAEH,2DAA2D;QAEzD,IAAA,KAAK,GAIH,SAAS,CAAA,KAJN,EACL,SAAS,GAGP,SAAS,CAAA,SAHF,EACT,aAAa,GAEX,SAAS,CAAA,aAFE,EACb,QAAQ,GACN,SAAS,CAAA,QADH,CACI;QAEd,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE;gBAC7B,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;gBACxB,KAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjD,OAAO;aACR;YACD,IAAI,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE;gBACrC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;gBAChC,IAAI,KAAI,CAAC,KAAK,EAAE;oBACd,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;iBAC7C;gBACD,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;aACnE;YACD,IAAI,aAAa,KAAK,SAAS,CAAC,aAAa,EAAE;gBAC7C,KAAI,CAAC,IAAI,CAAC,KAAK,CAAI,KAAI,CAAC,QAAQ,GAAA,OAAA,CAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAA,SAAA,CAAO,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;gBAClH,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;gBACxC,IAAI,KAAI,CAAC,KAAK,EAAE;oBACd,KAAI,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;oBACpD,KAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAG,KAAI,CAAC,KAAK,CAAC,CAAC;iBAChF,MAAM,IAAI,aAAa,EAAE;oBACxB,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;iBAC/D;aACF;YACD,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE;gBACnC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAC9B,KAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;aAC/C;QACH,CAAC,CAAC,CAAC;;IACL,CAAC;IAED,uBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,8BAA4B,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC3E,CAAC;IAED;;;OAGG,CACH,uBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,KAAK;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;OAEG,CACH,uBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;IACH,OAAA,sBAAC;AAAD,CAAC,AA/GD,CAAqC,gBAAgB,GA+GpD;AAED;;;;;;;GAOG,CAEH;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH;;;;GAIG,CAEH;;;;GAIG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,sBAAsB,CAAC", "debugId": null}}, {"offset": {"line": 4487, "column": 0}, "map": {"version": 3, "file": "remoteaudiotrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remoteaudiotrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEnE;;;;;;;;;;;GAWG,CACH,IAAA,8BAAA,SAAA,MAAA;IAA0C,UAAA,6BAAA,QAAsB;IAC9D;;;;;OAKG,CACH,SAAA,4BAAY,SAAS,EAAE,OAAO;eAC5B,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC3B,CAAC;IAED,4BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,mCAAiC,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAChF,CAAC;IACH,OAAA,2BAAC;AAAD,CAAC,AAdD,CAA0C,sBAAsB,GAc/D;AAED;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC", "debugId": null}}, {"offset": {"line": 4560, "column": 0}, "map": {"version": 3, "file": "remotedatatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotedatatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACtB,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,8EAApD,CAAC,GAAA,GAAA,UAAA,EAAE,aAAa,GAAA,GAAA,aAAoC,CAAC;AAEzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG,CACH,IAAA,kBAAA,SAAA,MAAA;IAA8B,UAAA,iBAAA,QAAK;IACjC;;;;;OAKG,CACH,SAAA,gBAAY,GAAG,EAAE,iBAAiB,EAAE,OAAO;QAA3C,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,EAAM,iBAAiB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,IAAA,IAAA,CAqD7C;QAnDC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,cAAc,EAAE;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;YACD,aAAa,EAAE;gBACb,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,cAAc,CAAC;gBAC7B,CAAC;aACF;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,iBAAiB;aAC3C;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,cAAc;aACxC;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,OAAO;aACjC;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAA;oBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACxB,CAAC;aACF;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,iBAAiB,KAAK,IAAI,IAC9C,iBAAiB,CAAC,cAAc,KAAK,IAAI;aAC/C;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,GAAG;aACX;SACF,CAAC,CAAC;QAEH,iBAAiB,CAAC,EAAE,CAAC,SAAS,EAAE,SAAA,IAAI;YAClC,KAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,KAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;;IACL,CAAC;IAED;;;;;;OAMG,CACH,gBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAQ;QAClB,IAAM,cAAc,GAAA,cAAA;YAAI,IAAI;SAAA,EAAA,OAAK,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtC,mCAAmC;YACnC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACnD;QAED,wDAAwD;QACxD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG,CACH,gBAAA,SAAA,CAAA,WAAW,GAAX;IACE,cAAc;IAChB,CAAC;IAED;;;OAGG,CACH,gBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,aAAa;QAC3B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;YACzC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC/D;IACH,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAnGD,CAA8B,KAAK,GAmGlC;AAED;;;;;;GAMG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC", "debugId": null}}, {"offset": {"line": 4750, "column": 0}, "map": {"version": 3, "file": "remotedatatrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotedatatrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEnE;;;;;;;;;GASG,CACH,IAAA,6BAAA,SAAA,MAAA;IAAyC,UAAA,4BAAA,QAAsB;IAC7D;;;;;OAKG,CACH,SAAA,2BAAY,SAAS,EAAE,OAAO;eAC5B,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC3B,CAAC;IAED,2BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,kCAAgC,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAC/E,CAAC;IACH,OAAA,0BAAC;AAAD,CAAC,AAdD,CAAyC,sBAAsB,GAc9D;AAED;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 4815, "column": 0}, "map": {"version": 3, "file": "remotevideotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotevideotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC5D,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC7E,IAAA,YAAY,GAAK,OAAO,CAAC,4BAA4B,CAAC,0EAAA,YAA1C,CAA2C;AAC/D,IAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE9C,IAAM,qBAAqB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAChE,IAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC;;;;;;;;;;;;;;GAcG,CACH,IAAA,mBAAA,SAAA,MAAA;IAA+B,UAAA,kBAAA,QAAqB;IAClD;;;;;;;;;;OAUG,CACH,SAAA,iBAAY,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO;QAAlG,IAAA,QAAA,IAAA,CAgFC;QA/EC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,2BAA2B,EAAE,MAAM;YACnC,sBAAsB,EAAE,MAAM;YAC9B,+BAA+B,EAAE,IAAI;SACtC,EAAE,OAAO,CAAC,CAAC;QAEZ,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,oBAAoB,EAAE,OAAO,oBAAoB,KAAK,WAAW,IAAI,OAAO,CAAC,2BAA2B,KAAK,MAAM,CAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB;YAC1J,cAAc,EAAE,OAAO,cAAc,KAAK,WAAW,IAAI,OAAO,CAAC,sBAAsB,KAAK,MAAM,CAAC,CAAC,CAAE,YAAY,CAAC,CAAC,CAAC,cAAc;SACpI,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,OAAA,IAAA,CAAA,IAAA,EAAM,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,IAAA,IAAA,CAAC;QAE9F,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,gCAAgC,EAAE;gBAChC,KAAK,EAAE,OAAO,CAAC,+BAA+B,KAAK,IAAI,IAAI,OAAO,CAAC,2BAA2B,KAAK,MAAM;aAC1G;YACD,iCAAiC,EAAE;gBACjC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,OAAO,CAAC,2BAA2B;aAC3C;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,OAAO,CAAC,sBAAsB;aACtC;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI,OAAO,CAAC;oBACjB,KAAI,CAAC,cAAc,CAAC;wBAAE,OAAO,EAAE,KAAK;oBAAA,CAAE,CAAC,CAAC;gBAC1C,CAAC,EAAE,sBAAsB,EAAE,KAAK,CAAC;aAClC;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,OAAO,CAAC,cAAc,CAAC,SAAA,OAAO;oBACvC,8DAA8D;oBAC9D,kGAAkG;oBAClG,2EAA2E;oBAC3E,IAAM,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,SAAA,KAAK;wBAAI,OAAA,CAAC,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;oBAA1C,CAA0C,CAAC,CAAC;oBAChG,IAAI,qBAAqB,EAAE;wBACzB,wBAAwB,CAAC,KAAI,CAAC,CAAC;qBAChC;gBACH,CAAC,CAAC;aACH;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,OAAO,CAAC,oBAAoB,CAAC,SAAA,OAAO;oBAC7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,OAAO,CAAC,SAAA,KAAK;wBACnB,IAAM,UAAU,GAAG,CAAC,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBAC9D,IAAI,UAAU,KAAK,KAAK,CAAC,cAAc,EAAE;4BACvC,IAAI,KAAK,CAAC,cAAc,EAAE;gCACxB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gCAC5D,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;6BAC9C,MAAM;gCACL,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gCAC5D,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;6BAC3C;4BACD,mBAAmB,GAAG,IAAI,CAAC;yBAC5B;oBACH,CAAC,CAAC,CAAC;oBACH,IAAI,mBAAmB,EAAE;wBACvB,sBAAsB,CAAC,KAAI,CAAC,CAAC;wBAE7B,wFAAwF;wBACxF,0FAA0F;wBAC1F,gCAAgC;wBAChC,wBAAwB,CAAC,KAAI,CAAC,CAAC;qBAChC;gBACH,CAAC,EAAE;oBAAE,SAAS,EAAE,IAAI;gBAAA,CAAE,CAAC;aACxB;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;OAEG,CACH,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,OAAO;QACZ,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,mFAAmF;QACnF,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,IAAI,IAAI,CAAC,4BAA4B,KAAK,QAAQ,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,sHAAsH,CAAC,CAAC;SACzI;QACD,IAAI,CAAC,cAAc,CAAC;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG,CACH,iBAAA,SAAA,CAAA,SAAS,GAAT;QACE,IAAI,IAAI,CAAC,4BAA4B,KAAK,QAAQ,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,uHAAuH,CAAC,CAAC;SAC1I;QACD,IAAI,CAAC,cAAc,CAAC;YAAE,OAAO,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACH,iBAAA,SAAA,CAAA,qBAAqB,GAArB,SAAsB,kBAAkB;QACtC,IAAI,IAAI,CAAC,uBAAuB,KAAK,QAAQ,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,iHAAiH,CAAC,CAAC;SACpI;QAED,IAAI,kBAAkB,CAAC,gBAAgB,EAAE;YACvC,IAAI,CAAC,cAAc,CAAC;gBAAE,gBAAgB,EAAE,kBAAkB,CAAC,gBAAgB;YAAA,CAAE,CAAC,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,EAAE;QACd,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,YAAY,EAAE;YAChB,EAAE,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YACzE,EAAE,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YACzE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACnC;IACH,CAAC;IAED,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,EAAE;QAAd,IAAA,QAAA,IAAA,CAWC;QAVC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACjB,IAAM,UAAU,GAAG,SAAA,KAAK;gBAAI,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;YAA3B,CAA2B,CAAC;YACxD,IAAM,UAAU,GAAG,SAAA,KAAK;gBAAI,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;YAA3B,CAA2B,CAAC;YACxD,IAAM,WAAW,GAAG,SAAA,KAAK;gBAAI,OAAA,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;YAA5B,CAA4B,CAAC;YAE1D,EAAE,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;YACzD,EAAE,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE;gBAAE,UAAU,EAAA,UAAA;gBAAE,UAAU,EAAA,UAAA;gBAAE,WAAW,EAAA,WAAA;YAAA,CAAE,CAAC,CAAC;SACzE;IACH,CAAC;IAED,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACrC,IAAA,WAAW,GAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA,WAAxC,CAAyC;QAC5D,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAClD,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7B,IAAA,WAAW,GAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA,WAAxC,CAAyC;QAC5D,IAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,CAAC;QAC/C,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACrD,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,iBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;QACP,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,EAAC,EAAE,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,4BAA4B,KAAK,MAAM,EAAE;YAChD,mDAAmD;YACnD,2EAA2E;YAC3E,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,iCAAiC,IAAI,8BAA8B,CAAC,IAAI,CAAC,CAAC;SACzH;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,iBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,EAAE;QAAT,IAAA,QAAA,IAAA,CAoBC;QAnBC,IAAM,MAAM,GAAG,OAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,EAAC,EAAE,CAAC,CAAC;QAChC,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAAC,MAAM;SAAC,CAAC;QAC3D,QAAQ,CAAC,OAAO,CAAC,SAAA,OAAO;YACtB,KAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxC,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACxC,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;YAChC,IAAI,IAAI,CAAC,iCAAiC,EAAE;gBAC1C,IAAI,CAAC,iCAAiC,EAAE,CAAC;gBACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;aAC/C;SACF;QAED,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7B,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG,CACH,iBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO,OAAA,SAAA,CAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG,CACH,iBAAA,SAAA,CAAA,eAAe,GAAf;QACE,OAAO,OAAA,SAAA,CAAM,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,GAAG,GAAA,GAAG,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG,CACH,iBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAQ;QAClB,OAAO,OAAA,SAAA,CAAM,WAAW,CAAA,IAAA,CAAA,IAAA,EAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAxTD,CAA+B,qBAAqB,GAwTnD;AAED,SAAS,uBAAuB,CAAC,gBAAgB;IAC/C,IAAI,CAAC,CAAC,0BAA0B,IAAI,UAAU,CAAC,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IAED,IAAM,SAAS,GAAG,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC;IAC7D,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,KAAK,CAAC;KACd;IAED,IAAM,MAAM,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IAEzE,OAAO,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,SAAA,EAAE;QAAI,OAAA,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;IAAd,CAAc,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,sBAAsB,CAAC,gBAAgB;IAC9C,IAAI,gBAAgB,CAAC,4BAA4B,KAAK,MAAM,EAAE;QAC5D,OAAO;KACR;IAED,IAAM,eAAe,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,SAAA,EAAE;QAAI,OAAA,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;IAA5C,CAA4C,CAAC,CAAC;IAC9H,IAAM,UAAU,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,SAAA,EAAE;QAAI,OAAA,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;IAAxC,CAAwC,CAAC,CAAC;IAErH,2EAA2E;IAC3E,IAAM,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,IACnC,uBAAuB,CAAC,gBAAgB,CAAC,IACxC,QAAQ,CAAC,eAAe,KAAK,SAAS,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEzE,IAAI,OAAO,KAAK,IAAI,EAAE;QACpB,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACvC,gBAAgB,CAAC,cAAc,CAAC;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;KACpD,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE;QAChD,mDAAmD;QACnD,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACxC;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,gBAAgB;IAChD,IAAI,gBAAgB,CAAC,uBAAuB,KAAK,MAAM,EAAE;QACvD,OAAO;KACR;IAED,IAAM,eAAe,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,SAAA,EAAE;QAAI,OAAA,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;IAA5C,CAA4C,CAAC,CAAC;IAC9H,IAAM,WAAW,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,SAAA,EAAE;QACnE,IAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3D,OAAO,SAAS,CAAC,CAAC,CAAC;YAAE,YAAY,EAAE,SAAS,CAAC,MAAM;YAAE,WAAW,EAAE,SAAS,CAAC,KAAK;QAAA,CAAE,CAAC,CAAC,CAAC;YAAE,YAAY,EAAE,CAAC;YAAE,WAAW,EAAE,CAAC;QAAA,CAAE,CAAC;IAC5H,CAAC,CAAC,CAAC;IACH,IAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC1D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,IAAA,KAAA,OAAkC,aAAa,CAAC,IAAI,CAAC,SAAC,GAAG,EAAE,GAAG;YAClE,OAAA,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,WAAW,GAAG,CAAC;QAA3E,CAA2E,CAAC,EAAA,EAAA,EADvE,KAAA,EAAA,CAAA,EAA6B,EAA3B,YAAY,GAAA,GAAA,YAAA,EAAE,WAAW,GAAA,GAAA,WAC4C,CAAC;QAC/E,IAAM,gBAAgB,GAAG;YAAE,MAAM,EAAE,YAAY;YAAE,KAAK,EAAE,WAAW;QAAA,CAAE,CAAC;QACtE,gBAAgB,CAAC,cAAc,CAAC;YAAE,gBAAgB,EAAA,gBAAA;QAAA,CAAE,CAAC,CAAC;KACvD;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,gBAAgB;IACtD,SAAS,mBAAmB;QAC1B,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAED,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACrE,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH;;;;;GAKG,CAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 5298, "column": 0}, "map": {"version": 3, "file": "remotevideotrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotevideotrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEnE;;;;;;;;;;;GAWG,CACH,IAAA,8BAAA,SAAA,MAAA;IAA0C,UAAA,6BAAA,QAAsB;IAC9D;;;;;OAKG,CACH,SAAA,4BAAY,SAAS,EAAE,OAAO;eAC5B,OAAA,IAAA,CAAA,IAAA,EAAM,SAAS,EAAE,OAAO,CAAC,IAAA,IAAA;IAC3B,CAAC;IAED,4BAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,mCAAiC,IAAI,CAAC,WAAW,GAAA,OAAK,IAAI,CAAC,QAAQ,GAAA,GAAG,CAAC;IAChF,CAAC;IACH,OAAA,2BAAC;AAAD,CAAC,AAdD,CAA0C,sBAAsB,GAc/D;AAED;;;;GAIG,CAEH;;;;;GAKG,CAEH;;;GAGG,CAEH;;;GAGG,CAEH;;;;GAIG,CAEH,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC", "debugId": null}}, {"offset": {"line": 5371, "column": 0}, "map": {"version": 3, "file": "receiver.js", "sourceRoot": "", "sources": ["../../../lib/media/track/receiver.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAEvD;;;GAGG,CACH,IAAA,qBAAA,SAAA,MAAA;IAAiC,UAAA,oBAAA,QAAqB;IACpD;;;;OAIG,CACH,SAAA,mBAAY,EAAE,EAAE,gBAAgB;eAC9B,OAAA,IAAA,CAAA,IAAA,EAAM,EAAE,EAAE,gBAAgB,CAAC,IAAA,IAAA;IAC7B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AATD,CAAiC,qBAAqB,GASrD;AAED,MAAM,CAAC,OAAO,GAAG,kBAAkB,CAAC", "debugId": null}}]}