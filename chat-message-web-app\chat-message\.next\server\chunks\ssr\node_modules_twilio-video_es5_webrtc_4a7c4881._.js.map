{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/util/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAEb;;;GAGG,CACH,SAAS,KAAK;IACZ,IAAM,QAAQ,GAAG,CAAA,CAAE,CAAC;IACpB,QAAQ,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;QAC7C,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;IAC3B,CAAC,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;GASG,CACH,SAAS,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU;IACzD,IAAI,UAAU,IAAI,OAAO,EAAE;QACzB,gCAAgC;QAChC,OAAO;KACR,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QACzC,6DAA6D;QAC7D,OAAO;KACR;IAGD,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI;QACF,IAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACrE,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;KACzC,CAAC,OAAO,KAAK,EAAE;IACd,gCAAgC;KACjC;IAED,0EAA0E;IAC1E,yFAAyF;IACzF,sEAAsE;IACtE,mFAAmF;IACnF,8FAA8F;IAC9F,iBAAiB;IACjB,IAAI,UAAU,EAAE;QACd,OAAO;KACR;IAED,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC;KAClC,CAAC,OAAO,KAAK,EAAE;IACd,uEAAuE;IACvE,mDAAmD;KACpD;IAED,IAAI,IAAI,KAAK,UAAU,EAAE;QACvB,6BAA6B;QAC7B,OAAO;KACR;IAED,yBAAA,EAA2B,CAC3B,OAAO,CAAC,UAAU,CAAC,GAAG;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;IACjE,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM;IAC9C,IAAK,IAAM,UAAU,IAAI,MAAM,CAAE;QAC/B,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;KACrD;AACH,CAAC;AAED;;;;;GAKG,CACH,SAAS,UAAU,CAAC,KAAK,EAAE,KAAK;IAC9B,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAExE,IAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;IAE7B,KAAK,CAAC,OAAO,CAAC,SAAA,IAAI;QAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACtB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;GAKG,CACH,SAAS,OAAO,CAAC,IAAI,EAAE,KAAK;IAC1B,IAAM,SAAS,GAAG,IAAI,YAAY,GAAG,IAAI,IAAI,YAAY,GAAG,GACxD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GACzB,IAAI,CAAC;IAET,OAAO,SAAS,CAAC,MAAM,CAAC,SAAC,SAAS,EAAE,IAAI;QAAK,OAAA,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAA7B,CAA6B,EAAE,EAAE,CAAC,CAAC;AAClF,CAAC;AAED;;;GAGG,CACH,SAAS,YAAY;IACnB,OAAO,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,QAAQ,GAC9E,SAAS,CAAC,SAAS,GACnB,IAAI,CAAC;AACX,CAAC;AAED;;;;GAIG,CACH,SAAS,YAAY,CAAC,SAAS;IAC7B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAClC,OAAO,QAAQ,CAAC;KACjB;IACD,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACnC,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC7C,OAAO,QAAQ,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG,CACH,SAAS,mBAAmB,CAAC,SAAS;IACpC,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,IAAM,MAAM,GAAG;QACb,MAAM,EAAE,cAAc;QACtB,OAAO,EAAE,eAAe;QACxB,MAAM,EAAE,SAAS;KAClB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAE3B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI,CAAC;KACb;IACD,IAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAI,MAAM,GAAA,aAAa,CAAC,CAAC;IAC5C,IAAA,KAAA,OAAc,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAA,EAAA,EAArC,KAAK,GAAA,EAAA,CAAA,EAAgC,CAAC;IAEjD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IACD,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC9C,OAAO;QACL,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC/C,CAAC;AACJ,CAAC;AAED;;;;GAIG,CACH,SAAS,WAAW,CAAC,SAAS;IAC5B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,OAAO,AAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,YAAY,EAAE,KAAK,QAAQ,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACvG,CAAC;AAED;;;;;GAKG,CACH,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI;IAClC,IAAI,eAAe,GAAG,IAAI,CAAC;IAC3B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,EAAE;QACzC,GAAG,EAAE;YACH,OAAO,eAAe,CAAC;QACzB,CAAC;QACD,GAAG,EAAE,SAAS,WAAW;YACvB,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;aACjD;YAED,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;gBACrC,eAAe,GAAG,WAAW,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;aAC9C,MAAM;gBACL,eAAe,GAAG,IAAI,CAAC;aACxB;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS;IAClD,OAAO,SAAS,GACZ,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,GAClC,OAAO,CAAC;AACd,CAAC;AAED;;;GAGG,CACH,SAAS,QAAQ;IACf,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,SAAA,CAAC;QAC9D,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,IAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM;IAC9C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAA,YAAY;QACrD,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY;IAC1D,IAAI,YAAY,IAAI,OAAO,EAAE;QAC3B,mCAAmC;QACnC,OAAO;KACR,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QAC3C,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE;YAC3C,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CACrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EACrB;YAAC,IAAA,OAAA,EAAA,CAAO;gBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;gBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;YAAK,OAAA,OAAO,CAAC,aAAa,CAAA,KAAA,CAArB,OAAO,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI;QAA7B,CAA8B,CAC5C,CAAC;QAEF,OAAO;KACR;IAED,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE;QAC3C,UAAU,EAAE,IAAI;QAChB,GAAG,EAAE;YACH,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG,CACH,SAAS,OAAO;IACd,OAAO,OAAO,SAAS,KAAK,QAAQ,IAC/B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ,IAC1C,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,KAAK,UAAU,IACzD,OAAO,iBAAiB,KAAK,UAAU,CAAC;AAC/C,CAAC;AAED;;;;GAIG,CACH,SAAS,wBAAwB,CAAC,IAAI;IACpC,IAAI,OAAO,YAAY,KAAK,WAAW,IAClC,OAAO,YAAY,CAAC,eAAe,KAAK,UAAU,EAAE;QACvD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAC5B,YAAY,CACT,eAAe,CAAC,IAAI,CAAC,CACrB,MAAM,CACN,GAAG,CAAC,SAAC,EAAY;gBAAV,QAAQ,GAAA,GAAA,QAAA;YAAO,OAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;QAApC,CAAoC,CAAC,CAC/D,CAAC,CAAC;KACJ;IACD,IAAI,OAAO,iBAAiB,KAAK,WAAW,IACvC,OAAO,iBAAiB,CAAC,SAAS,KAAK,WAAW,IAClD,OAAO,iBAAiB,CAAC,SAAS,CAAC,cAAc,KAAK,UAAU,IAChE,OAAO,iBAAiB,CAAC,SAAS,CAAC,KAAK,KAAK,UAAU,IACvD,OAAO,iBAAiB,CAAC,SAAS,CAAC,WAAW,KAAK,UAAU,EAAE;QAClE,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;KACnC;IACD,IAAM,EAAE,GAAG,IAAI,iBAAiB,EAAE,CAAC;IACnC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACxB,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAC,EAAO;YAAL,GAAG,GAAA,GAAA,GAAA;QACjC,EAAE,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAChD,GAAG,CAAC,SAAA,IAAI;YAAI,OAAA,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;QAAnD,CAAmD,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE;QACD,EAAE,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,GAAG,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,qEAAqE;AACrE,IAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AAElC;;;;;GAKG,CACH,SAAS,gBAAgB,CAAC,KAAK,EAAE,IAAI;IACnC,IAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,MAAM,EAAE;QACV,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KACzD;IACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAA,MAAM;QAC/C,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG,CACH,SAAS,0BAA0B;IACjC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC1B,CAAC;AAED;;;;;GAKG,CAEH,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;AAC1C,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACpC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAClD,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;AAClC,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC;AACxC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACtC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;AAC1C,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "file": "sdp.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/util/sdp.js"], "names": [], "mappings": "AAAA,gDAAA,EAAkD,CAElD,YAAY,CAAC;AAEP,IAAA,KAA4B,OAAO,CAAC,IAAI,CAAC,mGAAvC,OAAO,GAAA,GAAA,OAAA,EAAE,YAAY,GAAA,GAAA,YAAkB,CAAC;AAEhD,+EAA+E;AAC/E,oDAAoD;AACpD,IAAI,uBAAuB,GAAG,IAAI,CAAC;AAEnC;;;GAGG,CACH,SAAS,8BAA8B;IACrC,IAAI,OAAO,uBAAuB,KAAK,SAAS,EAAE;QAChD,OAAO,uBAAuB,CAAC;KAChC;IACD,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;QAC5C,uBAAuB,GAAG,KAAK,CAAC;QAChC,OAAO,uBAAuB,CAAC;KAChC;IACD,IAAI;QACF,kCAAkC;QAClC,IAAI,iBAAiB,CAAC;YAAE,YAAY,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;QAC/C,uBAAuB,GAAG,KAAK,CAAC;KACjC,CAAC,OAAO,CAAC,EAAE;QACV,uBAAuB,GAAG,IAAI,CAAC;KAChC;IACD,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED,qEAAqE;AACrE,oDAAoD;AACpD,IAAI,eAAe,GAAG,IAAI,CAAC;AAE3B;;GAEG,CACH,SAAS,0BAA0B;IACjC,eAAe,GAAG,IAAI,CAAC;AACzB,CAAC;AAED;;;GAGG,CACH,SAAS,yBAAyB;IAChC,IAAI,CAAC,eAAe,EAAE;QACpB,IAAI,OAAO,iBAAiB,KAAK,WAAW,IACvC,gBAAgB,IAAI,iBAAiB,CAAC,SAAS,EAAE;YACpD,IAAM,EAAE,GAAG,IAAI,iBAAiB,EAAE,CAAC;YACnC,IAAI;gBACF,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3B,eAAe,GAAG,SAAS,CAAC;aAC7B,CAAC,OAAO,CAAC,EAAE;gBACV,eAAe,GAAG,OAAO,CAAC;aAC3B;YACD,EAAE,CAAC,KAAK,EAAE,CAAC;SACZ,MAAM;YACL,eAAe,GAAG,OAAO,CAAC;SAC3B;KACF;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;GAIG,CACH,SAAS,kBAAkB,CAAC,YAAY;IACtC,IAAI,CAAC,YAAY,IAAI,CAAC,8BAA8B,EAAE,EAAE;QACtD,OAAO,yBAAyB,EAAE,CAAC;KACpC;IACD,QAAO;QACL,QAAQ,EAAE,OAAO;QACjB,cAAc,EAAE,SAAS;MAC1B,CAAC,YAAY,CAAC,CAAC;AAClB,CAAC;AAED;;;GAGG,CACH,SAAS,kBAAkB;IACzB,OAAO,OAAO,iBAAiB,KAAK,WAAW,IAC1C,kBAAkB,IAAI,iBAAiB,CAAC,SAAS,GAClD,SAAS,GACT,OAAO,CAAC;AACd,CAAC;AAED;;;;GAIG,CACH,SAAS,YAAY,CAAC,YAAY;IAChC,QAAO;QACL,MAAM,EAAE,kBAAkB,CAAC,YAAY,CAAC;QACxC,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,kBAAkB,EAAE;MAC7B,CAAC,YAAY,EAAE,CAAC,IAAI,IAAI,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG,CACH,SAAS,UAAU,CAAC,OAAO,EAAE,KAAK;IAChC,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7D,OAAO,OAAO,CAAC,MAAM,CAAC,SAAC,OAAO,EAAE,IAAI;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9C,OAAO,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IACjD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;;GAKG,CACH,SAAS,WAAW,CAAC,OAAO,EAAE,GAAG;IAC/B,OAAO,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;GAIG,CACH,SAAS,gBAAgB,CAAC,GAAG;IAC3B,OAAO,WAAW,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;AAC9D,CAAC;AAED;;;;GAIG,CACH,SAAS,sBAAsB,CAAC,GAAG;IACjC,OAAO,WAAW,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC;AAED;;;;;GAKG,CACH,SAAS,aAAa,CAAC,GAAG,EAAE,OAAO;IACjC,IAAM,OAAO,GAAG,mCAAiC,OAAO,GAAA,KAAK,CAAC;IAC9D,OAAO,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;GAMG,CACH,SAAS,gBAAgB,CAAC,GAAG,EAAE,IAAW,EAAE,SAAgB;IAA7B,IAAA,SAAA,KAAA,GAAA;QAAA,OAAA,IAAW;IAAA;IAAE,IAAA,cAAA,KAAA,GAAA;QAAA,YAAA,IAAgB;IAAA;IAC1D,OAAO,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAA,YAAY;QAAI,OAAA,OAAK,YAAc;IAAnB,CAAmB,CAAC,CAAC,MAAM,CAAC,SAAA,YAAY;QAC9F,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,OAAK,IAAM,EAAE,IAAI,CAAC,CAAC;QAClD,IAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,OAAK,SAAW,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG,CACH,SAAS,oBAAoB,CAAC,YAAY;IACxC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;GAKG,CACH,SAAS,mBAAmB,CAAC,GAAG,EAAE,OAAO;IACvC,IAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAE5C,IAAM,cAAc,GAAG,IAAI,MAAM,CAAC,oBAAkB,OAAO,GAAA,KAAK,EAAE,IAAI,CAAC,CAAC;IACxE,IAAM,qBAAqB,GAAG,aAAa,CAAC,MAAM,CAAC,SAAA,YAAY;QAAI,OAAA,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC;IAAlC,CAAkC,CAAC,CAAC;IAEvG,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;GAMG,CACH,SAAS,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE,GAAG;IACpD,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,SAAA,OAAO;QAAI,OAAA;YAAC,OAAO;YAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;SAAC;IAAjC,CAAiC,CAAC,CAAC,CAAC;AACjG,CAAC;AAED;;;;GAIG,CACH,SAAS,uBAAuB,CAAC,GAAG;IAClC,OAAO,kBAAkB,CAAC,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;AAClE,CAAC;AAED;;;;GAIG,CACH,SAAS,6BAA6B,CAAC,GAAG;IACxC,OAAO,kBAAkB,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,GAAG,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,qBAAqB,CAAC,kBAAkB,EAAE,eAAe,EAAE,GAAG;IACrE,IAAM,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACnD,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;IAErC,mDAAmD;IACnD,kBAAkB,CAAC,OAAO,CAAC,SAAC,KAAK,EAAE,OAAO;QACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACjC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpC,OAAO;SACR;QACD,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1D,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,QAAQ,CAAC,OAAO,CAAC,SAAC,OAAO,EAAE,CAAC;YAC1B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACzC,IAAM,OAAO,GAAG,aAAW,OAAO,GAAA,QAAQ,CAAC;YAC3C,IAAM,WAAW,GAAG,YAAU,OAAO,GAAA,KAAK,CAAC;YAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,wDAAwD;IACxD,IAAM,OAAO,GAAG,8BAA8B,CAAC;IAC/C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3D,OAAO,CAAC,OAAO,CAAC,SAAA,IAAI;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,IAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAA,OAAO;YAC9C,IAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAChD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;QACrC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,0BAA0B,CAAC,eAAe,EAAE,GAAG;IACtD,OAAO,qBAAqB,CAAC,uBAAuB,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,gCAAgC,CAAC,eAAe,EAAE,GAAG;IAC5D,OAAO,qBAAqB,CAAC,6BAA6B,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;AACpF,CAAC;AAED,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACpC,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;AACxD,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACtC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAClD,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,gCAAgC,GAAG,gCAAgC,CAAC", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "file": "getstats.js", "sourceRoot": "", "sources": ["../../lib/webrtc/getstats.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,IAAA,KAAiD,OAAO,CAAC,QAAQ,CAAC,+FAAhE,OAAO,GAAA,GAAA,OAAA,EAAE,YAAY,GAAA,GAAA,YAAA,EAAE,mBAAmB,GAAA,GAAA,mBAAsB,CAAC;AACjE,IAAA,YAAY,GAAK,OAAO,CAAC,YAAY,CAAC,wFAAA,YAA1B,CAA2B;AAE/C,IAAM,KAAK,GAAG,YAAY,EAAE,CAAC;AAC7B,IAAM,YAAY,GAAG,mBAAmB,EAAE,CAAC;AAC3C,IAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ,CAAC;AACpC,IAAM,SAAS,GAAG,KAAK,KAAK,SAAS,CAAC;AACtC,IAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ,CAAC;AAEpC,IAAM,kBAAkB,GAAG,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AAGhE,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAE5C;;;;;GAKG,CACH,SAAS,WAAW,CAAC,MAAM,EAAE,EAAE;IAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU,EAAE;QACpC,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KACvB;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAA,CAAC;QAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE;IAAX,CAAW,CAAC,CAAC;AACvC,CAAC;AAED;;;;;;;;;;;GAWG,CACH,SAAS,kBAAkB,CAAC,UAAU,EAAE,KAAK,EAAE,QAAgB;;IAAhB,IAAA,aAAA,KAAA,GAAA;QAAA,WAAA,KAAgB;IAAA;IAC7D,+BAA+B;IAC/B,IAAI,QAAQ,CAAC;IACb,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QAC7B,QAAQ,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,SAAA,IAAI;YAAI,OAAA;gBAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAAE,IAAI;aAAC;QAAxC,CAAwC,CAAC,CAAC,CAAC;KACtF,MAAM,IAAI,UAAU,YAAY,GAAG,EAAE;QACpC,QAAQ,GAAG,UAAU,CAAC;KACvB,MAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,EAAE;QAChE,2CAA2C;QAC3C,IAAM,UAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAA,GAAG;YACjC,UAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,QAAQ,GAAG,UAAQ,CAAC;KACrB,MAAM;QACL,OAAO,IAAI,GAAG,EAAE,CAAC;KAClB;IAED,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;QACvB,OAAO,IAAI,GAAG,EAAE,CAAC;KAClB;IAED,IAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;IACjC,IAAM,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;IACzB,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;IAE7B,gDAAgD;IAChD,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,cAAc,GAAG,IAAI,CAAC;IAC1B,IAAI,IAAI,GAAG,IAAI,CAAC;;QAEhB,wFAAwF;QACxF,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;YAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;YAClB,6EAA6E;YAC7E,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;gBAC/E,YAAY,GAAG,IAAI,CAAC;gBACpB,cAAc,GAAG,EAAE,CAAC;gBACpB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACjB,MAAM;aACP,MAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;gBACxF,6EAA6E;gBAC7E,YAAY,GAAG,IAAI,CAAC;gBACpB,cAAc,GAAG,EAAE,CAAC;gBACpB,MAAM;aACP,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;gBACpE,2DAA2D;gBAC3D,IAAI,CAAC,YAAY,EAAE;oBACjB,YAAY,GAAG,IAAI,CAAC;oBACpB,cAAc,GAAG,EAAE,CAAC;iBACrB;aACF;SACF;;;;;;;;;;;;IAED,8EAA8E;IAC9E,IAAI,CAAC,YAAY,EAAE;QACjB,oEAAoE;QACpE,IAAI,QAAQ,EAAE;YACZ,8CAA8C;YAC9C,IAAM,iBAAiB,GAAG,EAAE,CAAC;;gBAC7B,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;oBAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;oBAClB,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,EAAE;wBAC5F,iBAAiB,CAAC,IAAI,CAAC;4BAAE,EAAE,EAAA,EAAA;4BAAE,IAAI,EAAA,IAAA;wBAAA,CAAE,CAAC,CAAC;qBACtC;iBACF;;;;;;;;;;;;YAED,0DAA0D;YAC1D,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;gBAClC,6BAA6B;gBAC7B,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACzC,cAAc,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;aAC1B,MAAM,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvC,oEAAoE;gBACpE,oCAAoC;gBACpC,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACzC,cAAc,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;aAC1B;SACF,MAAM;;gBACL,mEAAmE;gBACnE,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;oBAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;oBAClB,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;wBAC3D,YAAY,GAAG,IAAI,CAAC;wBACpB,cAAc,GAAG,EAAE,CAAC;wBACpB,MAAM;qBACP;iBACF;;;;;;;;;;;;SACF;KACF;IAED,iEAAiE;IACjE,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,cAAc,CAAC;KACvB;IAED,+BAA+B;IAC/B,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAEjD,oDAAoD;IACpD,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;IAErC,uCAAuC;IACvC,IAAI,QAAQ,EAAE;QACZ,6CAA6C;QAC7C,IAAI,YAAY,CAAC,OAAO,EAAE;YAAE,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAAE;QAC3E,IAAI,YAAY,CAAC,WAAW,EAAE;YAAE,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;SAAE;QACnF,IAAI,YAAY,CAAC,QAAQ,EAAE;YAAE,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAAE;QAE7E,yCAAyC;QACzC,IAAI,IAAI,EAAE;;gBACR,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;oBAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;oBAClB,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;wBAC7D,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBAC5B;iBACF;;;;;;;;;;;;SACF;;YAED,yCAAyC;YACzC,IAAwB,IAAA,uBAAA,SAAA,kBAAkB,CAAA,EAAA,yBAAA,qBAAA,IAAA,EAAA,EAAA,CAAA,uBAAA,IAAA,EAAA,yBAAA,qBAAA,IAAA,GAAE;gBAAvC,IAAM,SAAS,GAAA,uBAAA,KAAA;gBAClB,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC3B,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;iBACxD;aACF;;;;;;;;;;;;;YAED,mCAAmC;YACnC,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;gBAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;gBAClB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;oBAC7D,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBAC9B;aACF;;;;;;;;;;;;KACF,MAAM;;YACL,6CAA6C;YAE7C,sDAAsD;YACtD,IAAyB,IAAA,aAAA,SAAA,QAAQ,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;gBAAxB,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAU,EAAT,EAAE,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA;gBAClB,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,aAAa,KAAK,cAAc,EAAE;oBACzE,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;oBAE7B,0BAA0B;oBAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;wBAAE,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAAE;oBAC3D,IAAI,IAAI,CAAC,WAAW,EAAE;wBAAE,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBAAE;oBAEnE,4DAA4D;oBAC5D,IAAM,UAAU,GAAG,EAAE,CAAC;;wBACtB,IAAqC,IAAA,aAAA,CAAA,MAAA,KAAA,GAAA,SAAA,QAAQ,CAAA,CAAA,EAAA,eAAA,WAAA,IAAA,EAAA,EAAA,CAAA,aAAA,IAAA,EAAA,eAAA,WAAA,IAAA,GAAE;4BAApC,IAAA,KAAA,OAAA,aAAA,KAAA,EAAA,EAAsB,EAArB,QAAQ,GAAA,EAAA,CAAA,EAAA,EAAE,UAAU,GAAA,EAAA,CAAA,EAAA;4BAC9B,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,IAAI,UAAU,CAAC,OAAO,KAAK,UAAU,EAAE;gCACjF,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;6BAC1C;yBACF;;;;;;;;;;;;iBACF;aACF;;;;;;;;;;;;;YAED,gCAAgC;YAChC,IAAwB,IAAA,uBAAA,SAAA,kBAAkB,CAAA,EAAA,yBAAA,qBAAA,IAAA,EAAA,EAAA,CAAA,uBAAA,IAAA,EAAA,yBAAA,qBAAA,IAAA,GAAE;gBAAvC,IAAM,SAAS,GAAA,uBAAA,KAAA;gBAClB,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC3B,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;iBACxD;aACF;;;;;;;;;;;;KACF;IAED,8DAA8D;IAC9D,wDAAwD;IACxD,8CAA8C;IAC9C,IAAI,cAAc,GAAG,IAAI,CAAC;IAC1B,IAAM,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;;QAE/B,0DAA0D;QAC1D,IAAmB,IAAA,KAAA,SAAA,cAAc,CAAC,MAAM,EAAE,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;YAAvC,IAAM,IAAI,GAAA,GAAA,KAAA;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACpC;SACF;;;;;;;;;;;;;QAED,qBAAqB;QACrB,IAA0B,IAAA,iBAAA,SAAA,YAAY,CAAA,EAAA,mBAAA,eAAA,IAAA,EAAA,EAAA,CAAA,iBAAA,IAAA,EAAA,mBAAA,eAAA,IAAA,GAAE;YAAnC,IAAM,WAAW,GAAA,iBAAA,KAAA;YACpB,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBAC7B,IAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAC5C,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;gBAE3C,oCAAoC;gBACpC,IAAI,SAAS,CAAC,uBAAuB,EAAE;oBACrC,cAAc,GAAG,SAAS,CAAC,uBAAuB,CAAC;iBACpD;gBAED,uBAAuB;gBACvB,IAAI,SAAS,CAAC,kBAAkB,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE;oBAC9E,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;iBAC9F;gBACD,IAAI,SAAS,CAAC,mBAAmB,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE;oBAChF,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC;iBAChG;aACF;SACF;;;;;;;;;;;;IAED,gEAAgE;IAChE,IAAI,cAAc,IAAI,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;QAClD,IAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAClD,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAEjD,4DAA4D;QAC5D,IAAI,YAAY,CAAC,gBAAgB,IAAI,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE;YAChF,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAChG;QACD,IAAI,YAAY,CAAC,iBAAiB,IAAI,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;YAClF,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,iBAAiB,EAAE,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC;SAClG;KACF;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;GAKG,CACH,SAAS,QAAQ,CAAC,cAAc,EAAE,OAAO;IACvC,IAAI,CAAC,CAAC,cAAc,IAAI,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;QACtE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAC;KACpF;IACD,OAAO,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG,CACH,SAAS,SAAS,CAAC,cAAc,EAAE,OAAO;IACxC,IAAM,gBAAgB,GAAG,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrE,IAAM,gBAAgB,GAAG,SAAS,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrE,IAAM,iBAAiB,GAAG,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAC7D,IAAM,iBAAiB,GAAG,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAE7D,IAAM,aAAa,GAAG;QACpB,sBAAsB,EAAE,IAAI;QAC5B,oBAAoB,EAAE,EAAE;QACxB,oBAAoB,EAAE,EAAE;QACxB,qBAAqB,EAAE,EAAE;QACzB,qBAAqB,EAAE,EAAE;KAC1B,CAAC;IAEF,IAAM,kBAAkB,GAAG,OAAO,CAAC;QACjC;YAAC,gBAAgB;YAAE,sBAAsB;YAAE,KAAK;SAAC;QACjD;YAAC,gBAAgB;YAAE,sBAAsB;YAAE,KAAK;SAAC;QACjD;YAAC,iBAAiB;YAAE,uBAAuB;YAAE,IAAI;SAAC;QAClD;YAAC,iBAAiB;YAAE,uBAAuB;YAAE,IAAI;SAAC;KACnD,EAAE,SAAC,EAAkC;YAAlC,KAAA,OAAA,IAAA,EAAkC,EAAjC,MAAM,GAAA,EAAA,CAAA,EAAA,EAAE,cAAc,GAAA,EAAA,CAAA,EAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,EAAA;QACnC,OAAO,MAAM,CAAC,GAAG,CAAC,SAAA,KAAK;YACrB,OAAO,aAAa,CAAC,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC;gBAAE,QAAQ,EAAA,QAAA;YAAA,CAAE,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAA,eAAe;gBACpG,eAAe,CAAC,OAAO,CAAC,SAAA,UAAU;oBAChC,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;oBAC9B,aAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC;QAC1C,OAAO,8BAA8B,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAA,iCAAiC;QACvC,aAAa,CAAC,sBAAsB,GAAG,iCAAiC,CAAC;QACzE,OAAO,aAAa,CAAC;IACvB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG,CACH,SAAS,8BAA8B,CAAC,cAAc,EAAE,OAAY;IAAZ,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAY;IAAA;IAClE,IAAI,OAAO,OAAO,CAAC,aAAa,KAAK,WAAW,IAAI,QAAQ,IACvD,OAAO,OAAO,CAAC,aAAa,KAAM,WAAW,IAAI,QAAQ,EAAE;QAC9D,OAAO,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CACnC,oDAAoD,CAAC,CAAC;KACzD;IACD,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,WAAW,IAAI,SAAS,EAAE;QAC9D,OAAO,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;KACtF;IACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;AACjF,CAAC;AAED;;;;GAIG,CACH,SAAS,oDAAoD,CAAC,KAAK;IACjE,IAAM,wBAAwB,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAC9D,SAAC,EAAmB;YAAjB,SAAS,GAAA,GAAA,SAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAAO,OAAA,IAAI,KAAK,gBAAgB,IAAI,SAAS;IAAtC,CAAsC,CAChE,CAAC;IAEF,IAAI,CAAC,wBAAwB,EAAE;QAC7B,OAAO,IAAI,CAAC;KACb;IAED,IAAM,yBAAyB,GAAG,WAAW,CAAC,KAAK,EAAE,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IAChG,IAAM,0BAA0B,GAAG,WAAW,CAAC,KAAK,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IAElG,IAAM,8BAA8B,GAAG;QACrC;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,IAAI;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC7B;YAAE,GAAG,EAAE,MAAM;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC/B;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,KAAK;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;KAC/B,CAAC;IAEF,IAAM,mCAAmC,GAAG,8BAA8B,CAAC,MAAM,CAAC;QAChF;YAAE,GAAG,EAAE,SAAS;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;KACzC,CAAC,CAAC;IAEH,IAAM,qCAAqC,GAAG,yBAAyB,GACnE,mCAAmC,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAa;YAAX,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAC/D,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,yBAAyB,CAAC,GAAG,CAAC,KAAK,IAAI,GACxD,yBAAyB,CAAC,GAAG,CAAC,GAC9B,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACrC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAA,CAAE,CAAC,GACJ,IAAI,CAAC;IAET,IAAM,sCAAsC,GAAG,0BAA0B,GACrE,8BAA8B,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAa;YAAX,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAC1D,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,0BAA0B,CAAC,GAAG,CAAC,KAAK,IAAI,GACzD,0BAA0B,CAAC,GAAG,CAAC,GAC/B,IAAI,CAAC;QACT,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAA,CAAE,CAAC,GACJ,IAAI,CAAC;IAET,OAAO;QACL;YAAE,GAAG,EAAE,0BAA0B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnD;YAAE,GAAG,EAAE,0BAA0B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnD;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,WAAW;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACpC;YAAE,GAAG,EAAE,qBAAqB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC9C;YAAE,GAAG,EAAE,sBAAsB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC/C;YAAE,GAAG,EAAE,6BAA6B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACtD;YAAE,GAAG,EAAE,yBAAyB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAClD;YAAE,GAAG,EAAE,WAAW;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACrC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACpC;YAAE,GAAG,EAAE,kBAAkB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC3C;YAAE,GAAG,EAAE,cAAc;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACvC;YAAE,GAAG,EAAE,mBAAmB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC5C;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,yBAAyB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAClD;YAAE,GAAG,EAAE,qBAAqB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC9C;YAAE,GAAG,EAAE,OAAO;YAAE,IAAI,EAAE,QAAQ;YAAE,KAAK,EAAE,SAAA,KAAK;gBAAM,OAAO,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;YAAC,CAAC;QAAA,CAAE;QAC5G;YAAE,GAAG,EAAE,oBAAoB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC7C;YAAE,GAAG,EAAE,aAAa;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACtC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;KACrC,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAoB;YAAlB,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA,EAAE,KAAK,GAAA,GAAA,KAAA;QAClC,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,wBAAwB,CAAC,GAAG,CAAC,KAAK,IAAI,GACtD,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,EAC9E,IAAI,CAAC;QACT,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE;QACD,cAAc,EAAE,qCAAqC;QACrD,eAAe,EAAE,sCAAsC;KACxD,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG,CACH,SAAS,6CAA6C,CAAC,KAAK;IAC1D,IAAM,wBAAwB,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAC9D,SAAC,EAAmB;YAAjB,SAAS,GAAA,GAAA,SAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAAO,OAAA,IAAI,KAAK,gBAAgB,IAAI,SAAS;IAAtC,CAAsC,CAChE,CAAC;IAEF,IAAI,CAAC,wBAAwB,EAAE;QAC7B,OAAO,IAAI,CAAC;KACb;IAED,IAAM,yBAAyB,GAAG,WAAW,CAAC,KAAK,EAAE,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IAChG,IAAM,0BAA0B,GAAG,WAAW,CAAC,KAAK,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IAElG,IAAM,8BAA8B,GAAG;QACrC;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,IAAI;YAAE,MAAM,EAAE;gBAAC,SAAS;gBAAE,WAAW;aAAC;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC/D;YAAE,GAAG,EAAE,MAAM;YAAE,MAAM,EAAE;gBAAC,YAAY;aAAC;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACvD;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,UAAU;YAAE,MAAM,EAAE;gBAAC,WAAW;aAAC;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC1D;YAAE,GAAG,EAAE,KAAK;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;KAC/B,CAAC;IAEF,IAAM,mCAAmC,GAAG,8BAA8B,CAAC,MAAM,CAAC;QAChF;YAAE,GAAG,EAAE,SAAS;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;KACzC,CAAC,CAAC;IAEH,IAAM,cAAc,GAAG;QACrB,IAAI,EAAE,MAAM;QACZ,aAAa,EAAE,OAAO;QACtB,OAAO,EAAE,OAAO;QAChB,eAAe,EAAE,OAAO;KACzB,CAAC;IAEF,IAAM,qCAAqC,GAAG,yBAAyB,GACnE,mCAAmC,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAqB;YAAnB,MAAM,GAAA,GAAA,MAAA,EAAE,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QACvE,IAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,SAAA,GAAG;YAAI,OAAA,GAAG,IAAI,yBAAyB;QAAhC,CAAgC,CAAC,IAAI,GAAG,CAAC;QAC3F,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,yBAAyB,CAAC,YAAY,CAAC,KAAK,IAAI,GACjE,YAAY,KAAK,eAAe,GAC9B,cAAc,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,IAAI,yBAAyB,CAAC,YAAY,CAAC,GAClG,yBAAyB,CAAC,YAAY,CAAC,GACzC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9C,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAA,CAAE,CAAC,GACJ,IAAI,CAAC;IAET,IAAM,sCAAsC,GAAG,0BAA0B,GACrE,8BAA8B,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAqB;YAAnB,MAAM,GAAA,GAAA,MAAA,EAAE,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAClE,IAAM,aAAa,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,SAAA,GAAG;YAAI,OAAA,GAAG,IAAI,0BAA0B;QAAjC,CAAiC,CAAC,IAAI,GAAG,CAAC;QAC7F,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,0BAA0B,CAAC,aAAa,CAAC,KAAK,IAAI,GACnE,aAAa,KAAK,eAAe,GAC/B,cAAc,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC,IAAI,0BAA0B,CAAC,aAAa,CAAC,GACtG,0BAA0B,CAAC,aAAa,CAAC,GAC3C,IAAI,CAAC;QACT,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAA,CAAE,CAAC,GACJ,IAAI,CAAC;IAET,OAAO;QACL;YAAE,GAAG,EAAE,0BAA0B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnD;YAAE,GAAG,EAAE,0BAA0B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnD;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,WAAW;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACpC;YAAE,GAAG,EAAE,qBAAqB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC9C;YAAE,GAAG,EAAE,sBAAsB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC/C;YAAE,GAAG,EAAE,6BAA6B;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACtD;YAAE,GAAG,EAAE,yBAAyB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAClD;YAAE,GAAG,EAAE,WAAW;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACrC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACnC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;QACpC;YAAE,GAAG,EAAE,kBAAkB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC3C;YAAE,GAAG,EAAE,cAAc;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACvC;YAAE,GAAG,EAAE,mBAAmB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC5C;YAAE,GAAG,EAAE,eAAe;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACxC;YAAE,GAAG,EAAE,yBAAyB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAClD;YAAE,GAAG,EAAE,qBAAqB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC9C;YAAE,GAAG,EAAE,OAAO;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAChC;YAAE,GAAG,EAAE,oBAAoB;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QAC7C;YAAE,GAAG,EAAE,aAAa;YAAE,IAAI,EAAE,QAAQ;QAAA,CAAE;QACtC;YAAE,GAAG,EAAE,UAAU;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE;KACrC,CAAC,MAAM,CAAC,SAAC,MAAM,EAAE,EAAa;YAAX,GAAG,GAAA,GAAA,GAAA,EAAE,IAAI,GAAA,GAAA,IAAA;QAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,wBAAwB,CAAC,GAAG,CAAC,KAAK,IAAI,GACvD,wBAAwB,CAAC,GAAG,CAAC,GAC7B,IAAI,CAAC;QACT,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE;QACD,cAAc,EAAE,qCAAqC;QACrD,eAAe,EAAE,sCAAsC;KACxD,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG,CACH,SAAS,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,aAAa;IACpD,IAAM,qBAAqB,GAAG,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;IACxF,IAAI,cAAc,CAAC,qBAAqB,CAAC,EAAE;QACzC,OAAO,cAAc,CAAC,qBAAqB,CAAC,EAAE,CAC3C,GAAG,CAAC,SAAC,EAAS;gBAAP,KAAK,GAAA,GAAA,KAAA;YAAO,OAAA,KAAK;QAAL,CAAK,CAAC,CACzB,MAAM,CAAC,SAAA,KAAK;YAAI,OAAA,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI;QAA5B,CAA4B,CAAC,CAAC;KAClD;IACD,IAAM,UAAU,GAAG,aAAa,KAAK,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,kBAAkB,CAAC;IACtF,IAAM,SAAS,GAAG,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC;IACzE,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE,SAAA,MAAM;QAAI,OAAA,MAAM,CAAC,SAAS,CAAC,EAAE;IAAnB,CAAmB,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,aAAa,CAAC,cAAc,EAAE,KAAK;;IAC1C,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IAED,sDAAsD;IACtD,IAAI,cAAc,CAAC,YAAY,EAAE;QAC/B,IAAM,SAAS,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC;;YAChD,IAAuB,IAAA,cAAA,SAAA,SAAS,CAAA,EAAA,gBAAA,YAAA,IAAA,EAAA,EAAA,CAAA,cAAA,IAAA,EAAA,gBAAA,YAAA,IAAA,GAAE;gBAA7B,IAAM,QAAQ,GAAA,cAAA,KAAA;gBACjB,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;oBACpD,OAAO,IAAI,CAAC;iBACb;aACF;;;;;;;;;;;;KACF;IAED,wDAAwD;IACxD,IAAI,cAAc,CAAC,gBAAgB,EAAE;QACnC,IAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;;YACxD,IAAqB,IAAA,kBAAA,SAAA,aAAa,CAAA,EAAA,oBAAA,gBAAA,IAAA,EAAA,EAAA,CAAA,kBAAA,IAAA,EAAA,oBAAA,gBAAA,IAAA,GAAE;gBAA/B,IAAM,MAAM,GAAA,kBAAA,KAAA;gBACf,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;;oBAClC,IAA0B,IAAA,WAAA,CAAA,OAAA,KAAA,GAAA,SAAA,MAAM,CAAA,CAAA,EAAA,aAAA,SAAA,IAAA,EAAA,EAAA,CAAA,WAAA,IAAA,EAAA,aAAA,SAAA,IAAA,GAAE;wBAA7B,IAAM,WAAW,GAAA,WAAA,KAAA;wBACpB,IAAI,WAAW,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;4BAC/B,OAAO,IAAI,CAAC;yBACb;qBACF;;;;;;;;;;;;aACF;;;;;;;;;;;;KACF;IAED,8DAA8D;IAC9D,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;GAMG,CACH,SAAS,aAAa,CAAC,cAAc,EAAE,KAAK,EAAE,OAAY;IAAZ,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAAY;IAAA;IACxD,IAAI,OAAO,OAAO,CAAC,aAAa,KAAK,WAAW,IAAI,QAAQ,EAAE;QAC5D,OAAO,2BAA2B,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KACpE;IACD,IAAI,OAAO,OAAO,CAAC,cAAc,KAAM,WAAW,IAAI,SAAS,EAAE;QAC/D,OAAO,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KAC7D;IACD,IAAI,OAAO,OAAO,CAAC,aAAa,KAAM,WAAW,IAAI,QAAQ,EAAE;QAC7D,IAAI,OAAO,OAAO,CAAC,aAAa,KAAM,WAAW,IAAI,YAAY,EAAE,KAAK,SAAS,EAAE;YACjF,OAAO,2BAA2B,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpE;QACD,sDAAsD;QACtD,iDAAiD;QACjD,kEAAkE;QAClE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;YAC9B,uDAAuD;YACvD,iEAAiE;SAClE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KACf;IACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;AACjF,CAAC;AAGD;;;;;;GAMG,CACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO;IACjE,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IACxB,IAAI,kBAAkB,IAAI,kBAAkB,GAAG,EAAE,EAAE;QACjD,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,cAAc,CAAC,QAAQ,CAAC,SAAA,QAAQ;gBAC9B,OAAO,CAAC;oBAAC,4BAA4B,CAAC,QAAQ,EAAE,KAAK,CAAC;iBAAC,CAAC,CAAC;YAC3D,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAClC,IAAI,CAAC,SAAA,QAAQ;QACZ,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACzC,OAAO,8BAA8B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC,CAAC,CACD,KAAK,CAAC;QACL,gEAAgE;QAChE,qEAAqE;QACrE,GAAG,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC5D,OAAO,cAAc,CAAC,QAAQ,EAAE,CAC7B,IAAI,CAAC,SAAA,KAAK;YACT,GAAG,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC3C,IAAM,QAAQ,GAAG,aAAa,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACtD,GAAG,CAAC,IAAI,CAAC,kCAAA,CAAgC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,IAAA,QAAQ,CAAC,CAAC;YAChF,IAAM,aAAa,GAAG,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACjE,GAAG,CAAC,IAAI,CAAC,mCAAA,CAAiC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,IAAA,QAAQ,CAAC,CAAC;YACjF,OAAO,8BAA8B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;GAMG,CACH,SAAS,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO;IAC1D,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAA,QAAQ;QACjD,OAAO;YAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC;SAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC;AACD;;;;;GAKG,CACH,SAAS,4BAA4B,CAAC,QAAQ,EAAE,KAAK;IACnD,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,SAAA,MAAM;QAC9C,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,GAAG,CAAA,CAAE,CAAC;IAE3B,IAAI,UAAU,EAAE;QACd,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;QACvE,iBAAiB,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,SAAC,KAAK,EAAE,IAAI;YACxD,OAAQ,IAAI,EAAE;gBACZ,KAAK,eAAe;oBAClB,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,SAAS;oBACZ,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpD,MAAM;gBACR,KAAK,oBAAoB;oBACvB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7C,MAAM;gBACR,KAAK,qBAAqB;oBACxB,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,sBAAsB;oBACzB,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,MAAM;gBACR,KAAK,oBAAoB;oBACvB,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,qBAAqB;oBACxB,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,wBAAwB;oBAC3B,KAAK,CAAC,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzD,MAAM;gBACR,KAAK,yBAAyB;oBAC5B,KAAK,CAAC,mBAAmB,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC1D,MAAM;gBACR,KAAK,oBAAoB;oBACvB,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,mBAAmB;oBACtB,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpD,MAAM;gBACR,KAAK,uBAAuB;oBAC1B,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACxD,MAAM;gBACR,KAAK,MAAM;oBACT,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpC,MAAM;gBACR,KAAK,eAAe,CAAC;gBACrB,KAAK,WAAW,CAAC;gBACjB,KAAK,aAAa,CAAC;gBACnB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,aAAa,CAAC;gBACnB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB;oBACrB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC5C,MAAM;aACT;YAED,OAAO,KAAK,CAAC;QACf,CAAC,EAAE,iBAAiB,CAAC,CAAC;KACvB;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;GAKG,CACH,SAAS,8BAA8B,CAAC,QAAQ,EAAE,EAAoD;QAAlD,KAAA,GAAA,wCAAgD,EAAhD,wCAAwC,GAAA,OAAA,KAAA,IAAG,KAAK,GAAA,EAAA;IAClG,IAAI,wCAAwC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IACD,IAAI,OAAO,GAAG,IAAI,CAAC;IAEnB,+EAA+E;IAC/E,kCAAkC;IAClC,IAAM,QAAQ,GAAG,EAAE,CAAC;IAEpB,IAAI,aAAa,GAAG,IAAI,CAAC;IACzB,IAAI,cAAc,GAAG,IAAI,CAAC;IAC1B,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,UAAU,GAAG,IAAI,CAAC;IAEtB,QAAQ,CAAC,OAAO,CAAC,SAAA,IAAI;QACX,IAAA,IAAI,GAAK,IAAI,CAAA,IAAT,CAAU;QACtB,OAAQ,IAAI,EAAE;YACZ,KAAK,aAAa;gBAChB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACR,KAAK,cAAc;gBACjB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpB,MAAM;YACR,KAAK,cAAc;gBACjB,UAAU,GAAG,IAAI,CAAC;gBAClB,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM;YACR,KAAK,oBAAoB;gBACvB,aAAa,GAAG,IAAI,CAAC;gBACrB,MAAM;YACR,KAAK,qBAAqB;gBACxB,cAAc,GAAG,IAAI,CAAC;gBACtB,MAAM;SACT;IACH,CAAC,CAAC,CAAC;IAEH,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IAC1D,IAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;QAAC,OAAO;KAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACpD,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,mBAAmB;IAEnF,WAAW,CAAC,OAAO,CAAC,SAAA,MAAM;QACxB,IAAM,iBAAiB,GAAG,CAAA,CAAE,CAAC;QAC7B,IAAM,WAAW,GAAG;YAClB,MAAM;YACN,UAAU;YACV,KAAK;YACL,KAAK;YACL,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,mBAAmB;SAC7F,CAAC;QAEF,SAAS,YAAY,CAAC,IAAI;YACxB,IAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,SAAA,UAAU;gBAC7C,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC;YAC/D,CAAC,CAAC,IAAI,IAAI,CAAC;YAEX,OAAO,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAChD,CAAC;QAED,IAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,iBAAiB,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SACvC;QAED,IAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;QAC5C,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEpD,IAAI,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,iBAAiB,CAAC,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC7D;QAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;SACpE;QAED,IAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;SACtD;QAED,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;QAC9C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAClC,IAAI,QAAQ,EAAE;gBACZ,iBAAiB,CAAC,kBAAkB,GAAG,UAAU,CAAC;aACnD,MAAM;gBACL,iBAAiB,CAAC,cAAc,GAAG,UAAU,CAAC;gBAC9C,iBAAiB,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC;aACjF;SACF;QAED,IAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,IAAI,QAAQ,EAAE;gBACZ,iBAAiB,CAAC,mBAAmB,GAAG,WAAW,CAAC;aACrD,MAAM;gBACL,iBAAiB,CAAC,eAAe,GAAG,WAAW,CAAC;gBAChD,iBAAiB,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;aACpF;SACF;QAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACvC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC;SACvF;QAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;SACjD;QAED,IAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;SACzC;QAED,IAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;SAC7C;QAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;SACrD;QAED,IAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;SAC7C;QAED,IAAI,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAClC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,6BAA6B,CAAC,CAAC;YACpE,IAAI,QAAQ,EAAE;gBACZ,iBAAiB,CAAC,gBAAgB,GAAG,UAAU,CAAC;aACjD,MAAM;gBACL,iBAAiB,CAAC,eAAe,GAAG,UAAU,CAAC;aAChD;SACF;QAED,IAAM,oBAAoB,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAClE,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC5C,iBAAiB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;SAC/D;QAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;SACrD;QAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;SACjD;QAED,IAAM,yBAAyB,GAAG,YAAY,CAAC,2BAA2B,CAAC,CAAC;QAC5E,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE;YACjD,iBAAiB,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;SACzE;QAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;SACrD;QAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;SACjD;QAED,IAAM,iBAAiB,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAC5D,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;YACzC,iBAAiB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;SACzD;QAED,IAAM,wBAAwB,GAAG,YAAY,CAAC,0BAA0B,CAAC,CAAC;QAC1E,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;YAChD,iBAAiB,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;SACvE;QAED,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG,CACH,SAAS,uBAAuB,CAAC,QAAoB,EAAE,EAA8D;IAApF,IAAA,aAAA,KAAA,GAAA;QAAA,WAAA,IAAe,GAAG,EAAE;IAAA;QAAI,QAAQ,GAAA,GAAA,QAAA,EAAE,KAAA,GAAA,wCAAgD,EAAhD,wCAAwC,GAAA,OAAA,KAAA,IAAG,KAAK,GAAA,EAAA;IACjH,IAAI,wCAAwC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IACD,uEAAuE;IACvE,6EAA6E;IAC7E,gDAAgD;IAChD,EAAE;IACF,yDAAyD;IACzD,EAAE;IAEF,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,QAAQ,GAAG,IAAI,CAAC;IAEpB,4FAA4F;IAC5F,6EAA6E;IAC7E,4EAA4E;IAC5E,2EAA2E;IAC3E,2EAA2E;IAC3E,6DAA6D;IAC7D,EAAE;IACF,gEAAgE;IAChE,EAAE;IACF,QAAQ,CAAC,OAAO,CAAC,SAAA,IAAI;QACX,IAAA,QAAQ,GAAqB,IAAI,CAAA,QAAzB,EAAE,QAAQ,GAAW,IAAI,CAAA,QAAf,EAAE,IAAI,GAAK,IAAI,CAAA,IAAT,CAAU;QAC1C,IAAI,QAAQ,EAAE;YACZ,OAAO;SACR;QACD,OAAQ,IAAI,EAAE;YACZ,KAAK,aAAa;gBAChB,OAAO,GAAG,IAAI,CAAC;gBACf,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,cAAc;gBACjB,QAAQ,GAAG,IAAI,CAAC;gBAChB,OAAO,GAAG,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC1C,MAAM;SACT;IACH,CAAC,CAAC,CAAC;IAEH,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;IAE7C,SAAS,YAAY,CAAC,IAAI;QACxB,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;YAC/C,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;SACpB;QACD,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;YACjD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAM,iBAAiB,GAAG,CAAA,CAAE,CAAC;IAC7B,IAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;IAC5C,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAEpD,IAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAClC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,iBAAiB,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;KACvC;IAED,IAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;IAC5C,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;KACzC;IAED,IAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;IAChD,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;KAC7C;IAED,IAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;IAChD,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;KAC7C;IAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,iDAAiD;QACjD,uFAAuF;QACvF,iDAAiD;QACjD,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;KACpE;IAED,IAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IACtC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;KACtD;IAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;KAC7D;IAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;KACjD;IAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;IACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;KACrD;IAED,IAAM,iBAAiB,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACxD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;QACzC,iBAAiB,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACrE;IAED,IAAM,oBAAoB,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC;IAClE,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;QAC5C,iBAAiB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;KAC/D;IAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;IACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;KACrD;IAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;KACjD;IAED,IAAM,yBAAyB,GAAG,YAAY,CAAC,2BAA2B,CAAC,CAAC;IAC5E,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE;QACjD,iBAAiB,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;KACzE;IAED,IAAM,eAAe,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;IACxD,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QACvC,iBAAiB,CAAC,eAAe,GAAG,eAAe,CAAC;KACrD;IAED,IAAM,aAAa,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;IACpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,iBAAiB,CAAC,aAAa,GAAG,aAAa,CAAC;KACjD;IAED,IAAM,iBAAiB,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC5D,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;QACzC,iBAAiB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KACzD;IAED,IAAM,wBAAwB,GAAG,YAAY,CAAC,0BAA0B,CAAC,CAAC;IAC1E,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;QAChD,iBAAiB,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;KACvE;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;;;GASG,CAEH;;;;;GAKG,CAEH;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG,CAEH;;;;;;;;GAQG,CAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG,CAEH,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 2113, "column": 0}, "map": {"version": 3, "file": "getusermedia.js", "sourceRoot": "", "sources": ["../../lib/webrtc/getusermedia.js"], "names": [], "mappings": "AAAA,qBAAA,EAAuB,CACvB,YAAY,CAAC;AAEb;;;;;;;;;GASG,CACH,SAAS,YAAY,CAAC,WAA0C;IAA1C,IAAA,gBAAA,KAAA,GAAA;QAAA,cAAA;YAAgB,KAAK,EAAE,IAAI;YAAE,KAAK,EAAE,IAAI;QAAA,CAAE;IAAA;IAC9D,IAAI,OAAO,SAAS,KAAK,QAAQ,IAC5B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ,IAC1C,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,KAAK,UAAU,EAAE;QAC9D,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACzD;IACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "file": "mediastream.js", "sourceRoot": "", "sources": ["../../lib/webrtc/mediastream.js"], "names": [], "mappings": "AAAA,uBAAA,EAAyB,CACzB,YAAY,CAAC;AAEb,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;IACrC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;CAC9B,MAAM;IACL,MAAM,CAAC,OAAO,GAAG,SAAS,WAAW;QACnC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC,CAAC;CACH", "debugId": null}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "file": "mediastreamtrack.js", "sourceRoot": "", "sources": ["../../lib/webrtc/mediastreamtrack.js"], "names": [], "mappings": "AAAA,2BAAA,EAA6B,CAC7B,YAAY,CAAC;AAEb,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;IAC1C,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC;CACnC,MAAM;IACL,MAAM,CAAC,OAAO,GAAG,SAAS,gBAAgB;QACxC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC,CAAC;CACH", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "file": "rtcicecandidate.js", "sourceRoot": "", "sources": ["../../lib/webrtc/rtcicecandidate.js"], "names": [], "mappings": "AAAA,0BAAA,EAA4B,CAC5B,YAAY,CAAC;AAEb,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;IACzC,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC;CAClC,MAAM;IACL,MAAM,CAAC,OAAO,GAAG,SAAS,eAAe;QACvC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC,CAAC;CACH", "debugId": null}}, {"offset": {"line": 2180, "column": 0}, "map": {"version": 3, "file": "chrome.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcsessiondescription/chrome.js"], "names": [], "mappings": "AAAA,iCAAA,EAAmC,CACnC,YAAY,CAAC;AAEb,8EAA8E;AAC9E,qEAAqE;AACrE,EAAE;AACF,wBAAwB;AACxB,gEAAgE;AAChE,EAAE;AACF,IAAA,8BAAA;IACE,SAAA,4BAAY,mBAAmB;QAC7B,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,4EAA4E;QAC5E,6EAA6E;QAC7E,gDAAgD;QAChD,IAAM,WAAW,GAAG,mBAAmB,IAAI,mBAAmB,CAAC,IAAI,KAAK,UAAU,GAC9E,IAAI,GACJ,IAAI,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;QAEnD,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,GAAG,EAAE;oBACH,OAAO,WAAW,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAA,cAAA,CAAI,4BAAA,SAAA,EAAA,KAAG,EAAA;aAAP;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;QAClF,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,4BAAA,SAAA,EAAA,MAAI,EAAA;aAAR;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QACpF,CAAC;;;OAAA;IACH,OAAA,2BAAC;AAAD,CAAC,AA3BD,IA2BC;AAED,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC", "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "file": "latch.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/util/latch.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,IAAI,CAAC,kGAAA,KAAlB,CAAmB;AAEhC,IAAM,MAAM,GAAG;IACb,IAAI,EAAE,IAAI,GAAG,CAAC;QAAC,KAAK;KAAC,CAAC;IACtB,GAAG,EAAE,IAAI,GAAG,CAAC;QAAC,MAAM;KAAC,CAAC;CACvB,CAAC;AAEF;;;;;;GAMG,CACH,IAAA,QAAA;IACE,SAAA,MAAY,YAAoB;QAApB,IAAA,iBAAA,KAAA,GAAA;YAAA,eAAA,KAAoB;QAAA;QAC9B,IAAI,KAAK,GAAG,YAAY,CAAC;QACzB,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,MAAM,EAAE;gBACN,GAAG,EAAE,SAAS,MAAM;oBAAf,IAAA,QAAA,IAAA,CAOJ;oBANC,IAAI,KAAK,KAAK,MAAM,EAAE;wBACpB,KAAK,GAAG,MAAM,CAAC;wBACf,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACrD,aAAa,CAAC,OAAO,CAAC,SAAA,QAAQ;4BAAI,OAAA,QAAQ,CAAC,OAAO,CAAC,KAAI,CAAC;wBAAtB,CAAsB,CAAC,CAAC;wBAC1D,aAAa,CAAC,KAAK,EAAE,CAAC;qBACvB;gBACH,CAAC;gBACD,GAAG,EAAE;oBACH,OAAO,KAAK,CAAC;gBACf,CAAC;aACF;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,CAAC;oBACb;wBAAC,MAAM;wBAAE,IAAI,GAAG,EAAE;qBAAC;oBACnB;wBAAC,KAAK;wBAAE,IAAI,GAAG,EAAE;qBAAC;iBACnB,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAA,cAAA,CAAI,MAAA,SAAA,EAAA,OAAK,EAAA;aAAT;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED;;;;OAIG,CACH,MAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACH,MAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;;;;OAKG,CACH,MAAA,SAAA,CAAA,UAAU,GAAV,SAAW,QAAQ;QACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACrC,MAAM,2BAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACH,MAAA,SAAA,CAAA,IAAI,GAAJ,SAAK,KAAK;QACR,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9B;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAClC,OAAO,OAAO,CAAC,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SACvE;QACD,IAAM,QAAQ,GAAG,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7C,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IACH,OAAA,KAAC;AAAD,CAAC,AA/ED,IA+EC;AAED;;;;;KAKK,CACL,SAAS,2BAA2B,CAAC,IAAI,EAAE,EAAE;IAC3C,OAAO,IAAI,KAAK,CAAC,8BAA2B,IAAI,GAAA,aAAS,EAAE,GAAA,IAAG,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC", "debugId": null}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "file": "rtcrtpsender.js", "sourceRoot": "", "sources": ["../../lib/webrtc/rtcrtpsender.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb;;;;GAIG,CACH,IAAA,mBAAA;IACE,SAAA,iBAAY,KAAK;QACf,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAVD,IAUC;AAED,yEAAyE;AACzE,sDAAsD;AACtD,EAAE;AACF,eAAe;AACf,qCAAqC;AACrC,qCAAqC;AACrC,0BAA0B;AAC1B,gDAAgD;AAChD,EAAE;AACF,eAAe;AACf,qCAAqC;AACrC,6CAA6C;AAC7C,gCAAgC;AAChC,yBAAyB;AACzB,6CAA6C;AAC7C,4CAA4C;AAC5C,EAAE;AACF,0FAA0F;AAC1F,gFAAgF;AAChF,+BAA+B;AAE/B,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "file": "chrome.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/chrome.js"], "names": [], "mappings": "AAAA,oEAAA,EAAsE,CACtE,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,2BAA2B,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC/E,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACvC,IAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC9C,IAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC9C,IAAA,KAAiF,OAAO,CAAC,aAAa,CAAC,wFAArG,YAAY,GAAA,GAAA,YAAA,EAAE,0BAA0B,GAAA,GAAA,0BAAA,EAAE,gCAAgC,GAAA,GAAA,gCAA2B,CAAC;AACxG,IAAA,KAAmF,OAAO,CAAC,SAAS,CAAC,8FAAnG,eAAe,GAAA,GAAA,eAAA,EAAE,cAAc,GAAA,GAAA,cAAA,EAAE,WAAW,GAAA,GAAA,WAAA,EAAE,aAAa,GAAA,GAAA,aAAA,EAAE,eAAe,GAAA,GAAA,eAAuB,CAAC;AAE5G,IAAM,aAAa,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC;AAEnD,8EAA8E;AAC9E,6EAA6E;AAC7E,eAAe;AACf,EAAE;AACF,oDAAoD;AACpD,mEAAmE;AACnE,EAAE;AACF,+DAA+D;AAC/D,EAAE;AACF,+BAA+B;AAC/B,EAAE;AACF,IAAA,0BAAA,SAAA,MAAA;IAAsC,UAAA,yBAAA,QAAW;IAC/C,SAAA,wBAAY,aAAkB,EAAE,WAAW;QAA/B,IAAA,kBAAA,KAAA,GAAA;YAAA,gBAAA,CAAA,CAAkB;QAAA;QAA9B,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAwER;QAtEC,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,kBAAkB,GACnE;YAAE,aAAa,EAAE,aAAa,CAAC,kBAAkB;QAAA,CAAE,GACnD,CAAA,CAAE,EAAE,aAAa,CAAC,CAAC;QAEvB,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,CAAC;QACpC,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAC7C,IAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAE5E,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,WAAW,EAAE;aACzB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,KAAK,EAAE;aACnB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAA,KAAK;YAClD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/B,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,IAAA,OAAA,EAAA,CAAO;gBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;gBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;YAC9D,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC9C,KAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,KAAI,CAAC,kBAAkB,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;gBACzD,KAAI,CAAC,aAAa,CAAA,KAAA,CAAlB,KAAI,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI,IAAE;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,OAAO,GAAG;QACvB,2EAA2E;QAC3E,iDAAiD;QACnD,CAAC,CAAC;QAEF,IAAI,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU,EAAE;YACjD,cAAc,CAAC,SAAS,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;SAC7C;QACD,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IACrE,CAAC;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,kBAAgB,EAAA;aAApB;YACE,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QACnG,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,mBAAiB,EAAA;aAArB;YACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QACtG,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,gBAAc,EAAA;aAAlB;YACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,OAAO,kBAAkB,CAAC;aAC3B,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACnC,OAAO,mBAAmB,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAC7C,CAAC;;;OAAA;IAED,kEAAkE;IAClE,yEAAyE;IACzE,yEAAyE;IACzE,gCAAgC;IAChC,wBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,KAAK;;QAAE,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,KAAA,EAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACrB,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,OAAO,CAAA,KAAA,IAAI,CAAC,eAAe,CAAA,CAAC,QAAQ,CAAA,KAAA,CAAA,IAAA,cAAA;gBAAC,KAAK;aAAA,EAAA,OAAK,IAAI,IAAE;SACtD;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,kCAAgC,KAAK,CAAC,EAAE,GAAA,iBACpD,KAAK,CAAC,IAAI,GAAA,gCAAgC,CAAC,CAAC;SACjD;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,mCAAiC,KAAK,CAAC,EAAE,GAAA,iBACrD,KAAK,CAAC,IAAI,GAAA,qCAAqC,CAAC,CAAC;SACtD;QACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,MAAM,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,oEAAoE;IACpE,uEAAuE;IACvE,4EAA4E;IAC5E,gCAAgC;IAChC,wBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,MAAM;QAChB,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAChF;QACD,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,IAAI;gBACF,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aACjD,CAAC,OAAO,CAAC,EAAE;YACV,uEAAuE;YACvE,sEAAsE;YACtE,2EAA2E;YAC3E,uBAAuB;YACvB,+DAA+D;aAChE;SACF,MAAM;YACG,IAAA,KAAK,GAAK,MAAM,CAAA,KAAX,CAAY;YACzB,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YACD,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACnD;SACF;IACH,CAAC;IAED,wBAAA,SAAA,CAAA,UAAU,GAAV;QACE,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;SAC1C;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,wBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,SAAS;QAAzB,IAAA,QAAA,IAAA,CAgBC;QAhB0B,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,KAAA,EAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QAChC,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC/C,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;gBACnD,OAAA,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC;YAA/C,CAA+C,CAAC,CAAC;SACpD,MAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAA,KAAA,CAAA,KAAA,GAAA,cAAA;YAAC,OAAO;SAAA,EAAA,OAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,8EAA8E;IAC9E,wEAAwE;IACxE,2BAA2B;IAC3B,wBAAA,SAAA,CAAA,KAAK,GAAL;QACE,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IAED,6EAA6E;IAC7E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,0EAA0E;IAC1E,oCAAoC;IACpC,wBAAA,SAAA,CAAA,YAAY,GAAZ;QAAA,IAAA,QAAA,IAAA,CAyCC;QAzCY,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QAClB,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC;gBACjF,0EAA0E;gBAC1E,yEAAyE;gBACzE,wDAAwD;gBACxD,KAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;gBAClC,OAAO,KAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,SAAA,MAAM;gBACZ,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAEhC,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,IAAI,2BAA2B,CAAC;oBACrC,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC7E,CAAC,CAAC;YACL,CAAC,EAAE,SAAA,KAAK;gBACN,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;SACJ,MAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,SAAA,MAAM;gBACvD,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,IAAI,2BAA2B,CAAC;oBACrC,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC7E,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAA,KAAA,CAAA,KAAA,GAAA,cAAA;YAAC,OAAO;SAAA,EAAA,OAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,wBAAA,SAAA,CAAA,WAAW,GAAX;QAAA,IAAA,QAAA,IAAA,CA2CC;QA3CW,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACX,IAAA,KAAA,OAAqB,IAAI,EAAA,EAAA,EAAxB,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAQ,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,IAAI,IAAI,IAAI,CAAA,CAAE,CAAC;QAEnC,IAAI,WAAW,EAAE,EAAE;YACjB,4EAA4E;YAC5E,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;gBACnC,IAAI;oBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAClC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;wBAAE,SAAS,EAAE,UAAU;oBAAA,CAAE,CAAC,GACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;iBAClC,CAAC,OAAO,CAAC,EAAE;oBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;YAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;gBACnC,IAAI;oBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAClC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;wBAAE,SAAS,EAAE,UAAU;oBAAA,CAAE,CAAC,GACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;iBAClC,CAAC,OAAO,CAAC,EAAE;oBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;SACF;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAA,KAAK;YAClE,0FAA0F;YAC1F,6DAA6D;YAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,IAAI,2BAA2B,CAAC;gBACrC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aAC5E,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAClC,OAAO,CAAC;IACd,CAAC;IAED,wBAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,KAAK,EAAE,eAAe;QACtC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACvD,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACnF,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,wBAAA,SAAA,CAAA,mBAAmB,GAAnB;QAAoB,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACnB,IAAA,KAAA,OAA4B,IAAI,EAAA,EAAA,EAA/B,WAAW,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAQ,CAAC;QAEvC,uFAAuF;QACvF,+DAA+D;QAC/D,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7D,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;SACvC;QAED,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAClC,OAAO,CAAC;IACd,CAAC;IAED,wBAAA,SAAA,CAAA,oBAAoB,GAApB;QAAqB,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACpB,IAAA,KAAA,OAA4B,IAAI,EAAA,EAAA,EAA/B,WAAW,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAQ,CAAC;QAEvC,wFAAwF;QACxF,wEAAwE;QACxE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAEtC,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAClC,OAAO,CAAC;IACd,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AA7TD,CAAsC,WAAW,GA6ThD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,uBAAuB,CAAC,SAAS,EACjC,iBAAiB,CAAC,CAAC;AAErB,2EAA2E;AAC3E,yFAAyF;AACzF,+EAA+E;AAC/E,yEAAyE;AACzE,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,WAAW;IACxD,SAAS,oBAAoB,CAAC,KAAK;QACjC,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C,MAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC5C;IACH,CAAC;IAED,SAAS,sBAAsB;QAC7B,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC1C,MAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC3C;IACH,CAAC;IAED,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC;IACzG,IAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC;IAC1G,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC;IAC3E,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IACnF,IAAI,OAAO,CAAC;IAEZ,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjE,OAAO,GAAG,eAAe,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KACxD,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QACvC,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;YACrG,qDAAqD;YACrD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAA,CAAc,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAA,qBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SAC9H;QAED,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,2EAA2E;QAC3E,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,oBAAoB,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7E,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SAC7C;QACD,IAAM,sBAAsB,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1C,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE5B,iEAAiE;QACjE,IAAI,cAAc,CAAC,cAAc,KAAK,sBAAsB,EAAE;YAC5D,OAAO,CAAC,IAAI,CAAC;gBAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAA/D,CAA+D,CAAC,CAAC;SACrF;KAEF,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;QAC1C,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,EAAE;YACvD,qDAAqD;YACrD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qBAAA,CAAmB,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAA,qBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACtI,MAAM;YACL,2BAA2B;YAC3B,sBAAsB,EAAE,CAAC;YAEzB,8EAA8E;YAC9E,gFAAgF;YAChF,iFAAiF;YACjF,kDAAkD;YAClD,cAAc,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACjF,cAAc,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAE9E,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC;gBAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAA/D,CAA+D,CAAC,CAAC;SACrF;KACF;IAED,OAAO,OAAO,IAAI,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7F,CAAC;AAED,SAAS,eAAe,CAAC,cAAc,EAAE,MAAM;IAC7C,iCAAiC;IACjC,IAAM,iBAAiB,GAAG,cAAc,CAAC,kBAAkB,CAAC;IAC5D,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;QAChF,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACzC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,IAAI,CAAC;QACN,0EAA0E;QAC1E,yEAAyE;QACzE,wDAAwD;QACxD,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC9C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG,CACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,IAAI;IACvD,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,SAAC,EAAiB;YAAf,KAAA,GAAA,QAAa,EAAb,QAAQ,GAAA,OAAA,KAAA,IAAG,CAAA,CAAE,GAAA,EAAA;QACrD,IAAA,KAAe,QAAQ,CAAA,KAAb,EAAV,KAAK,GAAA,OAAA,KAAA,IAAG,CAAA,CAAE,GAAA,EAAA,CAAc;QAChC,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,MAAM,CAAC,WAAW;IACzB,IAAI,WAAW,YAAY,2BAA2B,EAAE;QACtD,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,OAAO,WAAW,CAAC,YAAY,CAAC;SACjC;KACF;IACD,OAAO,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC;AAChD,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,0BAA0B;IACjC,OAAO,mBAAmB,IAAI,cAAc,CAAC,SAAS,IACjD,CAAC,CAAC,mBAAmB,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;GAKG,CACH,SAAS,mBAAmB,CAAC,eAAe;IAC1C,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,eAAe,CAAC,CAAC;IACrD,IAAI,0BAA0B,EAAE,IAAI,mBAAmB,IAAI,eAAe,EAAE;QAC1E,eAAe,CAAC,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC;KACvE;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;GAKG,CACH,SAAS,eAAe,CAAC,WAAW;IAClC,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,EAAE;QACnD,KAAK,EAAE,WAAW,CAAC,cAAc,KAAK,KAAK,GACvC,IAAI,GACJ,WAAW,CAAC,cAAc;KAC/B,CAAC,CAAC;IACH,IAAI,0BAA0B,EAAE,EAAE;QAChC,4EAA4E;QAC5E,EAAE;QACF,iEAAiE;QACjE,EAAE;QACF,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,EAAE;YACtD,KAAK,EAAE,WAAW,CAAC,iBAAiB,KAAK,KAAK,GAC1C,IAAI,GACJ,WAAW,CAAC,iBAAiB;SAClC,CAAC,CAAC;KACJ;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,qBAAqB,CAAC,SAAS,EAAE,aAAa,EAAE,GAAG;IAC1D,OAAO,SAAS,KAAK,SAAS,GAC1B,gCAAgC,CAAC,aAAa,EAAE,GAAG,CAAC,GACpD,0BAA0B,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 2938, "column": 0}, "map": {"version": 3, "file": "firefox.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcsessiondescription/firefox.js"], "names": [], "mappings": "AAAA,iCAAA,EAAmC,CACnC,YAAY,CAAC;AAEb,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "file": "firefox.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/firefox.js"], "names": [], "mappings": "AAAA,6BAAA,EAA+B,CAC/B,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,4BAA4B,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;AACzE,IAAkC,mBAAmB,GAAK,OAAO,CAAC,aAAa,CAAC,uFAAA,gCAA3B,CAA4B;AACnF,IAAA,KAAsE,OAAO,CAAC,SAAS,CAAC,8FAAtF,eAAe,GAAA,GAAA,eAAA,EAAE,cAAc,GAAA,GAAA,cAAA,EAAE,aAAa,GAAA,GAAA,aAAA,EAAE,eAAe,GAAA,GAAA,eAAuB,CAAC;AAE/F,+EAA+E;AAC/E,8EAA8E;AAC9E,eAAe;AACf,EAAE;AACF,4EAA4E;AAC5E,mEAAmE;AACnE,qBAAqB;AACrB,EAAE;AACF,6EAA6E;AAC7E,EAAE;AACF,4EAA4E;AAC5E,OAAO;AACP,EAAE;AACF,yDAAyD;AACzD,EAAE;AACF,sEAAsE;AACtE,qEAAqE;AACrE,EAAE;AACF,2DAA2D;AAC3D,EAAE;AACF,IAAA,2BAAA,SAAA,MAAA;IAAuC,UAAA,0BAAA,QAAW;IAChD,SAAA,yBAAY,aAAa;QAAzB,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CA2DR;QAzDC,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAE7C,oBAAA,EAAsB,CACtB,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YAED,yEAAyE;YACzE,wEAAwE;YACxE,0DAA0D;YAC1D,kEAAkE;YAClE,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC;oBACrB,GAAG,EAAE,EAAE;oBACP,IAAI,EAAE,EAAE;iBACT,CAAC;aACH;SACF,CAAC,CAAC;QAEH,IAAI,sBAAsB,CAAC;QAE3B,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,IAAA,OAAA,EAAA,CAAO;gBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;gBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;YAC9D,IAAI,CAAC,KAAI,CAAC,YAAY,IAAI,KAAI,CAAC,cAAc,KAAK,sBAAsB,EAAE;gBACxE,sBAAsB,GAAG,KAAI,CAAC,cAAc,CAAC;gBAE7C,gEAAgE;gBAChE,iDAAiD;gBACjD,qEAAqE;gBACrE,yDAAyD;gBACzD,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,UAAU,CAAC;wBAAM,OAAA,KAAI,CAAC,aAAa,CAAA,KAAA,CAAlB,KAAI,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI;oBAA1B,CAA2B,CAAC,CAAC;iBAC/C,MAAM;oBACL,KAAI,CAAC,aAAa,CAAA,KAAA,CAAlB,KAAI,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI,IAAE;iBAC7B;aACF;QACH,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IACrE,CAAC;IAED,OAAA,cAAA,CAAI,yBAAA,SAAA,EAAA,mBAAiB,EAAA;aAArB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC9E,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,yBAAA,SAAA,EAAA,kBAAgB,EAAA;aAApB;YACE,OAAO,wCAAwC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC5H,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,yBAAA,SAAA,EAAA,gBAAc,EAAA;aAAlB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QACzE,CAAC;;;OAAA;IAED,yBAAA,SAAA,CAAA,YAAY,GAAZ;QAAA,IAAA,QAAA,IAAA,CAWC;QAXY,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QAClB,IAAI,OAAO,CAAC;QAEZ,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,SAAA,MAAM;YACvD,+BAA+B,CAAC,KAAI,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,wCAAwC,CAAC,MAAM,EAAE,KAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,GAChC,aAAa,CAAA,KAAA,CAAA,KAAA,GAAA,cAAA;YAAC,OAAO;SAAA,EAAA,OAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,0EAA0E;IAC1E,+EAA+E;IAC/E,6EAA6E;IAC7E,gEAAgE;IAChE,2EAA2E;IAC3E,yEAAyE;IACzE,oCAAoC;IACpC,yBAAA,SAAA,CAAA,WAAW,GAAX;QAAA,IAAA,QAAA,IAAA,CAuBC;QAvBW,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACX,IAAA,KAAA,OAAqB,IAAI,EAAA,EAAA,EAAxB,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAA,EAAE,IAAI,GAAA,EAAA,CAAA,EAAQ,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,IAAI,IAAI,IAAI,CAAA,CAAE,CAAC;QACnC,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,cAAc,KAAK,kBAAkB,IAC5C,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,KAAK,kBAAkB,CAAC;YACzD,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;gBAAM,OAAA,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAAzB,CAAyB,CAAC,CAAC;SAClE,MAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACrD;QAED,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,SAAA,KAAK;YAC1B,OAAO,IAAI,4BAA4B,CAAC;gBACtC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,mBAAmB,CAAC,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aACzD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAClC,OAAO,CAAC;IACd,CAAC;IAED,oEAAoE;IACpE,8EAA8E;IAC9E,4EAA4E;IAC5E,yCAAyC;IACzC,yBAAA,SAAA,CAAA,mBAAmB,GAAnB;;QAAoB,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACnB,IAAA,KAAA,OAAyB,IAAI,CAAA,EAA5B,WAAW,GAAA,EAAA,CAAA,EAAA,EAAK,IAAI,GAAA,GAAA,KAAA,CAAA,EAAQ,CAAC;QACpC,IAAI,OAAO,CAAC;QAEZ,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,cAAc,KAAK,kBAAkB,EAAE;YAC9F,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAC;SAC1F;QAED,IAAI,OAAO,EAAE;YACX,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAA,KAAA,CAAA,KAAA,GAAA,cAAA;gBAAC,OAAO;aAAA,EAAA,OAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;SACb;QAED,OAAO,CAAA,KAAA,IAAI,CAAC,eAAe,CAAA,CAAC,mBAAmB,CAAA,KAAA,CAAA,IAAA,cAAA,EAAA,EAAA,OAAI,IAAI,IAAE;IAC3D,CAAC;IAED,+EAA+E;IAC/E,0EAA0E;IAC1E,+FAA+F;IAC/F,6EAA6E;IAC7E,6EAA6E;IAC7E,mBAAmB;IACnB,EAAE;IACF,8EAA8E;IAC9E,yEAAyE;IACzE,4EAA4E;IAC5E,sBAAsB;IACtB,yBAAA,SAAA,CAAA,oBAAoB,GAApB;QAAA,IAAA,QAAA,IAAA,CAsBC;QAtBoB,IAAA,OAAA,EAAA,CAAO;YAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;YAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;QACpB,IAAA,KAAA,OAAyB,IAAI,CAAA,EAA5B,WAAW,GAAA,EAAA,CAAA,EAAA,EAAK,IAAI,GAAA,GAAA,KAAA,CAAA,EAAQ,CAAC;QAEpC,IAAI,OAAO,CAAC;QAEZ,IAAI,WAAW,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC9D,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC,CAAC;aAC5F,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;gBACvC,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;oBAAM,OAAA,KAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,CAAC;gBAAtD,CAAsD,CAAC,CAAC;aAC/F;SACF;QAED,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;SAClE;QAED,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;YAAM,OAAA,+BAA+B,CAAC,KAAI,EAAE,WAAW,EAAE,IAAI,CAAC;QAAxD,CAAwD,CAAC,CAAC;QAEvF,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAClB,aAAa,CAAA,KAAA,CAAA,KAAA,GAAA,cAAA;YAAC,OAAO;SAAA,EAAA,OAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,+EAA+E;IAC/E,0EAA0E;IAC1E,6EAA6E;IAC7E,yBAAA,SAAA,CAAA,KAAK,GAAL;QACE,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AAzLD,CAAuC,WAAW,GAyLjD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,wBAAwB,CAAC,SAAS,EAClC,iBAAiB,CAAC,CAAC;AAErB,SAAS,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,cAAc;IACrD,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IACnF,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;IACnC,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,4BAA4B,CAAC;QAC1F,IAAI,EAAE,UAAU;KACjB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAA,MAAM;QAClC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,SAAA,KAAK;QACN,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;GAaG,CACH,SAAS,+BAA+B,CAAC,cAAc,EAAE,WAAW,EAAE,MAAM;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,QAAQ;IACR,IAAI,cAAc,CAAC,4BAA4B,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QAC/E,OAAO;KACR;IAED,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IAED,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,cAAc,CAAC,4BAA4B,GAAG,MAAM,CAAC,CAAC,EAAC;QACrD,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,QAAQ;MAClB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AACzB,CAAC;AAED;;;;;;;;;;;GAWG,CACH,SAAS,wCAAwC,CAAC,WAAW,EAAE,QAAQ;IACrE,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAAE;QAC5D,OAAO,IAAI,4BAA4B,CAAC;YACtC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,GAAG,QAAQ,CAAC;SACvE,CAAC,CAAC;KACJ;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,wBAAwB,CAAC", "debugId": null}}, {"offset": {"line": 3283, "column": 0}, "map": {"version": 3, "file": "safari.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/safari.js"], "names": [], "mappings": "AAAA,oDAAA,EAAsD,CACtD,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACjC,IAAA,KAAiF,OAAO,CAAC,aAAa,CAAC,wFAArG,YAAY,GAAA,GAAA,YAAA,EAAE,0BAA0B,GAAA,GAAA,0BAAA,EAAE,gCAAgC,GAAA,GAAA,gCAA2B,CAAC;AACxG,IAAA,KAAuD,OAAO,CAAC,SAAS,CAAC,8FAAvE,eAAe,GAAA,GAAA,eAAA,EAAE,cAAc,GAAA,GAAA,cAAA,EAAE,eAAe,GAAA,GAAA,eAAuB,CAAC;AAEhF,IAAM,aAAa,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC;AAEnD,IAAM,qBAAqB,GAAG,aAAa,GACvC,gCAAgC,GAChC,0BAA0B,CAAC;AAE/B,IAAA,0BAAA,SAAA,MAAA;IAAsC,UAAA,yBAAA,QAAW;IAC/C,SAAA,wBAAY,aAAa;QAAzB,IAAA,QACE,OAAA,IAAA,CAAA,IAAA,CAAO,IAAA,IAAA,CAqFR;QAnFC,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,CAAC;QACpC,cAAc,CAAC,KAAI,EAAE,0BAA0B,CAAC,CAAC;QACjD,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAC7C,cAAc,CAAC,KAAI,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,KAAK,EAAE;aACnB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAA,KAAK;YAClD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/B,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,0BAA0B,EAAE;YAAC,IAAA,OAAA,EAAA,CAAO;gBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;gBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;YAClE,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,OAAO;aACR;YACD,KAAI,CAAC,aAAa,CAAA,KAAA,CAAlB,KAAI,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI,IAAE;QAC9B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,IAAA,OAAA,EAAA,CAAO;gBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;gBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;YAC9D,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,OAAO;aACR;YACD,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC9C,KAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,KAAI,CAAC,kBAAkB,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;gBACzD,KAAI,CAAC,aAAa,CAAA,KAAA,CAAlB,KAAI,EAAA,cAAA,EAAA,EAAA,OAAkB,IAAI,IAAE;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,sGAAsG;QACtG,iFAAiF;QACjF,yFAAyF;QACzF,2EAA2E;QAC3E,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAA,KAAK;YAC5C,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IAErE,CAAC;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,kBAAgB,EAAA;aAApB;YACE,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAC1E,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,oBAAkB,EAAA;aAAtB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QAC7E,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,mBAAiB,EAAA;aAArB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC9E,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,mBAAiB,EAAA;aAArB;YACE,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC5E,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,wBAAA,SAAA,EAAA,gBAAc,EAAA;aAAlB;YACE,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,OAAO,QAAQ,CAAC;aACjB,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAClC,OAAO,kBAAkB,CAAC;aAC3B,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACnC,OAAO,mBAAmB,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAC7C,CAAC;;;OAAA;IAED,wBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,SAAS;QAAzB,IAAA,QAAA,IAAA,CAKC;QAJC,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;gBAAM,OAAA,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC;YAA/C,CAA+C,CAAC,CAAC;SAC1G;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAED,wBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,OAAO;QAAnB,IAAA,QAAA,IAAA,CAqCC;QApCC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,OAAO,CAAC,CAAC;QAErC,4EAA4E;QAC5E,mCAAmC;QACnC,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;YAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;YACnC,IAAI;gBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAClC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;oBAAE,SAAS,EAAE,UAAU;gBAAA,CAAE,CAAC,GACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aAClC,CAAC,OAAO,CAAC,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;YAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;YACnC,IAAI;gBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAClC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;oBAAE,SAAS,EAAE,UAAU;gBAAA,CAAE,CAAC,GACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aAClC,CAAC,OAAO,CAAC,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAA,KAAK;YACzD,+EAA+E;YAC/E,wEAAwE;YACxE,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,IAAI,qBAAqB,CAAC;gBAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aAC3D,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,OAAO;QAApB,IAAA,QAAA,IAAA,CAgCC;QA/BC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC;gBAC9E,KAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;gBAClC,OAAO,KAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,SAAA,MAAM;gBACZ,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAEhC,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;oBAC/C,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC5D,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACd,CAAC,EAAE,SAAA,KAAK;gBACN,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAA,MAAM;YAC3D,2FAA2F;YAC3F,6DAA6D;YAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;gBAC/C,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;aAC5D,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAA,SAAA,CAAA,iBAAiB,GAAjB,SAAkB,KAAK,EAAE,eAAe;QACtC,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACnF,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,wBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,MAAM;QAChB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,wBAAA,SAAA,CAAA,mBAAmB,GAAnB,SAAoB,WAAW;QAC7B,uFAAuF;QACvF,+DAA+D;QAC/D,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7D,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;SACvC;QACD,OAAO,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACjD,CAAC;IAED,wBAAA,SAAA,CAAA,oBAAoB,GAApB,SAAqB,WAAW;QAC9B,wFAAwF;QACxF,wEAAwE;QACxE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,wBAAA,SAAA,CAAA,KAAK,GAAL;QAAA,IAAA,QAAA,IAAA,CAUC;QATC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,UAAU,CAAC;YACT,KAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;YAC1D,KAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,uBAAC;AAAD,CAAC,AA3OD,CAAsC,WAAW,GA2OhD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,uBAAuB,CAAC,SAAS,EACjC,iBAAiB,CAAC,CAAC;AAErB,SAAS,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,WAAW;IACxD,SAAS,oBAAoB,CAAC,KAAK;QACjC,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C,MAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC5C;IACH,CAAC;IAED,SAAS,sBAAsB;QAC7B,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC1C,MAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC3C;IACH,CAAC;IAED,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC;IACzG,IAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC;IAC1G,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC;IAC3E,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IAEnF,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjE,OAAO,eAAe,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KACrD,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QACvC,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;YACrG,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAA,CAAc,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAA,8BACrD,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,oBAAoB,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7E,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SAC7C;QACD,IAAM,sBAAsB,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAElC,iEAAiE;QACjE,IAAI,cAAc,CAAC,cAAc,KAAK,sBAAsB,EAAE;YAC5D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;gBAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAA/D,CAA+D,CAAC,CAAC;SACtG;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;QAC1C,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,EAAE;YACvD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+BAAA,CAC5B,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAA,qBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACpF;QACD,sBAAsB,EAAE,CAAC;QAEzB,8EAA8E;QAC9E,+EAA+E;QAC/E,+EAA+E;QAC/E,oDAAoD;QACpD,cAAc,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACjF,cAAc,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9E,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAA/D,CAA+D,CAAC,CAAC;KACtG;IAED,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,eAAe,CAAC,cAAc,EAAE,MAAM;IAC7C,IAAM,iBAAiB,GAAG,cAAc,CAAC,kBAAkB,CAAC;IAC5D,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;QAChF,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACzC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,IAAI,CAAC;QAAM,OAAA,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE;IAA3C,CAA2C,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;GAMG,CACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,IAAI;IACvD,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,SAAC,EAAiB;YAAf,KAAA,GAAA,QAAa,EAAb,QAAQ,GAAA,OAAA,KAAA,IAAG,CAAA,CAAE,GAAA,EAAA;QACrD,IAAA,KAAe,QAAQ,CAAA,KAAb,EAAV,KAAK,GAAA,OAAA,KAAA,IAAG,CAAA,CAAE,GAAA,EAAA,CAAc;QAChC,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG,CACH,SAAS,eAAe,CAAC,WAAW;IAClC,OAAO,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE;QAC1C,iBAAiB,EAAE;YACjB,KAAK,EAAE,WAAW,CAAC,iBAAiB,KAAK,KAAK,GAC1C,IAAI,GACJ,WAAW,CAAC,iBAAiB;SAClC;QACD,cAAc,EAAE;YACd,KAAK,EAAE,WAAW,CAAC,cAAc,KAAK,KAAK,GACvC,IAAI,GACJ,WAAW,CAAC,cAAc;SAC/B;KACF,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,IAAI,OAAO,iBAAiB,KAAK,UAAU,EAAE;IACnC,IAAA,YAAY,GAAK,OAAO,CAAC,SAAS,CAAC,6FAAA,YAAvB,CAAwB;IAC5C,OAAQ,YAAY,EAAE,EAAE;QACtB,KAAK,QAAQ;YACX,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,SAAS;YACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM;QACR,KAAK,QAAQ;YACX,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM;QACR;YACE,MAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC;YACnC,MAAM;KACT;CACF,MAAM;IACL,MAAM,CAAC,OAAO,GAAG,SAAS,iBAAiB;QACzC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACxD,CAAC,CAAC;CACH", "debugId": null}}, {"offset": {"line": 3700, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcsessiondescription/index.js"], "names": [], "mappings": "AAAA,iCAAA,EAAmC,CACnC,YAAY,CAAC;AAEb,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;IACvC,IAAA,YAAY,GAAK,OAAO,CAAC,SAAS,CAAC,6FAAA,YAAvB,CAAwB;IAC5C,OAAQ,YAAY,EAAE,EAAE;QACtB,KAAK,QAAQ;YACX,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,SAAS;YACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;YACtC,MAAM;QACR;YACE,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC;YACvC,MAAM;KACT;CACF,MAAM;IACL,MAAM,CAAC,OAAO,GAAG,SAAS,qBAAqB;QAC7C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC,CAAC;CACH", "debugId": null}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/webrtc/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,IAAM,MAAM,GAAG,CAAA,CAAE,CAAC;AAElB,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE;IAC9B,QAAQ,EAAE;QACR,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;KAC7B;IACD,YAAY,EAAE;QACZ,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;KACjC;IACD,WAAW,EAAE;QACX,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,eAAe,CAAC;KAChC;IACD,gBAAgB,EAAE;QAChB,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,oBAAoB,CAAC;KACrC;IACD,eAAe,EAAE;QACf,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,mBAAmB,CAAC;KACpC;IACD,iBAAiB,EAAE;QACjB,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,qBAAqB,CAAC;KACtC;IACD,qBAAqB,EAAE;QACrB,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,OAAO,CAAC,yBAAyB,CAAC;KAC1C;CACF,CAAC,CAAC;AAEH,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC", "debugId": null}}]}