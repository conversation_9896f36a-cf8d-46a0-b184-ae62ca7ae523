(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/twilio-video/es5/vendor/loglevel.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Copyright (c) 2013 Tim Perry
 * Licensed under the MIT license.
 *
 * Copied from https://github.com/pimterry/loglevel (1.7.0)
 * and modified to remove browser and AMD module support, while keeping CommonJS.
 * It was causing a conflict when this is bundled using CommonJS, and then loaded via RequireJS.
 * The proper way to fix this module is to have a build that outputs CommonJS and AMD separately
 * which needs to be submitted to the original module's repo.
 */ /* istanbul ignore file */ /* eslint-disable */ // Slightly dubious tricks to cut down minimized file size
var noop = function() {};
var undefinedType = "undefined";
var isIE = typeof window !== undefinedType && typeof window.navigator !== undefinedType && /Trident\/|MSIE /.test(window.navigator.userAgent);
var logMethods = [
    "trace",
    "debug",
    "info",
    "warn",
    "error"
];
// Cross-browser bind equivalent that works at least back to IE6
function bindMethod(obj, methodName) {
    var method = obj[methodName];
    if (typeof method.bind === 'function') {
        return method.bind(obj);
    } else {
        try {
            return Function.prototype.bind.call(method, obj);
        } catch (e) {
            // Missing bind shim or IE8 + Modernizr, fallback to wrapping
            return function() {
                return Function.prototype.apply.apply(method, [
                    obj,
                    arguments
                ]);
            };
        }
    }
}
// Trace() doesn't print the message in IE, so for that case we need to wrap it
function traceForIE() {
    if (console.log) {
        if (console.log.apply) {
            console.log.apply(console, arguments);
        } else {
            // In old IE, native console methods themselves don't have apply().
            Function.prototype.apply.apply(console.log, [
                console,
                arguments
            ]);
        }
    }
    if (console.trace) console.trace();
}
// Build the best logging method possible for this env
// Wherever possible we want to bind, not wrap, to preserve stack traces
function realMethod(methodName) {
    if (methodName === 'debug') {
        methodName = 'log';
    }
    if (typeof console === undefinedType) {
        return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives
    } else if (methodName === 'trace' && isIE) {
        return traceForIE;
    } else if (console[methodName] !== undefined) {
        return bindMethod(console, methodName);
    } else if (console.log !== undefined) {
        return bindMethod(console, 'log');
    } else {
        return noop;
    }
}
// These private functions always need `this` to be set properly
function replaceLoggingMethods(level, loggerName) {
    /*jshint validthis:true */ for(var i = 0; i < logMethods.length; i++){
        var methodName = logMethods[i];
        this[methodName] = i < level ? noop : this.methodFactory(methodName, level, loggerName);
    }
    // Define log.log as an alias for log.debug
    this.log = this.debug;
}
// In old IE versions, the console isn't present until you first open it.
// We build realMethod() replacements here that regenerate logging methods
function enableLoggingWhenConsoleArrives(methodName, level, loggerName) {
    return function() {
        if (typeof console !== undefinedType) {
            replaceLoggingMethods.call(this, level, loggerName);
            this[methodName].apply(this, arguments);
        }
    };
}
// By default, we use closely bound real methods wherever possible, and
// otherwise we wait for a console to appear, and then try again.
function defaultMethodFactory(methodName, level, loggerName) {
    /*jshint validthis:true */ return realMethod(methodName) || enableLoggingWhenConsoleArrives.apply(this, arguments);
}
function Logger(name, defaultLevel, factory) {
    var self = this;
    var currentLevel;
    var storageKey = "loglevel";
    if (typeof name === "string") {
        storageKey += ":" + name;
    } else if (typeof name === "symbol") {
        storageKey = undefined;
    }
    function persistLevelIfPossible(levelNum) {
        var levelName = (logMethods[levelNum] || 'silent').toUpperCase();
        if (typeof window === undefinedType || !storageKey) return;
        // Use localStorage if available
        try {
            window.localStorage[storageKey] = levelName;
            return;
        } catch (ignore) {}
        // Use session cookie as fallback
        try {
            window.document.cookie = encodeURIComponent(storageKey) + "=" + levelName + ";";
        } catch (ignore) {}
    }
    function getPersistedLevel() {
        var storedLevel;
        if (typeof window === undefinedType || !storageKey) return;
        try {
            storedLevel = window.localStorage[storageKey];
        } catch (ignore) {}
        // Fallback to cookies if local storage gives us nothing
        if (typeof storedLevel === undefinedType) {
            try {
                var cookie = window.document.cookie;
                var location = cookie.indexOf(encodeURIComponent(storageKey) + "=");
                if (location !== -1) {
                    storedLevel = /^([^;]+)/.exec(cookie.slice(location))[1];
                }
            } catch (ignore) {}
        }
        // If the stored level is not valid, treat it as if nothing was stored.
        if (self.levels[storedLevel] === undefined) {
            storedLevel = undefined;
        }
        return storedLevel;
    }
    /*
     *
     * Public logger API - see https://github.com/pimterry/loglevel for details
     *
     */ self.name = name;
    self.levels = {
        "TRACE": 0,
        "DEBUG": 1,
        "INFO": 2,
        "WARN": 3,
        "ERROR": 4,
        "SILENT": 5
    };
    self.methodFactory = factory || defaultMethodFactory;
    self.getLevel = function() {
        return currentLevel;
    };
    self.setLevel = function(level, persist) {
        if (typeof level === "string" && self.levels[level.toUpperCase()] !== undefined) {
            level = self.levels[level.toUpperCase()];
        }
        if (typeof level === "number" && level >= 0 && level <= self.levels.SILENT) {
            currentLevel = level;
            if (persist !== false) {
                persistLevelIfPossible(level);
            }
            replaceLoggingMethods.call(self, level, name);
            if (typeof console === undefinedType && level < self.levels.SILENT) {
                return "No console available for logging";
            }
        } else {
            throw "log.setLevel() called with invalid level: " + level;
        }
    };
    self.setDefaultLevel = function(level) {
        if (!getPersistedLevel()) {
            self.setLevel(level, false);
        }
    };
    self.enableAll = function(persist) {
        self.setLevel(self.levels.TRACE, persist);
    };
    self.disableAll = function(persist) {
        self.setLevel(self.levels.SILENT, persist);
    };
    // Initialize with the right level
    var initialLevel = getPersistedLevel();
    if (initialLevel == null) {
        initialLevel = defaultLevel == null ? "WARN" : defaultLevel;
    }
    self.setLevel(initialLevel, false);
}
/*
 *
 * Top-level API
 *
 */ var defaultLogger = new Logger();
var _loggersByName = {};
defaultLogger.getLogger = function getLogger(name) {
    if (typeof name !== "symbol" && typeof name !== "string" || name === "") {
        throw new TypeError("You must supply a name when creating a logger.");
    }
    var logger = _loggersByName[name];
    if (!logger) {
        logger = _loggersByName[name] = new Logger(name, defaultLogger.getLevel(), defaultLogger.methodFactory);
    }
    return logger;
};
// Grab the current global log variable in case of overwrite
var _log = typeof window !== undefinedType ? window.log : undefined;
defaultLogger.noConflict = function() {
    if (typeof window !== undefinedType && window.log === defaultLogger) {
        window.log = _log;
    }
    return defaultLogger;
};
defaultLogger.getLoggers = function getLoggers() {
    return _loggersByName;
};
// ES6 default export, for compatibility
defaultLogger['default'] = defaultLogger;
module.exports = defaultLogger; //# sourceMappingURL=loglevel.js.map
}}),
"[project]/node_modules/twilio-video/es5/vendor/inherits.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Copyright (c) 2011-2022 Isaac Z. Schlueter
 * Licensed under the ISC License.
 *
 * Copied from https://github.com/isaacs/inherits (2.0.4)
*/ module.exports = function inherits(ctor, superCtor) {
    if (ctor && superCtor) {
        ctor.super_ = superCtor;
        if (typeof Object.create === 'function') {
            // implementation from standard node.js 'util' module
            ctor.prototype = Object.create(superCtor.prototype, {
                constructor: {
                    value: ctor,
                    enumerable: false,
                    writable: true,
                    configurable: true
                }
            });
        } else {
            // old school shim for old browsers
            var TempCtor = function() {
                function TempCtor() {}
                return TempCtor;
            }();
            TempCtor.prototype = superCtor.prototype;
            ctor.prototype = new TempCtor();
            ctor.prototype.constructor = ctor;
        }
    }
}; //# sourceMappingURL=inherits.js.map
}}),
"[project]/node_modules/twilio-video/es5/noisecancellationadapter.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createNoiseCancellationAudioProcessor = void 0;
var dynamicImport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/dynamicimport.js [app-client] (ecmascript)");
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var PLUGIN_CONFIG = {
    krisp: {
        supportedVersion: '1.0.0',
        pluginFile: 'krispsdk.mjs'
    },
    rnnoise: {
        supportedVersion: '0.6.0',
        pluginFile: 'rnnoise_sdk.mjs'
    }
};
var ensureVersionSupported = function(_a) {
    var supportedVersion = _a.supportedVersion, plugin = _a.plugin, log = _a.log;
    if (!plugin.getVersion || !plugin.isSupported) {
        throw new Error('Plugin does not export getVersion/isSupported api. Are you using old version of the plugin ?');
    }
    var pluginVersion = plugin.getVersion();
    log.debug("Plugin Version = " + pluginVersion);
    var supportedVersions = supportedVersion.split('.').map(function(version) {
        return Number(version);
    });
    var pluginVersions = pluginVersion.split('.').map(function(version) {
        return Number(version);
    });
    if (supportedVersions.length !== 3 || pluginVersions.length !== 3) {
        throw new Error("Unsupported Plugin version format: " + supportedVersion + ", " + pluginVersion);
    }
    if (supportedVersions[0] !== pluginVersions[0]) {
        throw new Error("Major version mismatch: [Plugin version " + pluginVersion + "],  [Supported Version " + supportedVersion + "]");
    }
    if (pluginVersions[1] < supportedVersions[1]) {
        throw new Error("Minor version mismatch: [Plugin version " + pluginVersion + "] < [Supported Version " + supportedVersion + "]");
    }
    var tempContext = new AudioContext();
    var isSupported = plugin.isSupported(tempContext);
    tempContext.close();
    if (!isSupported) {
        throw new Error('Noise Cancellation plugin is not supported on your browser');
    }
};
var audioProcessors = new Map();
function createNoiseCancellationAudioProcessor(noiseCancellationOptions, log) {
    return __awaiter(this, void 0, void 0, function() {
        var audioProcessor, pluginConfig, supportedVersion, pluginFile, rootDir, sdkFilePath, dynamicModule, plugin_1, er_1;
        return __generator(this, function(_a) {
            switch(_a.label){
                case 0:
                    audioProcessor = audioProcessors.get(noiseCancellationOptions.vendor);
                    if (!!audioProcessor) return [
                        3 /*break*/ ,
                        6
                    ];
                    pluginConfig = PLUGIN_CONFIG[noiseCancellationOptions.vendor];
                    if (!pluginConfig) {
                        throw new Error("Unsupported NoiseCancellationOptions.vendor: " + noiseCancellationOptions.vendor);
                    }
                    supportedVersion = pluginConfig.supportedVersion, pluginFile = pluginConfig.pluginFile;
                    rootDir = noiseCancellationOptions.sdkAssetsPath;
                    sdkFilePath = rootDir + "/" + pluginFile;
                    _a.label = 1;
                case 1:
                    _a.trys.push([
                        1,
                        5,
                        ,
                        6
                    ]);
                    log.debug('loading noise cancellation sdk: ', sdkFilePath);
                    return [
                        4 /*yield*/ ,
                        dynamicImport(sdkFilePath)
                    ];
                case 2:
                    dynamicModule = _a.sent();
                    log.debug('Loaded noise cancellation sdk:', dynamicModule);
                    plugin_1 = dynamicModule.default;
                    ensureVersionSupported({
                        supportedVersion: supportedVersion,
                        plugin: plugin_1,
                        log: log
                    });
                    if (!!plugin_1.isInitialized()) return [
                        3 /*break*/ ,
                        4
                    ];
                    log.debug('initializing noise cancellation sdk: ', rootDir);
                    return [
                        4 /*yield*/ ,
                        plugin_1.init({
                            rootDir: rootDir
                        })
                    ];
                case 3:
                    _a.sent();
                    log.debug('noise cancellation sdk initialized!');
                    _a.label = 4;
                case 4:
                    audioProcessor = {
                        vendor: noiseCancellationOptions.vendor,
                        isInitialized: function() {
                            return plugin_1.isInitialized();
                        },
                        isConnected: function() {
                            return plugin_1.isConnected();
                        },
                        isEnabled: function() {
                            return plugin_1.isEnabled();
                        },
                        disconnect: function() {
                            return plugin_1.disconnect();
                        },
                        enable: function() {
                            return plugin_1.enable();
                        },
                        disable: function() {
                            return plugin_1.disable();
                        },
                        destroy: function() {
                            return plugin_1.destroy();
                        },
                        setLogging: function(enable) {
                            return plugin_1.setLogging(enable);
                        },
                        connect: function(sourceTrack) {
                            log.debug('connect: ', sourceTrack.id);
                            if (plugin_1.isConnected()) {
                                plugin_1.disconnect();
                            }
                            var mediaStream = plugin_1.connect(new MediaStream([
                                sourceTrack
                            ]));
                            if (!mediaStream) {
                                throw new Error('Error connecting with noise cancellation sdk');
                            }
                            var cleanTrack = mediaStream.getAudioTracks()[0];
                            if (!cleanTrack) {
                                throw new Error('Error getting clean track from noise cancellation sdk');
                            }
                            plugin_1.enable();
                            return cleanTrack;
                        }
                    };
                    audioProcessors.set(noiseCancellationOptions.vendor, audioProcessor);
                    return [
                        3 /*break*/ ,
                        6
                    ];
                case 5:
                    er_1 = _a.sent();
                    log.error("Error loading noise cancellation sdk:" + sdkFilePath, er_1);
                    throw er_1;
                case 6:
                    return [
                        2 /*return*/ ,
                        audioProcessor
                    ];
            }
        });
    });
}
exports.createNoiseCancellationAudioProcessor = createNoiseCancellationAudioProcessor; //# sourceMappingURL=noisecancellationadapter.js.map
}}),
"[project]/node_modules/twilio-video/es5/eventtarget.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var EventTarget = function() {
    function EventTarget() {
        Object.defineProperties(this, {
            _eventEmitter: {
                value: new EventEmitter()
            }
        });
    }
    EventTarget.prototype.dispatchEvent = function(event) {
        return this._eventEmitter.emit(event.type, event);
    };
    EventTarget.prototype.addEventListener = function() {
        var _a;
        return (_a = this._eventEmitter).addListener.apply(_a, __spreadArray([], __read(arguments)));
    };
    EventTarget.prototype.removeEventListener = function() {
        var _a;
        return (_a = this._eventEmitter).removeListener.apply(_a, __spreadArray([], __read(arguments)));
    };
    return EventTarget;
}();
module.exports = EventTarget; //# sourceMappingURL=eventtarget.js.map
}}),
"[project]/node_modules/twilio-video/es5/webaudio/detectsilence.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Return a Promise that resolves after `timeout` milliseconds.
 * @param {?number} [timeout=0]
 * @returns {Promise<void>}
 */ function delay(timeout) {
    timeout = typeof timeout === 'number' ? timeout : 0;
    return new Promise(function(resolve) {
        return setTimeout(resolve, timeout);
    });
}
/**
 * Attempt to detect silence. The Promise returned by this function returns
 * false as soon as audio is detected or true after `timeout` milliseconds.
 * @param {AudioContext} audioContext
 * @param {MediaStream} stream
 * @param {?number} [timeout=250]
 * @returns {Promise<boolean>}
 */ function detectSilence(audioContext, stream, timeout) {
    timeout = typeof timeout === 'number' ? timeout : 250;
    var source = audioContext.createMediaStreamSource(stream);
    var analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048;
    source.connect(analyser);
    var samples = new Uint8Array(analyser.fftSize);
    var timeoutDidFire = false;
    setTimeout(function() {
        timeoutDidFire = true;
    }, timeout);
    /**
     * We can't use async/await yet, so I need to factor this out.
     * @returns {Promise<boolean>}
     */ function doDetectSilence() {
        if (timeoutDidFire) {
            return Promise.resolve(true);
        }
        analyser.getByteTimeDomainData(samples);
        // NOTE(mpatwardhan): An audio MediaStreamTrack can be silent either due to all samples
        // being equal to 128 or all samples being equal to 0.
        return samples.some(function(sample) {
            return sample !== 128 && sample !== 0;
        }) ? Promise.resolve(false) : delay().then(doDetectSilence);
    }
    return doDetectSilence().then(function(isSilent) {
        source.disconnect();
        return isSilent;
    }, function(error) {
        source.disconnect();
        throw error;
    });
}
module.exports = detectSilence; //# sourceMappingURL=detectsilence.js.map
}}),
"[project]/node_modules/twilio-video/es5/webaudio/audiocontext.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals webkitAudioContext, AudioContext */ 'use strict';
var NativeAudioContext = typeof AudioContext !== 'undefined' ? AudioContext : typeof webkitAudioContext !== 'undefined' ? webkitAudioContext : null;
/**
 * @interface AudioContextFactoryOptions
 * @property {AudioContext} [AudioContext] - The AudioContext constructor
 */ /**
 * {@link AudioContextFactory} ensures we construct at most one AudioContext
 * at a time, and that it is eventually closed when we no longer need it.
 * @property {AudioContextFactory} AudioContextFactory - The
 *   {@link AudioContextFactory} constructor
 */ var AudioContextFactory = function() {
    /**
     * @param {AudioContextFactoryOptions} [options]
     */ function AudioContextFactory(options) {
        options = Object.assign({
            AudioContext: NativeAudioContext
        }, options);
        Object.defineProperties(this, {
            _AudioContext: {
                value: options.AudioContext
            },
            _audioContext: {
                value: null,
                writable: true
            },
            _holders: {
                value: new Set()
            },
            AudioContextFactory: {
                enumerable: true,
                value: AudioContextFactory
            }
        });
    }
    /**
     * Each call to {@link AudioContextFactory#getOrCreate} should be paired with a
     * call to {@link AudioContextFactory#release}. Calling this increments an
     * internal reference count.
     * @param {*} holder - The object to hold a reference to the AudioContext
     * @returns {?AudioContext}
     */ AudioContextFactory.prototype.getOrCreate = function(holder) {
        if (!this._holders.has(holder)) {
            this._holders.add(holder);
            if (this._AudioContext && !this._audioContext) {
                try {
                    this._audioContext = new this._AudioContext();
                } catch (error) {
                // Do nothing;
                }
            }
        }
        return this._audioContext;
    };
    /**
     * Decrement the internal reference count. If it reaches zero, close and destroy
     * the AudioContext.
     * @param {*} holder - The object that held a reference to the AudioContext
     * @returns {void}
     */ AudioContextFactory.prototype.release = function(holder) {
        if (this._holders.has(holder)) {
            this._holders.delete(holder);
            if (!this._holders.size && this._audioContext) {
                this._audioContext.close();
                this._audioContext = null;
            }
        }
    };
    return AudioContextFactory;
}();
module.exports = new AudioContextFactory(); //# sourceMappingURL=audiocontext.js.map
}}),
"[project]/node_modules/twilio-video/es5/webaudio/workaround180748.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var detectSilence = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/detectsilence.js [app-client] (ecmascript)");
/**
 * This function attempts to workaround WebKit Bug 180748. It does so by
 *
 *   1. Calling `getUserMedia`, and
 *   2. Checking to see if the resulting MediaStream is silent.
 *   3. If so, repeat Step 1; otherwise, return the MediaStream.
 *
 * The function only repeats up to `n` times, and it only waits `timeout`
 * milliseconds when detecting silence. Assuming `getUserMedia` is
 * instantaneous, in the best case, this function returns a Promise that
 * resolves immediately; in the worst case, this function returns a Promise that
 * resolves in `n` * `timeout` milliseconds.
 *
 * @param {Log} log
 * @param {function(MediaStreamConstraints): Promise<MediaStream>} getUserMedia
 * @param {MediaStreamConstraints} constraints
 * @param {number} [n=3]
 * @param {number} [timeout=250]
 * @returns Promise<MediaStream>
 */ function workaround(log, getUserMedia, constraints, n, timeout) {
    n = typeof n === 'number' ? n : 3;
    var retry = 0;
    // NOTE(mroberts): We have to delay require-ing AudioContextFactory, because
    // it exports a default instance whose constructor calls Object.assign.
    var AudioContextFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/audiocontext.js [app-client] (ecmascript)");
    var holder = {};
    var audioContext = AudioContextFactory.getOrCreate(holder);
    /**
     * We can't use async/await yet, so I need to factor this out.
     * @returns {Promise<MediaStream>}
     */ function doWorkaround() {
        return getUserMedia(constraints).then(function(stream) {
            var isSilentPromise = constraints.audio ? detectSilence(audioContext, stream, timeout).catch(function(err) {
                log.warn('Encountered an error while detecting silence', err);
                return true;
            }) : Promise.resolve(false);
            return isSilentPromise.then(function(isSilent) {
                if (!isSilent) {
                    log.info('Got a non-silent audio MediaStreamTrack; returning it.');
                    return stream;
                } else if (n <= 0) {
                    log.warn('Got a silent audio MediaStreamTrack. Normally we would try \
to get a new one, but we\'ve run out of retries; returning it anyway.');
                    return stream;
                }
                log.warn("Got a silent audio MediaStreamTrack. Stopping all MediaStreamTracks and calling getUserMedia again. This is retry #" + ++retry + ".");
                stream.getTracks().forEach(function(track) {
                    return track.stop();
                });
                n--;
                return doWorkaround();
            });
        });
    }
    return doWorkaround().then(function(stream) {
        AudioContextFactory.release(holder);
        return stream;
    }, function(error) {
        AudioContextFactory.release(holder);
        throw error;
    });
}
module.exports = workaround; //# sourceMappingURL=workaround180748.js.map
}}),
"[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var hidePrivateAndCertainPublicPropertiesInClass = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").hidePrivateAndCertainPublicPropertiesInClass;
module.exports = hidePrivateAndCertainPublicPropertiesInClass(EventEmitter, [
    'domain'
]); //# sourceMappingURL=eventemitter.js.map
}}),
"[project]/node_modules/twilio-video/es5/queueingeventemitter.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
/**
 * A {@link QueueingEventEmitter} can queue events until a listener has been
 * added.
 * @extends EventEmitter
 */ var QueueingEventEmitter = function(_super) {
    __extends(QueueingEventEmitter, _super);
    /**
     * Construct a {@link QueueingEventEmitter}
     */ function QueueingEventEmitter() {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _queuedEvents: {
                value: new Map()
            }
        });
        return _this;
    }
    /**
     * Emit any queued events.
     * @returns {boolean} true if every event had listeners, false otherwise
    */ /**
     * Emit any queued events matching the event name.
     * @param {string} event
     * @returns {boolean} true if every event had listeners, false otherwise
     */ QueueingEventEmitter.prototype.dequeue = function(event) {
        var _this = this;
        var result = true;
        if (!event) {
            this._queuedEvents.forEach(function(_, queuedEvent) {
                result = this.dequeue(queuedEvent) && result;
            }, this);
            return result;
        }
        var queue = this._queuedEvents.get(event) || [];
        this._queuedEvents.delete(event);
        return queue.reduce(function(result, args) {
            return _this.emit.apply(_this, __spreadArray([], __read([
                event
            ].concat(args)))) && result;
        }, result);
    };
    /**
     * If the event has listeners, emit the event; otherwise, queue the event.
     * @param {string} event
     * @param {...*} args
     * @returns {boolean} true if the event had listeners, false if the event was queued
     */ QueueingEventEmitter.prototype.queue = function() {
        var args = [].slice.call(arguments);
        if (this.emit.apply(this, __spreadArray([], __read(args)))) {
            return true;
        }
        var event = args[0];
        if (!this._queuedEvents.has(event)) {
            this._queuedEvents.set(event, []);
        }
        this._queuedEvents.get(event).push(args.slice(1));
        return false;
    };
    return QueueingEventEmitter;
}(EventEmitter);
module.exports = QueueingEventEmitter; //# sourceMappingURL=queueingeventemitter.js.map
}}),
"[project]/node_modules/twilio-video/es5/transceiver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var QueueingEventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/queueingeventemitter.js [app-client] (ecmascript)");
/**
 * A {@link TrackTransceiver} represents either one or more local RTCRtpSenders
 * or RTCDataChannels, or a single RTCRtpReceiver or remote RTCDataChannel.
 * @extends QueueingEventEmitter
 * @property {Track.ID} id
 * @property {Track.kind} kind
 */ var TrackTransceiver = function(_super) {
    __extends(TrackTransceiver, _super);
    /**
     * Construct a {@link TrackTransceiver}.
     * @param {Track.ID} id
     * @param {Track.kind} kind
     */ function TrackTransceiver(id, kind) {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            id: {
                enumerable: true,
                value: id
            },
            kind: {
                enumerable: true,
                value: kind
            }
        });
        return _this;
    }
    /**
     * Stop the {@link TrackTransceiver}.
     * #emits TrackTransceiver#stopped
     * @returns {void}
     */ TrackTransceiver.prototype.stop = function() {
        this.emit('stopped');
    };
    return TrackTransceiver;
}(QueueingEventEmitter);
/**
 * The {@link TrackTransceiver} was stopped.
 * @event TrackTransceiver#stopped
 */ module.exports = TrackTransceiver; //# sourceMappingURL=transceiver.js.map
}}),
"[project]/node_modules/twilio-video/es5/data/transceiver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/transceiver.js [app-client] (ecmascript)");
/**
 * A {@link DataTrackTransceiver} represents either one or more local
 * RTCDataChannels or a single remote RTCDataChannel. It can be used to send or
 * receive data.
 * @extends TrackTransceiver
 * @property {string} id
 * @property {string} kind - "data"
 * @property {?number} maxPacketLifeTime
 * @property {?number} maxRetransmits
 * @property {boolean} ordered
 */ var DataTrackTransceiver = function(_super) {
    __extends(DataTrackTransceiver, _super);
    /**
     * Construct a {@link DataTrackTransceiver}.
     * @param {string} id
     * @param {?number} maxPacketLifeTime
     * @param {?number} maxRetransmits
     * @param {boolean} ordered
     */ function DataTrackTransceiver(id, maxPacketLifeTime, maxRetransmits, ordered) {
        var _this = _super.call(this, id, 'data') || this;
        Object.defineProperties(_this, {
            maxPacketLifeTime: {
                enumerable: true,
                value: maxPacketLifeTime
            },
            maxRetransmits: {
                enumerable: true,
                value: maxRetransmits
            },
            ordered: {
                enumerable: true,
                value: ordered
            }
        });
        return _this;
    }
    return DataTrackTransceiver;
}(TrackTransceiver);
module.exports = DataTrackTransceiver; //# sourceMappingURL=transceiver.js.map
}}),
"[project]/node_modules/twilio-video/es5/data/sender.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var DataTrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/data/transceiver.js [app-client] (ecmascript)");
var makeUUID = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").makeUUID;
/**
 * A {@link DataTrackSender} represents a {@link DataTrackTransceiver} over
 * which data can be sent. Internally, it uses a collection of RTCDataChannels
 * to send data.
 * @extends DataTrackTransceiver
 */ var DataTrackSender = function(_super) {
    __extends(DataTrackSender, _super);
    /**
     * Construct a {@link DataTrackSender}.
     * @param {?number} maxPacketLifeTime
     * @param {?number} maxRetransmits
     * @param {boolean} ordered
     */ function DataTrackSender(maxPacketLifeTime, maxRetransmtis, ordered) {
        var _this = _super.call(this, makeUUID(), maxPacketLifeTime, maxRetransmtis, ordered) || this;
        Object.defineProperties(_this, {
            _clones: {
                value: new Set()
            },
            _dataChannels: {
                value: new Set()
            }
        });
        return _this;
    }
    /**
     * Add a cloned {@link DataTrackSender}.
     * @private
     * @returns {void}
     */ DataTrackSender.prototype._addClone = function(clone) {
        this._clones.add(clone);
    };
    /**
     * Remove a cloned {@link DataTrackSender}.
     * @returns {void}
     */ DataTrackSender.prototype.removeClone = function(clone) {
        this._clones.delete(clone);
    };
    /**
     * Add an RTCDataChannel to the {@link DataTrackSender}.
     * @param {RTCDataChannel} dataChannel
     * @returns {this}
     */ DataTrackSender.prototype.addDataChannel = function(dataChannel) {
        this._dataChannels.add(dataChannel);
        return this;
    };
    /**
     * Return a new {@link DataTrackSender}. Any message sent over this
     * {@link DataTrackSender} will also be sent over the clone. Whenever this
     * {@link DataTrackSender} is stopped, so to will the clone.
     * @returns {DataTrackSender}
     */ DataTrackSender.prototype.clone = function() {
        var _this = this;
        var clone = new DataTrackSender(this.maxPacketLifeTime, this.maxRetransmits, this.ordered);
        this._addClone(clone);
        clone.once('stopped', function() {
            return _this.removeClone(clone);
        });
        return clone;
    };
    /**
     * Remove an RTCDataChannel from the {@link DataTrackSender}.
     * @param {RTCDataChannel} dataChannel
     * @returns {this}
     */ DataTrackSender.prototype.removeDataChannel = function(dataChannel) {
        this._dataChannels.delete(dataChannel);
        return this;
    };
    /**
     * Send data over the {@link DataTrackSender}. Internally, this calls
     * <code>send</code> over each of the underlying RTCDataChannels.
     * @param {string|Blob|ArrayBuffer|ArrayBufferView} data
     * @returns {this}
     */ DataTrackSender.prototype.send = function(data) {
        this._dataChannels.forEach(function(dataChannel) {
            try {
                dataChannel.send(data);
            } catch (error) {
            // Do nothing.
            }
        });
        this._clones.forEach(function(clone) {
            try {
                clone.send(data);
            } catch (error) {
            // Do nothing.
            }
        });
        return this;
    };
    DataTrackSender.prototype.stop = function() {
        this._dataChannels.forEach(function(dataChannel) {
            return dataChannel.close();
        });
        this._clones.forEach(function(clone) {
            return clone.stop();
        });
        _super.prototype.stop.call(this);
    };
    return DataTrackSender;
}(DataTrackTransceiver);
module.exports = DataTrackSender; //# sourceMappingURL=sender.js.map
}}),
"[project]/node_modules/twilio-video/es5/data/transport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
/**
 * @classdesc A {@link DataTransport} implements {@link MediaSignalingTransport}
 *   in terms of an RTCDataChannel.
 * @extends EventEmitter
 * @implements MediaSignalingTransport
 * @emits DataTransport#message
 */ var DataTransport = function(_super) {
    __extends(DataTransport, _super);
    /**
     * Construct a {@link DataTransport}.
     * @param {RTCDataChannel} dataChannel
     */ function DataTransport(dataChannel) {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _dataChannel: {
                value: dataChannel
            },
            _messageQueue: {
                value: []
            }
        });
        dataChannel.addEventListener('open', function() {
            _this._messageQueue.splice(0).forEach(function(message) {
                return _this._publish(message);
            });
        });
        dataChannel.addEventListener('message', function(_a) {
            var data = _a.data;
            try {
                var message = JSON.parse(data);
                _this.emit('message', message);
            } catch (error) {
            // Do nothing.
            }
        });
        _this.publish({
            type: 'ready'
        });
        return _this;
    }
    /**
     * @param message
     * @private
     */ DataTransport.prototype._publish = function(message) {
        var data = JSON.stringify(message);
        try {
            this._dataChannel.send(data);
        } catch (error) {
        // Do nothing.
        }
    };
    /**
     * Publish a message. Returns true if calling the method resulted in
     * publishing (or eventually publishing) the update.
     * @param {object} message
     * @returns {boolean}
     */ DataTransport.prototype.publish = function(message) {
        var dataChannel = this._dataChannel;
        if (dataChannel.readyState === 'closing' || dataChannel.readyState === 'closed') {
            return false;
        }
        if (dataChannel.readyState === 'connecting') {
            this._messageQueue.push(message);
            return true;
        }
        this._publish(message);
        return true;
    };
    return DataTransport;
}(EventEmitter);
/**
 * The {@link DataTransport} received a message.
 * @event DataTransport#message
 * @param {object} message
 */ module.exports = DataTransport; //# sourceMappingURL=transport.js.map
}}),
"[project]/node_modules/twilio-video/es5/data/receiver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var DataTrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/data/transceiver.js [app-client] (ecmascript)");
var DataTransport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/data/transport.js [app-client] (ecmascript)");
/**
 * A {@link DataTrackReceiver} represents a {@link DataTrackTransceiver} over
 * which data can be received. Internally, it users a single RTCDataChannel to
 * receive data.
 * @extends DataTrackTransceiver
 * @emits DataTrackReceiver#message
 * @emits DataTrackReceiver#close
 */ var DataTrackReceiver = function(_super) {
    __extends(DataTrackReceiver, _super);
    /**
     * Construct an {@link DataTrackReceiver}.
     * @param {RTCDataChannel} dataChannel
     */ function DataTrackReceiver(dataChannel) {
        var _this = _super.call(this, dataChannel.label, dataChannel.maxPacketLifeTime, dataChannel.maxRetransmits, dataChannel.ordered) || this;
        Object.defineProperties(_this, {
            _dataChannel: {
                value: dataChannel
            }
        });
        // NOTE(mmalavalli): In Firefox, the default value for "binaryType" is "blob".
        // So, we set it to "arraybuffer" to ensure that it is consistent with Chrome
        // and Safari.
        dataChannel.binaryType = 'arraybuffer';
        dataChannel.addEventListener('message', function(event) {
            _this.emit('message', event.data);
        });
        dataChannel.addEventListener('close', function() {
            _this.emit('close');
        });
        return _this;
    }
    DataTrackReceiver.prototype.stop = function() {
        this._dataChannel.close();
        _super.prototype.stop.call(this);
    };
    /**
     * Create a {@link DataTransport} from the {@link DataTrackReceiver}.
     * @returns {DataTransport}
     */ DataTrackReceiver.prototype.toDataTransport = function() {
        return new DataTransport(this._dataChannel);
    };
    return DataTrackReceiver;
}(DataTrackTransceiver);
/**
 * @event DataTrackReceiver#message
 * @param {string|ArrayBuffer} data
 */ /**
 * @event DataTrackReceiver#close
 */ module.exports = DataTrackReceiver; //# sourceMappingURL=receiver.js.map
}}),
"[project]/node_modules/twilio-video/es5/createlocaltracks.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ 'use strict';
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createLocalTracks = void 0;
var noisecancellationimpl_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/noisecancellationimpl.js [app-client] (ecmascript)");
var buildLogLevels = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").buildLogLevels;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)"), getUserMedia = _a.getUserMedia, MediaStreamTrack = _a.MediaStreamTrack;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)"), LocalAudioTrack = _b.LocalAudioTrack, LocalDataTrack = _b.LocalDataTrack, LocalVideoTrack = _b.LocalVideoTrack;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var _c = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), DEFAULT_LOG_LEVEL = _c.DEFAULT_LOG_LEVEL, DEFAULT_LOGGER_NAME = _c.DEFAULT_LOGGER_NAME, INVALID_VALUE = _c.typeErrors.INVALID_VALUE;
var workaround180748 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/workaround180748.js [app-client] (ecmascript)");
// This is used to make out which createLocalTracks() call a particular Log
// statement belongs to. Each call to createLocalTracks() increments this
// counter.
var createLocalTrackCalls = 0;
/**
 * Request {@link LocalTrack}s. By default, it requests a
 * {@link LocalAudioTrack} and a {@link LocalVideoTrack}.
 * Note that on mobile browsers, the camera can be reserved by only one {@link LocalVideoTrack}
 * at any given time. If you attempt to create a second {@link LocalVideoTrack}, video frames
 * will no longer be supplied to the first {@link LocalVideoTrack}.
 * @alias module:twilio-video.createLocalTracks
 * @param {CreateLocalTracksOptions} [options]
 * @returns {Promise<Array<LocalTrack>>}
 * @example
 * var Video = require('twilio-video');
 * // Request audio and video tracks
 * Video.createLocalTracks().then(function(localTracks) {
 *   var localMediaContainer = document.getElementById('local-media-container-id');
 *   localTracks.forEach(function(track) {
 *     localMediaContainer.appendChild(track.attach());
 *   });
 * });
 * @example
 * var Video = require('twilio-video');
 * // Request just the default audio track
 * Video.createLocalTracks({ audio: true }).then(function(localTracks) {
 *   return Video.connect('my-token', {
 *     name: 'my-cool-room',
 *     tracks: localTracks
 *   });
 * });
 * @example
 * var Video = require('twilio-video');
 * // Request the audio and video tracks with custom names
 * Video.createLocalTracks({
 *   audio: { name: 'microphone' },
 *   video: { name: 'camera' }
 * }).then(function(localTracks) {
 *   localTracks.forEach(function(localTrack) {
 *     console.log(localTrack.name);
 *   });
 * });
 *
 * @example
 * var Video = require('twilio-video');
 * var localTracks;
 *
 * // Pre-acquire tracks to display camera preview.
 * Video.createLocalTracks().then(function(tracks) {
 *  localTracks = tracks;
 *  var localVideoTrack = localTracks.find(track => track.kind === 'video');
 *  divContainer.appendChild(localVideoTrack.attach());
 * })
 *
 * // Later, join the Room with the pre-acquired LocalTracks.
 * Video.connect('token', {
 *   name: 'my-cool-room',
 *   tracks: localTracks
 * });
 *
 */ function createLocalTracks(options) {
    return __awaiter(this, void 0, void 0, function() {
        var isAudioVideoAbsent, fullOptions, logComponentName, logLevels, log, localTrackOptions, extraLocalTrackOptions, noiseCancellationOptions, mediaStreamConstraints, workaroundWebKitBug180748, mediaStream, mediaStreamTracks, error_1;
        var _this = this;
        return __generator(this, function(_a) {
            switch(_a.label){
                case 0:
                    isAudioVideoAbsent = !(options && ('audio' in options || 'video' in options));
                    fullOptions = __assign({
                        audio: isAudioVideoAbsent,
                        getUserMedia: getUserMedia,
                        loggerName: DEFAULT_LOGGER_NAME,
                        logLevel: DEFAULT_LOG_LEVEL,
                        LocalAudioTrack: LocalAudioTrack,
                        LocalDataTrack: LocalDataTrack,
                        LocalVideoTrack: LocalVideoTrack,
                        MediaStreamTrack: MediaStreamTrack,
                        Log: Log,
                        video: isAudioVideoAbsent
                    }, options);
                    logComponentName = "[createLocalTracks #" + ++createLocalTrackCalls + "]";
                    logLevels = buildLogLevels(fullOptions.logLevel);
                    log = new fullOptions.Log('default', logComponentName, logLevels, fullOptions.loggerName);
                    localTrackOptions = Object.assign({
                        log: log
                    }, fullOptions);
                    // NOTE(mmalavalli): The Room "name" in "options" was being used
                    // as the LocalTrack name in asLocalTrack(). So we pass a copy of
                    // "options" without the "name".
                    // NOTE(joma): CreateLocalTracksOptions type does not really have a "name" property when used publicly by customers.
                    // But we are passing this property when used internally by other JS files.
                    // We can update this "any" type once those JS files are converted to TS.
                    delete localTrackOptions.name;
                    if (fullOptions.audio === false && fullOptions.video === false) {
                        log.info('Neither audio nor video requested, so returning empty LocalTracks');
                        return [
                            2 /*return*/ ,
                            []
                        ];
                    }
                    if (fullOptions.tracks) {
                        log.info('Adding user-provided LocalTracks');
                        log.debug('LocalTracks:', fullOptions.tracks);
                        return [
                            2 /*return*/ ,
                            fullOptions.tracks
                        ];
                    }
                    extraLocalTrackOptions = {
                        audio: typeof fullOptions.audio === 'object' && fullOptions.audio.name ? {
                            name: fullOptions.audio.name
                        } : {
                            defaultDeviceCaptureMode: 'auto'
                        },
                        video: typeof fullOptions.video === 'object' && fullOptions.video.name ? {
                            name: fullOptions.video.name
                        } : {}
                    };
                    extraLocalTrackOptions.audio.isCreatedByCreateLocalTracks = true;
                    extraLocalTrackOptions.video.isCreatedByCreateLocalTracks = true;
                    if (typeof fullOptions.audio === 'object') {
                        if (typeof fullOptions.audio.workaroundWebKitBug1208516 === 'boolean') {
                            extraLocalTrackOptions.audio.workaroundWebKitBug1208516 = fullOptions.audio.workaroundWebKitBug1208516;
                        }
                        if ('noiseCancellationOptions' in fullOptions.audio) {
                            noiseCancellationOptions = fullOptions.audio.noiseCancellationOptions;
                            delete fullOptions.audio.noiseCancellationOptions;
                        }
                        if (!('defaultDeviceCaptureMode' in fullOptions.audio)) {
                            extraLocalTrackOptions.audio.defaultDeviceCaptureMode = 'auto';
                        } else if ([
                            'auto',
                            'manual'
                        ].every(function(mode) {
                            return mode !== fullOptions.audio.defaultDeviceCaptureMode;
                        })) {
                            // eslint-disable-next-line new-cap
                            throw INVALID_VALUE('CreateLocalAudioTrackOptions.defaultDeviceCaptureMode', [
                                'auto',
                                'manual'
                            ]);
                        } else {
                            extraLocalTrackOptions.audio.defaultDeviceCaptureMode = fullOptions.audio.defaultDeviceCaptureMode;
                        }
                    }
                    if (typeof fullOptions.video === 'object' && typeof fullOptions.video.workaroundWebKitBug1208516 === 'boolean') {
                        extraLocalTrackOptions.video.workaroundWebKitBug1208516 = fullOptions.video.workaroundWebKitBug1208516;
                    }
                    if (typeof fullOptions.audio === 'object') {
                        delete fullOptions.audio.name;
                    }
                    if (typeof fullOptions.video === 'object') {
                        delete fullOptions.video.name;
                    }
                    mediaStreamConstraints = {
                        audio: fullOptions.audio,
                        video: fullOptions.video
                    };
                    workaroundWebKitBug180748 = typeof fullOptions.audio === 'object' && fullOptions.audio.workaroundWebKitBug180748;
                    _a.label = 1;
                case 1:
                    _a.trys.push([
                        1,
                        4,
                        ,
                        5
                    ]);
                    return [
                        4 /*yield*/ ,
                        workaroundWebKitBug180748 ? workaround180748(log, fullOptions.getUserMedia, mediaStreamConstraints) : fullOptions.getUserMedia(mediaStreamConstraints)
                    ];
                case 2:
                    mediaStream = _a.sent();
                    mediaStreamTracks = __spreadArray(__spreadArray([], __read(mediaStream.getAudioTracks())), __read(mediaStream.getVideoTracks()));
                    log.info('Call to getUserMedia successful; got tracks:', mediaStreamTracks);
                    return [
                        4 /*yield*/ ,
                        Promise.all(mediaStreamTracks.map(function(mediaStreamTrack) {
                            return __awaiter(_this, void 0, void 0, function() {
                                var _a, cleanTrack, noiseCancellation;
                                return __generator(this, function(_b) {
                                    switch(_b.label){
                                        case 0:
                                            if (!(mediaStreamTrack.kind === 'audio' && noiseCancellationOptions)) return [
                                                3 /*break*/ ,
                                                2
                                            ];
                                            return [
                                                4 /*yield*/ ,
                                                noisecancellationimpl_1.applyNoiseCancellation(mediaStreamTrack, noiseCancellationOptions, log)
                                            ];
                                        case 1:
                                            _a = _b.sent(), cleanTrack = _a.cleanTrack, noiseCancellation = _a.noiseCancellation;
                                            return [
                                                2 /*return*/ ,
                                                new localTrackOptions.LocalAudioTrack(cleanTrack, __assign(__assign(__assign({}, extraLocalTrackOptions.audio), localTrackOptions), {
                                                    noiseCancellation: noiseCancellation
                                                }))
                                            ];
                                        case 2:
                                            if (mediaStreamTrack.kind === 'audio') {
                                                return [
                                                    2 /*return*/ ,
                                                    new localTrackOptions.LocalAudioTrack(mediaStreamTrack, __assign(__assign({}, extraLocalTrackOptions.audio), localTrackOptions))
                                                ];
                                            }
                                            _b.label = 3;
                                        case 3:
                                            return [
                                                2 /*return*/ ,
                                                new localTrackOptions.LocalVideoTrack(mediaStreamTrack, __assign(__assign({}, extraLocalTrackOptions.video), localTrackOptions))
                                            ];
                                    }
                                });
                            });
                        }))
                    ];
                case 3:
                    return [
                        2 /*return*/ ,
                        _a.sent()
                    ];
                case 4:
                    error_1 = _a.sent();
                    log.warn('Call to getUserMedia failed:', error_1);
                    throw error_1;
                case 5:
                    return [
                        2 /*return*/ 
                    ];
            }
        });
    });
}
exports.createLocalTracks = createLocalTracks; //# sourceMappingURL=createlocaltracks.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/timer.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Timer = void 0;
var Timer = function() {
    function Timer() {
        // eslint-disable-next-line no-undefined
        this._end = undefined;
        this.start();
    }
    Timer.prototype.start = function() {
        this._start = Date.now();
        return this;
    };
    Timer.prototype.stop = function() {
        this._end = Date.now();
        return this;
    };
    Timer.prototype.getTimeMeasurement = function() {
        return {
            start: this._start,
            end: this._end,
            // eslint-disable-next-line no-undefined
            duration: this._end === undefined ? undefined : this._end - this._start
        };
    };
    return Timer;
}();
exports.Timer = Timer; //# sourceMappingURL=timer.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/mos.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mosToScore = exports.calculateMOS = void 0;
var r0 = 94.768; // Constant used in computing "rFactor".
// copied from https://code.hq.twilio.com/client/sdk-frd/blob/master/voice/voice-mos-calculation.md
function calculateMOS(rtt, jitter, fractionLost) {
    // Compute the effective latency.
    var effectiveLatency = rtt + jitter * 2 + 10;
    // Compute the initial "rFactor" from effective latency.
    var rFactor = 0;
    switch(true){
        case effectiveLatency < 160:
            rFactor = r0 - effectiveLatency / 40;
            break;
        case effectiveLatency < 1000:
            rFactor = r0 - (effectiveLatency - 120) / 10;
            break;
    }
    // Adjust "rFactor" with the fraction of packets lost.
    switch(true){
        case fractionLost <= rFactor / 2.5:
            rFactor = Math.max(rFactor - fractionLost * 2.5, 6.52);
            break;
        default:
            rFactor = 0;
            break;
    }
    // Compute MOS from "rFactor".
    var mos = 1 + 0.035 * rFactor + 0.000007 * rFactor * (rFactor - 60) * (100 - rFactor);
    return mos;
}
exports.calculateMOS = calculateMOS;
function mosToScore(mosValue) {
    var score = 0;
    if (!mosValue) {
        score = 0;
    } else if (mosValue > 4.2) {
        score = 5;
    } else if (mosValue > 4.0) {
        score = 4;
    } else if (mosValue > 3.6) {
        score = 3;
    } else if (mosValue > 3) {
        score = 2;
    } else {
        score = 1;
    }
    return score;
}
exports.mosToScore = mosToScore; //# sourceMappingURL=mos.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/getCombinedConnectionStats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getCombinedConnectionStats = void 0;
function getStatValues(report, statName, kind, reportTypes) {
    var results = [];
    report.forEach(function(stat) {
        if ((reportTypes.length === 0 || reportTypes.includes(stat.type)) && (kind.length === 0 || kind.includes(stat.kind)) && typeof stat[statName] === 'number') {
            results.push(stat[statName]);
        }
    });
    return results;
}
function getCombinedConnectionStats(_a) {
    var publisher = _a.publisher, subscriber = _a.subscriber;
    return __awaiter(this, void 0, void 0, function() {
        var _b, publisherStats, subscriberStats, timestamps, timestamp, jitter, packets, packetsLost, trackRoundTripTime, currentRoundTripTime, roundTripTime, bytesSent, bytesReceived, selectedIceCandidatePairStats, iceCandidateStats;
        return __generator(this, function(_c) {
            switch(_c.label){
                case 0:
                    return [
                        4 /*yield*/ ,
                        Promise.all([
                            publisher,
                            subscriber
                        ].map(function(pc) {
                            return pc.getStats();
                        }))
                    ];
                case 1:
                    _b = __read.apply(void 0, [
                        _c.sent(),
                        2
                    ]), publisherStats = _b[0], subscriberStats = _b[1];
                    timestamps = getStatValues(subscriberStats, 'timestamp', [
                        'audio'
                    ], [
                        'inbound-rtp'
                    ]);
                    timestamp = timestamps.length > 0 ? timestamps[0] : 0;
                    jitter = getStatValues(subscriberStats, 'jitter', [
                        'audio'
                    ], [
                        'inbound-rtp'
                    ]).reduce(function(a, b) {
                        return Math.max(a, b);
                    }, 0);
                    packets = getStatValues(subscriberStats, 'packetsReceived', [
                        'audio',
                        'video'
                    ], [
                        'inbound-rtp'
                    ]).reduce(function(a, b) {
                        return a + b;
                    }, 0);
                    packetsLost = getStatValues(subscriberStats, 'packetsLost', [
                        'audio',
                        'video'
                    ], [
                        'inbound-rtp'
                    ]).reduce(function(a, b) {
                        return a + b;
                    }, 0);
                    trackRoundTripTime = getStatValues(publisherStats, 'roundTripTime', [
                        'audio',
                        'video'
                    ], [
                        'remote-inbound-rtp'
                    ]).reduce(function(a, b) {
                        return Math.max(a, b);
                    }, 0);
                    currentRoundTripTime = getStatValues(subscriberStats, 'currentRoundTripTime', [], [
                        'candidate-pair'
                    ]).reduce(function(a, b) {
                        return Math.max(a, b);
                    }, 0);
                    roundTripTime = (currentRoundTripTime || trackRoundTripTime) * 1000;
                    bytesSent = getStatValues(publisherStats, 'bytesSent', [], [
                        'candidate-pair'
                    ]).reduce(function(a, b) {
                        return a + b;
                    }, 0);
                    bytesReceived = getStatValues(subscriberStats, 'bytesReceived', [], [
                        'candidate-pair'
                    ]).reduce(function(a, b) {
                        return a + b;
                    }, 0);
                    selectedIceCandidatePairStats = extractSelectedActiveCandidatePair(subscriberStats);
                    iceCandidateStats = [];
                    subscriberStats.forEach(function(stat) {
                        if (stat.type === 'local-candidate' || stat.type === 'remote-candidate') {
                            iceCandidateStats.push(makeStandardCandidateStats(stat));
                        }
                    });
                    return [
                        2 /*return*/ ,
                        {
                            timestamp: timestamp,
                            jitter: jitter,
                            packets: packets,
                            packetsLost: packetsLost,
                            roundTripTime: roundTripTime,
                            bytesSent: bytesSent,
                            bytesReceived: bytesReceived,
                            selectedIceCandidatePairStats: selectedIceCandidatePairStats,
                            iceCandidateStats: iceCandidateStats
                        }
                    ];
            }
        });
    });
}
exports.getCombinedConnectionStats = getCombinedConnectionStats;
function makeStandardCandidateStats(input) {
    var standardizedCandidateStatsKeys = [
        {
            key: 'transportId',
            type: 'string'
        },
        {
            key: 'candidateType',
            type: 'string'
        },
        {
            key: 'port',
            altKeys: [
                'portNumber'
            ],
            type: 'number'
        },
        {
            key: 'address',
            altKeys: [
                'ip',
                'ipAddress'
            ],
            type: 'string'
        },
        {
            key: 'priority',
            type: 'number'
        },
        {
            key: 'protocol',
            altKeys: [
                'transport'
            ],
            type: 'string'
        },
        {
            key: 'url',
            type: 'string'
        },
        {
            key: 'relayProtocol',
            type: 'string'
        }
    ];
    return standardizedCandidateStatsKeys.reduce(function(report, keyInfo) {
        var keysToLookFor = [
            keyInfo.key
        ];
        if (keyInfo.altKeys) {
            keysToLookFor = keysToLookFor.concat(keyInfo.altKeys);
        }
        var key = keysToLookFor.find(function(key) {
            return key in input;
        });
        if (key && typeof input[key] === keyInfo.type) {
            report[keyInfo.key] = input[key];
        }
        return report;
    }, {});
}
function extractSelectedActiveCandidatePair(stats) {
    var selectedCandidatePairId = null;
    var candidatePairs = [];
    stats.forEach(function(stat) {
        if (stat.type === 'transport' && stat.selectedCandidatePairId) {
            selectedCandidatePairId = stat.selectedCandidatePairId;
        } else if (stat.type === 'candidate-pair') {
            candidatePairs.push(stat);
        }
    });
    var activeCandidatePairStatsFound = candidatePairs.find(function(pair) {
        // Firefox
        return pair.selected || selectedCandidatePairId && pair.id === selectedCandidatePairId;
    });
    if (!activeCandidatePairStatsFound) {
        return null;
    }
    var activeCandidatePairStats = activeCandidatePairStatsFound;
    var activeLocalCandidateStats = stats.get(activeCandidatePairStats.localCandidateId);
    var activeRemoteCandidateStats = stats.get(activeCandidatePairStats.remoteCandidateId);
    if (!activeLocalCandidateStats || !activeRemoteCandidateStats) {
        return null;
    }
    return {
        localCandidate: makeStandardCandidateStats(activeLocalCandidateStats),
        remoteCandidate: makeStandardCandidateStats(activeRemoteCandidateStats)
    };
} //# sourceMappingURL=getCombinedConnectionStats.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/getturncredentials.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getTurnCredentials = void 0;
/* eslint-disable camelcase */ var TwilioConnection = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/twilioconnection.js [app-client] (ecmascript)");
var ICE_VERSION = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").ICE_VERSION;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), createTwilioError = _a.createTwilioError, SignalingConnectionError = _a.SignalingConnectionError;
var events_1 = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)");
function getTurnCredentials(token, wsServer) {
    return new Promise(function(resolve, reject) {
        var eventObserver = new events_1.EventEmitter();
        var connectionOptions = {
            networkMonitor: null,
            eventObserver: eventObserver,
            helloBody: {
                edge: 'roaming',
                preflight: true,
                token: token,
                type: 'ice',
                version: ICE_VERSION
            }
        };
        var twilioConnection = new TwilioConnection(wsServer, connectionOptions);
        var done = false;
        twilioConnection.once('close', function() {
            if (!done) {
                done = true;
                reject(new SignalingConnectionError());
            }
        });
        twilioConnection.on('message', function(messageData) {
            var code = messageData.code, message = messageData.message, ice_servers = messageData.ice_servers, type = messageData.type;
            if ((type === 'iced' || type === 'error') && !done) {
                done = true;
                if (type === 'iced') {
                    resolve(ice_servers);
                } else {
                    reject(createTwilioError(code, message));
                }
                twilioConnection.close();
            }
        });
    });
}
exports.getTurnCredentials = getTurnCredentials; //# sourceMappingURL=getturncredentials.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/makestat.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.makeStat = void 0;
/**
 * Computes min, max, average for given array.
 * @param {Array<number>} values
 * @returns {{min: number, max: number: average: number}|null}
 */ function makeStat(values) {
    if (values && values.length) {
        var min = Math.min.apply(Math, __spreadArray([], __read(values)));
        var max = Math.max.apply(Math, __spreadArray([], __read(values)));
        var average = values.reduce(function(total, value) {
            return total + value;
        }, 0) / values.length;
        return {
            min: min,
            max: max,
            average: average
        };
    }
    return null;
}
exports.makeStat = makeStat; //# sourceMappingURL=makestat.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/syntheticaudio.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.syntheticAudio = void 0;
function syntheticAudio() {
    // NOTE(mpatwardhan): We have to delay require-ing AudioContextFactory, because
    // it exports a default instance whose constructor calls Object.assign.
    var audioContextFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/audiocontext.js [app-client] (ecmascript)");
    var holder = {};
    var audioContext = audioContextFactory.getOrCreate(holder);
    var oscillator = audioContext.createOscillator();
    var dst = oscillator.connect(audioContext.createMediaStreamDestination());
    oscillator.start();
    var track = dst.stream.getAudioTracks()[0];
    var originalStop = track.stop;
    track.stop = function() {
        originalStop.call(track);
        audioContextFactory.release(holder);
    };
    return track;
}
exports.syntheticAudio = syntheticAudio; //# sourceMappingURL=syntheticaudio.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/syntheticvideo.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.syntheticVideo = void 0;
function syntheticVideo(_a) {
    var _b = _a === void 0 ? {} : _a, _c = _b.width, width = _c === void 0 ? 640 : _c, _d = _b.height, height = _d === void 0 ? 480 : _d;
    var canvas = Object.assign(document.createElement('canvas'), {
        width: width,
        height: height
    });
    var ctx = canvas.getContext('2d');
    ctx.fillStyle = 'green';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    var stopped = false;
    requestAnimationFrame(function animate() {
        if (!stopped) {
            // draw random rect/circle.
            var r = Math.round(Math.random() * 255);
            var g = Math.round(Math.random() * 255);
            var b = Math.round(Math.random() * 255);
            var a = Math.round(Math.random() * 255);
            ctx.fillStyle = "rgba(" + r + ", " + g + ", " + b + ", " + a + ")";
            ctx.fillRect(Math.random() * width, Math.random() * height, 50, 50);
            requestAnimationFrame(animate);
        }
    });
    var stream = canvas.captureStream(30);
    var track = stream.getTracks()[0];
    var originalStop = track.stop;
    track.stop = function() {
        stopped = true;
        originalStop.call(track);
    };
    return track;
}
exports.syntheticVideo = syntheticVideo; //# sourceMappingURL=syntheticvideo.js.map
}}),
"[project]/node_modules/twilio-video/es5/preflight/preflighttest.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.runPreflight = exports.PreflightTest = void 0;
var constants_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)");
var timer_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/timer.js [app-client] (ecmascript)");
var mos_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/mos.js [app-client] (ecmascript)");
var getCombinedConnectionStats_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/getCombinedConnectionStats.js [app-client] (ecmascript)");
var getturncredentials_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/getturncredentials.js [app-client] (ecmascript)");
var makestat_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/makestat.js [app-client] (ecmascript)");
var syntheticaudio_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/syntheticaudio.js [app-client] (ecmascript)");
var syntheticvideo_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/syntheticvideo.js [app-client] (ecmascript)");
var index_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)");
var WS_SERVER = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").WS_SERVER;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var EventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)");
var MovingAverageDelta = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/movingaveragedelta.js [app-client] (ecmascript)");
var EventObserver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/eventobserver.js [app-client] (ecmascript)");
var InsightsPublisher = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/insightspublisher/index.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sid.js [app-client] (ecmascript)"), createSID = _a.createSID, sessionSID = _a.sessionSID;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), SignalingConnectionTimeoutError = _b.SignalingConnectionTimeoutError, MediaConnectionError = _b.MediaConnectionError;
var SECOND = 1000;
var DEFAULT_TEST_DURATION = 10 * SECOND;
/**
 * progress values that are sent by {@link PreflightTest#event:progress}
 * @enum {string}
 */ var PreflightProgress = {
    /**
     * {@link PreflightTest} has successfully generated synthetic tracks
     */ mediaAcquired: 'mediaAcquired',
    /**
     * {@link PreflightTest} has successfully connected to twilio server and obtained turn credentials
     */ connected: 'connected',
    /**
     * SubscriberParticipant successfully subscribed to media tracks.
     */ mediaSubscribed: 'mediaSubscribed',
    /**
     * Media flow was detected.
     */ mediaStarted: 'mediaStarted',
    /**
     * Established DTLS connection. This is measured from RTCDtlsTransport `connecting` to `connected` state.
     * On Safari, Support for measuring this is missing, this event will be not be emitted on Safari.
     */ dtlsConnected: 'dtlsConnected',
    /**
     * Established a PeerConnection, This is measured from PeerConnection `connecting` to `connected` state.
     * On Firefox, Support for measuring this is missing, this event will be not be emitted on Firefox.
     */ peerConnectionConnected: 'peerConnectionConnected',
    /**
     * Established ICE connection. This is measured from ICE connection `checking` to `connected` state.
     */ iceConnected: 'iceConnected'
};
function notEmpty(value) {
    return value !== null && typeof value !== 'undefined';
}
var nInstances = 0;
/**
 * A {@link PreflightTest} monitors progress of an ongoing preflight test.
 * <br><br>
 * Instance of {@link PreflightTest} is returned by calling {@link module:twilio-video.runPreflight}
 * @extends EventEmitter
 * @emits PreflightTest#completed
 * @emits PreflightTest#failed
 * @emits PreflightTest#progress
 */ var PreflightTest = function(_super) {
    __extends(PreflightTest, _super);
    /**
     * Constructs {@link PreflightTest}.
     * @param {string} token
     * @param {?PreflightOptions} [options]
     */ function PreflightTest(token, options) {
        var _this = _super.call(this) || this;
        _this._testTiming = new timer_1.Timer();
        _this._dtlsTiming = new timer_1.Timer();
        _this._iceTiming = new timer_1.Timer();
        _this._peerConnectionTiming = new timer_1.Timer();
        _this._mediaTiming = new timer_1.Timer();
        _this._connectTiming = new timer_1.Timer();
        _this._sentBytesMovingAverage = new MovingAverageDelta();
        _this._packetLossMovingAverage = new MovingAverageDelta();
        _this._progressEvents = [];
        _this._receivedBytesMovingAverage = new MovingAverageDelta();
        var internalOptions = options;
        var _a = internalOptions.environment, environment = _a === void 0 ? 'prod' : _a, _b = internalOptions.region, region = _b === void 0 ? 'gll' : _b, _c = internalOptions.duration, duration = _c === void 0 ? DEFAULT_TEST_DURATION : _c;
        // eslint-disable-next-line new-cap
        var wsServer = internalOptions.wsServer || WS_SERVER(environment, region);
        _this._log = new Log('default', _this, constants_1.DEFAULT_LOG_LEVEL, constants_1.DEFAULT_LOGGER_NAME);
        _this._testDuration = duration;
        _this._instanceId = nInstances++;
        _this._testTiming.start();
        _this._runPreflightTest(token, environment, wsServer);
        return _this;
    }
    PreflightTest.prototype.toString = function() {
        return "[Preflight #" + this._instanceId + "]";
    };
    /**
     * stops ongoing tests and emits error
     */ PreflightTest.prototype.stop = function() {
        this._stopped = true;
    };
    PreflightTest.prototype._generatePreflightReport = function(collectedStats) {
        this._testTiming.stop();
        return {
            testTiming: this._testTiming.getTimeMeasurement(),
            networkTiming: {
                dtls: this._dtlsTiming.getTimeMeasurement(),
                ice: this._iceTiming.getTimeMeasurement(),
                peerConnection: this._peerConnectionTiming.getTimeMeasurement(),
                connect: this._connectTiming.getTimeMeasurement(),
                media: this._mediaTiming.getTimeMeasurement()
            },
            stats: {
                jitter: makestat_1.makeStat(collectedStats === null || collectedStats === void 0 ? void 0 : collectedStats.jitter),
                rtt: makestat_1.makeStat(collectedStats === null || collectedStats === void 0 ? void 0 : collectedStats.rtt),
                packetLoss: makestat_1.makeStat(collectedStats === null || collectedStats === void 0 ? void 0 : collectedStats.packetLoss)
            },
            selectedIceCandidatePairStats: collectedStats ? collectedStats.selectedIceCandidatePairStats : null,
            iceCandidateStats: collectedStats ? collectedStats.iceCandidateStats : [],
            progressEvents: this._progressEvents,
            // NOTE(mpatwardhan): internal properties.
            mos: makestat_1.makeStat(collectedStats === null || collectedStats === void 0 ? void 0 : collectedStats.mos)
        };
    };
    PreflightTest.prototype._executePreflightStep = function(stepName, step, timeoutError) {
        return __awaiter(this, void 0, void 0, function() {
            var MAX_STEP_DURATION, stepPromise, timer, timeoutPromise, result;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this._log.debug('Executing step: ', stepName);
                        MAX_STEP_DURATION = this._testDuration + 10 * SECOND;
                        if (this._stopped) {
                            throw new Error('stopped');
                        }
                        stepPromise = Promise.resolve().then(step);
                        timer = null;
                        timeoutPromise = new Promise(function(_resolve, reject) {
                            timer = setTimeout(function() {
                                reject(timeoutError || new Error(stepName + " timeout."));
                            }, MAX_STEP_DURATION);
                        });
                        _a.label = 1;
                    case 1:
                        _a.trys.push([
                            1,
                            ,
                            3,
                            4
                        ]);
                        return [
                            4 /*yield*/ ,
                            Promise.race([
                                timeoutPromise,
                                stepPromise
                            ])
                        ];
                    case 2:
                        result = _a.sent();
                        return [
                            2 /*return*/ ,
                            result
                        ];
                    case 3:
                        if (timer !== null) {
                            clearTimeout(timer);
                        }
                        return [
                            7 /*endfinally*/ 
                        ];
                    case 4:
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    PreflightTest.prototype._collectNetworkTimings = function(pc) {
        var _this = this;
        return new Promise(function(resolve) {
            var dtlsTransport;
            pc.addEventListener('iceconnectionstatechange', function() {
                if (pc.iceConnectionState === 'checking') {
                    _this._iceTiming.start();
                }
                if (pc.iceConnectionState === 'connected') {
                    _this._iceTiming.stop();
                    _this._updateProgress(PreflightProgress.iceConnected);
                    if (!dtlsTransport || dtlsTransport && dtlsTransport.state === 'connected') {
                        resolve();
                    }
                }
            });
            // firefox does not support connectionstatechange.
            pc.addEventListener('connectionstatechange', function() {
                if (pc.connectionState === 'connecting') {
                    _this._peerConnectionTiming.start();
                }
                if (pc.connectionState === 'connected') {
                    _this._peerConnectionTiming.stop();
                    _this._updateProgress(PreflightProgress.peerConnectionConnected);
                }
            });
            // Safari does not expose sender.transport.
            var senders = pc.getSenders();
            var transport = senders.map(function(sender) {
                return sender.transport;
            }).find(notEmpty);
            if (typeof transport !== 'undefined') {
                dtlsTransport = transport;
                dtlsTransport.addEventListener('statechange', function() {
                    if (dtlsTransport.state === 'connecting') {
                        _this._dtlsTiming.start();
                    }
                    if (dtlsTransport.state === 'connected') {
                        _this._dtlsTiming.stop();
                        _this._updateProgress(PreflightProgress.dtlsConnected);
                        if (pc.iceConnectionState === 'connected') {
                            resolve();
                        }
                    }
                });
            }
        });
    };
    PreflightTest.prototype._setupInsights = function(_a) {
        var token = _a.token, _b = _a.environment, environment = _b === void 0 ? constants_1.DEFAULT_ENVIRONMENT : _b, _c = _a.realm, realm = _c === void 0 ? constants_1.DEFAULT_REALM : _c;
        var eventPublisherOptions = {};
        var eventPublisher = new InsightsPublisher(token, constants_1.SDK_NAME, constants_1.SDK_VERSION, environment, realm, eventPublisherOptions);
        // event publisher requires room sid/participant sid. supply fake ones.
        eventPublisher.connect('PREFLIGHT_ROOM_SID', 'PREFLIGHT_PARTICIPANT');
        var eventObserver = new EventObserver(eventPublisher, Date.now(), this._log);
        // eslint-disable-next-line no-undefined
        var undefinedValue = undefined;
        return {
            reportToInsights: function(_a) {
                var _b, _c;
                var report = _a.report;
                var jitterStats = report.stats.jitter || undefinedValue;
                var rttStats = report.stats.rtt || undefinedValue;
                var packetLossStats = report.stats.packetLoss || undefinedValue;
                var mosStats = report.mos || undefinedValue;
                // stringify important info from ice candidates.
                var candidateTypeToProtocols = new Map();
                report.iceCandidateStats.forEach(function(candidateStats) {
                    if (candidateStats.candidateType && candidateStats.protocol) {
                        var protocols = candidateTypeToProtocols.get(candidateStats.candidateType) || [];
                        if (protocols.indexOf(candidateStats.protocol) < 0) {
                            protocols.push(candidateStats.protocol);
                        }
                        candidateTypeToProtocols.set(candidateStats.candidateType, protocols);
                    }
                });
                var iceCandidateStats = JSON.stringify(Object.fromEntries(candidateTypeToProtocols));
                var insightsReport = {
                    name: 'report',
                    group: 'preflight',
                    level: report.error ? 'error' : 'info',
                    payload: {
                        sessionSID: sessionSID,
                        preflightSID: createSID('PF'),
                        progressEvents: JSON.stringify(report.progressEvents),
                        testTiming: report.testTiming,
                        dtlsTiming: report.networkTiming.dtls,
                        iceTiming: report.networkTiming.ice,
                        peerConnectionTiming: report.networkTiming.peerConnection,
                        connectTiming: report.networkTiming.connect,
                        mediaTiming: report.networkTiming.media,
                        selectedLocalCandidate: (_b = report.selectedIceCandidatePairStats) === null || _b === void 0 ? void 0 : _b.localCandidate,
                        selectedRemoteCandidate: (_c = report.selectedIceCandidatePairStats) === null || _c === void 0 ? void 0 : _c.remoteCandidate,
                        iceCandidateStats: iceCandidateStats,
                        jitterStats: jitterStats,
                        rttStats: rttStats,
                        packetLossStats: packetLossStats,
                        mosStats: mosStats,
                        error: report.error
                    }
                };
                eventObserver.emit('event', insightsReport);
                setTimeout(function() {
                    return eventPublisher.disconnect();
                }, 2000);
            }
        };
    };
    PreflightTest.prototype._runPreflightTest = function(token, environment, wsServer) {
        return __awaiter(this, void 0, void 0, function() {
            var localTracks, pcs, reportToInsights, elements_1, iceServers, senderPC_1, receiverPC_1, remoteTracks_1, collectedStats_1, report, error_1, preflightReport;
            var _this = this;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        localTracks = [];
                        pcs = [];
                        reportToInsights = this._setupInsights({
                            token: token,
                            environment: environment
                        }).reportToInsights;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([
                            1,
                            8,
                            9,
                            10
                        ]);
                        elements_1 = [];
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Acquire media', function() {
                                return [
                                    syntheticaudio_1.syntheticAudio(),
                                    syntheticvideo_1.syntheticVideo({
                                        width: 640,
                                        height: 480
                                    })
                                ];
                            })
                        ];
                    case 2:
                        localTracks = _a.sent();
                        this._updateProgress(PreflightProgress.mediaAcquired);
                        this.emit('debug', {
                            localTracks: localTracks
                        });
                        this._connectTiming.start();
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Get turn credentials', function() {
                                return getturncredentials_1.getTurnCredentials(token, wsServer);
                            }, new SignalingConnectionTimeoutError())
                        ];
                    case 3:
                        iceServers = _a.sent();
                        this._connectTiming.stop();
                        this._updateProgress(PreflightProgress.connected);
                        senderPC_1 = new RTCPeerConnection({
                            iceServers: iceServers,
                            iceTransportPolicy: 'relay',
                            bundlePolicy: 'max-bundle'
                        });
                        receiverPC_1 = new RTCPeerConnection({
                            iceServers: iceServers,
                            bundlePolicy: 'max-bundle'
                        });
                        pcs.push(senderPC_1);
                        pcs.push(receiverPC_1);
                        this._mediaTiming.start();
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Setup Peer Connections', function() {
                                return __awaiter(_this, void 0, void 0, function() {
                                    var remoteTracksPromise, offer, updatedOffer, answer;
                                    return __generator(this, function(_a) {
                                        switch(_a.label){
                                            case 0:
                                                senderPC_1.addEventListener('icecandidate', function(event) {
                                                    return event.candidate && receiverPC_1.addIceCandidate(event.candidate);
                                                });
                                                receiverPC_1.addEventListener('icecandidate', function(event) {
                                                    return event.candidate && senderPC_1.addIceCandidate(event.candidate);
                                                });
                                                localTracks.forEach(function(track) {
                                                    return senderPC_1.addTrack(track);
                                                });
                                                remoteTracksPromise = new Promise(function(resolve) {
                                                    var remoteTracks = [];
                                                    receiverPC_1.addEventListener('track', function(event) {
                                                        remoteTracks.push(event.track);
                                                        if (remoteTracks.length === localTracks.length) {
                                                            resolve(remoteTracks);
                                                        }
                                                    });
                                                });
                                                return [
                                                    4 /*yield*/ ,
                                                    senderPC_1.createOffer()
                                                ];
                                            case 1:
                                                offer = _a.sent();
                                                updatedOffer = offer;
                                                return [
                                                    4 /*yield*/ ,
                                                    senderPC_1.setLocalDescription(updatedOffer)
                                                ];
                                            case 2:
                                                _a.sent();
                                                return [
                                                    4 /*yield*/ ,
                                                    receiverPC_1.setRemoteDescription(updatedOffer)
                                                ];
                                            case 3:
                                                _a.sent();
                                                return [
                                                    4 /*yield*/ ,
                                                    receiverPC_1.createAnswer()
                                                ];
                                            case 4:
                                                answer = _a.sent();
                                                return [
                                                    4 /*yield*/ ,
                                                    receiverPC_1.setLocalDescription(answer)
                                                ];
                                            case 5:
                                                _a.sent();
                                                return [
                                                    4 /*yield*/ ,
                                                    senderPC_1.setRemoteDescription(answer)
                                                ];
                                            case 6:
                                                _a.sent();
                                                return [
                                                    4 /*yield*/ ,
                                                    this._collectNetworkTimings(senderPC_1)
                                                ];
                                            case 7:
                                                _a.sent();
                                                return [
                                                    2 /*return*/ ,
                                                    remoteTracksPromise
                                                ];
                                        }
                                    });
                                });
                            }, new MediaConnectionError())
                        ];
                    case 4:
                        remoteTracks_1 = _a.sent();
                        this.emit('debug', {
                            remoteTracks: remoteTracks_1
                        });
                        remoteTracks_1.forEach(function(track) {
                            track.addEventListener('ended', function() {
                                return _this._log.warn(track.kind + ':ended');
                            });
                            track.addEventListener('mute', function() {
                                return _this._log.warn(track.kind + ':muted');
                            });
                            track.addEventListener('unmute', function() {
                                return _this._log.warn(track.kind + ':unmuted');
                            });
                        });
                        this._updateProgress(PreflightProgress.mediaSubscribed);
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Wait for tracks to start', function() {
                                return new Promise(function(resolve) {
                                    var element = document.createElement('video');
                                    element.autoplay = true;
                                    element.playsInline = true;
                                    element.muted = true;
                                    element.srcObject = new MediaStream(remoteTracks_1);
                                    elements_1.push(element);
                                    _this.emit('debugElement', element);
                                    element.oncanplay = resolve;
                                });
                            }, new MediaConnectionError())
                        ];
                    case 5:
                        _a.sent();
                        this._mediaTiming.stop();
                        this._updateProgress(PreflightProgress.mediaStarted);
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Collect stats for duration', function() {
                                return _this._collectRTCStatsForDuration(_this._testDuration, initCollectedStats(), senderPC_1, receiverPC_1);
                            })
                        ];
                    case 6:
                        collectedStats_1 = _a.sent();
                        return [
                            4 /*yield*/ ,
                            this._executePreflightStep('Generate report', function() {
                                return _this._generatePreflightReport(collectedStats_1);
                            })
                        ];
                    case 7:
                        report = _a.sent();
                        reportToInsights({
                            report: report
                        });
                        this.emit('completed', report);
                        return [
                            3 /*break*/ ,
                            10
                        ];
                    case 8:
                        error_1 = _a.sent();
                        preflightReport = this._generatePreflightReport();
                        reportToInsights({
                            report: __assign(__assign({}, preflightReport), {
                                error: error_1 === null || error_1 === void 0 ? void 0 : error_1.toString()
                            })
                        });
                        this.emit('failed', error_1, preflightReport);
                        return [
                            3 /*break*/ ,
                            10
                        ];
                    case 9:
                        pcs.forEach(function(pc) {
                            return pc.close();
                        });
                        localTracks.forEach(function(track) {
                            return track.stop();
                        });
                        return [
                            7 /*endfinally*/ 
                        ];
                    case 10:
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    PreflightTest.prototype._collectRTCStats = function(collectedStats, senderPC, receiverPC) {
        return __awaiter(this, void 0, void 0, function() {
            var combinedStats, timestamp, bytesSent, bytesReceived, packets, packetsLost, roundTripTime, jitter, selectedIceCandidatePairStats, iceCandidateStats, hasLastData, fractionPacketLost, percentPacketsLost, score;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            getCombinedConnectionStats_1.getCombinedConnectionStats({
                                publisher: senderPC,
                                subscriber: receiverPC
                            })
                        ];
                    case 1:
                        combinedStats = _a.sent();
                        timestamp = combinedStats.timestamp, bytesSent = combinedStats.bytesSent, bytesReceived = combinedStats.bytesReceived, packets = combinedStats.packets, packetsLost = combinedStats.packetsLost, roundTripTime = combinedStats.roundTripTime, jitter = combinedStats.jitter, selectedIceCandidatePairStats = combinedStats.selectedIceCandidatePairStats, iceCandidateStats = combinedStats.iceCandidateStats;
                        hasLastData = collectedStats.jitter.length > 0;
                        collectedStats.jitter.push(jitter);
                        collectedStats.rtt.push(roundTripTime);
                        this._sentBytesMovingAverage.putSample(bytesSent, timestamp);
                        this._receivedBytesMovingAverage.putSample(bytesReceived, timestamp);
                        this._packetLossMovingAverage.putSample(packetsLost, packets);
                        if (hasLastData) {
                            // convert BytesMovingAverage which is in bytes/millisecond to bits/second
                            collectedStats.outgoingBitrate.push(this._sentBytesMovingAverage.get() * 1000 * 8);
                            collectedStats.incomingBitrate.push(this._receivedBytesMovingAverage.get() * 1000 * 8);
                            fractionPacketLost = this._packetLossMovingAverage.get();
                            percentPacketsLost = Math.min(100, fractionPacketLost * 100);
                            collectedStats.packetLoss.push(percentPacketsLost);
                            score = mos_1.calculateMOS(roundTripTime, jitter, fractionPacketLost);
                            collectedStats.mos.push(score);
                        }
                        if (!collectedStats.selectedIceCandidatePairStats) {
                            collectedStats.selectedIceCandidatePairStats = selectedIceCandidatePairStats;
                        }
                        if (collectedStats.iceCandidateStats.length === 0) {
                            collectedStats.iceCandidateStats = iceCandidateStats;
                        }
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    PreflightTest.prototype._collectRTCStatsForDuration = function(duration, collectedStats, senderPC, receiverPC) {
        return __awaiter(this, void 0, void 0, function() {
            var startTime, STAT_INTERVAL, remainingDuration;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        startTime = Date.now();
                        STAT_INTERVAL = Math.min(1000, duration);
                        return [
                            4 /*yield*/ ,
                            index_1.waitForSometime(STAT_INTERVAL)
                        ];
                    case 1:
                        _a.sent();
                        return [
                            4 /*yield*/ ,
                            this._collectRTCStats(collectedStats, senderPC, receiverPC)
                        ];
                    case 2:
                        _a.sent();
                        remainingDuration = duration - (Date.now() - startTime);
                        if (!(remainingDuration > 0)) return [
                            3 /*break*/ ,
                            4
                        ];
                        return [
                            4 /*yield*/ ,
                            this._collectRTCStatsForDuration(remainingDuration, collectedStats, senderPC, receiverPC)
                        ];
                    case 3:
                        collectedStats = _a.sent();
                        _a.label = 4;
                    case 4:
                        return [
                            2 /*return*/ ,
                            collectedStats
                        ];
                }
            });
        });
    };
    PreflightTest.prototype._updateProgress = function(name) {
        var duration = Date.now() - this._testTiming.getTimeMeasurement().start;
        this._progressEvents.push({
            duration: duration,
            name: name
        });
        this.emit('progress', name);
    };
    return PreflightTest;
}(EventEmitter);
exports.PreflightTest = PreflightTest;
function initCollectedStats() {
    return {
        mos: [],
        jitter: [],
        rtt: [],
        outgoingBitrate: [],
        incomingBitrate: [],
        packetLoss: [],
        selectedIceCandidatePairStats: null,
        iceCandidateStats: []
    };
}
/**
 * Represents network timing measurements captured during preflight test
 * @typedef {object} NetworkTiming
 * @property {TimeMeasurement} [connect] - Time to establish signaling connection and acquire turn credentials
 * @property {TimeMeasurement} [media] - Time to start media. This is measured from calling connect to remote media getting started.
 * @property {TimeMeasurement} [dtls] - Time to establish dtls connection. This is measured from RTCDtlsTransport `connecting` to `connected` state. (Not available on Safari)
 * @property {TimeMeasurement} [ice] - Time to establish ice connectivity. This is measured from ICE connection `checking` to `connected` state.
 * @property {TimeMeasurement} [peerConnection] - Time to establish peer connectivity. This is measured from PeerConnection `connecting` to `connected` state. (Not available on Firefox)
 */ /**
 * Represents stats for a numerical metric.
 * @typedef {object} Stats
 * @property  {number} [average] - Average value observed.
 * @property  {number} [max] - Max value observed.
 * @property  {number} [min] - Min value observed.
 */ /**
 * Represents stats for a numerical metric.
 * @typedef {object} SelectedIceCandidatePairStats
 * @property  {RTCIceCandidateStats} [localCandidate] - Selected local ice candidate
 * @property  {RTCIceCandidateStats} [remoteCandidate] - Selected local ice candidate
 */ /**
 * Represents RTC related stats that were observed during preflight test
 * @typedef {object} PreflightReportStats
 * @property {Stats} [jitter] - Packet delay variation in seconds
 * @property {Stats} [rtt] - Round trip time, to the server back to the client in milliseconds.
 * @property {Stats} [packetLoss] - Packet loss as a percent of total packets sent.
*/ /**
 * A {@link PreflightProgress} event with timing information.
 * @typedef {object} ProgressEvent
 * @property {number} [duration] - The duration of the event, measured from the start of the test.
 * @property {string} [name] - The {@link PreflightProgress} event name.
 */ /**
 * Represents report generated by {@link PreflightTest}.
 * @typedef {object} PreflightTestReport
 * @property {TimeMeasurement} [testTiming] - Time measurements of test run time.
 * @property {NetworkTiming} [networkTiming] - Network related time measurements.
 * @property {PreflightReportStats} [stats] - RTC related stats captured during the test.
 * @property {Array<RTCIceCandidateStats>} [iceCandidateStats] - List of gathered ice candidates.
 * @property {SelectedIceCandidatePairStats} selectedIceCandidatePairStats - Stats for the ice candidates that were used for the connection.
 * @property {Array<ProgressEvent>} [progressEvents] - {@link ProgressEvent} events detected during the test.
 * Use this information to determine which steps were completed and which ones were not.
 */ /**
 * You may pass these options to {@link module:twilio-video.testPreflight} in order to override the
 * default behavior.
 * @typedef {object} PreflightOptions
 * @property {string} [region='gll'] - Preferred signaling region; By default, you will be connected to the
 *   nearest signaling server determined by latency based routing. Setting a value other
 *   than <code style="padding:0 0">gll</code> bypasses routing and guarantees that signaling traffic will be
 *   terminated in the region that you prefer. Please refer to this <a href="https://www.twilio.com/docs/video/ip-address-whitelisting#signaling-communication" target="_blank">table</a>
 *   for the list of supported signaling regions.
 * @property {number} [duration=10000] - number of milliseconds to run test for.
 *   once connected test will run for this duration before generating the stats report.
 */ /**
 * Preflight test has completed successfully.
 * @param {PreflightTestReport} report - Results of the test.
 * @event PreflightTest#completed
 */ /**
 * Preflight test has encountered a failure and is now stopped.
 * @param {TwilioError|Error} error - A TwilioError or a DOMException.
 * Possible TwilioErrors include Signaling and Media related errors which can be found
 * <a href="https://www.twilio.com/docs/video/build-js-video-application-recommendations-and-best-practices#connection-errors" target="_blank">here</a>.
 * @param {PreflightTestReport} report - Partial results gathered during the test. Use this information to help determine the cause of failure.
 * @event PreflightTest#failed
 */ /**
 * Emitted to indicate progress of the test
 * @param {PreflightProgress} progress - Indicates the status completed.
 * @event PreflightTest#progress
 */ /**
 * @method
 * @name runPreflight
 * @description Run a preflight test. This method will start a test to check the quality of network connection.
 * @memberof module:twilio-video
 * @param {string} token - The Access Token string
 * @param {PreflightOptions} options - Options for the test
 * @returns {PreflightTest} preflightTest - An instance to be used to monitor progress of the test.
 * @example
 * var { runPreflight } = require('twilio-video');
 * var preflight = runPreflight(token, preflightOptions);
 * preflightTest.on('progress', progress => {
 *   console.log('preflight progress:', progress);
 * });
 *
 * preflightTest.on('failed', (error, report) => {
 *   console.error('preflight error:', error, report);
 * });
 *
 * preflightTest.on('completed', report => {
 *   console.log('preflight completed:', report));
 * });
*/ function runPreflight(token, options) {
    if (options === void 0) {
        options = {};
    }
    var preflight = new PreflightTest(token, options);
    return preflight;
}
exports.runPreflight = runPreflight; //# sourceMappingURL=preflighttest.js.map
}}),
"[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)");
/**
 * {@link StateMachine} represents a state machine. The state machine supports a
 * reentrant locking mechanism to allow asynchronous state transitions to ensure
 * they have not been preempted. Calls to {@link StateMachine#takeLock} are
 * guaranteed to be resolved in FIFO order.
 * @extends EventEmitter
 * @property {boolean} isLocked - whether or not the {@link StateMachine} is
 *   locked performing asynchronous state transition
 * @property {string} state - the current state
 * @emits {@link StateMachine#stateChanged}
 */ var StateMachine = function(_super) {
    __extends(StateMachine, _super);
    /**
     * Construct a {@link StateMachine}.
     * @param {string} initialState - the intiial state
     * @param {object} states
     */ function StateMachine(initialState, states) {
        var _this = _super.call(this) || this;
        var lock = null;
        var state = initialState;
        states = transformStates(states);
        Object.defineProperties(_this, {
            _lock: {
                get: function() {
                    return lock;
                },
                set: function(_lock) {
                    lock = _lock;
                }
            },
            _reachableStates: {
                value: reachable(states)
            },
            _state: {
                get: function() {
                    return state;
                },
                set: function(_state) {
                    state = _state;
                }
            },
            _states: {
                value: states
            },
            _whenDeferreds: {
                value: new Set()
            },
            isLocked: {
                enumerable: true,
                get: function() {
                    return lock !== null;
                }
            },
            state: {
                enumerable: true,
                get: function() {
                    return state;
                }
            }
        });
        _this.on('stateChanged', function(state) {
            _this._whenDeferreds.forEach(function(deferred) {
                deferred.when(state, deferred.resolve, deferred.reject);
            });
        });
        return _this;
    }
    /**
     * Returns a promise whose executor function is called on each state change.
     * @param {function(state: string, resolve: function, reject: function): void} when
     * @returns {Promise.<*>}
     * @private
     */ StateMachine.prototype._whenPromise = function(when) {
        var _this = this;
        if (typeof when !== 'function') {
            return Promise.reject(new Error('when() executor must be a function'));
        }
        var deferred = util.defer();
        deferred.when = when;
        this._whenDeferreds.add(deferred);
        return deferred.promise.then(function(payload) {
            _this._whenDeferreds.delete(deferred);
            return payload;
        }, function(error) {
            _this._whenDeferreds.delete(deferred);
            throw error;
        });
    };
    /**
     * This method takes a lock and passes the {@link StateMachine#Key} to your
     * transition function. You may perform zero or more state transitions in your
     * transition function, but you should check for preemption in each tick. You
     * may also reenter the lock. Once the Promise returned by your transition
     * function resolves or rejects, this method releases the lock it acquired for
     * you.
     * @param {string} name - a name for the lock
     * @param {function(StateMachine#Key): Promise} transitionFunction
     * @returns {Promise}
     */ // NOTE(mroberts): This method is named after a Haskell function:
    // https://hackage.haskell.org/package/base-4.8.2.0/docs/Control-Exception.html#v:bracket
    StateMachine.prototype.bracket = function(name, transitionFunction) {
        var key;
        var self = this;
        function releaseLock(error) {
            if (self.hasLock(key)) {
                self.releaseLockCompletely(key);
            }
            if (error) {
                throw error;
            }
        }
        return this.takeLock(name).then(function gotKey(_key) {
            key = _key;
            return transitionFunction(key);
        }).then(function success(result) {
            releaseLock();
            return result;
        }, releaseLock);
    };
    /**
     * Check whether or not a {@link StateMachine#Key} matches the lock.
     * @param {StateMachine#Key} key
     * @returns {boolean}
     */ StateMachine.prototype.hasLock = function(key) {
        return this._lock === key;
    };
    /**
     * Preempt any pending state transitions and immediately transition to the new
     * state. If a lock name is specified, take the lock and return the
     * {@link StateMachine#Key}.
     * @param {string} newState
     * @param {?string} [name=null] - a name for the lock
     * @param {Array<*>} [payload=[]]
     * @returns {?StateMachine#Key}
     */ StateMachine.prototype.preempt = function(newState, name, payload) {
        // 1. Check that the new state is valid.
        if (!isValidTransition(this._states, this.state, newState)) {
            throw new Error("Cannot transition from \"" + this.state + "\" to \"" + newState + "\"");
        }
        // 2. Release the old lock, if any.
        var oldLock;
        if (this.isLocked) {
            oldLock = this._lock;
            this._lock = null;
        }
        // 3. Take the lock, if requested.
        var key = null;
        if (name) {
            key = this.takeLockSync(name);
        }
        // 4. If a lock wasn't requested, take a "preemption" lock in order to
        // maintain FIFO order of those taking locks.
        var preemptionKey = key ? null : this.takeLockSync('preemption');
        // 5. Transition.
        this.transition(newState, key || preemptionKey, payload);
        // 6. Preempt anyone blocked on the old lock.
        if (oldLock) {
            oldLock.resolve();
        }
        // 7. Release the "preemption" lock, if we took it.
        if (preemptionKey) {
            this.releaseLock(preemptionKey);
        }
        return key;
    };
    /**
     * Release a lock. This method succeeds only if the {@link StateMachine} is
     * still locked and has not been preempted.
     * @param {StateMachine#Key} key
     * @throws Error
     */ StateMachine.prototype.releaseLock = function(key) {
        if (!this.isLocked) {
            throw new Error("Could not release the lock for " + key.name + " because the StateMachine is not locked");
        } else if (!this.hasLock(key)) {
            throw new Error("Could not release the lock for " + key.name + " because " + this._lock.name + " has the lock");
        }
        if (key.depth === 0) {
            this._lock = null;
            key.resolve();
        } else {
            key.depth--;
        }
    };
    /**
     * Release a lock completely, even if it has been reentered. This method
     * succeeds only if the {@link StateMachine} is still locked and has not been
     * preempted.
     * @param {StateMachine#Key} key
     * @throws Error
     */ StateMachine.prototype.releaseLockCompletely = function(key) {
        if (!this.isLocked) {
            throw new Error("Could not release the lock for " + key.name + " because the StateMachine is not locked");
        } else if (!this.hasLock(key)) {
            throw new Error("Could not release the lock for " + key.name + " because " + this._lock.name + " has the lock");
        }
        key.depth = 0;
        this._lock = null;
        key.resolve();
    };
    /**
     * Take a lock, returning a Promise for the {@link StateMachine#Key}. You should
     * take a lock anytime you intend to perform asynchronous transitions. Calls to
     * this method are guaranteed to be resolved in FIFO order. You may reenter
     * a lock by passing its {@link StateMachine#Key}.
     * @param {string|StateMachine#Key} nameOrKey - a name for the lock or an
     * existing {@link StateMachine#Key}
     * @returns {Promise<object>}
     */ StateMachine.prototype.takeLock = function(nameOrKey) {
        var _this = this;
        // Reentrant lock
        if (typeof nameOrKey === 'object') {
            var key_1 = nameOrKey;
            return new Promise(function(resolve) {
                resolve(_this.takeLockSync(key_1));
            });
        }
        // New lock
        var name = nameOrKey;
        if (this.isLocked) {
            var takeLock = this.takeLock.bind(this, name);
            return this._lock.promise.then(takeLock);
        }
        return Promise.resolve(this.takeLockSync(name));
    };
    /**
     * Take a lock, returning the {@Link StateMachine#Key}. This method throws if
     * the {@link StateMachine} is locked or the wrong {@link StateMachine#Key} is
     * provided. You may reenter a lock by passing its {@link StateMachine#Key}.
     * @param {string|StateMachine#Key} nameOrKey - a name for the lock or an
     * existing {@link StateMachine#Key}
     * @returns {object}
     * @throws Error
     */ StateMachine.prototype.takeLockSync = function(nameOrKey) {
        var key = typeof nameOrKey === 'string' ? null : nameOrKey;
        var name = key ? key.name : nameOrKey;
        if (key && !this.hasLock(key) || !key && this.isLocked) {
            throw new Error("Could not take the lock for " + name + " because the lock for " + this._lock.name + " was not released");
        }
        // Reentrant lock
        if (key) {
            key.depth++;
            return key;
        }
        // New lock
        var lock = makeLock(name);
        this._lock = lock;
        return lock;
    };
    /**
     * Transition to a new state. If the {@link StateMachine} is locked, you must
     * provide the {@link StateMachine#Key}. An invalid state or the wrong
     * {@link StateMachine#Key} will throw an error.
     * @param {string} newState
     * @param {?StateMachine#Key} [key=null]
     * @param {Array<*>} [payload=[]]
     * @throws {Error}
     */ StateMachine.prototype.transition = function(newState, key, payload) {
        payload = payload || [];
        // 1. If we're locked, required the key.
        if (this.isLocked) {
            if (!key) {
                throw new Error('You must provide the key in order to ' + 'transition');
            } else if (!this.hasLock(key)) {
                throw new Error("Could not transition using the key for " + key.name + " because " + this._lock.name + " has the lock");
            }
        } else if (key) {
            throw new Error("Key provided for " + key.name + ", but the StateMachine was not locked (possibly due to preemption)");
        }
        // 2. Check that the new state is valid.
        if (!isValidTransition(this._states, this.state, newState)) {
            throw new Error("Cannot transition from \"" + this.state + "\" to \"" + newState + "\"");
        }
        // 3. Update the state and emit an event.
        this._state = newState;
        this.emit.apply(this, __spreadArray([], __read([
            'stateChanged',
            newState
        ].concat(payload))));
    };
    /**
     * Attempt to transition to a new state. Unlike {@link StateMachine#transition},
     * this method does not throw.
     * @param {string} newState
     * @param {?StateMachine#Key} [key=null]
     * @param {Array<*>} [payload=[]]
     * @returns {boolean}
     */ StateMachine.prototype.tryTransition = function(newState, key, payload) {
        try {
            this.transition(newState, key, payload);
        } catch (error) {
            return false;
        }
        return true;
    };
    /**
     * Return a Promise that resolves when the {@link StateMachine} transitions to
     * the specified state. If the {@link StateMachine} transitions such that the
     * requested state becomes unreachable, the Promise rejects.
     * @param {string} state
     * @returns {Promise<this>}
     */ StateMachine.prototype.when = function(state) {
        var _this = this;
        if (this.state === state) {
            return Promise.resolve(this);
        } else if (!isValidTransition(this._reachableStates, this.state, state)) {
            return Promise.reject(createUnreachableError(this.state, state));
        }
        return this._whenPromise(function(newState, resolve, reject) {
            if (newState === state) {
                resolve(_this);
            } else if (!isValidTransition(_this._reachableStates, newState, state)) {
                reject(createUnreachableError(newState, state));
            }
        });
    };
    return StateMachine;
}(EventEmitter);
/**
 * @event StateMachine#stateChanged
 * @param {string} newState
 */ /**
 * Check if a transition is valid.
 * @private
 * @param {Map<*, Set<*>>} graph
 * @param {*} from
 * @param {*} to
 * @returns {boolean}
 */ function isValidTransition(graph, from, to) {
    return graph.get(from).has(to);
}
/**
 * @typedef {object} StateMachine#Key
 */ function makeLock(name) {
    var lock = util.defer();
    lock.name = name;
    lock.depth = 0;
    return lock;
}
/**
 * Compute the transitive closure of a graph (i.e. what nodes are reachable from
 * where).
 * @private
 * @param {Map<*, Set<*>>} graph
 * @returns {Map<*, Set<*>>}
 */ function reachable(graph) {
    return Array.from(graph.keys()).reduce(function(newGraph, from) {
        return newGraph.set(from, reachableFrom(graph, from));
    }, new Map());
}
/**
 * Compute the Set of node reachable from a particular node in the graph.
 * @private
 * @param {Map<*, Set<*>>} graph
 * @param {*} from
 * @param {Set<*>} [to]
 * @returns {Set<*>}
 */ function reachableFrom(graph, from, to) {
    to = to || new Set();
    graph.get(from).forEach(function(node) {
        if (!to.has(node)) {
            to.add(node);
            reachableFrom(graph, node, to).forEach(to.add, to);
        }
    });
    return to;
}
function transformStates(states) {
    var newStates = new Map();
    for(var key in states){
        newStates.set(key, new Set(states[key]));
    }
    return newStates;
}
/**
 * Create an "unreachable state" Error.
 * @param {string} here
 * @param {string} there
 * @returns {Error}
 */ function createUnreachableError(here, there) {
    return new Error("\"" + there + "\" cannot be reached from \"" + here + "\"");
}
module.exports = StateMachine; //# sourceMappingURL=statemachine.js.map
}}),
"[project]/node_modules/twilio-video/es5/twilioconnection.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), buildLogLevels = _a.buildLogLevels, makeUUID = _a.makeUUID;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var NetworkMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/networkmonitor.js [app-client] (ecmascript)");
var Timeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var nInstances = 0;
/*
  TwilioConnection states
  -----------------------

       ------------------------------------------
       |                                        |
       |                                        v
  +---------+       +--------------+       +----------+
  |  early  | ----> |  connecting  | ----> |  closed  |
  +---------+       +--------------+       +----------+
    ^                     | ^ |                 ^ ^
    | --------------------- | |                 | |
    | | --------------------- |                 | |
    | | | --------------------|------------------ |
    | v | |                   v                   |
  +----------+           +--------+               |
  | waiting  | --------> |  open  | ---------------
  +----------+           +--------+
 */ var states = {
    closed: [],
    connecting: [
        'closed',
        'open',
        'waiting'
    ],
    early: [
        'closed',
        'connecting'
    ],
    open: [
        'closed'
    ],
    waiting: [
        'closed',
        'connecting',
        'early',
        'open'
    ]
};
var events = {
    closed: 'close',
    open: 'open',
    waiting: 'waiting'
};
var TCMP_VERSION = 2;
var DEFAULT_MAX_CONSECUTIVE_MISSED_HEARTBEATS = 3;
var DEFAULT_MAX_CONSECUTIVE_FAILED_HELLOS = 3;
var DEFAULT_MAX_REQUESTED_HEARTBEAT_TIMEOUT = 5000;
var DEFAULT_OPEN_TIMEOUT = 15000;
var DEFAULT_WELCOME_TIMEOUT = 5000;
var OUTGOING_HEARTBEAT_OFFSET = 200;
var WS_CLOSE_NORMAL = 1000;
var WS_CLOSE_WELCOME_TIMEOUT = 3000;
var WS_CLOSE_HEARTBEATS_MISSED = 3001;
var WS_CLOSE_HELLO_FAILED = 3002;
var WS_CLOSE_SEND_FAILED = 3003;
var WS_CLOSE_NETWORK_CHANGED = 3004;
var WS_CLOSE_BUSY_WAIT = 3005;
var WS_CLOSE_SERVER_BUSY = 3006;
var WS_CLOSE_OPEN_TIMEOUT = 3007;
// NOTE(joma): If you want to use close code 3008, please increment
// the close code in test/integration/spec/docker/reconnection.js
// line number 492.
var toplevel = globalThis;
var WebSocket = toplevel.WebSocket ? toplevel.WebSocket : __turbopack_context__.r("[project]/node_modules/twilio-video/src/ws.js [app-client] (ecmascript)");
var CloseReason = {
    BUSY: 'busy',
    FAILED: 'failed',
    LOCAL: 'local',
    REMOTE: 'remote',
    TIMEOUT: 'timeout'
};
var wsCloseCodesToCloseReasons = new Map([
    [
        WS_CLOSE_WELCOME_TIMEOUT,
        CloseReason.TIMEOUT
    ],
    [
        WS_CLOSE_HEARTBEATS_MISSED,
        CloseReason.TIMEOUT
    ],
    [
        WS_CLOSE_HELLO_FAILED,
        CloseReason.FAILED
    ],
    [
        WS_CLOSE_SEND_FAILED,
        CloseReason.FAILED
    ],
    [
        WS_CLOSE_NETWORK_CHANGED,
        CloseReason.TIMEOUT
    ],
    [
        WS_CLOSE_SERVER_BUSY,
        CloseReason.BUSY
    ],
    [
        WS_CLOSE_OPEN_TIMEOUT,
        CloseReason.TIMEOUT
    ]
]);
/**
 * A {@link TwilioConnection} represents a WebSocket connection
 * to a Twilio Connections Messaging Protocol (TCMP) server.
 * @fires TwilioConnection#close
 * @fires TwilioConnection#error
 * @fires TwilioConnection#message
 * @fires TwilioConnection#open
 * @fires TwilioConnection#waiting
 */ var TwilioConnection = function(_super) {
    __extends(TwilioConnection, _super);
    /**
     * Construct a {@link TwilioConnection}.
     * @param {string} serverUrl - TCMP server url
     * @param {TwilioConnectionOptions} options - {@link TwilioConnection} options
     */ function TwilioConnection(serverUrl, options) {
        var _this = _super.call(this, 'early', states) || this;
        options = Object.assign({
            helloBody: null,
            maxConsecutiveFailedHellos: DEFAULT_MAX_CONSECUTIVE_FAILED_HELLOS,
            maxConsecutiveMissedHeartbeats: DEFAULT_MAX_CONSECUTIVE_MISSED_HEARTBEATS,
            requestedHeartbeatTimeout: DEFAULT_MAX_REQUESTED_HEARTBEAT_TIMEOUT,
            openTimeout: DEFAULT_OPEN_TIMEOUT,
            welcomeTimeout: DEFAULT_WELCOME_TIMEOUT,
            Log: Log,
            WebSocket: WebSocket
        }, options);
        var logLevels = buildLogLevels(options.logLevel);
        var log = new options.Log('default', _this, logLevels, options.loggerName);
        var networkMonitor = options.networkMonitor ? new NetworkMonitor(function() {
            var type = networkMonitor.type;
            var reason = "Network changed" + (type ? " to " + type : '');
            log.debug(reason);
            _this._close({
                code: WS_CLOSE_NETWORK_CHANGED,
                reason: reason
            });
        }) : null;
        Object.defineProperties(_this, {
            _busyWaitTimeout: {
                value: null,
                writable: true
            },
            _consecutiveHeartbeatsMissed: {
                value: 0,
                writable: true
            },
            _cookie: {
                value: null,
                writable: true
            },
            _eventObserver: {
                value: options.eventObserver
            },
            _heartbeatTimeout: {
                value: null,
                writable: true
            },
            _hellosLeft: {
                value: options.maxConsecutiveFailedHellos,
                writable: true
            },
            _instanceId: {
                value: ++nInstances
            },
            _log: {
                value: log
            },
            _messageQueue: {
                value: []
            },
            _networkMonitor: {
                value: networkMonitor
            },
            _options: {
                value: options
            },
            _openTimeout: {
                value: null,
                writable: true
            },
            _sendHeartbeatTimeout: {
                value: null,
                writable: true
            },
            _serverUrl: {
                value: serverUrl
            },
            _welcomeTimeout: {
                value: null,
                writable: true
            },
            _ws: {
                value: null,
                writable: true
            }
        });
        var eventsToLevels = {
            connecting: 'info',
            early: 'info',
            open: 'info',
            waiting: 'warning',
            closed: 'info'
        };
        _this.on('stateChanged', function(state) {
            var args = [];
            for(var _i = 1; _i < arguments.length; _i++){
                args[_i - 1] = arguments[_i];
            }
            if (state in events) {
                _this.emit.apply(_this, __spreadArray([
                    events[state]
                ], __read(args)));
            }
            var event = {
                name: state,
                group: 'signaling',
                level: eventsToLevels[_this.state]
            };
            if (state === 'closed') {
                var _a = __read(args, 1), reason = _a[0];
                event.payload = {
                    reason: reason
                };
                event.level = reason === CloseReason.LOCAL ? 'info' : 'error';
            }
            _this._eventObserver.emit('event', event);
        });
        _this._eventObserver.emit('event', {
            name: _this.state,
            group: 'signaling',
            level: eventsToLevels[_this.state]
        });
        _this._connect();
        return _this;
    }
    TwilioConnection.prototype.toString = function() {
        return "[TwilioConnection #" + this._instanceId + ": " + this._ws.url + "]";
    };
    /**
     * Close the {@link TwilioConnection}.
     * @param {{code: number, reason: string}} event
     * @private
     */ TwilioConnection.prototype._close = function(_a) {
        var code = _a.code, reason = _a.reason;
        if (this.state === 'closed') {
            return;
        }
        if (this._openTimeout) {
            this._openTimeout.clear();
        }
        if (this._welcomeTimeout) {
            this._welcomeTimeout.clear();
        }
        if (this._heartbeatTimeout) {
            this._heartbeatTimeout.clear();
        }
        if (this._sendHeartbeatTimeout) {
            this._sendHeartbeatTimeout.clear();
        }
        if (this._networkMonitor) {
            this._networkMonitor.stop();
        }
        if (this._busyWaitTimeout && code !== WS_CLOSE_BUSY_WAIT) {
            this._busyWaitTimeout.clear();
        }
        this._messageQueue.splice(0);
        var log = this._log;
        if (code === WS_CLOSE_NORMAL) {
            log.debug('Closed');
            this.transition('closed', null, [
                CloseReason.LOCAL
            ]);
        } else {
            log.warn("Closed: " + code + " - " + reason);
            if (code !== WS_CLOSE_BUSY_WAIT) {
                this.transition('closed', null, [
                    wsCloseCodesToCloseReasons.get(code) || CloseReason.REMOTE
                ]);
            }
        }
        var readyState = this._ws.readyState;
        var WebSocket = this._options.WebSocket;
        if (readyState !== WebSocket.CLOSING && readyState !== WebSocket.CLOSED) {
            this._ws.close(code, reason);
        }
    };
    /**
     * Connect to the TCMP server.
     * @private
     */ TwilioConnection.prototype._connect = function() {
        var _this = this;
        var log = this._log;
        if (this.state === 'waiting') {
            this.transition('early');
        } else if (this.state !== 'early') {
            log.warn("Unexpected state \"" + this.state + "\" for connecting to the" + ' TCMP server.');
            return;
        }
        this._ws = new this._options.WebSocket(this._serverUrl);
        var ws = this._ws;
        log.debug('Created a new WebSocket:', ws);
        ws.addEventListener('close', function(event) {
            return _this._close(event);
        });
        var openTimeout = this._options.openTimeout;
        // Add a timeout for getting the onopen event on the WebSocket (15 sec). After that, attempt to reconnect only if this is not the first attempt.
        this._openTimeout = new Timeout(function() {
            var reason = "Failed to open in " + openTimeout + " ms";
            _this._close({
                code: WS_CLOSE_OPEN_TIMEOUT,
                reason: reason
            });
        }, openTimeout);
        ws.addEventListener('open', function() {
            log.debug('WebSocket opened:', ws);
            _this._openTimeout.clear();
            _this._startHandshake();
            if (_this._networkMonitor) {
                _this._networkMonitor.start();
            }
        });
        ws.addEventListener('message', function(message) {
            log.debug("Incoming: " + message.data);
            try {
                message = JSON.parse(message.data);
            } catch (error) {
                _this.emit('error', error);
                return;
            }
            switch(message.type){
                case 'bad':
                    _this._handleBad(message);
                    break;
                case 'busy':
                    _this._handleBusy(message);
                    break;
                case 'bye':
                    break;
                case 'msg':
                    _this._handleMessage(message);
                // NOTE(mpatwardhan): Each incoming message should be treated as an incoming
                // heartbeat intentionally falling through to 'heartbeat' case.
                // eslint-disable-next-line no-fallthrough
                case 'heartbeat':
                    _this._handleHeartbeat();
                    break;
                case 'welcome':
                    _this._handleWelcome(message);
                    break;
                default:
                    _this._log.debug("Unknown message type: " + message.type);
                    _this.emit('error', new Error("Unknown message type: " + message.type));
                    break;
            }
        });
    };
    /**
     * Handle an incoming "bad" message.
     * @param {{reason: string}} message
     * @private
     */ TwilioConnection.prototype._handleBad = function(_a) {
        var reason = _a.reason;
        var log = this._log;
        if (![
            'connecting',
            'open'
        ].includes(this.state)) {
            log.warn("Unexpected state \"" + this.state + "\" for handling a \"bad\" message" + ' from the TCMP server.');
            return;
        }
        if (this.state === 'connecting') {
            log.warn("Closing: " + WS_CLOSE_HELLO_FAILED + " - " + reason);
            this._close({
                code: WS_CLOSE_HELLO_FAILED,
                reason: reason
            });
            return;
        }
        log.debug("Error: " + reason);
        this.emit('error', new Error(reason));
    };
    /**
     * Handle an incoming "busy" message.
     * @param {{cookie: ?string, keepAlive: boolean, retryAfter: number}} message
     * @private
     */ TwilioConnection.prototype._handleBusy = function(_a) {
        var _this = this;
        var cookie = _a.cookie, keepAlive = _a.keepAlive, retryAfter = _a.retryAfter;
        var log = this._log;
        if (![
            'connecting',
            'waiting'
        ].includes(this.state)) {
            log.warn("Unexpected state \"" + this.state + "\" for handling a \"busy\" message" + ' from the TCMP server.');
            return;
        }
        if (this._busyWaitTimeout) {
            this._busyWaitTimeout.clear();
        }
        if (this._welcomeTimeout) {
            this._welcomeTimeout.clear();
        }
        var reason = retryAfter < 0 ? 'Received terminal "busy" message' : "Received \"busy\" message, retrying after " + retryAfter + " ms";
        if (retryAfter < 0) {
            log.warn("Closing: " + WS_CLOSE_SERVER_BUSY + " - " + reason);
            this._close({
                code: WS_CLOSE_SERVER_BUSY,
                reason: reason
            });
            return;
        }
        var maxConsecutiveFailedHellos = this._options.maxConsecutiveFailedHellos;
        this._hellosLeft = maxConsecutiveFailedHellos;
        this._cookie = cookie || null;
        if (keepAlive) {
            log.warn(reason);
            this._busyWaitTimeout = new Timeout(function() {
                return _this._startHandshake();
            }, retryAfter);
        } else {
            log.warn("Closing: " + WS_CLOSE_BUSY_WAIT + " - " + reason);
            this._close({
                code: WS_CLOSE_BUSY_WAIT,
                reason: reason
            });
            this._busyWaitTimeout = new Timeout(function() {
                return _this._connect();
            }, retryAfter);
        }
        this.transition('waiting', null, [
            keepAlive,
            retryAfter
        ]);
    };
    /**
     * Handle an incoming "heartbeat" message.
     * @private
     */ TwilioConnection.prototype._handleHeartbeat = function() {
        if (this.state !== 'open') {
            this._log.warn("Unexpected state \"" + this.state + "\" for handling a \"heartbeat\"" + ' message from the TCMP server.');
            return;
        }
        this._heartbeatTimeout.reset();
    };
    /**
     * Handle a missed "heartbeat" message.
     * @private
     */ TwilioConnection.prototype._handleHeartbeatTimeout = function() {
        if (this.state !== 'open') {
            return;
        }
        var log = this._log;
        var maxConsecutiveMissedHeartbeats = this._options.maxConsecutiveMissedHeartbeats;
        log.debug("Consecutive heartbeats missed: " + maxConsecutiveMissedHeartbeats);
        var reason = "Missed " + maxConsecutiveMissedHeartbeats + " \"heartbeat\" messages";
        log.warn("Closing: " + WS_CLOSE_HEARTBEATS_MISSED + " - " + reason);
        this._close({
            code: WS_CLOSE_HEARTBEATS_MISSED,
            reason: reason
        });
    };
    /**
     * Handle an incoming "msg" message.
     * @param {{body: object}} message
     * @private
     */ TwilioConnection.prototype._handleMessage = function(_a) {
        var body = _a.body;
        if (this.state !== 'open') {
            this._log.warn("Unexpected state \"" + this.state + "\" for handling a \"msg\" message" + ' from the TCMP server.');
            return;
        }
        this.emit('message', body);
    };
    /**
     * Handle an incoming "welcome" message.
     * @param {{ negotiatedTimeout: number }} message
     * @private
     */ TwilioConnection.prototype._handleWelcome = function(_a) {
        var _this = this;
        var negotiatedTimeout = _a.negotiatedTimeout;
        var log = this._log;
        if (![
            'connecting',
            'waiting'
        ].includes(this.state)) {
            log.warn("Unexpected state \"" + this.state + "\" for handling a \"welcome\"" + ' message from the TCMP server.');
            return;
        }
        if (this.state === 'waiting') {
            log.debug('Received "welcome" message, no need to retry connection.');
            this._busyWaitTimeout.clear();
        }
        var maxConsecutiveMissedHeartbeats = this._options.maxConsecutiveMissedHeartbeats;
        var heartbeatTimeout = negotiatedTimeout * maxConsecutiveMissedHeartbeats;
        var outgoingHeartbeatTimeout = negotiatedTimeout - OUTGOING_HEARTBEAT_OFFSET;
        this._welcomeTimeout.clear();
        this._heartbeatTimeout = new Timeout(function() {
            return _this._handleHeartbeatTimeout();
        }, heartbeatTimeout);
        this._messageQueue.splice(0).forEach(function(message) {
            return _this._send(message);
        });
        this._sendHeartbeatTimeout = new Timeout(function() {
            return _this._sendHeartbeat();
        }, outgoingHeartbeatTimeout);
        this.transition('open');
    };
    /**
     * Handle a missed "welcome" message.
     * @private
     */ TwilioConnection.prototype._handleWelcomeTimeout = function() {
        if (this.state !== 'connecting') {
            return;
        }
        var log = this._log;
        if (this._hellosLeft <= 0) {
            var reason = 'All handshake attempts failed';
            log.warn("Closing: " + WS_CLOSE_WELCOME_TIMEOUT + " - " + reason);
            this._close({
                code: WS_CLOSE_WELCOME_TIMEOUT,
                reason: reason
            });
            return;
        }
        var maxConsecutiveFailedHellos = this._options.maxConsecutiveFailedHellos;
        log.warn("Handshake attempt " + (maxConsecutiveFailedHellos - this._hellosLeft) + " failed");
        this._startHandshake();
    };
    /**
     * Send a message to the TCMP server.
     * @param {*} message
     * @private
     */ TwilioConnection.prototype._send = function(message) {
        var readyState = this._ws.readyState;
        var WebSocket = this._options.WebSocket;
        if (readyState === WebSocket.OPEN) {
            var data = JSON.stringify(message);
            this._log.debug("Outgoing: " + data);
            try {
                this._ws.send(data);
                if (this._sendHeartbeatTimeout) {
                    // Each outgoing message is to be treated as an outgoing heartbeat.
                    this._sendHeartbeatTimeout.reset();
                }
            } catch (error) {
                var reason = 'Failed to send message';
                this._log.warn("Closing: " + WS_CLOSE_SEND_FAILED + " - " + reason);
                this._close({
                    code: WS_CLOSE_SEND_FAILED,
                    reason: reason
                });
            }
        }
    };
    /**
     * Send a "heartbeat" message.
     * @private
     */ TwilioConnection.prototype._sendHeartbeat = function() {
        if (this.state === 'closed') {
            return;
        }
        this._send({
            type: 'heartbeat'
        });
    };
    /**
     * Send a "hello" message.
     * @private
     */ TwilioConnection.prototype._sendHello = function() {
        var _a = this._options, helloBody = _a.helloBody, timeout = _a.requestedHeartbeatTimeout;
        var hello = {
            id: makeUUID(),
            timeout: timeout,
            type: 'hello',
            version: TCMP_VERSION
        };
        if (this._cookie) {
            hello.cookie = this._cookie;
        }
        if (helloBody) {
            hello.body = helloBody;
        }
        this._send(hello);
    };
    /**
     * Send or enqueue a message.
     * @param {*} message
     * @private
     */ TwilioConnection.prototype._sendOrEnqueue = function(message) {
        var _this = this;
        if (this.state === 'closed') {
            return;
        }
        var sendOrEnqueue = this.state === 'open' ? function(message) {
            return _this._send(message);
        } : function(message) {
            return _this._messageQueue.push(message);
        };
        sendOrEnqueue(message);
    };
    /**
     * Start the TCMP handshake.
     * @private
     */ TwilioConnection.prototype._startHandshake = function() {
        var _this = this;
        if ([
            'early',
            'waiting'
        ].includes(this.state)) {
            this.transition('connecting');
        }
        if (this.state !== 'connecting') {
            return;
        }
        this._hellosLeft--;
        this._sendHello();
        var welcomeTimeout = this._options.welcomeTimeout;
        this._welcomeTimeout = new Timeout(function() {
            return _this._handleWelcomeTimeout();
        }, welcomeTimeout);
    };
    /**
     * Close the {@link TwilioConnection}.
     * @returns {void}
     */ TwilioConnection.prototype.close = function() {
        if (this.state === 'closed') {
            return;
        }
        this._sendOrEnqueue({
            type: 'bye'
        });
        this._close({
            code: WS_CLOSE_NORMAL,
            reason: 'Normal'
        });
    };
    /**
     * Send a "msg" message.
     * @param {*} body
     * @returns {void}
     */ TwilioConnection.prototype.sendMessage = function(body) {
        this._sendOrEnqueue({
            body: body,
            type: 'msg'
        });
    };
    return TwilioConnection;
}(StateMachine);
/**
 * A unique string depicting the reason for the {@link TwilioConnection} being closed.
 * @enum {string}
 */ TwilioConnection.CloseReason = CloseReason;
/**
 * A {@link TwilioConnection} was closed.
 * @event TwilioConnection#close
 * @param {CloseReason} reason - The reason for the {@link TwilioConnection} being closed
 */ /**
 * A {@link TwilioConnection} received an error from the TCMP server.
 * @event TwilioConnection#error
 * @param {Error} error - The TCMP server error
 */ /**
 * A {@link TwilioConnection} received a message from the TCMP server.
 * @event TwilioConnection#message
 * @param {*} body - Message body
 */ /**
 * A {@link TwilioConnection} completed a hello/welcome handshake with the TCMP server.
 * @event TwilioConnection#open
 */ /**
 * A {@link TwilioConnection} received a "busy" message from the TCMP server.
 * @event TwilioConnection#waiting
 * @param {boolean} keepAlive - true if the WebSocket connection is retained
 * @param {number} retryAfter - delay in milliseconds after which a retry is attempted
 */ /**
 * {@link TwilioConnection} options
 * @typedef {object} TwilioConnectionOptions
 * @property {EventObserver} [eventObserver] - Optional event observer
 * @property {*} [helloBody=null] - Optional body for "hello" message
 * @property {LogLevel} [logLevel=warn] - Log level of the {@link TwilioConnection}
 * @property {number} [maxConsecutiveFailedHellos=3] - Max. number of consecutive failed "hello"s
 * @property {number} [maxConsecutiveMissedHeartbeats=3] - Max. number of (effective) consecutive "heartbeat" messages that can be missed
 * @property {number} [requestedHeartbeatTimeout=5000] - "heartbeat" timeout (ms) requested by the {@link TwilioConnection}
 * @property {number} [welcomeTimeout=5000] - Time (ms) to wait for the "welcome" message after sending the "hello" message
 */ module.exports = TwilioConnection; //# sourceMappingURL=twilioconnection.js.map
}}),
"[project]/node_modules/twilio-video/es5/cancelableroompromise.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var CancelablePromise = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/cancelablepromise.js [app-client] (ecmascript)");
/**
 * Create a {@link CancelablePromise<Room>}.
 * @param {function(function(Array<LocalTrack>): CancelablePromise<RoomSignaling>):
 *   Promise<function(): CancelablePromise<RoomSignaling>>} getLocalTracks
 * @param {function(Array<LocalTrack>): LocalParticipant} createLocalParticipant
 * @param {function(Array<LocalTrack>): CancelablePromise<RoomSignaling>} createRoomSignaling
 * @param {function(LocalParticipant, RoomSignaling): Room} createRoom
 * @returns CancelablePromise<Room>
 */ function createCancelableRoomPromise(getLocalTracks, createLocalParticipant, createRoomSignaling, createRoom) {
    var cancelableRoomSignalingPromise;
    var cancellationError = new Error('Canceled');
    return new CancelablePromise(function onCreate(resolve, reject, isCanceled) {
        var localParticipant;
        getLocalTracks(function getLocalTracksSucceeded(localTracks) {
            if (isCanceled()) {
                return CancelablePromise.reject(cancellationError);
            }
            localParticipant = createLocalParticipant(localTracks);
            return createRoomSignaling(localParticipant).then(function createRoomSignalingSucceeded(getCancelableRoomSignalingPromise) {
                if (isCanceled()) {
                    throw cancellationError;
                }
                cancelableRoomSignalingPromise = getCancelableRoomSignalingPromise();
                return cancelableRoomSignalingPromise;
            });
        }).then(function roomSignalingConnected(roomSignaling) {
            if (isCanceled()) {
                roomSignaling.disconnect();
                throw cancellationError;
            }
            resolve(createRoom(localParticipant, roomSignaling));
        }).catch(function onError(error) {
            reject(error);
        });
    }, function onCancel() {
        if (cancelableRoomSignalingPromise) {
            cancelableRoomSignalingPromise.cancel();
        }
    });
}
module.exports = createCancelableRoomPromise; //# sourceMappingURL=cancelableroompromise.js.map
}}),
"[project]/node_modules/twilio-video/es5/encodingparameters.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
/**
 * {@link EncodingParametersImpl} represents an object which notifies its
 * listeners of any changes in the values of its properties.
 * @extends EventEmitter
 * @implements EncodingParameters
 * @emits EncodingParametersImpl#changed
 * @property {?number} maxAudioBitrate
 * @property {?number} maxVideoBitrate
 */ var EncodingParametersImpl = function(_super) {
    __extends(EncodingParametersImpl, _super);
    /**
     * Construct an {@link EncodingParametersImpl}.
     * @param {EncodingParamters} encodingParameters - Initial {@link EncodingParameters}
     * @param {Boolean} adaptiveSimulcast - true if adaptive simulcast was enabled by connect options.
     */ function EncodingParametersImpl(encodingParameters, adaptiveSimulcast) {
        var _this = _super.call(this) || this;
        encodingParameters = Object.assign({
            maxAudioBitrate: null,
            maxVideoBitrate: null
        }, encodingParameters);
        Object.defineProperties(_this, {
            maxAudioBitrate: {
                value: encodingParameters.maxAudioBitrate,
                writable: true
            },
            maxVideoBitrate: {
                value: encodingParameters.maxVideoBitrate,
                writable: true
            },
            adaptiveSimulcast: {
                value: adaptiveSimulcast
            }
        });
        return _this;
    }
    /**
     * Returns the bitrate values in an {@link EncodingParameters}.
     * @returns {EncodingParameters}
     */ EncodingParametersImpl.prototype.toJSON = function() {
        return {
            maxAudioBitrate: this.maxAudioBitrate,
            maxVideoBitrate: this.maxVideoBitrate
        };
    };
    /**
     * Update the bitrate values with those in the given {@link EncodingParameters}.
     * @param {EncodingParameters} encodingParameters - The new {@link EncodingParameters}
     * @fires EncodingParametersImpl#changed
     */ EncodingParametersImpl.prototype.update = function(encodingParameters) {
        var _this = this;
        encodingParameters = Object.assign({
            maxAudioBitrate: this.maxAudioBitrate,
            maxVideoBitrate: this.maxVideoBitrate
        }, encodingParameters);
        var shouldEmitChanged = [
            'maxAudioBitrate',
            'maxVideoBitrate'
        ].reduce(function(shouldEmitChanged, maxKindBitrate) {
            if (_this[maxKindBitrate] !== encodingParameters[maxKindBitrate]) {
                _this[maxKindBitrate] = encodingParameters[maxKindBitrate];
                shouldEmitChanged = true;
            }
            return shouldEmitChanged;
        }, false);
        if (shouldEmitChanged) {
            this.emit('changed');
        }
    };
    return EncodingParametersImpl;
}(EventEmitter);
/**
 * At least one of the {@link EncodingParametersImpl}'s bitrate values changed.
 * @event EncodingParametersImpl#changed
 */ module.exports = EncodingParametersImpl; //# sourceMappingURL=encodingparameters.js.map
}}),
"[project]/node_modules/twilio-video/es5/participant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)");
var RemoteAudioTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remoteaudiotrack.js [app-client] (ecmascript)");
var RemoteAudioTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remoteaudiotrackpublication.js [app-client] (ecmascript)");
var RemoteDataTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotedatatrack.js [app-client] (ecmascript)");
var RemoteDataTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotedatatrackpublication.js [app-client] (ecmascript)");
var RemoteVideoTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotevideotrack.js [app-client] (ecmascript)");
var RemoteVideoTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotevideotrackpublication.js [app-client] (ecmascript)");
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)");
var nInstances = 0;
/**
 * {@link NetworkQualityLevel} is a value from 0–5, inclusive, representing the
 * quality of a network connection.
 * @typedef {number} NetworkQualityLevel
 */ /**
 * @extends EventEmitter
 * @property {Map<Track.SID, AudioTrackPublication>} audioTracks -
 *    The {@link Participant}'s {@link AudioTrackPublication}s
 * @property {Map<Track.SID, DataTrackPublication>} dataTracks -
 *    The {@link Participant}'s {@link DataTrackPublication}s.
 * @property {Participant.Identity} identity - The identity of the {@link Participant}
 * @property {?NetworkQualityLevel} networkQualityLevel - The
 *    {@link Participant}'s current {@link NetworkQualityLevel}, if any
 * @property {?NetworkQualityStats} networkQualityStats - The
 *    {@link Participant}'s current {@link NetworkQualityStats}, if any
 * @property {Participant.SID} sid - The {@link Participant}'s SID
 * @property {string} state - "connected", "disconnected" or "reconnecting"
 * @property {Map<Track.SID, TrackPublication>} tracks -
 *    The {@link Participant}'s {@link TrackPublication}s
 * @property {Map<Track.SID, VideoTrackPublication>} videoTracks -
 *    The {@link Participant}'s {@link VideoTrackPublication}s
 * @emits Participant#disconnected
 * @emits Participant#networkQualityLevelChanged
 * @emits Participant#reconnected
 * @emits Participant#reconnecting
 * @emits Participant#trackDimensionsChanged
 * @emits Participant#trackStarted
 */ var Participant = function(_super) {
    __extends(Participant, _super);
    /**
     * Construct a {@link Participant}.
     * @param {ParticipantSignaling} signaling
     * @param {object} [options]
     */ function Participant(signaling, options) {
        var _this = _super.call(this) || this;
        options = Object.assign({
            RemoteAudioTrack: RemoteAudioTrack,
            RemoteAudioTrackPublication: RemoteAudioTrackPublication,
            RemoteDataTrack: RemoteDataTrack,
            RemoteDataTrackPublication: RemoteDataTrackPublication,
            RemoteVideoTrack: RemoteVideoTrack,
            RemoteVideoTrackPublication: RemoteVideoTrackPublication,
            tracks: []
        }, options);
        var indexed = indexTracksById(options.tracks);
        var log = options.log.createLog('default', _this);
        var audioTracks = new Map(indexed.audioTracks);
        var dataTracks = new Map(indexed.dataTracks);
        var tracks = new Map(indexed.tracks);
        var videoTracks = new Map(indexed.videoTracks);
        Object.defineProperties(_this, {
            _RemoteAudioTrack: {
                value: options.RemoteAudioTrack
            },
            _RemoteAudioTrackPublication: {
                value: options.RemoteAudioTrackPublication
            },
            _RemoteDataTrack: {
                value: options.RemoteDataTrack
            },
            _RemoteDataTrackPublication: {
                value: options.RemoteDataTrackPublication
            },
            _RemoteVideoTrack: {
                value: options.RemoteVideoTrack
            },
            _RemoteVideoTrackPublication: {
                value: options.RemoteVideoTrackPublication
            },
            _audioTracks: {
                value: audioTracks
            },
            _dataTracks: {
                value: dataTracks
            },
            _instanceId: {
                value: ++nInstances
            },
            _clientTrackSwitchOffControl: {
                value: options.clientTrackSwitchOffControl
            },
            _contentPreferencesMode: {
                value: options.contentPreferencesMode
            },
            _log: {
                value: log
            },
            _signaling: {
                value: signaling
            },
            _tracks: {
                value: tracks
            },
            _trackEventReemitters: {
                value: new Map()
            },
            _trackPublicationEventReemitters: {
                value: new Map()
            },
            _trackSignalingUpdatedEventCallbacks: {
                value: new Map()
            },
            _videoTracks: {
                value: videoTracks
            },
            audioTracks: {
                enumerable: true,
                value: new Map()
            },
            dataTracks: {
                enumerable: true,
                value: new Map()
            },
            identity: {
                enumerable: true,
                get: function() {
                    return signaling.identity;
                }
            },
            networkQualityLevel: {
                enumerable: true,
                get: function() {
                    return signaling.networkQualityLevel;
                }
            },
            networkQualityStats: {
                enumerable: true,
                get: function() {
                    return signaling.networkQualityStats;
                }
            },
            sid: {
                enumerable: true,
                get: function() {
                    return signaling.sid;
                }
            },
            state: {
                enumerable: true,
                get: function() {
                    return signaling.state;
                }
            },
            tracks: {
                enumerable: true,
                value: new Map()
            },
            videoTracks: {
                enumerable: true,
                value: new Map()
            },
            _MediaStream: {
                value: options.MediaStream
            },
            _mapMediaElement: {
                value: options.mapMediaElement
            },
            _disposeMediaElement: {
                value: options.disposeMediaElement
            }
        });
        _this._tracks.forEach(reemitTrackEvents.bind(null, _this));
        signaling.on('networkQualityLevelChanged', function() {
            return _this.emit('networkQualityLevelChanged', _this.networkQualityLevel, _this.networkQualityStats && (_this.networkQualityStats.audio || _this.networkQualityStats.video) ? _this.networkQualityStats : null);
        });
        reemitSignalingStateChangedEvents(_this, signaling);
        log.info("Created a new Participant" + (_this.identity ? ": " + _this.identity : ''));
        return _this;
    }
    /**
     * Get the {@link RemoteTrack} events to re-emit.
     * @private
     * @returns {Array<Array<string>>} events
     */ Participant.prototype._getTrackEvents = function() {
        return [
            [
                'dimensionsChanged',
                'trackDimensionsChanged'
            ],
            [
                'message',
                'trackMessage'
            ],
            [
                'started',
                'trackStarted'
            ]
        ];
    };
    /**
     * @private
     */ Participant.prototype._getTrackPublicationEvents = function() {
        return [];
    };
    Participant.prototype.toString = function() {
        return "[Participant #" + this._instanceId + ": " + this.sid + "]";
    };
    /**
     * @private
     * @param {RemoteTrack} track
     * @param {Track.ID} id
     * @returns {?RemoteTrack}
     */ Participant.prototype._addTrack = function(track, id) {
        var log = this._log;
        if (this._tracks.has(id)) {
            return null;
        }
        this._tracks.set(id, track);
        var tracksByKind = {
            audio: this._audioTracks,
            video: this._videoTracks,
            data: this._dataTracks
        }[track.kind];
        tracksByKind.set(id, track);
        reemitTrackEvents(this, track, id);
        log.info("Added a new " + util.trackClass(track) + ":", id);
        log.debug(util.trackClass(track) + ":", track);
        return track;
    };
    /**
     * @private
     * @param {RemoteTrackPublication} publication
     * @returns {?RemoteTrackPublication}
     */ Participant.prototype._addTrackPublication = function(publication) {
        var log = this._log;
        if (this.tracks.has(publication.trackSid)) {
            return null;
        }
        this.tracks.set(publication.trackSid, publication);
        var trackPublicationsByKind = {
            audio: this.audioTracks,
            data: this.dataTracks,
            video: this.videoTracks
        }[publication.kind];
        trackPublicationsByKind.set(publication.trackSid, publication);
        reemitTrackPublicationEvents(this, publication);
        log.info("Added a new " + util.trackPublicationClass(publication) + ":", publication.trackSid);
        log.debug(util.trackPublicationClass(publication) + ":", publication);
        return publication;
    };
    /**
     * @private
     */ Participant.prototype._handleTrackSignalingEvents = function() {
        var _a = this, log = _a._log, clientTrackSwitchOffControl = _a._clientTrackSwitchOffControl, contentPreferencesMode = _a._contentPreferencesMode, MediaStream = _a._MediaStream, mapMediaElement = _a._mapMediaElement, disposeMediaElement = _a._disposeMediaElement;
        var self = this;
        if (this.state === 'disconnected') {
            return;
        }
        var RemoteAudioTrack = this._RemoteAudioTrack;
        var RemoteAudioTrackPublication = this._RemoteAudioTrackPublication;
        var RemoteVideoTrack = this._RemoteVideoTrack;
        var RemoteVideoTrackPublication = this._RemoteVideoTrackPublication;
        var RemoteDataTrack = this._RemoteDataTrack;
        var RemoteDataTrackPublication = this._RemoteDataTrackPublication;
        var participantSignaling = this._signaling;
        function trackSignalingAdded(signaling) {
            var RemoteTrackPublication = {
                audio: RemoteAudioTrackPublication,
                data: RemoteDataTrackPublication,
                video: RemoteVideoTrackPublication
            }[signaling.kind];
            var publication = new RemoteTrackPublication(signaling, {
                log: log
            });
            self._addTrackPublication(publication);
            var isSubscribed = signaling.isSubscribed;
            if (isSubscribed) {
                trackSignalingSubscribed(signaling);
            }
            self._trackSignalingUpdatedEventCallbacks.set(signaling.sid, function() {
                if (isSubscribed !== signaling.isSubscribed) {
                    isSubscribed = signaling.isSubscribed;
                    if (isSubscribed) {
                        trackSignalingSubscribed(signaling);
                        return;
                    }
                    trackSignalingUnsubscribed(signaling);
                }
            });
            signaling.on('updated', self._trackSignalingUpdatedEventCallbacks.get(signaling.sid));
        }
        function trackSignalingRemoved(signaling) {
            if (signaling.isSubscribed) {
                signaling.setTrackTransceiver(null);
            }
            var updated = self._trackSignalingUpdatedEventCallbacks.get(signaling.sid);
            if (updated) {
                signaling.removeListener('updated', updated);
                self._trackSignalingUpdatedEventCallbacks.delete(signaling.sid);
            }
            var publication = self.tracks.get(signaling.sid);
            if (publication) {
                self._removeTrackPublication(publication);
            }
        }
        function trackSignalingSubscribed(signaling) {
            var isEnabled = signaling.isEnabled, name = signaling.name, kind = signaling.kind, sid = signaling.sid, trackTransceiver = signaling.trackTransceiver, isSwitchedOff = signaling.isSwitchedOff;
            var RemoteTrack = {
                audio: RemoteAudioTrack,
                video: RemoteVideoTrack,
                data: RemoteDataTrack
            }[kind];
            var publication = self.tracks.get(sid);
            // NOTE(mroberts): It should never be the case that the TrackSignaling and
            // MediaStreamTrack or DataTrackReceiver kinds disagree; however, just in
            // case, we handle it here.
            if (!RemoteTrack || kind !== trackTransceiver.kind) {
                return;
            }
            var options = {
                log: log,
                name: name,
                clientTrackSwitchOffControl: clientTrackSwitchOffControl,
                contentPreferencesMode: contentPreferencesMode,
                mapMediaElement: mapMediaElement,
                disposeMediaElement: disposeMediaElement
            };
            if (MediaStream) {
                options.MediaStream = MediaStream;
            }
            var setPriority = function(newPriority) {
                return participantSignaling.updateSubscriberTrackPriority(sid, newPriority);
            };
            var setRenderHint = function(renderHint) {
                if (signaling.isSubscribed) {
                    participantSignaling.updateTrackRenderHint(sid, renderHint);
                }
            };
            var track = kind === 'data' ? new RemoteTrack(sid, trackTransceiver, options) : new RemoteTrack(sid, trackTransceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options);
            self._addTrack(track, publication, trackTransceiver.id);
        }
        function trackSignalingUnsubscribed(signaling) {
            var _a = __read(Array.from(self._tracks.entries()).find(function(_a) {
                var _b = __read(_a, 2), track = _b[1];
                return track.sid === signaling.sid;
            }), 2), id = _a[0], track = _a[1];
            var publication = self.tracks.get(signaling.sid);
            if (track) {
                self._removeTrack(track, publication, id);
            }
        }
        participantSignaling.on('trackAdded', trackSignalingAdded);
        participantSignaling.on('trackRemoved', trackSignalingRemoved);
        participantSignaling.tracks.forEach(trackSignalingAdded);
        participantSignaling.on('stateChanged', function stateChanged(state) {
            if (state === 'disconnected') {
                log.debug('Removing event listeners');
                participantSignaling.removeListener('stateChanged', stateChanged);
                participantSignaling.removeListener('trackAdded', trackSignalingAdded);
                participantSignaling.removeListener('trackRemoved', trackSignalingRemoved);
            } else if (state === 'connected') {
                // NOTE(mmalavalli): Any transition to "connected" here is a result of
                // successful signaling reconnection, and not a first-time establishment
                // of the signaling connection.
                log.info('reconnected');
                // NOTE(mpatwardhan): `stateChanged` can get emitted with StateMachine locked.
                // Do not signal  public events synchronously with lock held.
                setTimeout(function() {
                    return self.emit('reconnected');
                }, 0);
            }
        });
    };
    /**
     * @private
     * @param {RemoteTrack} track
     * @param {Track.ID} id
     * @returns {?RemoteTrack}
     */ Participant.prototype._removeTrack = function(track, id) {
        if (!this._tracks.has(id)) {
            return null;
        }
        this._tracks.delete(id);
        var tracksByKind = {
            audio: this._audioTracks,
            video: this._videoTracks,
            data: this._dataTracks
        }[track.kind];
        tracksByKind.delete(id);
        var reemitters = this._trackEventReemitters.get(id) || new Map();
        reemitters.forEach(function(reemitter, event) {
            track.removeListener(event, reemitter);
        });
        var log = this._log;
        log.info("Removed a " + util.trackClass(track) + ":", id);
        log.debug(util.trackClass(track) + ":", track);
        return track;
    };
    /**
     * @private
     * @param {RemoteTrackPublication} publication
     * @returns {?RemoteTrackPublication}
     */ Participant.prototype._removeTrackPublication = function(publication) {
        publication = this.tracks.get(publication.trackSid);
        if (!publication) {
            return null;
        }
        this.tracks.delete(publication.trackSid);
        var trackPublicationsByKind = {
            audio: this.audioTracks,
            data: this.dataTracks,
            video: this.videoTracks
        }[publication.kind];
        trackPublicationsByKind.delete(publication.trackSid);
        var reemitters = this._trackPublicationEventReemitters.get(publication.trackSid) || new Map();
        reemitters.forEach(function(reemitter, event) {
            publication.removeListener(event, reemitter);
        });
        var log = this._log;
        log.info("Removed a " + util.trackPublicationClass(publication) + ":", publication.trackSid);
        log.debug(util.trackPublicationClass(publication) + ":", publication);
        return publication;
    };
    Participant.prototype.toJSON = function() {
        return util.valueToJSON(this);
    };
    return Participant;
}(EventEmitter);
/**
 * A {@link Participant.SID} is a 34-character string starting with "PA"
 * that uniquely identifies a {@link Participant}.
 * @type string
 * @typedef Participant.SID
 */ /**
 * A {@link Participant.Identity} is a string that identifies a
 * {@link Participant}. You can think of it like a name.
 * @typedef {string} Participant.Identity
 */ /**
 * The {@link Participant} has disconnected.
 * @param {Participant} participant - The {@link Participant} that disconnected.
 * @event Participant#disconnected
 */ /**
 * The {@link Participant}'s {@link NetworkQualityLevel} changed.
 * @param {NetworkQualityLevel} networkQualityLevel - The new
 *   {@link NetworkQualityLevel}
 * @param {?NetworkQualityStats} networkQualityStats - The {@link NetworkQualityStats}
 *   based on which {@link NetworkQualityLevel} is calculated, if any
 * @event Participant#networkQualityLevelChanged
 */ /**
 * The {@link Participant} has reconnected to the {@link Room} after a signaling connection disruption.
 * @event Participant#reconnected
 */ /**
 * The {@link Participant} is reconnecting to the {@link Room} after a signaling connection disruption.
 * @event Participant#reconnecting
 */ /**
 * One of the {@link Participant}'s {@link VideoTrack}'s dimensions changed.
 * @param {VideoTrack} track - The {@link VideoTrack} whose dimensions changed
 * @event Participant#trackDimensionsChanged
 */ /**
 * One of the {@link Participant}'s {@link Track}s started.
 * @param {Track} track - The {@link Track} that started
 * @event Participant#trackStarted
 */ /**
 * Indexed {@link Track}s by {@link Track.ID}.
 * @typedef {object} IndexedTracks
 * @property {Array<{0: Track.ID, 1: AudioTrack}>} audioTracks - Indexed
 *   {@link AudioTrack}s
 * @property {Array<{0: Track.ID, 1: DataTrack}>} dataTracks - Indexed
 *   {@link DataTrack}s
 * @property {Array<{0: Track.ID, 1: Track}>} tracks - Indexed {@link Track}s
 * @property {Array<{0: Track.ID, 1: VideoTrack}>} videoTracks - Indexed
 *   {@link VideoTrack}s
 * @private
 */ /**
 * Index tracks by {@link Track.ID}.
 * @param {Array<Track>} tracks
 * @returns {IndexedTracks}
 * @private
 */ function indexTracksById(tracks) {
    var indexedTracks = tracks.map(function(track) {
        return [
            track.id,
            track
        ];
    });
    var indexedAudioTracks = indexedTracks.filter(function(keyValue) {
        return keyValue[1].kind === 'audio';
    });
    var indexedVideoTracks = indexedTracks.filter(function(keyValue) {
        return keyValue[1].kind === 'video';
    });
    var indexedDataTracks = indexedTracks.filter(function(keyValue) {
        return keyValue[1].kind === 'data';
    });
    return {
        audioTracks: indexedAudioTracks,
        dataTracks: indexedDataTracks,
        tracks: indexedTracks,
        videoTracks: indexedVideoTracks
    };
}
/**
 * Re-emit {@link ParticipantSignaling} 'stateChanged' events.
 * @param {Participant} participant
 * @param {ParticipantSignaling} signaling
 * @private
 */ function reemitSignalingStateChangedEvents(participant, signaling) {
    var log = participant._log;
    if (participant.state === 'disconnected') {
        return;
    }
    // Reemit state transition events from the ParticipantSignaling.
    signaling.on('stateChanged', function stateChanged(state) {
        log.debug('Transitioned to state:', state);
        participant.emit(state, participant);
        if (state === 'disconnected') {
            log.debug('Removing Track event reemitters');
            signaling.removeListener('stateChanged', stateChanged);
            participant._tracks.forEach(function(track) {
                var reemitters = participant._trackEventReemitters.get(track.id);
                if (track && reemitters) {
                    reemitters.forEach(function(reemitter, event) {
                        track.removeListener(event, reemitter);
                    });
                }
            });
            // eslint-disable-next-line no-warning-comments
            // TODO(joma): Removing this introduced unit test failures in the RemoteParticipant.
            // Investigate further before removing.
            signaling.tracks.forEach(function(trackSignaling) {
                var track = participant._tracks.get(trackSignaling.id);
                var reemitters = participant._trackEventReemitters.get(trackSignaling.id);
                if (track && reemitters) {
                    reemitters.forEach(function(reemitter, event) {
                        track.removeListener(event, reemitter);
                    });
                }
            });
            participant._trackEventReemitters.clear();
            participant.tracks.forEach(function(publication) {
                participant._trackPublicationEventReemitters.get(publication.trackSid).forEach(function(reemitter, event) {
                    publication.removeListener(event, reemitter);
                });
            });
            participant._trackPublicationEventReemitters.clear();
        }
    });
}
/**
 * Re-emit {@link Track} events.
 * @param {Participant} participant
 * @param {Track} track
 * @param {Track.ID} id
 * @private
 */ function reemitTrackEvents(participant, track, id) {
    var trackEventReemitters = new Map();
    if (participant.state === 'disconnected') {
        return;
    }
    participant._getTrackEvents().forEach(function(eventPair) {
        var trackEvent = eventPair[0];
        var participantEvent = eventPair[1];
        trackEventReemitters.set(trackEvent, function() {
            var args = [
                participantEvent
            ].concat([].slice.call(arguments));
            return participant.emit.apply(participant, __spreadArray([], __read(args)));
        });
        track.on(trackEvent, trackEventReemitters.get(trackEvent));
    });
    participant._trackEventReemitters.set(id, trackEventReemitters);
}
/**
 * Re-emit {@link TrackPublication} events.
 * @private
 * @param {Participant} participant
 * @param {TrackPublication} publication
 */ function reemitTrackPublicationEvents(participant, publication) {
    var publicationEventReemitters = new Map();
    if (participant.state === 'disconnected') {
        return;
    }
    participant._getTrackPublicationEvents().forEach(function(_a) {
        var _b = __read(_a, 2), publicationEvent = _b[0], participantEvent = _b[1];
        publicationEventReemitters.set(publicationEvent, function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            participant.emit.apply(participant, __spreadArray(__spreadArray([
                participantEvent
            ], __read(args)), [
                publication
            ]));
        });
        publication.on(publicationEvent, publicationEventReemitters.get(publicationEvent));
    });
    participant._trackPublicationEventReemitters.set(publication.trackSid, publicationEventReemitters);
}
module.exports = Participant; //# sourceMappingURL=participant.js.map
}}),
"[project]/node_modules/twilio-video/es5/localparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaStreamTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)").MediaStreamTrack;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), asLocalTrack = _a.asLocalTrack, asLocalTrackPublication = _a.asLocalTrackPublication, trackClass = _a.trackClass;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), E = _b.typeErrors, trackPriority = _b.trackPriority;
var validateLocalTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/validate.js [app-client] (ecmascript)").validateLocalTrack;
var _c = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)"), LocalAudioTrack = _c.LocalAudioTrack, LocalDataTrack = _c.LocalDataTrack, LocalVideoTrack = _c.LocalVideoTrack;
var LocalAudioTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localaudiotrackpublication.js [app-client] (ecmascript)");
var LocalDataTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localdatatrackpublication.js [app-client] (ecmascript)");
var LocalVideoTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localvideotrackpublication.js [app-client] (ecmascript)");
var Participant = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/participant.js [app-client] (ecmascript)");
/**
 * A {@link LocalParticipant} represents the local {@link Participant} in a
 * {@link Room}.
 * @extends Participant
 * @property {Map<Track.SID, LocalAudioTrackPublication>} audioTracks -
 *    The {@link LocalParticipant}'s {@link LocalAudioTrackPublication}s
 * @property {Map<Track.SID, LocalDataTrackPublication>} dataTracks -
 *    The {@link LocalParticipant}'s {@link LocalDataTrackPublication}s
 * @property {Map<Track.SID, LocalTrackPublication>} tracks -
 *    The {@link LocalParticipant}'s {@link LocalTrackPublication}s
 * @property {Map<Track.SID, LocalVideoTrackPublication>} videoTracks -
 *    The {@link LocalParticipant}'s {@link LocalVideoTrackPublication}s
 * @property {string} signalingRegion - The geographical region of the
 *     signaling edge the {@link LocalParticipant} is connected to.
 *
 * @emits RemoteParticipant#reconnected
 * @emits RemoteParticipant#reconnecting
 * @emits LocalParticipant#trackDimensionsChanged
 * @emits LocalParticipant#trackDisabled
 * @emits LocalParticipant#trackEnabled
 * @emits LocalParticipant#trackPublicationFailed
 * @emits LocalParticipant#trackPublished
 * @emits LocalParticipant#trackStarted
 * @emits LocalParticipant#trackStopped
 * @emits LocalParticipant#trackWarning
 * @emits LocalParticipant#trackWarningsCleared
 */ var LocalParticipant = function(_super) {
    __extends(LocalParticipant, _super);
    /**
     * Construct a {@link LocalParticipant}.
     * @param {ParticipantSignaling} signaling
     * @param {Array<LocalTrack>} localTracks
     * @param {Object} options
     */ function LocalParticipant(signaling, localTracks, options) {
        var _this = this;
        options = Object.assign({
            LocalAudioTrack: LocalAudioTrack,
            LocalVideoTrack: LocalVideoTrack,
            LocalDataTrack: LocalDataTrack,
            MediaStreamTrack: MediaStreamTrack,
            LocalAudioTrackPublication: LocalAudioTrackPublication,
            LocalVideoTrackPublication: LocalVideoTrackPublication,
            LocalDataTrackPublication: LocalDataTrackPublication,
            shouldStopLocalTracks: false,
            tracks: localTracks
        }, options);
        var tracksToStop = options.shouldStopLocalTracks ? new Set(localTracks.filter(function(localTrack) {
            return localTrack.kind !== 'data';
        })) : new Set();
        _this = _super.call(this, signaling, options) || this;
        Object.defineProperties(_this, {
            _eventObserver: {
                value: options.eventObserver
            },
            _LocalAudioTrack: {
                value: options.LocalAudioTrack
            },
            _LocalDataTrack: {
                value: options.LocalDataTrack
            },
            _LocalVideoTrack: {
                value: options.LocalVideoTrack
            },
            _MediaStreamTrack: {
                value: options.MediaStreamTrack
            },
            _LocalAudioTrackPublication: {
                value: options.LocalAudioTrackPublication
            },
            _LocalDataTrackPublication: {
                value: options.LocalDataTrackPublication
            },
            _LocalVideoTrackPublication: {
                value: options.LocalVideoTrackPublication
            },
            _tracksToStop: {
                value: tracksToStop
            },
            signalingRegion: {
                enumerable: true,
                get: function() {
                    return signaling.signalingRegion;
                }
            }
        });
        _this._handleTrackSignalingEvents();
        return _this;
    }
    /**
     * @private
     * @param {LocalTrack} track
     * @param {Track.ID} id
     * @param {Track.Priority} priority
     * @returns {?LocalTrack}
     */ LocalParticipant.prototype._addTrack = function(track, id, priority) {
        var addedTrack = _super.prototype._addTrack.call(this, track, id);
        if (addedTrack && this.state !== 'disconnected') {
            this._addLocalTrack(track, priority);
        }
        return addedTrack;
    };
    /**
     * @private
     * @param {LocalTrack} track
     * @param {Track.Priority} priority
     * @returns {void}
     */ LocalParticipant.prototype._addLocalTrack = function(track, priority) {
        var _a;
        // check if track has noise cancellation enabled.
        var vendor = (_a = track.noiseCancellation) === null || _a === void 0 ? void 0 : _a.vendor;
        this._signaling.addTrack(track._trackSender, track.name, priority, vendor);
        this._log.info("Added a new " + trackClass(track, true) + ":", track.id);
        this._log.debug(trackClass(track, true) + ":", track);
    };
    /**
     * @private
     * @param {LocalTrack} track
     * @param {Track.ID} id
     * @returns {?LocalTrack}
     */ LocalParticipant.prototype._removeTrack = function(track, id) {
        var removedTrack = _super.prototype._removeTrack.call(this, track, id);
        if (removedTrack && this.state !== 'disconnected') {
            this._signaling.removeTrack(track._trackSender);
            this._log.info("Removed a " + trackClass(track, true) + ":", track.id);
            this._log.debug(trackClass(track, true) + ":", track);
        }
        return removedTrack;
    };
    /**
     * Get the {@link LocalTrack} events to re-emit.
     * @private
     * @returns {Array<Array<string>>} events
     */ LocalParticipant.prototype._getTrackEvents = function() {
        return _super.prototype._getTrackEvents.call(this).concat([
            [
                'disabled',
                'trackDisabled'
            ],
            [
                'enabled',
                'trackEnabled'
            ],
            [
                'stopped',
                'trackStopped'
            ]
        ]);
    };
    LocalParticipant.prototype.toString = function() {
        return "[LocalParticipant #" + this._instanceId + (this.sid ? ": " + this.sid : '') + "]";
    };
    /**
     * @private
     */ LocalParticipant.prototype._handleTrackSignalingEvents = function() {
        var _this = this;
        var log = this._log;
        if (this.state === 'disconnected') {
            return;
        }
        var localTrackDisabled = function(localTrack) {
            var trackSignaling = _this._signaling.getPublication(localTrack._trackSender);
            if (trackSignaling) {
                trackSignaling.disable();
                log.debug("Disabled the " + trackClass(localTrack, true) + ":", localTrack.id);
            }
        };
        var localTrackEnabled = function(localTrack) {
            var trackSignaling = _this._signaling.getPublication(localTrack._trackSender);
            if (trackSignaling) {
                trackSignaling.enable();
                log.debug("Enabled the " + trackClass(localTrack, true) + ":", localTrack.id);
            }
        };
        var localTrackStopped = function(localTrack) {
            // NOTE(mroberts): We shouldn't need to check for `stop`, since DataTracks
            // do not emit "stopped".
            var trackSignaling = _this._signaling.getPublication(localTrack._trackSender);
            if (trackSignaling) {
                trackSignaling.stop();
            }
            return trackSignaling;
        };
        var stateChanged = function(state) {
            log.debug('Transitioned to state:', state);
            if (state === 'disconnected') {
                log.debug('Removing LocalTrack event listeners');
                _this._signaling.removeListener('stateChanged', stateChanged);
                _this.removeListener('trackDisabled', localTrackDisabled);
                _this.removeListener('trackEnabled', localTrackEnabled);
                _this.removeListener('trackStopped', localTrackStopped);
                // NOTE(mmalavalli): Remove the stale MediaTrackSender clones so that we
                // do not call replaceTrack() on their RTCRtpSenders.
                _this._tracks.forEach(function(track) {
                    var trackSignaling = localTrackStopped(track);
                    if (trackSignaling) {
                        track._trackSender.removeClone(trackSignaling._trackTransceiver);
                    }
                });
                log.info("LocalParticipant disconnected. Stopping " + _this._tracksToStop.size + " automatically-acquired LocalTracks");
                _this._tracksToStop.forEach(function(track) {
                    track.stop();
                });
            } else if (state === 'connected') {
                // NOTE(mmalavalli): Any transition to "connected" here is a result of
                // successful signaling reconnection, and not a first-time establishment
                // of the signaling connection.
                log.info('reconnected');
                // NOTE(mpatwardhan): `stateChanged` can get emitted with StateMachine locked.
                // Do not signal  public events synchronously with lock held.
                setTimeout(function() {
                    return _this.emit('reconnected');
                }, 0);
            }
        };
        this.on('trackDisabled', localTrackDisabled);
        this.on('trackEnabled', localTrackEnabled);
        this.on('trackStopped', localTrackStopped);
        this._signaling.on('stateChanged', stateChanged);
        this._tracks.forEach(function(track) {
            _this._addLocalTrack(track, trackPriority.PRIORITY_STANDARD);
            _this._getOrCreateLocalTrackPublication(track).catch(function(error) {
                // Just log a warning for now.
                log.warn("Failed to get or create LocalTrackPublication for " + track + ":", error);
            });
        });
    };
    /**
     * @private
     * @param {LocalTrack} localTrack
     * @returns {Promise<LocalTrackPublication>}
     */ LocalParticipant.prototype._getOrCreateLocalTrackPublication = function(localTrack) {
        var localTrackPublication = getTrackPublication(this.tracks, localTrack);
        if (localTrackPublication) {
            return Promise.resolve(localTrackPublication);
        }
        var log = this._log;
        var self = this;
        var trackSignaling = this._signaling.getPublication(localTrack._trackSender);
        if (!trackSignaling) {
            return Promise.reject(new Error("Unexpected error: The " + localTrack + " cannot be published"));
        }
        return new Promise(function(resolve, reject) {
            function updated() {
                var error = trackSignaling.error;
                if (error) {
                    trackSignaling.removeListener('updated', updated);
                    log.warn("Failed to publish the " + trackClass(localTrack, true) + ": " + error.message);
                    self._removeTrack(localTrack, localTrack.id);
                    setTimeout(function() {
                        self.emit('trackPublicationFailed', error, localTrack);
                    });
                    reject(error);
                    return;
                }
                if (!self._tracks.has(localTrack.id)) {
                    trackSignaling.removeListener('updated', updated);
                    reject(new Error("The " + localTrack + " was unpublished"));
                    return;
                }
                var sid = trackSignaling.sid;
                if (!sid) {
                    return;
                }
                trackSignaling.removeListener('updated', updated);
                var options = {
                    log: log,
                    LocalAudioTrackPublication: self._LocalAudioTrackPublication,
                    LocalDataTrackPublication: self._LocalDataTrackPublication,
                    LocalVideoTrackPublication: self._LocalVideoTrackPublication
                };
                localTrackPublication = getTrackPublication(self.tracks, localTrack);
                var warningHandler = function(twilioWarningName) {
                    return self.emit('trackWarning', twilioWarningName, localTrackPublication);
                };
                var warningsClearedHandler = function() {
                    return self.emit('trackWarningsCleared', localTrackPublication);
                };
                var unpublish = function(publication) {
                    localTrackPublication.removeListener('trackWarning', warningHandler);
                    localTrackPublication.removeListener('trackWarningsCleared', warningsClearedHandler);
                    self.unpublishTrack(publication.track);
                };
                if (!localTrackPublication) {
                    localTrackPublication = asLocalTrackPublication(localTrack, trackSignaling, unpublish, options);
                    self._addTrackPublication(localTrackPublication);
                }
                localTrackPublication.on('warning', warningHandler);
                localTrackPublication.on('warningsCleared', warningsClearedHandler);
                var state = self._signaling.state;
                if (state === 'connected' || state === 'connecting') {
                    if (localTrack._processorEventObserver) {
                        localTrack._processorEventObserver.on('event', function(event) {
                            self._eventObserver.emit('event', {
                                name: event.name,
                                payload: event.data,
                                group: 'video-processor',
                                level: 'info'
                            });
                        });
                    }
                    // NOTE(csantos): For tracks created before joining a room or already joined but about to publish it
                    if (localTrack.processedTrack) {
                        localTrack._captureFrames();
                        localTrack._setSenderMediaStreamTrack(true);
                    }
                }
                if (state === 'connected') {
                    setTimeout(function() {
                        self.emit('trackPublished', localTrackPublication);
                    });
                }
                resolve(localTrackPublication);
            }
            trackSignaling.on('updated', updated);
        });
    };
    /**
     * Publishes a {@link LocalTrack} to the {@link Room}.
     * @param {LocalTrack} localTrack - The {@link LocalTrack} to publish
     * @param {LocalTrackPublishOptions} [options] - The {@link LocalTrackPublishOptions}
     *   for publishing the {@link LocalTrack}
     * @returns {Promise<LocalTrackPublication>} - Resolves with the corresponding
     *   {@link LocalTrackPublication} if successful; In a Large Group Room (Maximum
     *   Participants greater than 50), rejects with a {@link ParticipantMaxTracksExceededError}
     *   if either the total number of published Tracks in the Room exceeds 16, or the {@link LocalTrack}
     *   is part of a set of {@link LocalTrack}s which along with the published Tracks exceeds 16.
     * @throws {TypeError}
     * @throws {RangeError}
     * @example
     * var Video = require('twilio-video');
     *
     * Video.connect(token, {
     *   name: 'my-cool-room',
     *   audio: true
     * }).then(function(room) {
     *   return Video.createLocalVideoTrack({
     *     name: 'camera'
     *   }).then(function(localVideoTrack) {
     *     return room.localParticipant.publishTrack(localVideoTrack, {
     *       priority: 'high'
     *     });
     *   });
     * }).then(function(publication) {
     *   console.log('The LocalTrack "' + publication.trackName
     *     + '" was successfully published with priority "'
     *     * publication.priority + '"');
     * });
    */ /**
     * Publishes a MediaStreamTrack to the {@link Room}.
     * @param {MediaStreamTrack} mediaStreamTrack - The MediaStreamTrack
     *   to publish; if a corresponding {@link LocalAudioTrack} or
     *   {@link LocalVideoTrack} has not yet been published, this method will
     *   construct one
     * @param {MediaStreamTrackPublishOptions} [options] - The options for publishing
     *   the MediaStreamTrack
     * @returns {Promise<LocalTrackPublication>} - Resolves with the corresponding
     *   {@link LocalTrackPublication} if successful; In a Large Group Room (Maximum
     *   Participants greater than 50), rejects with a {@link ParticipantMaxTracksExceededError}
     *   if the total number of published Tracks in the Room exceeds 16, or the {@link LocalTrack}
     *   is part of a set of {@link LocalTrack}s which along with the published Tracks exceeds 16.
     * @throws {TypeError}
     * @throws {RangeError}
     * @example
     * var Video = require('twilio-video');
     *
     * Video.connect(token, {
     *   name: 'my-cool-room',
     *   audio: true
     * }).then(function(room) {
     *   return navigator.mediaDevices.getUserMedia({
     *     video: true
     *   }).then(function(mediaStream) {
     *     var mediaStreamTrack = mediaStream.getTracks()[0];
     *     return room.localParticipant.publishTrack(mediaStreamTrack, {
     *       name: 'camera',
     *       priority: 'high'
     *     });
     *   });
     * }).then(function(publication) {
     *   console.log('The LocalTrack "' + publication.trackName
     *     + '" was successfully published with priority "'
     *     * publication.priority + '"');
     * });
     */ LocalParticipant.prototype.publishTrack = function(localTrackOrMediaStreamTrack, options) {
        var trackPublication = getTrackPublication(this.tracks, localTrackOrMediaStreamTrack);
        if (trackPublication) {
            return Promise.resolve(trackPublication);
        }
        options = Object.assign({
            log: this._log,
            priority: trackPriority.PRIORITY_STANDARD,
            LocalAudioTrack: this._LocalAudioTrack,
            LocalDataTrack: this._LocalDataTrack,
            LocalVideoTrack: this._LocalVideoTrack,
            MediaStreamTrack: this._MediaStreamTrack
        }, options);
        var localTrack;
        try {
            localTrack = asLocalTrack(localTrackOrMediaStreamTrack, options);
        } catch (error) {
            return Promise.reject(error);
        }
        var noiseCancellation = localTrack.noiseCancellation;
        var allowedAudioProcessors = this._signaling.audioProcessors;
        if (noiseCancellation && !allowedAudioProcessors.includes(noiseCancellation.vendor)) {
            this._log.warn(noiseCancellation.vendor + " is not supported in this room. disabling it permanently");
            noiseCancellation.disablePermanently();
        }
        var priorityValues = Object.values(trackPriority);
        if (!priorityValues.includes(options.priority)) {
            // eslint-disable-next-line new-cap
            return Promise.reject(E.INVALID_VALUE('LocalTrackPublishOptions.priority', priorityValues));
        }
        var addedLocalTrack = this._addTrack(localTrack, localTrack.id, options.priority) || this._tracks.get(localTrack.id);
        return this._getOrCreateLocalTrackPublication(addedLocalTrack);
    };
    /**
     * Publishes multiple {@link LocalTrack}s to the {@link Room}.
     * @param {Array<LocalTrack|MediaStreamTrack>} tracks - The {@link LocalTrack}s
     *   to publish; for any MediaStreamTracks provided, if a corresponding
     *   {@link LocalAudioTrack} or {@link LocalVideoTrack} has not yet been
     *   published, this method will construct one
     * @returns {Promise<Array<LocalTrackPublication>>} - The resulting
     *   {@link LocalTrackPublication}s if successful; In a Large Group Room (Maximum
     *   Participants greater than 50), rejects with a {@link ParticipantMaxTracksExceededError}
     *   if the total number of published Tracks in the Room exceeds 16, or the {@link LocalTrack}s
     *   along with the published Tracks exceeds 16.
     * @throws {TypeError}
     */ LocalParticipant.prototype.publishTracks = function(tracks) {
        if (!Array.isArray(tracks)) {
            // eslint-disable-next-line new-cap
            throw E.INVALID_TYPE('tracks', 'Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack');
        }
        return Promise.all(tracks.map(this.publishTrack, this));
    };
    LocalParticipant.prototype.setBandwidthProfile = function() {
        this._log.warn('setBandwidthProfile is not implemented yet and may be available in future versions of twilio-video.js');
    };
    /**
     * Sets the {@link NetworkQualityVerbosity} for the {@link LocalParticipant} and
     * {@link RemoteParticipant}s. It does nothing if Network Quality is not enabled
     * while calling {@link connect}.
     * @param {NetworkQualityConfiguration} networkQualityConfiguration - The new
     *   {@link NetworkQualityConfiguration}; If either or both of the local and
     *   remote {@link NetworkQualityVerbosity} values are absent, then the corresponding
     *   existing values are retained
     * @returns {this}
     * @example
     * // Update verbosity levels for both LocalParticipant and RemoteParticipants
     * localParticipant.setNetworkQualityConfiguration({
     *   local: 1,
     *   remote: 2
     * });
     * @example
     * // Update verbosity level for only the LocalParticipant
     * localParticipant.setNetworkQualityConfiguration({
     *   local: 1
     * });
     *  @example
     * // Update verbosity level for only the RemoteParticipants
     * localParticipant.setNetworkQualityConfiguration({
     *   remote: 2
     * });
     */ LocalParticipant.prototype.setNetworkQualityConfiguration = function(networkQualityConfiguration) {
        if (typeof networkQualityConfiguration !== 'object' || networkQualityConfiguration === null) {
            // eslint-disable-next-line new-cap
            throw E.INVALID_TYPE('networkQualityConfiguration', 'NetworkQualityConfiguration');
        }
        [
            'local',
            'remote'
        ].forEach(function(prop) {
            if (prop in networkQualityConfiguration && (typeof networkQualityConfiguration[prop] !== 'number' || isNaN(networkQualityConfiguration[prop]))) {
                // eslint-disable-next-line new-cap
                throw E.INVALID_TYPE("networkQualityConfiguration." + prop, 'number');
            }
        });
        this._signaling.setNetworkQualityConfiguration(networkQualityConfiguration);
        return this;
    };
    /**
     * Set the {@link LocalParticipant}'s {@link EncodingParameters}.
     * @param {?EncodingParameters} [encodingParameters] - The new
     *   {@link EncodingParameters}; If null, then the bitrate limits are removed;
     *   If not specified, then the existing bitrate limits are preserved
     * @returns {this}
     * @throws {TypeError}
     */ LocalParticipant.prototype.setParameters = function(encodingParameters) {
        if (typeof encodingParameters !== 'undefined' && typeof encodingParameters !== 'object') {
            // eslint-disable-next-line new-cap
            throw E.INVALID_TYPE('encodingParameters', 'EncodingParameters, null or undefined');
        }
        if (encodingParameters) {
            if (this._signaling.getParameters().adaptiveSimulcast && encodingParameters.maxVideoBitrate) {
                // eslint-disable-next-line new-cap
                throw E.INVALID_TYPE('encodingParameters', 'encodingParameters.maxVideoBitrate is not compatible with "preferredVideoCodecs=auto"');
            }
            [
                'maxAudioBitrate',
                'maxVideoBitrate'
            ].forEach(function(prop) {
                if (typeof encodingParameters[prop] !== 'undefined' && typeof encodingParameters[prop] !== 'number' && encodingParameters[prop] !== null) {
                    // eslint-disable-next-line new-cap
                    throw E.INVALID_TYPE("encodingParameters." + prop, 'number, null or undefined');
                }
            });
        } else if (encodingParameters === null) {
            encodingParameters = {
                maxAudioBitrate: null,
                maxVideoBitrate: null
            };
        }
        this._signaling.setParameters(encodingParameters);
        return this;
    };
    /**
     * Stops publishing a {@link LocalTrack} to the {@link Room}.
     * @param {LocalTrack|MediaStreamTrack} track - The {@link LocalTrack}
     *   to stop publishing; if a MediaStreamTrack is provided, this method
     *   looks up the corresponding {@link LocalAudioTrack} or
     *   {@link LocalVideoTrack} to stop publishing
     * @returns {?LocalTrackPublication} - The corresponding
     *   {@link LocalTrackPublication} if the {@link LocalTrack} was previously
     *   published, null otherwise
     * @throws {TypeError}
    */ LocalParticipant.prototype.unpublishTrack = function(track) {
        validateLocalTrack(track, {
            LocalAudioTrack: this._LocalAudioTrack,
            LocalDataTrack: this._LocalDataTrack,
            LocalVideoTrack: this._LocalVideoTrack,
            MediaStreamTrack: this._MediaStreamTrack
        });
        var localTrack = this._tracks.get(track.id);
        if (!localTrack) {
            return null;
        }
        var trackSignaling = this._signaling.getPublication(localTrack._trackSender);
        trackSignaling.publishFailed(new Error("The " + localTrack + " was unpublished"));
        localTrack = this._removeTrack(localTrack, localTrack.id);
        if (!localTrack) {
            return null;
        }
        var localTrackPublication = getTrackPublication(this.tracks, localTrack);
        if (localTrackPublication) {
            this._removeTrackPublication(localTrackPublication);
        }
        return localTrackPublication;
    };
    /**
     * Stops publishing multiple {@link LocalTrack}s to the {@link Room}.
     * @param {Array<LocalTrack|MediaStreamTrack>} tracks - The {@link LocalTrack}s
     *   to stop publishing; for any MediaStreamTracks provided, this method looks
     *   up the corresponding {@link LocalAudioTrack} or {@link LocalVideoTrack} to
     *   stop publishing
     * @returns {Array<LocalTrackPublication>} - The corresponding
     *   {@link LocalTrackPublication}s that were successfully unpublished
     * @throws {TypeError}
     */ LocalParticipant.prototype.unpublishTracks = function(tracks) {
        var _this = this;
        if (!Array.isArray(tracks)) {
            // eslint-disable-next-line new-cap
            throw E.INVALID_TYPE('tracks', 'Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack');
        }
        return tracks.reduce(function(unpublishedTracks, track) {
            var unpublishedTrack = _this.unpublishTrack(track);
            return unpublishedTrack ? unpublishedTracks.concat(unpublishedTrack) : unpublishedTracks;
        }, []);
    };
    return LocalParticipant;
}(Participant);
/**
 * The {@link LocalParticipant} has reconnected to the {@link Room} after a signaling connection disruption.
 * @event LocalParticipant#reconnected
 */ /**
 * The {@link LocalParticipant} is reconnecting to the {@link Room} after a signaling connection disruption.
 * @event LocalParticipant#reconnecting
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalVideoTrack}'s dimensions changed.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} whose dimensions changed
 * @event LocalParticipant#trackDimensionsChanged
 */ /**
 * A {@link LocalTrack} was disabled by the {@link LocalParticipant}.
 * @param {LocalTrack} track - The {@link LocalTrack} that was disabled
 * @event LocalParticipant#trackDisabled
 */ /**
 * A {@link LocalTrack} was enabled by the {@link LocalParticipant}.
 * @param {LocalTrack} track - The {@link LocalTrack} that was enabled
 * @event LocalParticipant#trackEnabled
 */ /**
 * A {@link LocalTrack} failed to publish. Check the error message for more
 * information. In a Large Group Room (Maximum Participants greater than 50),
 * this event is raised with a {@link ParticipantMaxTracksExceededError} either
 * when attempting to publish the {@link LocalTrack} will exceed the Maximum Published
 * Tracks limit of 16, or the {@link LocalTrack} is part of a set of {@link LocalTrack}s
 * which along with the published Tracks exceeds 16.
 * @param {TwilioError} error - A {@link TwilioError} explaining why publication
 *   failed
 * @param {LocalTrack} localTrack - The {@link LocalTrack} that failed to
 *   publish
 * @event LocalParticipant#trackPublicationFailed
 */ /**
 * A {@link LocalTrack} that was added using {@link LocalParticipant#publishTrack} was successfully published. This event
 * is not raised for {@link LocalTrack}s added in {@link ConnectOptions}<code>.tracks</code> or auto-created within
 * <a href="module-twilio-video.html#.connect__anchor"><code>{@link connect}</code></a>.
 * @param {LocalTrackPublication} publication - The resulting
 *   {@link LocalTrackPublication} for the published {@link LocalTrack}
 * @event LocalParticipant#trackPublished
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrack}s started.
 * @param {LocalTrack} track - The {@link LocalTrack} that started
 * @event LocalParticipant#trackStarted
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrack}s stopped, either
 * because {@link LocalTrack#stop} was called or because the underlying
 * MediaStreamTrack ended).
 * @param {LocalTrack} track - The {@link LocalTrack} that stopped
 * @event LocalParticipant#trackStopped
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrackPublication}s encountered a warning.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @param {string} name - The warning that was raised.
 * @param {LocalTrackPublication} publication - The {@link LocalTrackPublication} that encountered the warning.
 * @event LocalParticipant#trackWarning
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrackPublication}s cleared all warnings.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @param {LocalTrackPublication} publication - The {@link LocalTrackPublication} that cleared all warnings.
 * @event LocalParticipant#trackWarningsCleared
 */ /**
 * Outgoing media encoding parameters.
 * @typedef {object} EncodingParameters
 * @property {?number} [maxAudioBitrate] - Max outgoing audio bitrate (bps);
 *   If not specified, retains the existing bitrate limit; A <code>null</code> or a
 *   <code>0</code> value removes any previously set bitrate limit; This value is set
 *   as a hint for variable bitrate codecs, but will not take effect for fixed bitrate
 *   codecs; Based on our tests, Chrome, Firefox and Safari support a bitrate range of
 *   12000 bps to 256000 bps for Opus codec; This parameter has no effect on iSAC, PCMU
 *   and PCMA codecs
 * @property {?number} [maxVideoBitrate] - Max outgoing video bitrate (bps);
 *   If not specified, retains the existing bitrate limit; A <code>null</code> or
 *   a <code>0</code> value removes any previously set bitrate limit; This value is
 *   set as a hint for variable bitrate codecs, but will not take effect for fixed
 *   bitrate codecs; Based on our tests, Chrome, Firefox and Safari all seem to support
 *   an average bitrate range of 20000 bps (20 kbps) to 8000000 bps (8 mbps) for a
 *   720p VideoTrack.
 *   Note: this limit is not applied for screen share tracks published on Chrome.
 */ /**
 * Options for publishing a {@link LocalTrack}.
 * @typedef {object} LocalTrackPublishOptions
 * @property {Track.Priority} [priority='standard'] - The priority with which the {@link LocalTrack}
 *   is to be published; In Group or Small Group Rooms, the appropriate bandwidth is
 *   allocated to the {@link LocalTrack} based on its {@link Track.Priority}; It has no
 *   effect in Peer-to-Peer Rooms; It defaults to "standard" when not provided
 */ /**
 * Options for publishing a {@link MediaStreamTrack}.
 * @typedef {LocalTrackOptions} MediaStreamTrackPublishOptions
 * @property {Track.Priority} [priority='standard'] - The priority with which the {@link LocalTrack}
 *   is to be published; In Group or Small Group Rooms, the appropriate bandwidth is
 *   allocated to the {@link LocalTrack} based on its {@link Track.Priority}; It has no
 *   effect in Peer-to-Peer Rooms; It defaults to "standard" when not provided
 */ /**
 * @private
 * @param {Map<Track.SID, LocalTrackPublication>} trackPublications
 * @param {LocalTrack|MediaStreamTrack} track
 * @returns {?LocalTrackPublication} trackPublication
 */ function getTrackPublication(trackPublications, track) {
    return Array.from(trackPublications.values()).find(function(trackPublication) {
        return trackPublication.track === track || trackPublication.track.mediaStreamTrack === track;
    }) || null;
}
module.exports = LocalParticipant; //# sourceMappingURL=localparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/networkqualityconfiguration.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), DEFAULT_NQ_LEVEL_LOCAL = _a.DEFAULT_NQ_LEVEL_LOCAL, DEFAULT_NQ_LEVEL_REMOTE = _a.DEFAULT_NQ_LEVEL_REMOTE, MAX_NQ_LEVEL = _a.MAX_NQ_LEVEL;
var inRange = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").inRange;
/**
 * {@link NetworkQualityConfigurationImpl} represents an object which notifies its
 * listeners of any changes in the values of its properties.
 * @extends EventEmitter
 * @implements NetworkQualityConfiguration
 * @property {?NetworkQualityVerbosity} local - Verbosity level for {@link LocalParticipant}
 * @property {?NetworkQualityVerbosity} remote - Verbosity level for {@link RemoteParticipant}s
 */ var NetworkQualityConfigurationImpl = function(_super) {
    __extends(NetworkQualityConfigurationImpl, _super);
    /**
     * Construct an {@link NetworkQualityConfigurationImpl}.
     * @param {NetworkQualityConfiguration} networkQualityConfiguration - Initial {@link NetworkQualityConfiguration}
     */ function NetworkQualityConfigurationImpl(networkQualityConfiguration) {
        var _this = _super.call(this) || this;
        networkQualityConfiguration = Object.assign({
            local: DEFAULT_NQ_LEVEL_LOCAL,
            remote: DEFAULT_NQ_LEVEL_REMOTE
        }, networkQualityConfiguration);
        Object.defineProperties(_this, {
            local: {
                value: inRange(networkQualityConfiguration.local, DEFAULT_NQ_LEVEL_LOCAL, MAX_NQ_LEVEL) ? networkQualityConfiguration.local : DEFAULT_NQ_LEVEL_LOCAL,
                writable: true
            },
            remote: {
                value: inRange(networkQualityConfiguration.remote, DEFAULT_NQ_LEVEL_REMOTE, MAX_NQ_LEVEL) ? networkQualityConfiguration.remote : DEFAULT_NQ_LEVEL_REMOTE,
                writable: true
            }
        });
        return _this;
    }
    /**
     * Update the verbosity levels for network quality information for
     * {@link LocalParticipant} and {@link RemoteParticipant} with those
     * in the given {@link NetworkQualityConfiguration}.
     * @param {NetworkQualityConfiguration} networkQualityConfiguration - The new {@link NetworkQualityConfiguration}
     */ NetworkQualityConfigurationImpl.prototype.update = function(networkQualityConfiguration) {
        var _this = this;
        networkQualityConfiguration = Object.assign({
            local: this.local,
            remote: this.remote
        }, networkQualityConfiguration);
        [
            [
                'local',
                DEFAULT_NQ_LEVEL_LOCAL,
                3
            ],
            [
                'remote',
                DEFAULT_NQ_LEVEL_REMOTE,
                3
            ]
        ].forEach(function(_a) {
            var _b = __read(_a, 3), localOrRemote = _b[0], min = _b[1], max = _b[2];
            _this[localOrRemote] = typeof networkQualityConfiguration[localOrRemote] === 'number' && inRange(networkQualityConfiguration[localOrRemote], min, max) ? networkQualityConfiguration[localOrRemote] : min;
        });
    };
    return NetworkQualityConfigurationImpl;
}(EventEmitter);
module.exports = NetworkQualityConfigurationImpl; //# sourceMappingURL=networkqualityconfiguration.js.map
}}),
"[project]/node_modules/twilio-video/es5/remoteparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var Participant = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/participant.js [app-client] (ecmascript)");
/**
 * A {@link RemoteParticipant} represents a remote {@link Participant} in a
 * {@link Room}.
 * @extends Participant
 * @property {Map<Track.SID, RemoteAudioTrackPublication>} audioTracks -
 *    The {@link Participant}'s {@link RemoteAudioTrackPublication}s
 * @property {Map<Track.SID, RemoteDataTrackPublication>} dataTracks -
 *    The {@link Participant}'s {@link RemoteDataTrackPublication}s
 * @property {Map<Track.SID, RemoteTrackPublication>} tracks -
 *    The {@link Participant}'s {@link RemoteTrackPublication}s
 * @property {Map<Track.SID, RemoteVideoTrackPublication>} videoTracks -
 *    The {@link Participant}'s {@link RemoteVideoTrackPublication}s
 * @emits RemoteParticipant#reconnected
 * @emits RemoteParticipant#reconnecting
 * @emits RemoteParticipant#trackDimensionsChanged
 * @emits RemoteParticipant#trackDisabled
 * @emits RemoteParticipant#trackEnabled
 * @emits RemoteParticipant#trackMessage
 * @emits RemoteParticipant#trackPublished
 * @emits RemoteParticipant#trackPublishPriorityChanged
 * @emits RemoteParticipant#trackStarted
 * @emits RemoteParticipant#trackSubscribed
 * @emits RemoteParticipant#trackSubscriptionFailed
 * @emits RemoteParticipant#trackSwitchedOff
 * @emits RemoteParticipant#trackSwitchedOn
 * @emits RemoteParticipant#trackUnpublished
 * @emits RemoteParticipant#trackUnsubscribed
 */ var RemoteParticipant = function(_super) {
    __extends(RemoteParticipant, _super);
    /**
     * Construct a {@link RemoteParticipant}.
     * @param {ParticipantSignaling} signaling
     * @param {object} [options]
     */ function RemoteParticipant(signaling, options) {
        var _this = _super.call(this, signaling, options) || this;
        _this._handleTrackSignalingEvents();
        _this.once('disconnected', _this._unsubscribeTracks.bind(_this));
        return _this;
    }
    RemoteParticipant.prototype.toString = function() {
        return "[RemoteParticipant #" + this._instanceId + (this.sid ? ": " + this.sid : '') + "]";
    };
    /**
     * @private
     * @param {RemoteTrack} remoteTrack
     * @param {RemoteTrackPublication} publication
     * @param {Track.ID} id
     * @returns {?RemoteTrack}
     */ RemoteParticipant.prototype._addTrack = function(remoteTrack, publication, id) {
        if (!_super.prototype._addTrack.call(this, remoteTrack, id)) {
            return null;
        }
        publication._subscribed(remoteTrack);
        this.emit('trackSubscribed', remoteTrack, publication);
        return remoteTrack;
    };
    /**
     * @private
     * @param {RemoteTrackPublication} publication
     * @returns {?RemoteTrackPublication}
     */ RemoteParticipant.prototype._addTrackPublication = function(publication) {
        var addedPublication = _super.prototype._addTrackPublication.call(this, publication);
        if (!addedPublication) {
            return null;
        }
        this.emit('trackPublished', addedPublication);
        return addedPublication;
    };
    /**
     * @private
     */ RemoteParticipant.prototype._getTrackPublicationEvents = function() {
        return __spreadArray(__spreadArray([], __read(_super.prototype._getTrackPublicationEvents.call(this))), [
            [
                'subscriptionFailed',
                'trackSubscriptionFailed'
            ],
            [
                'trackDisabled',
                'trackDisabled'
            ],
            [
                'trackEnabled',
                'trackEnabled'
            ],
            [
                'publishPriorityChanged',
                'trackPublishPriorityChanged'
            ],
            [
                'trackSwitchedOff',
                'trackSwitchedOff'
            ],
            [
                'trackSwitchedOn',
                'trackSwitchedOn'
            ]
        ]);
    };
    /**
     * @private
     */ RemoteParticipant.prototype._unsubscribeTracks = function() {
        var _this = this;
        this.tracks.forEach(function(publication) {
            if (publication.isSubscribed) {
                var track = publication.track;
                publication._unsubscribe();
                _this.emit('trackUnsubscribed', track, publication);
            }
        });
    };
    /**
     * @private
     * @param {RemoteTrack} remoteTrack
     * @param {RemoteTrackPublication} publication
     * @param {Track.ID} id
     * @returns {?RemoteTrack}
     */ RemoteParticipant.prototype._removeTrack = function(remoteTrack, publication, id) {
        var unsubscribedTrack = this._tracks.get(id);
        if (!unsubscribedTrack) {
            return null;
        }
        _super.prototype._removeTrack.call(this, unsubscribedTrack, id);
        publication._unsubscribe();
        this.emit('trackUnsubscribed', unsubscribedTrack, publication);
        return unsubscribedTrack;
    };
    /**
     * @private
     * @param {RemoteTrackPublication} publication
     * @returns {?RemoteTrackPublication}
     */ RemoteParticipant.prototype._removeTrackPublication = function(publication) {
        this._signaling.clearTrackHint(publication.trackSid);
        var removedPublication = _super.prototype._removeTrackPublication.call(this, publication);
        if (!removedPublication) {
            return null;
        }
        this.emit('trackUnpublished', removedPublication);
        return removedPublication;
    };
    return RemoteParticipant;
}(Participant);
/**
 * The {@link RemoteParticipant} has reconnected to the {@link Room} after a signaling connection disruption.
 * @event RemoteParticipant#reconnected
 */ /**
 * The {@link RemoteParticipant} is reconnecting to the {@link Room} after a signaling connection disruption.
 * @event RemoteParticipant#reconnecting
 */ /**
 * One of the {@link RemoteParticipant}'s {@link RemoteVideoTrack}'s dimensions changed.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} whose dimensions changed
 * @event RemoteParticipant#trackDimensionsChanged
 */ /**
 * A {@link RemoteTrack} was disabled by the {@link RemoteParticipant}.
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication} associated with the disabled {@link RemoteTrack}
 * @event RemoteParticipant#trackDisabled
 */ /**
 * A {@link RemoteTrack} was enabled by the {@link RemoteParticipant}.
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication} associated with the enabled {@link RemoteTrack}
 * @event RemoteParticipant#trackEnabled
 */ /**
 * A message was received over one of the {@link RemoteParticipant}'s
 * {@link RemoteDataTrack}s.
 * @event RemoteParticipant#trackMessage
 * @param {string|ArrayBuffer} data
 * @param {RemoteDataTrack} track - The {@link RemoteDataTrack} over which the
 *   message was received
 */ /**
 * A {@link RemoteTrack} was published by the {@link RemoteParticipant} after
 * connecting to the {@link Room}. This event is not emitted for
 * {@link RemoteTrack}s that were published while the {@link RemoteParticipant}
 * was connecting to the {@link Room}.
 * @event RemoteParticipant#trackPublished
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   which represents the published {@link RemoteTrack}
 * @example
 * function trackPublished(publication) {
 *   console.log(`Track ${publication.trackSid} was published`);
 * }
 *
 * room.on('participantConnected', participant => {
 *   // Handle RemoteTracks published while connecting to the Room.
 *   participant.trackPublications.forEach(trackPublished);
 *
 *   // Handle RemoteTracks published after connecting to the Room.
 *   participant.on('trackPublished', trackPublished);
 * });
 */ /**
 * One of the {@link RemoteParticipant}'s {@link RemoteTrack}s started.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that started
 * @event RemoteParticipant#trackStarted
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was subscribed to.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was subscribed to
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was subscribed to
 * @event RemoteParticipant#trackSubscribed
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} could not be subscribed to.
 * @param {TwilioError} error - The reason the {@link RemoteTrack} could not be
 *   subscribed to
 * @param {RemoteTrackPublication} publication - The
 *   {@link RemoteTrackPublication} for the {@link RemoteTrack} that could not
 *   be subscribed to
 * @event RemoteParticipant#trackSubscriptionFailed
 */ /**
 * The {@link RemoteTrackPublication}'s publish {@link Track.Priority} was changed by the
 * {@link RemoteParticipant}.
 * @param {Track.Priority} priority - the {@link RemoteTrack}'s new publish
 *   {@link Track.Priority};
 * @param {RemoteTrackPublication} publication - The
 *   {@link RemoteTrackPublication} for the {@link RemoteTrack} that changed priority
 * @event RemoteParticipant#trackPublishPriorityChanged
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was subscribed to.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was switched off
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was switched off
 * @event RemoteParticipant#trackSwitchedOff
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was switched on.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was switched on.
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was switched on
 * @event RemoteParticipant#trackSwitchedOn
 */ /**
 * A {@link RemoteTrack} was unpublished by the {@link RemoteParticipant}.
 * @event RemoteParticipant#trackUnpublished
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   which represents the unpublished {@link RemoteTrack}
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was unsubscribed from.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was unsubscribed from
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was unsubscribed from
 * @event RemoteParticipant#trackUnsubscribed
 */ module.exports = RemoteParticipant; //# sourceMappingURL=remoteparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/trackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Statistics for a {@link Track}.
 * @property {Track.ID} trackId - The {@link Track} ID
 * @property {Track.SID} trackSid - The {@link Track}'s SID when published in
 *  in a {@link Room}
 * @property {number} timestamp - A Unix timestamp in milliseconds indicating
 *   when the {@link TrackStats} were gathered
 * @property {string} ssrc - The {@link Track}'s SSRC when transmitted over the
 *   RTCPeerConnection
 * @property {?number} packetsLost - The number of packets lost
 * @property {?string} codec - The name of the codec used to encode the
 *   {@link Track}'s media
 */ var TrackStats = function() {
    /**
     * @param {string} trackId - {@link Track} ID
     * @param {StandardizedTrackStatsReport} statsReport
     */ function TrackStats(trackId, statsReport) {
        if (typeof trackId !== 'string') {
            throw new Error('Track id must be a string');
        }
        Object.defineProperties(this, {
            trackId: {
                value: trackId,
                enumerable: true
            },
            trackSid: {
                value: statsReport.trackSid,
                enumerable: true
            },
            timestamp: {
                value: statsReport.timestamp,
                enumerable: true
            },
            ssrc: {
                value: statsReport.ssrc,
                enumerable: true
            },
            packetsLost: {
                value: typeof statsReport.packetsLost === 'number' ? statsReport.packetsLost : null,
                enumerable: true
            },
            codec: {
                value: typeof statsReport.codecName === 'string' ? statsReport.codecName : null,
                enumerable: true
            }
        });
    }
    return TrackStats;
}();
module.exports = TrackStats; //# sourceMappingURL=trackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/localtrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/trackstats.js [app-client] (ecmascript)");
/**
 * Statistics for a {@link LocalTrack}.
 * @extends TrackStats
 * @property {?number} bytesSent - Number of bytes sent
 * @property {?number} packetsSent - Number of packets sent
 * @property {?number} roundTripTime - Round trip time in milliseconds
 */ var LocalTrackStats = function(_super) {
    __extends(LocalTrackStats, _super);
    /**
     * @param {string} trackId - {@link LocalTrack} ID
     * @param {StandardizedTrackStatsReport} statsReport
     * @param {boolean} prepareForInsights
     */ function LocalTrackStats(trackId, statsReport, prepareForInsights) {
        var _this = _super.call(this, trackId, statsReport) || this;
        Object.defineProperties(_this, {
            bytesSent: {
                value: typeof statsReport.bytesSent === 'number' ? statsReport.bytesSent : prepareForInsights ? 0 : null,
                enumerable: true
            },
            packetsSent: {
                value: typeof statsReport.packetsSent === 'number' ? statsReport.packetsSent : prepareForInsights ? 0 : null,
                enumerable: true
            },
            roundTripTime: {
                value: typeof statsReport.roundTripTime === 'number' ? statsReport.roundTripTime : prepareForInsights ? 0 : null,
                enumerable: true
            }
        });
        return _this;
    }
    return LocalTrackStats;
}(TrackStats);
module.exports = LocalTrackStats; //# sourceMappingURL=localtrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/localaudiotrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/localtrackstats.js [app-client] (ecmascript)");
/**
 * Statistics for a {@link LocalAudioTrack}.
 * @extends LocalTrackStats
 * @property {?AudioLevel} audioLevel - Input {@link AudioLevel}
 * @property {?number} jitter - Audio jitter in milliseconds
 */ var LocalAudioTrackStats = function(_super) {
    __extends(LocalAudioTrackStats, _super);
    /**
     * @param {string} trackId - {@link LocalAudioTrack} ID
     * @param {StandardizedTrackStatsReport} statsReport
     * @param {boolean} prepareForInsights
     */ function LocalAudioTrackStats(trackId, statsReport, prepareForInsights) {
        var _this = _super.call(this, trackId, statsReport, prepareForInsights) || this;
        Object.defineProperties(_this, {
            audioLevel: {
                value: typeof statsReport.audioInputLevel === 'number' ? statsReport.audioInputLevel : null,
                enumerable: true
            },
            jitter: {
                value: typeof statsReport.jitter === 'number' ? statsReport.jitter : null,
                enumerable: true
            }
        });
        return _this;
    }
    return LocalAudioTrackStats;
}(LocalTrackStats);
/**
 * The maximum absolute amplitude of a set of audio samples in the
 * range of 0 to 32767 inclusive.
 * @typedef {number} AudioLevel
 */ module.exports = LocalAudioTrackStats; //# sourceMappingURL=localaudiotrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/localvideotrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/localtrackstats.js [app-client] (ecmascript)");
/**
 * Statistics for a {@link LocalVideoTrack}.
 * @extends LocalTrackStats
 * @property {?VideoTrack#Dimensions} captureDimensions - Video capture resolution
 * @property {?VideoTrack#Dimensions} dimensions - Video encoding resolution
 * @property {?number} captureFrameRate - Video capture frame rate
 * @property {?number} frameRate - Video encoding frame rate
 */ var LocalVideoTrackStats = function(_super) {
    __extends(LocalVideoTrackStats, _super);
    /**
     * @param {string} trackId - {@link LocalVideoTrack} ID
     * @param {StandardizedTrackStatsReport} statsReport
     * @param {boolean} prepareForInsights
     */ function LocalVideoTrackStats(trackId, statsReport, prepareForInsights) {
        var _this = _super.call(this, trackId, statsReport, prepareForInsights) || this;
        var captureDimensions = null;
        if (typeof statsReport.frameWidthInput === 'number' && typeof statsReport.frameHeightInput === 'number') {
            captureDimensions = {};
            Object.defineProperties(captureDimensions, {
                width: {
                    value: statsReport.frameWidthInput,
                    enumerable: true
                },
                height: {
                    value: statsReport.frameHeightInput,
                    enumerable: true
                }
            });
        }
        var dimensions = null;
        if (typeof statsReport.frameWidthSent === 'number' && typeof statsReport.frameHeightSent === 'number') {
            dimensions = {};
            Object.defineProperties(dimensions, {
                width: {
                    value: statsReport.frameWidthSent,
                    enumerable: true
                },
                height: {
                    value: statsReport.frameHeightSent,
                    enumerable: true
                }
            });
        }
        Object.defineProperties(_this, {
            captureDimensions: {
                value: captureDimensions,
                enumerable: true
            },
            dimensions: {
                value: dimensions,
                enumerable: true
            },
            captureFrameRate: {
                value: typeof statsReport.frameRateInput === 'number' ? statsReport.frameRateInput : null,
                enumerable: true
            },
            frameRate: {
                value: typeof statsReport.frameRateSent === 'number' ? statsReport.frameRateSent : null,
                enumerable: true
            }
        });
        return _this;
    }
    return LocalVideoTrackStats;
}(LocalTrackStats);
module.exports = LocalVideoTrackStats; //# sourceMappingURL=localvideotrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/remotetrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/trackstats.js [app-client] (ecmascript)");
/**
 * Statistics for a remote {@link Track}.
 * @extends TrackStats
 * @property {?number} bytesReceived - Number of bytes received
 * @property {?number} packetsReceived - Number of packets received
 */ var RemoteTrackStats = function(_super) {
    __extends(RemoteTrackStats, _super);
    /*
     * @param {string} trackId - {@link Track} ID
     * @param {StandardizedTrackStatsReport} statsReport
     */ function RemoteTrackStats(trackId, statsReport) {
        var _this = _super.call(this, trackId, statsReport) || this;
        Object.defineProperties(_this, {
            bytesReceived: {
                value: typeof statsReport.bytesReceived === 'number' ? statsReport.bytesReceived : null,
                enumerable: true
            },
            packetsReceived: {
                value: typeof statsReport.packetsReceived === 'number' ? statsReport.packetsReceived : null,
                enumerable: true
            }
        });
        return _this;
    }
    return RemoteTrackStats;
}(TrackStats);
module.exports = RemoteTrackStats; //# sourceMappingURL=remotetrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/remoteaudiotrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/remotetrackstats.js [app-client] (ecmascript)");
/**
 * Statistics for an {@link AudioTrack}.
 * @extends RemoteTrackStats
 * @property {?AudioLevel} audioLevel - Output {@link AudioLevel}
 * @property {?number} jitter - Audio jitter in milliseconds
 */ var RemoteAudioTrackStats = function(_super) {
    __extends(RemoteAudioTrackStats, _super);
    /**
     * @param {string} trackId - {@link AudioTrack} ID
     * @param {StandardizedTrackStatsReport} statsReport
     */ function RemoteAudioTrackStats(trackId, statsReport) {
        var _this = _super.call(this, trackId, statsReport) || this;
        Object.defineProperties(_this, {
            audioLevel: {
                value: typeof statsReport.audioOutputLevel === 'number' ? statsReport.audioOutputLevel : null,
                enumerable: true
            },
            jitter: {
                value: typeof statsReport.jitter === 'number' ? statsReport.jitter : null,
                enumerable: true
            }
        });
        return _this;
    }
    return RemoteAudioTrackStats;
}(RemoteTrackStats);
module.exports = RemoteAudioTrackStats; //# sourceMappingURL=remoteaudiotrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/remotevideotrackstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/remotetrackstats.js [app-client] (ecmascript)");
/**
 * Statistics for a {@link VideoTrack}.
 * @extends RemoteTrackStats
 * @property {?VideoTrack#Dimensions} dimensions - Received video resolution
 * @property {?number} frameRate - Received video frame rate
 */ var RemoteVideoTrackStats = function(_super) {
    __extends(RemoteVideoTrackStats, _super);
    /**
     * @param {string} trackId - {@link VideoTrack} ID
     * @param {StandardizedTrackStatsReport} statsReport
     */ function RemoteVideoTrackStats(trackId, statsReport) {
        var _this = _super.call(this, trackId, statsReport) || this;
        var dimensions = null;
        if (typeof statsReport.frameWidthReceived === 'number' && typeof statsReport.frameHeightReceived === 'number') {
            dimensions = {};
            Object.defineProperties(dimensions, {
                width: {
                    value: statsReport.frameWidthReceived,
                    enumerable: true
                },
                height: {
                    value: statsReport.frameHeightReceived,
                    enumerable: true
                }
            });
        }
        Object.defineProperties(_this, {
            dimensions: {
                value: dimensions,
                enumerable: true
            },
            frameRate: {
                value: typeof statsReport.frameRateReceived === 'number' ? statsReport.frameRateReceived : null,
                enumerable: true
            }
        });
        return _this;
    }
    return RemoteVideoTrackStats;
}(RemoteTrackStats);
module.exports = RemoteVideoTrackStats; //# sourceMappingURL=remotevideotrackstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/statsreport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var LocalAudioTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/localaudiotrackstats.js [app-client] (ecmascript)");
var LocalVideoTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/localvideotrackstats.js [app-client] (ecmascript)");
var RemoteAudioTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/remoteaudiotrackstats.js [app-client] (ecmascript)");
var RemoteVideoTrackStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/remotevideotrackstats.js [app-client] (ecmascript)");
/**
 * Statistics report for an RTCPeerConnection.
 * @property {string} peerConnectionId - ID of the RTCPeerConnection
 * @property {Array<LocalAudioTrackStats>} localAudioTrackStats - List of {@link LocalAudioTrackStats}
 * @property {Array<LocalVideoTrackStats>} localVideoTrackStats - List of {@link LocalVideoTrackStats}
 * @property {Array<RemoteAudioTrackStats>} remoteAudioTrackStats - List of {@link RemoteAudioTrackStats}
 * @property {Array<RemoteVideoTrackStats>} remoteVideoTrackStats - List of {@link RemoteVideoTrackStats}
 */ var StatsReport = function() {
    /**
     * @param {string} peerConnectionId - RTCPeerConnection ID
     * @param {StandardizedStatsResponse} statsResponse
     * @param {boolean} prepareForInsights - if report is being prepared to send to insights.
     */ function StatsReport(peerConnectionId, statsResponse, prepareForInsights) {
        if (typeof peerConnectionId !== 'string') {
            throw new Error('RTCPeerConnection id must be a string');
        }
        Object.defineProperties(this, {
            peerConnectionId: {
                value: peerConnectionId,
                enumerable: true
            },
            localAudioTrackStats: {
                value: statsResponse.localAudioTrackStats.map(function(report) {
                    return new LocalAudioTrackStats(report.trackId, report, prepareForInsights);
                }),
                enumerable: true
            },
            localVideoTrackStats: {
                value: statsResponse.localVideoTrackStats.map(function(report) {
                    return new LocalVideoTrackStats(report.trackId, report, prepareForInsights);
                }),
                enumerable: true
            },
            remoteAudioTrackStats: {
                value: statsResponse.remoteAudioTrackStats.map(function(report) {
                    return new RemoteAudioTrackStats(report.trackId, report);
                }),
                enumerable: true
            },
            remoteVideoTrackStats: {
                value: statsResponse.remoteVideoTrackStats.map(function(report) {
                    return new RemoteVideoTrackStats(report.trackId, report);
                }),
                enumerable: true
            }
        });
    }
    return StatsReport;
}();
module.exports = StatsReport; //# sourceMappingURL=statsreport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/icereport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * @property {number} [availableSend] - bps (undefined in Firefox)
 * @property {number} recv - bps
 * @property {number} [rtt] - s (undefined in Firefox)
 * @property {number} send - bps
 */ var IceReport = function() {
    /**
     * Construct an {@link IceReport}.
     * @param {number} send - bps
     * @param {number} recv - bps
     * @param {number} [rtt] - s
     * @param {number} [availableSend] - bps
     */ function IceReport(send, recv, availableSend, rtt) {
        Object.defineProperties(this, {
            availableSend: {
                enumerable: true,
                value: availableSend
            },
            recv: {
                enumerable: true,
                value: recv
            },
            rtt: {
                enumerable: true,
                value: rtt
            },
            send: {
                enumerable: true,
                value: send
            }
        });
    }
    /**
     * @param {RTCStats} olderStats
     * @param {RTCStats} newerStats
     * @returns {IceReport}
     */ IceReport.of = function(olderStats, newerStats) {
        var secondsElapsed = (newerStats.timestamp - olderStats.timestamp) / 1000;
        var deltaBytesSent = newerStats.bytesSent - olderStats.bytesSent;
        var deltaBytesReceived = newerStats.bytesReceived - olderStats.bytesReceived;
        var send = secondsElapsed > 0 ? deltaBytesSent / secondsElapsed * 8 : 0;
        var recv = secondsElapsed > 0 ? deltaBytesReceived / secondsElapsed * 8 : 0;
        var availableSend = newerStats.availableOutgoingBitrate, rtt = newerStats.currentRoundTripTime;
        return new IceReport(send, recv, availableSend, rtt);
    };
    return IceReport;
}();
module.exports = IceReport; //# sourceMappingURL=icereport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/icereportfactory.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var IceReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/icereport.js [app-client] (ecmascript)");
/**
 * @property {IceReport} lastReport
 * @property {?RTCStats} lastStats
 */ var IceReportFactory = function() {
    /**
     * Construct an {@link IceReportFactory}.
     */ function IceReportFactory() {
        Object.defineProperties(this, {
            lastReport: {
                enumerable: true,
                value: new IceReport(0, 0),
                writable: true
            },
            lastStats: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
    }
    /**
     * Create an {@link IceReport}.
     * @param {RTCStats} newerStats;
     * @returns {IceReport}
     */ IceReportFactory.prototype.next = function(newerStats) {
        var olderStats = this.lastStats;
        this.lastStats = newerStats;
        if (olderStats) {
            var report = olderStats.id === newerStats.id ? IceReport.of(olderStats, newerStats) : new IceReport(0, 0);
            this.lastReport = report;
        }
        return this.lastReport;
    };
    return IceReportFactory;
}();
module.exports = IceReportFactory; //# sourceMappingURL=icereportfactory.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/average.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint no-undefined:0 */ 'use strict';
/**
 * @param {Array<number|undefined>} xs
 * @returns {number|undefined}
 */ function average(xs) {
    xs = xs.filter(function(x) {
        return typeof x === 'number';
    });
    return xs.length < 1 ? undefined : xs.reduce(function(y, x) {
        return x + y;
    }) / xs.length;
}
module.exports = average; //# sourceMappingURL=average.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/senderorreceiverreport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * @property {StatsId} id
 * @property {TrackId} trackId
 * @property {number} bitrate - bps
 */ var SenderOrReceiverReport = function() {
    /**
     * Construct a {@link SenderOrReceiverReport}.
     * @param {StatsId} id
     * @param {TrackId} trackId
     * @param {number} bitrate - bps
     */ function SenderOrReceiverReport(id, trackId, bitrate) {
        Object.defineProperties(this, {
            id: {
                enumerable: true,
                value: id
            },
            trackId: {
                enumerable: true,
                value: trackId
            },
            bitrate: {
                enumerable: true,
                value: bitrate
            }
        });
    }
    return SenderOrReceiverReport;
}();
module.exports = SenderOrReceiverReport; //# sourceMappingURL=senderorreceiverreport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/sum.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * @param {Array<number|undefined>} xs
 * @returns {number}
 */ function sum(xs) {
    return xs.reduce(function(y, x) {
        return typeof x === 'number' ? x + y : y;
    }, 0);
}
module.exports = sum; //# sourceMappingURL=sum.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/receiverreport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var average = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/average.js [app-client] (ecmascript)");
var SenderOrReceiverReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderorreceiverreport.js [app-client] (ecmascript)");
var sum = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/sum.js [app-client] (ecmascript)");
/**
 * @interface ReceiverSummary
 * @property {number} bitrate
 * @property {number} fractionLost - 0–1
 * @property {number} [jitter] - s (undefined for video tracks in Chrome)
 */ /**
 * @extends SenderOrReceiverReport
 * @property {number} deltaPacketsLost
 * @property {number} deltaPacketsReceived
 * @property {number} [fractionLost] - 0–1 (undefined in Firefox)
 * @property {number} [jitter] - s (undefined for video tracks in Chrome)
 * @property {number} phonyPacketsLost - 0–1
 */ var ReceiverReport = function(_super) {
    __extends(ReceiverReport, _super);
    /**
     * @param {StatsId} id
     * @param {TrackId} trackId
     * @param {number} bitrate - bps
     * @param {number} deltaPacketsLost
     * @param {number} deltaPacketsReceived
     * @param {number} [fractionLost] - 0–1 (undefined in Firefox)
     * @param {number} [jitter] - s (undefined for video tracks in Chrome)
     */ function ReceiverReport(id, trackId, bitrate, deltaPacketsLost, deltaPacketsReceived, fractionLost, jitter) {
        var _this = _super.call(this, id, trackId, bitrate) || this;
        var phonyFractionLost = deltaPacketsReceived > 0 ? deltaPacketsLost / deltaPacketsReceived : 0;
        Object.defineProperties(_this, {
            deltaPacketsLost: {
                enumerable: true,
                value: deltaPacketsLost
            },
            deltaPacketsReceived: {
                enumerable: true,
                value: deltaPacketsReceived
            },
            fractionLost: {
                enumerable: true,
                value: fractionLost
            },
            jitter: {
                enumerable: true,
                value: jitter
            },
            phonyFractionLost: {
                enumerable: true,
                value: phonyFractionLost
            }
        });
        return _this;
    }
    /**
     * Create a {@link ReceiverReport}.
     * @param {string} trackId
     * @param {RTCStats} olderStats
     * @param {RTCStats} newerStats
     * @returns {ReceiverReport}
     */ ReceiverReport.of = function(trackId, olderStats, newerStats) {
        if (olderStats.id !== newerStats.id) {
            throw new Error('RTCStats IDs must match');
        }
        var secondsElapsed = (newerStats.timestamp - olderStats.timestamp) / 1000;
        var deltaBytesReceived = newerStats.bytesReceived - olderStats.bytesReceived;
        var bitrate = secondsElapsed > 0 ? deltaBytesReceived / secondsElapsed * 8 : 0;
        var deltaPacketsLost = Math.max(newerStats.packetsLost - olderStats.packetsLost, 0);
        var deltaPacketsReceived = newerStats.packetsReceived - olderStats.packetsReceived;
        var fractionLost = newerStats.fractionLost, jitter = newerStats.jitter;
        return new ReceiverReport(olderStats.id, trackId, bitrate, deltaPacketsLost, deltaPacketsReceived, fractionLost, jitter);
    };
    /**
     * Summarize {@link ReceiverReport}s by summing and averaging their values.
     * @param {Array<ReceiverReport>} reports
     * @returns {ReceiverSummary}
     */ ReceiverReport.summarize = function(reports) {
        var summaries = reports.map(function(report) {
            return report.summarize();
        });
        var bitrate = sum(summaries.map(function(summary) {
            return summary.bitrate;
        }));
        var fractionLost = average(summaries.map(function(summary) {
            return summary.fractionLost;
        }));
        var jitter = average(summaries.map(function(summary) {
            return summary.jitter;
        }));
        return {
            bitrate: bitrate,
            fractionLost: fractionLost,
            jitter: jitter
        };
    };
    /**
     * Summarize the {@link ReceiveReport}.
     * @returns {ReceiverSummary}
     */ ReceiverReport.prototype.summarize = function() {
        return {
            bitrate: this.bitrate,
            fractionLost: typeof this.fractionLost === 'number' ? this.fractionLost : this.phonyFractionLost,
            jitter: this.jitter
        };
    };
    return ReceiverReport;
}(SenderOrReceiverReport);
module.exports = ReceiverReport; //# sourceMappingURL=receiverreport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/senderreport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint no-undefined:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var average = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/average.js [app-client] (ecmascript)");
var SenderOrReceiverReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderorreceiverreport.js [app-client] (ecmascript)");
var sum = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/sum.js [app-client] (ecmascript)");
/**
 * @interface SenderSummary
 * @property {number} bitrate
 * @property {number} [rtt] - s (undefined in Chrome)
 */ /**
 * @extends SenderOrReceiverReport
 * @property {number} [rtt] - s (undefined in Chrome)
 */ var SenderReport = function(_super) {
    __extends(SenderReport, _super);
    /**
     * Construct a {@link SenderReport}.
     * @param {StatsId} id
     * @param {TrackId} trackId
     * @param {number} bitrate - bps
     * @param {number} [rtt] - s
     */ function SenderReport(id, trackId, bitrate, rtt) {
        var _this = _super.call(this, id, trackId, bitrate) || this;
        Object.defineProperties(_this, {
            rtt: {
                enumerable: true,
                value: rtt
            }
        });
        return _this;
    }
    /**
     * Create a {@link SenderReport}.
     * @param {string} trackId
     * @param {RTCStats} olderStats
     * @param {RTCStats} newerStats
     * @param {RTCRemoteInboundRtpStreamStats} [newerRemoteStats]
     * @returns {SenderReport}
     */ SenderReport.of = function(trackId, olderStats, newerStats, newerRemoteStats) {
        if (olderStats.id !== newerStats.id) {
            throw new Error('RTCStats IDs must match');
        }
        var secondsElapsed = (newerStats.timestamp - olderStats.timestamp) / 1000;
        var deltaBytesSent = newerStats.bytesSent - olderStats.bytesSent;
        var bitrate = secondsElapsed > 0 ? deltaBytesSent / secondsElapsed * 8 : 0;
        var rtt = newerRemoteStats && typeof newerRemoteStats.roundTripTime === 'number' ? newerRemoteStats.roundTripTime / 1000 : undefined;
        return new SenderReport(olderStats.id, trackId, bitrate, rtt);
    };
    /**
     * Summarize {@link SenderReport}s by summing and averaging their values.
     * @param {Array<SenderReport>} reports
     * @returns {SenderSummary}
     */ SenderReport.summarize = function(reports) {
        var bitrate = sum(reports.map(function(report) {
            return report.bitrate;
        }));
        var rtt = average(reports.map(function(report) {
            return report.rtt;
        }));
        return {
            bitrate: bitrate,
            rtt: rtt
        };
    };
    return SenderReport;
}(SenderOrReceiverReport);
module.exports = SenderReport; //# sourceMappingURL=senderreport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/peerconnectionreport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var ReceiverReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/receiverreport.js [app-client] (ecmascript)");
var SenderReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderreport.js [app-client] (ecmascript)");
/**
 * @interface SenderAndReceiverReports
 * @property {Array<SenderReport>} send
 * @property {Array<ReceiverReport>} recv
 */ /**
 * @interface SenderAndReceiverSummary
 * @property {SenderSummary} send
 * @property {ReceiverSummary} recv
 */ /**
 * @interface PeerConnectionSummary
 * @property {IceReport} ice
 * @property {SenderSummary} send
 * @property {ReceiverSummary} recv
 * @property {SenderAndReceiverSummary} audio
 * @property {SenderAndReceiverSummary} video
 */ /**
 * @property {IceReport} ice
 * @roperty {SenderAndReceiverReports} audio
 * @roperty {SenderAndReceiverReports} video
 */ var PeerConnectionReport = function() {
    /**
     * Construct a {@link PeerConnectionReport}.
     * @param {IceReport} ice
     * @param {SenderAndReceiverReports} audio
     * @param {SenderAndReceiverReports} video
     */ function PeerConnectionReport(ice, audio, video) {
        Object.defineProperties(this, {
            ice: {
                enumerable: true,
                value: ice
            },
            audio: {
                enumerable: true,
                value: audio
            },
            video: {
                enumerable: true,
                value: video
            }
        });
    }
    /**
     * Summarize the {@link PeerConnectionReport} by summarizing its
     * {@link SenderReport}s and {@link ReceiverReport}s.
     * @returns {PeerConnectionSummary}
     */ PeerConnectionReport.prototype.summarize = function() {
        var senderReports = this.audio.send.concat(this.video.send);
        var send = SenderReport.summarize(senderReports);
        var receiverReports = this.audio.recv.concat(this.video.recv);
        var recv = ReceiverReport.summarize(receiverReports);
        return {
            ice: this.ice,
            send: send,
            recv: recv,
            audio: {
                send: SenderReport.summarize(this.audio.send),
                recv: ReceiverReport.summarize(this.audio.recv)
            },
            video: {
                send: SenderReport.summarize(this.video.send),
                recv: ReceiverReport.summarize(this.video.recv)
            }
        };
    };
    return PeerConnectionReport;
}();
module.exports = PeerConnectionReport; //# sourceMappingURL=peerconnectionreport.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/senderorreceiverreportfactory.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * @property {StatsId} id
 * @property {TrackId} trackId
 * @property {RTCStats} lastStats
 */ var SenderOrReceiverReportFactory = function() {
    /**
     * @param {StatsId} id
     * @param {TrackId} trackId
     * @param {RTCStats} initialStats
     */ function SenderOrReceiverReportFactory(id, trackId, initialStats) {
        Object.defineProperties(this, {
            id: {
                enumerable: true,
                value: id,
                writable: true
            },
            trackId: {
                enumerable: true,
                value: trackId,
                writable: true
            },
            lastStats: {
                enumerable: true,
                value: initialStats,
                writable: true
            }
        });
    }
    return SenderOrReceiverReportFactory;
}();
module.exports = SenderOrReceiverReportFactory; //# sourceMappingURL=senderorreceiverreportfactory.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/receiverreportfactory.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var ReceiverReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/receiverreport.js [app-client] (ecmascript)");
var SenderOrReceiverReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderorreceiverreportfactory.js [app-client] (ecmascript)");
/**
 * @extends SenderOrReceiverReportFactory
 * @param {?ReceiverReport} lastReport
 */ var ReceiverReportFactory = function(_super) {
    __extends(ReceiverReportFactory, _super);
    /**
     * Construct a {@link ReceiverReportFactory}.
     * @param {TrackId} trackId
     * @param {RTCStats} initialStats
     */ function ReceiverReportFactory(trackId, initialStats) {
        var _this = _super.call(this, initialStats.id, trackId, initialStats) || this;
        Object.defineProperties(_this, {
            lastReport: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
        return _this;
    }
    /**
     * Create a {@link ReceiverReport}.
     * @param {TrackId} trackId
     * @param {RTCStats} newerStats
     * @returns {ReceiverReport}
     */ ReceiverReportFactory.prototype.next = function(trackId, newerStats) {
        var olderStats = this.lastStats;
        this.lastStats = newerStats;
        this.trackId = trackId;
        var report = ReceiverReport.of(trackId, olderStats, newerStats);
        this.lastReport = report;
        return report;
    };
    return ReceiverReportFactory;
}(SenderOrReceiverReportFactory);
module.exports = ReceiverReportFactory; //# sourceMappingURL=receiverreportfactory.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/senderreportfactory.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var SenderOrReceiverReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderorreceiverreportfactory.js [app-client] (ecmascript)");
var SenderReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderreport.js [app-client] (ecmascript)");
/**
 * @extends {SenderOrReceiverReportFactory}
 * @property {?SenderReport} lastReport
 */ var SenderReportFactory = function(_super) {
    __extends(SenderReportFactory, _super);
    /**
     * Construct a {@link SenderReportFactory}.
     * @param {TrackId} trackId
     * @param {RTCStats} initialStats
     */ function SenderReportFactory(trackId, initialStats) {
        var _this = _super.call(this, initialStats.id, trackId, initialStats) || this;
        Object.defineProperties(_this, {
            lastReport: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
        return _this;
    }
    /**
     * @param {TrackId} trackId
     * @param {RTCStats} newerStats
     * @param {RTCRemoteInboundRtpStreamStats} [newerRemoteStats]
     * @returns {SenderReport}
     */ SenderReportFactory.prototype.next = function(trackId, newerStats, newerRemoteStats) {
        var olderStats = this.lastStats;
        this.lastStats = newerStats;
        this.trackId = trackId;
        var report = SenderReport.of(trackId, olderStats, newerStats, newerRemoteStats);
        this.lastReport = report;
        return report;
    };
    return SenderReportFactory;
}(SenderOrReceiverReportFactory);
module.exports = SenderReportFactory; //# sourceMappingURL=senderreportfactory.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/peerconnectionreportfactory.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var guessBrowser = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").guessBrowser;
var IceReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/icereportfactory.js [app-client] (ecmascript)");
var PeerConnectionReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/peerconnectionreport.js [app-client] (ecmascript)");
var ReceiverReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/receiverreportfactory.js [app-client] (ecmascript)");
var SenderReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/senderreportfactory.js [app-client] (ecmascript)");
/**
 * @typedef {string} TrackId
 */ /**
 * @typedef {string} StatsId
 */ /**
 * @interface SenderReportFactoriesByMediaType
 * @property {Map<StatsId, SenderReportFactory>} audio
 * @property {Map<StatsId, SenderReportFactory>} video
 */ /**
 * @interface ReceiverReportFactoriesByMediaType
 * @property {Map<StatsId, ReceiverReportFactory>} audio
 * @property {Map<StatsId, ReceiverReportFactory>} video
 */ /**
 * @interface SenderAndReceiverReportFactories
 * @property {Map<StatsId, SenderReportFactories>} send
 * @property {Map<StatsId, ReceiverReportFactories>} recv
 */ /**
 * @interface {StatsIdsByMediaType}
 * @property {Set<StatsId>} audio
 * @property {Set<StatsId>} video
 */ /**
 * @property {RTCPeerConnection} pc
 * @property {IceReportFactory} iceReportFactory
 * @property {SenderAndReceiverReportFactories} audio
 * @property {SenderAndReceiverReportFactories} video
 * @property {?PeerConnectionReport} lastReport
 */ var PeerConnectionReportFactory = function() {
    /**
     * Construct a {@link PeerConnectionReportFactory}.
     * @param {RTCPeerConnection} pc
     */ function PeerConnectionReportFactory(pc) {
        Object.defineProperties(this, {
            pc: {
                enumerable: true,
                value: pc
            },
            ice: {
                enumerable: true,
                value: new IceReportFactory()
            },
            audio: {
                enumerable: true,
                value: {
                    send: new Map(),
                    recv: new Map()
                }
            },
            video: {
                enumerable: true,
                value: {
                    send: new Map(),
                    recv: new Map()
                }
            },
            lastReport: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
    }
    /**
     * Create a {@link PeerConnectionReport}.
     * @returns {Promise<PeerConnectionReport>}
     */ PeerConnectionReportFactory.prototype.next = function() {
        var _this = this;
        var updatePromise = guessBrowser() === 'firefox' ? updateFirefox(this) : updateChrome(this);
        return updatePromise.then(function() {
            var audioSenderReportFactories = __spreadArray([], __read(_this.audio.send.values()));
            var videoSenderReportFactories = __spreadArray([], __read(_this.video.send.values()));
            var audioReceiverReportFactories = __spreadArray([], __read(_this.audio.recv.values()));
            var videoReceiverReportFactories = __spreadArray([], __read(_this.video.recv.values()));
            var report = new PeerConnectionReport(_this.ice.lastReport, {
                send: audioSenderReportFactories.map(function(factory) {
                    return factory.lastReport;
                }).filter(function(report) {
                    return report;
                }),
                recv: audioReceiverReportFactories.map(function(factory) {
                    return factory.lastReport;
                }).filter(function(report) {
                    return report;
                })
            }, {
                send: videoSenderReportFactories.map(function(factory) {
                    return factory.lastReport;
                }).filter(function(report) {
                    return report;
                }),
                recv: videoReceiverReportFactories.map(function(factory) {
                    return factory.lastReport;
                }).filter(function(report) {
                    return report;
                })
            });
            _this.lastReport = report;
            return report;
        });
    };
    return PeerConnectionReportFactory;
}();
/**
 * Construct a Map from MediaStreamTrack Ids to RTCStatsReports.
 * @param {Array<RTCRtpSender>|Array<RTCRtpReceiver>} sendersOrReceivers - each
 *   RTCRtpSender should have a non-null track
 * @returns {Promise<Map<TrackId, RTCStats>>}
 */ function getSenderOrReceiverReports(sendersOrReceivers) {
    return Promise.all(sendersOrReceivers.map(function(senderOrReceiver) {
        var trackId = senderOrReceiver.track.id;
        return senderOrReceiver.getStats().then(function(report) {
            var e_1, _a;
            try {
                // NOTE(mroberts): We have to rewrite Ids due to this bug:
                //
                //   https://bugzilla.mozilla.org/show_bug.cgi?id=1463430
                //
                for(var _b = __values(report.values()), _c = _b.next(); !_c.done; _c = _b.next()){
                    var stats = _c.value;
                    if (stats.type === 'inbound-rtp') {
                        stats.id = trackId + "-" + stats.id;
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            return [
                trackId,
                report
            ];
        });
    })).then(function(pairs) {
        return new Map(pairs);
    });
}
/**
 * @param {SenderReportFactory.constructor} SenderReportFactory
 * @param {SenderReportFactoriesByMediaType} sendersByMediaType
 * @param {RTCStatsReport} report
 * @param {RTCStats} stats
 * @param {TrackId} [trackId]
 * @returns {?SenderReportFactory}
 */ /**
* @param {ReceiverReportFactory.constructor} ReceiverReportFactory
* @param {ReceiverReportFactoriesByMediaType} receiversByMediaType
* @param {RTCStatsReport} report
* @param {RTCStats} stats
* @param {TrackId} [trackId]
* @returns {?ReceiverReportFactory}
*/ function getOrCreateSenderOrReceiverReportFactory(SenderOrReceiverReportFactory, sendersOrReceiversByMediaType, report, stats, trackId) {
    var sendersOrReceivers = sendersOrReceiversByMediaType[stats.mediaType];
    if (!trackId) {
        var trackStats = report.get(stats.trackId);
        if (trackStats) {
            trackId = trackStats.trackIdentifier;
        }
    }
    if (sendersOrReceivers && trackId) {
        if (sendersOrReceivers.has(stats.id)) {
            return sendersOrReceivers.get(stats.id);
        }
        var senderOrReceiverFactory = new SenderOrReceiverReportFactory(trackId, stats);
        sendersOrReceivers.set(stats.id, senderOrReceiverFactory);
    }
    return null;
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @returns {SenderReportFactoriesByMediaType}
 */ function getSenderReportFactoriesByMediaType(factory) {
    return {
        audio: factory.audio.send,
        video: factory.video.send
    };
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @returns {ReceiverReportFactoriesByMediaType}
 */ function getReceiverReportFactoriesByMediaType(factory) {
    return {
        audio: factory.audio.recv,
        video: factory.video.recv
    };
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @param {RTCStatsReport} report
 * @param {RTCStats} stats
 * @param {TrackId} [trackId]
 * @returns {?SenderReportFactory}
 */ function getOrCreateSenderReportFactory(factory, report, stats, trackId) {
    return getOrCreateSenderOrReceiverReportFactory(SenderReportFactory, getSenderReportFactoriesByMediaType(factory), report, stats, trackId);
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @param {RTCStatsReport} report
 * @param {RTCStats} stats
 * @param {TrackId} [trackId]
 * @returns {?ReceiverReportFactory}
 */ function getOrCreateReceiverReportFactory(factory, report, stats, trackId) {
    return getOrCreateSenderOrReceiverReportFactory(ReceiverReportFactory, getReceiverReportFactoriesByMediaType(factory), report, stats, trackId);
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @retuns {StatsIdsByMediaType}
 */ function getSenderReportFactoryIdsByMediaType(factory) {
    return {
        audio: new Set(factory.audio.send.keys()),
        video: new Set(factory.video.send.keys())
    };
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @retuns {StatsIdsByMediaType}
 */ function getReceiverReportFactoryIdsByMediaType(factory) {
    return {
        audio: new Set(factory.audio.recv.keys()),
        video: new Set(factory.video.recv.keys())
    };
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @param {RTCStatsReport} report
 * @param {StatsIdsByMediaType} senderReportFactoryIdsToDeleteByMediaType
 * @param {TrackId} [trackId]
 * @returns {void}
 */ function updateSenderReports(factory, report, senderReportFactoryIdsToDeleteByMediaType, trackId) {
    var e_2, _a;
    try {
        for(var _b = __values(report.values()), _c = _b.next(); !_c.done; _c = _b.next()){
            var stats = _c.value;
            if (stats.type === 'outbound-rtp' && !stats.isRemote) {
                if (guessBrowser() !== 'firefox' && !stats.trackId) {
                    continue;
                }
                var senderReportFactoryIdsToDelete = senderReportFactoryIdsToDeleteByMediaType[stats.mediaType];
                if (senderReportFactoryIdsToDelete) {
                    senderReportFactoryIdsToDelete.delete(stats.id);
                }
                var senderReportFactory = getOrCreateSenderReportFactory(factory, report, stats, trackId);
                if (senderReportFactory) {
                    var remoteInboundStats = report.get(stats.remoteId);
                    senderReportFactory.next(trackId || senderReportFactory.trackId, stats, remoteInboundStats);
                }
            }
        }
    } catch (e_2_1) {
        e_2 = {
            error: e_2_1
        };
    } finally{
        try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
        } finally{
            if (e_2) throw e_2.error;
        }
    }
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @param {RTCStatsReport} report
 * @param {StatsIdsByMediaType} receiverReportFactoryIdsToDeleteByMediaType
 * @param {TrackId} [trackId]
 * @returns {void}
 */ function updateReceiverReports(factory, report, receiverReportFactoryIdsToDeleteByMediaType, trackId) {
    var e_3, _a;
    try {
        for(var _b = __values(report.values()), _c = _b.next(); !_c.done; _c = _b.next()){
            var stats = _c.value;
            if (stats.type === 'inbound-rtp' && !stats.isRemote) {
                var receiverReportFactoryIdsToDelete = receiverReportFactoryIdsToDeleteByMediaType[stats.mediaType];
                if (receiverReportFactoryIdsToDelete) {
                    receiverReportFactoryIdsToDelete.delete(stats.id);
                }
                var receiverReportFactory = getOrCreateReceiverReportFactory(factory, report, stats, trackId);
                if (receiverReportFactory) {
                    receiverReportFactory.next(trackId || receiverReportFactory.trackId, stats);
                }
            }
        }
    } catch (e_3_1) {
        e_3 = {
            error: e_3_1
        };
    } finally{
        try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
        } finally{
            if (e_3) throw e_3.error;
        }
    }
}
/**
 * @param {SenderReportFactoriesByMediaType|ReceiverReportFactoriesByMediaType} senderOrReceiverReportFactoriesByMediaType
 * @param {StatsIdsByMediaType} senderOrReceiverReportFactoryIdsByMediaType
 * @returns {void}
 */ function deleteSenderOrReceiverReportFactories(senderOrReceiverReportFactoriesByMediaType, senderOrReceiverReportFactoryIdsByMediaType) {
    var _loop_1 = function(mediaType) {
        var senderOrReceiverReportFactories = senderOrReceiverReportFactoriesByMediaType[mediaType];
        var senderOrReceiverReportFactoryIds = senderOrReceiverReportFactoryIdsByMediaType[mediaType];
        senderOrReceiverReportFactoryIds.forEach(function(senderOrReceiverReportFactoryId) {
            return senderOrReceiverReportFactories.delete(senderOrReceiverReportFactoryId);
        });
    };
    for(var mediaType in senderOrReceiverReportFactoryIdsByMediaType){
        _loop_1(mediaType);
    }
}
/**
 * @param {IceReportFactory} ice
 * @param {RTCStatsReport} report
 * @returns {void}
 */ function updateIceReport(ice, report) {
    var e_4, _a, e_5, _b;
    var selectedCandidatePair;
    try {
        for(var _c = __values(report.values()), _d = _c.next(); !_d.done; _d = _c.next()){
            var stats = _d.value;
            if (stats.type === 'transport') {
                selectedCandidatePair = report.get(stats.selectedCandidatePairId);
            }
        }
    } catch (e_4_1) {
        e_4 = {
            error: e_4_1
        };
    } finally{
        try {
            if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
        } finally{
            if (e_4) throw e_4.error;
        }
    }
    if (selectedCandidatePair) {
        ice.next(selectedCandidatePair);
        return;
    }
    try {
        for(var _e = __values(report.values()), _f = _e.next(); !_f.done; _f = _e.next()){
            var stats = _f.value;
            if (stats.type === 'candidate-pair' && stats.nominated && ('selected' in stats ? stats.selected : true)) {
                ice.next(stats);
            }
        }
    } catch (e_5_1) {
        e_5 = {
            error: e_5_1
        };
    } finally{
        try {
            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
        } finally{
            if (e_5) throw e_5.error;
        }
    }
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @returns {Promise<PeerConnectionReport>}
 */ function updateFirefox(factory) {
    var senders = factory.pc.getTransceivers().filter(function(transceiver) {
        return transceiver.currentDirection && transceiver.currentDirection.match(/send/) && transceiver.sender.track;
    }).map(function(transceiver) {
        return transceiver.sender;
    });
    var receivers = factory.pc.getTransceivers().filter(function(transceiver) {
        return transceiver.currentDirection && transceiver.currentDirection.match(/recv/);
    }).map(function(transceiver) {
        return transceiver.receiver;
    });
    return Promise.all([
        getSenderOrReceiverReports(senders),
        getSenderOrReceiverReports(receivers),
        factory.pc.getStats()
    ]).then(function(_a) {
        var _b = __read(_a, 3), senderReports = _b[0], receiverReports = _b[1], pcReport = _b[2];
        var senderReportFactoriesByMediaType = getSenderReportFactoriesByMediaType(factory);
        var senderReportFactoryIdsToDeleteByMediaType = getSenderReportFactoryIdsByMediaType(factory);
        senderReports.forEach(function(report, trackId) {
            return updateSenderReports(factory, report, senderReportFactoryIdsToDeleteByMediaType, trackId);
        });
        deleteSenderOrReceiverReportFactories(senderReportFactoriesByMediaType, senderReportFactoryIdsToDeleteByMediaType);
        var receiverReportFactoriesByMediaType = getReceiverReportFactoriesByMediaType(factory);
        var receiverReportFactoryIdsToDeleteByMediaType = getReceiverReportFactoryIdsByMediaType(factory);
        receiverReports.forEach(function(report, trackId) {
            return updateReceiverReports(factory, report, receiverReportFactoryIdsToDeleteByMediaType, trackId);
        });
        deleteSenderOrReceiverReportFactories(receiverReportFactoriesByMediaType, receiverReportFactoryIdsToDeleteByMediaType);
        updateIceReport(factory.ice, pcReport);
    });
}
/**
 * @param {PeerConnectionReportFactory} factory
 * @returns {Promise<PeerConnectionReport>}
 */ function updateChrome(factory) {
    return factory.pc.getStats().then(function(report) {
        var senderReportFactoriesByMediaType = getSenderReportFactoriesByMediaType(factory);
        var senderReportFactoryIdsToDeleteByMediaType = getSenderReportFactoryIdsByMediaType(factory);
        updateSenderReports(factory, report, senderReportFactoryIdsToDeleteByMediaType);
        deleteSenderOrReceiverReportFactories(senderReportFactoriesByMediaType, senderReportFactoryIdsToDeleteByMediaType);
        var receiverReportFactoriesByMediaType = getReceiverReportFactoriesByMediaType(factory);
        var receiverReportFactoryIdsToDeleteByMediaType = getReceiverReportFactoryIdsByMediaType(factory);
        updateReceiverReports(factory, report, receiverReportFactoryIdsToDeleteByMediaType);
        deleteSenderOrReceiverReportFactories(receiverReportFactoriesByMediaType, receiverReportFactoryIdsToDeleteByMediaType);
        updateIceReport(factory.ice, report);
    });
}
module.exports = PeerConnectionReportFactory; //# sourceMappingURL=peerconnectionreportfactory.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitybandwidthstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Bandwidth network quality statistics.
 * @property {?number} actual - the actual bandwidth used, in bits per second
 * @property {?number} available - an estimate of available useable bandwidth, in bits per second
 * @property {?NetworkQualityLevel} level - {@link NetworkQualityLevel} for bandwidth
 */ var NetworkQualityBandwidthStats = function() {
    /**
     * Construct a {@link NetworkQualityBandwidthStats}.
     * @param {BandwidthStats} bandwidthStats
     */ function NetworkQualityBandwidthStats(_a) {
        var _b = _a.actual, actual = _b === void 0 ? null : _b, _c = _a.available, available = _c === void 0 ? null : _c, _d = _a.level, level = _d === void 0 ? null : _d;
        Object.defineProperties(this, {
            actual: {
                value: actual,
                enumerable: true
            },
            available: {
                value: available,
                enumerable: true
            },
            level: {
                value: level,
                enumerable: true
            }
        });
    }
    return NetworkQualityBandwidthStats;
}();
module.exports = NetworkQualityBandwidthStats; //# sourceMappingURL=networkqualitybandwidthstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualityfractionloststats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Fraction lost network quality statistics.
 * @property {?number} fractionLost - packets lost
 * @property {?NetworkQualityLevel} level - {@link NetworkQualityLevel} for fraction lost
 */ var NetworkQualityFractionLostStats = function() {
    /**
     * Construct a {@link NetworkQualityFractionLostStats}.
     * @param {FractionLostStats} fractionLostStats
     */ function NetworkQualityFractionLostStats(_a) {
        var _b = _a.fractionLost, fractionLost = _b === void 0 ? null : _b, _c = _a.level, level = _c === void 0 ? null : _c;
        Object.defineProperties(this, {
            fractionLost: {
                value: fractionLost,
                enumerable: true
            },
            level: {
                value: level,
                enumerable: true
            }
        });
    }
    return NetworkQualityFractionLostStats;
}();
module.exports = NetworkQualityFractionLostStats; //# sourceMappingURL=networkqualityfractionloststats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitylatencystats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Latency network quality statistics.
 * @property {?number} jitter - media jitter in seconds
 * @property {?number} rtt - round trip time in seconds
 * @property {?NetworkQualityLevel} level - {@link NetworkQualityLevel} for latency
 */ var NetworkQualityLatencyStats = function() {
    /**
     * Construct a {@link NetworkQualityLatencyStats}.
     * @param {LatencyStats} latencyStats
     */ function NetworkQualityLatencyStats(_a) {
        var _b = _a.jitter, jitter = _b === void 0 ? null : _b, _c = _a.rtt, rtt = _c === void 0 ? null : _c, _d = _a.level, level = _d === void 0 ? null : _d;
        Object.defineProperties(this, {
            jitter: {
                value: jitter,
                enumerable: true
            },
            rtt: {
                value: rtt,
                enumerable: true
            },
            level: {
                value: level,
                enumerable: true
            }
        });
    }
    return NetworkQualityLatencyStats;
}();
module.exports = NetworkQualityLatencyStats; //# sourceMappingURL=networkqualitylatencystats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitysendorrecvstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var NetworkQualityBandwidthStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitybandwidthstats.js [app-client] (ecmascript)");
var NetworkQualityFractionLostStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualityfractionloststats.js [app-client] (ecmascript)");
var NetworkQualityLatencyStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitylatencystats.js [app-client] (ecmascript)");
/**
 * Network quality statistics shared between {@link NetworkQualitySendStats} and
 * {@link NetworkQualityRecvStats} based on which a {@link Participant}'s
 * {@link NetworkQualityMediaStats}<code style="padding:0 0">#send</code> or
 * {@link NetworkQualityMediaStats}<code style="padding:0 0">#recv</code> is calculated.
 * @property {?NetworkQualityBandwidthStats} bandwidth - bandwidth statistics
 * @property {?NetworkQualityLatencyStats} latency - latency statistics
 * @property {?NetworkQualityFractionLostStats} fractionLost - fraction lost statistics
 */ var NetworkQualitySendOrRecvStats = function() {
    /**
     * Construct a {@link NetworkQualitySendOrRecvStats}.
     * @param {SendOrRecvStats} sendOrRecvStats
     */ function NetworkQualitySendOrRecvStats(_a) {
        var _b = _a.bandwidth, bandwidth = _b === void 0 ? null : _b, _c = _a.fractionLost, fractionLost = _c === void 0 ? null : _c, _d = _a.latency, latency = _d === void 0 ? null : _d;
        Object.defineProperties(this, {
            bandwidth: {
                value: bandwidth ? new NetworkQualityBandwidthStats(bandwidth) : null,
                enumerable: true
            },
            fractionLost: {
                value: fractionLost ? new NetworkQualityFractionLostStats(fractionLost) : null,
                enumerable: true
            },
            latency: {
                value: latency ? new NetworkQualityLatencyStats(latency) : null,
                enumerable: true
            }
        });
    }
    return NetworkQualitySendOrRecvStats;
}();
module.exports = NetworkQualitySendOrRecvStats; //# sourceMappingURL=networkqualitysendorrecvstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitysendstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var NetworkQualitySendOrRecvStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitysendorrecvstats.js [app-client] (ecmascript)");
/**
 * {@link NetworkQualitySendOrRecvStats} based on which a {@link Participant}'s
 * {@link NetworkQualityMediaStats}<code style="padding:0 0">#send</code> is calculated.
 */ var NetworkQualitySendStats = function(_super) {
    __extends(NetworkQualitySendStats, _super);
    /**
     * Construct a {@link NetworkQualitySendStats}.
     * @param {SendOrRecvStats} sendOrRecvStats
     */ function NetworkQualitySendStats(sendOrRecvStats) {
        return _super.call(this, sendOrRecvStats) || this;
    }
    return NetworkQualitySendStats;
}(NetworkQualitySendOrRecvStats);
module.exports = NetworkQualitySendStats; //# sourceMappingURL=networkqualitysendstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualityrecvstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var NetworkQualitySendOrRecvStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitysendorrecvstats.js [app-client] (ecmascript)");
/**
 * {@link NetworkQualitySendOrRecvStats} based on which a {@link Participant}'s
 * {@link NetworkQualityMediaStats}<code style="padding:0 0">#recv</code> is calculated.
 */ var NetworkQualityRecvStats = function(_super) {
    __extends(NetworkQualityRecvStats, _super);
    /**
     * Construct a {@link NetworkQualityRecvStats}.
     * @param {SendOrRecvStats} sendOrRecvStats
     */ function NetworkQualityRecvStats(sendOrRecvStats) {
        return _super.call(this, sendOrRecvStats) || this;
    }
    return NetworkQualityRecvStats;
}(NetworkQualitySendOrRecvStats);
module.exports = NetworkQualityRecvStats; //# sourceMappingURL=networkqualityrecvstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitymediastats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var NetworkQualitySendStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitysendstats.js [app-client] (ecmascript)");
var NetworkQualityRecvStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualityrecvstats.js [app-client] (ecmascript)");
/**
 * Network quality statistics shared between a {@link Participant}'s audio or video.
 * @property {NetworkQualityLevel} send - {@link NetworkQualityLevel} of the
 *  {@link Participant}'s published audio or video
 * @property {number} recv - {@link NetworkQualityLevel} of the
 *  {@link Participant}'s subscribed audio or video
 * @property {?NetworkQualitySendOrRecvStats} sendStats - {@link NetworkQualitySendOrRecvStats}
 *   based on which {@link NetworkQualityMediaStats}<code style="padding:0 0">#send</code>
 *   is calculated
 * @property {?NetworkQualitySendOrRecvStats} recvStats - {@link NetworkQualitySendOrRecvStats}
 *   based on which {@link NetworkQualityMediaStats}<code style="padding:0 0">#recv</code>
 *   is calculated
 */ var NetworkQualityMediaStats = function() {
    /**
     * Construct a {@link NetworkQualityMediaStats}.
     * @param {MediaLevels} mediaLevels
     */ function NetworkQualityMediaStats(_a) {
        var send = _a.send, recv = _a.recv, _b = _a.sendStats, sendStats = _b === void 0 ? null : _b, _c = _a.recvStats, recvStats = _c === void 0 ? null : _c;
        Object.defineProperties(this, {
            send: {
                value: send,
                enumerable: true
            },
            recv: {
                value: recv,
                enumerable: true
            },
            sendStats: {
                value: sendStats ? new NetworkQualitySendStats(sendStats) : null,
                enumerable: true
            },
            recvStats: {
                value: recvStats ? new NetworkQualityRecvStats(recvStats) : null,
                enumerable: true
            }
        });
    }
    return NetworkQualityMediaStats;
}();
module.exports = NetworkQualityMediaStats; //# sourceMappingURL=networkqualitymediastats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualityaudiostats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var NetworkQualityMediaStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitymediastats.js [app-client] (ecmascript)");
/**
 * {@link NetworkQualityMediaStats} for a {@link Participant}'s audio.
 */ var NetworkQualityAudioStats = function(_super) {
    __extends(NetworkQualityAudioStats, _super);
    /**
     * Construct a {@link NetworkQualityAudioStats}.
     * @param {MediaLevels} mediaLevels
     */ function NetworkQualityAudioStats(mediaLevels) {
        return _super.call(this, mediaLevels) || this;
    }
    return NetworkQualityAudioStats;
}(NetworkQualityMediaStats);
module.exports = NetworkQualityAudioStats; //# sourceMappingURL=networkqualityaudiostats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualityvideostats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var NetworkQualityMediaStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitymediastats.js [app-client] (ecmascript)");
/**
 * {@link NetworkQualityMediaStats} for a {@link Participant}'s video.
 */ var NetworkQualityVideoStats = function(_super) {
    __extends(NetworkQualityVideoStats, _super);
    /**
     * Construct a {@link NetworkQualityVideoStats}.
     * @param {MediaLevels} mediaLevels
     */ function NetworkQualityVideoStats(mediaLevels) {
        return _super.call(this, mediaLevels) || this;
    }
    return NetworkQualityVideoStats;
}(NetworkQualityMediaStats);
module.exports = NetworkQualityVideoStats; //# sourceMappingURL=networkqualityvideostats.js.map
}}),
"[project]/node_modules/twilio-video/es5/stats/networkqualitystats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var NetworkQualityAudioStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualityaudiostats.js [app-client] (ecmascript)");
var NetworkQualityVideoStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualityvideostats.js [app-client] (ecmascript)");
/**
 * Network quality statistics for a {@link Participant}.
 * @property {NetworkQualityLevel} level - {@link NetworkQualityLevel} of the {@link Participant}
 * @property {?NetworkQualityAudioStats} audio - {@link NetworkQualityMediaStats}
 *   for audio; <code>null</code> if {@link NetworkQualityVerbosity} is {@link NetworkQualityVerbosity}<code style="padding:0 0">#minimal</code>
 *   or below
 * @property {?NetworkQualityVideoStats} video - {@link NetworkQualityMediaStats}
 *   for video; <code>null</code> if {@link NetworkQualityVerbosity} is {@link NetworkQualityVerbosity}<code style="padding:0 0">#minimal</code>
 *   or below
 */ var NetworkQualityStats = function() {
    /**
     * Construct a {@link NetworkQualityStats}.
     * @param {NetworkQualityLevels} networkQualityLevels
     */ function NetworkQualityStats(_a) {
        var level = _a.level, audio = _a.audio, video = _a.video;
        Object.defineProperties(this, {
            level: {
                value: level,
                enumerable: true
            },
            audio: {
                value: audio ? new NetworkQualityAudioStats(audio) : null,
                enumerable: true
            },
            video: {
                value: video ? new NetworkQualityVideoStats(video) : null,
                enumerable: true
            }
        });
    }
    return NetworkQualityStats;
}();
module.exports = NetworkQualityStats; //# sourceMappingURL=networkqualitystats.js.map
}}),
"[project]/node_modules/twilio-video/es5/room.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)");
var RemoteParticipant = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/remoteparticipant.js [app-client] (ecmascript)");
var StatsReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/statsreport.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), flatMap = _a.flatMap, valueToJSON = _a.valueToJSON;
var nInstances = 0;
/**
 * A {@link Room} represents communication between you and one or more
 * {@link RemoteParticipant}s sharing {@link AudioTrack}s and
 * {@link VideoTrack}s.
 * <br><br>
 * You can connect to a {@link Room} by calling {@link module:twilio-video.connect}.
 * @extends EventEmitter
 * @property {?RemoteParticipant} dominantSpeaker - The Dominant Speaker in the
 *   {@link Room}, if any
 * @property {boolean} isRecording - Whether or not the {@link Room} is being
 *   recorded
 * @property {LocalParticipant} localParticipant - Your {@link LocalParticipant}
 *   in the {@link Room}
 * @property {string} mediaRegion - String indicating geographical region
 *    where  media is processed for the {@link Room}.
 * @property {string} name - The {@link Room}'s name
 * @property {Map<Participant.SID, RemoteParticipant>} participants -
 *   The {@link RemoteParticipant}s participating in this {@link Room}
 * @property {Room.SID} sid - The {@link Room}'s SID
 * @property {string} state - "connected", "reconnecting", or "disconnected"
 * @throws {SignalingConnectionDisconnectedError}
 * @emits Room#disconnected
 * @emits Room#transcription
 * @emits Room#participantConnected
 * @emits Room#participantDisconnected
 * @emits Room#participantReconnected
 * @emits Room#participantReconnecting
 * @emits Room#reconnected
 * @emits Room#reconnecting
 * @emits Room#recordingStarted
 * @emits Room#recordingStopped
 * @emits Room#trackDimensionsChanged
 * @emits Room#trackDisabled
 * @emits Room#trackEnabled
 * @emits Room#trackMessage
 * @emits Room#trackPublished
 * @emits Room#trackPublishPriorityChanged
 * @emits Room#trackStarted
 * @emits Room#trackSubscribed
 * @emits Room#trackSwitchedOff
 * @emits Room#trackSwitchedOn
 * @emits Room#trackUnpublished
 * @emits Room#trackUnsubscribed
 * @emits Room#trackWarning
 * @emits Room#trackWarningsCleared
 */ var Room = function(_super) {
    __extends(Room, _super);
    /**
     * Construct a {@link Room}.
     * @param {RoomSignaling} signaling
     * @param {?object} [options={}]
     */ function Room(localParticipant, signaling, options) {
        var _this = _super.call(this) || this;
        var log = options.log.createLog('default', _this);
        var participants = new Map();
        /* istanbul ignore next */ Object.defineProperties(_this, {
            _log: {
                value: log
            },
            _clientTrackSwitchOffControl: {
                value: options.clientTrackSwitchOffControl || 'disabled'
            },
            _contentPreferencesMode: {
                value: options.contentPreferencesMode || 'disabled'
            },
            _instanceId: {
                value: ++nInstances
            },
            _options: {
                value: options
            },
            _participants: {
                value: participants
            },
            _signaling: {
                value: signaling
            },
            dominantSpeaker: {
                enumerable: true,
                get: function() {
                    return this.participants.get(signaling.dominantSpeakerSid) || null;
                }
            },
            isRecording: {
                enumerable: true,
                get: function() {
                    return signaling.recording.isEnabled || false;
                }
            },
            localParticipant: {
                enumerable: true,
                value: localParticipant
            },
            name: {
                enumerable: true,
                value: signaling.name
            },
            participants: {
                enumerable: true,
                value: participants
            },
            sid: {
                enumerable: true,
                value: signaling.sid
            },
            state: {
                enumerable: true,
                get: function() {
                    return signaling.state;
                }
            },
            mediaRegion: {
                enumerable: true,
                value: signaling.mediaRegion
            }
        });
        handleLocalParticipantEvents(_this, localParticipant);
        handleRecordingEvents(_this, signaling.recording);
        handleSignalingEvents(_this, signaling);
        verifyNoiseCancellation(_this);
        log.info('Created a new Room:', _this.name);
        log.debug('Initial RemoteParticipants:', Array.from(_this._participants.values()));
        return _this;
    }
    Room.prototype.toString = function() {
        return "[Room #" + this._instanceId + ": " + this.sid + "]";
    };
    /**
     * Disconnect from the {@link Room}.
     * @returns {this}
     */ Room.prototype.disconnect = function() {
        this._log.info('Disconnecting');
        this._signaling.disconnect();
        return this;
    };
    /**
     * Get the {@link Room}'s media statistics. This is not supported in Safari 12.0 or below
     * due to this bug : https://bugs.webkit.org/show_bug.cgi?id=192601
     *
     * @returns {Promise.<Array<StatsReport>>}
     */ Room.prototype.getStats = function() {
        var _this = this;
        return this._signaling.getStats().then(function(responses) {
            return Array.from(responses).map(function(_a) {
                var _b = __read(_a, 2), id = _b[0], response = _b[1];
                return new StatsReport(id, Object.assign({}, response, {
                    localAudioTrackStats: rewriteLocalTrackIds(_this, response.localAudioTrackStats),
                    localVideoTrackStats: rewriteLocalTrackIds(_this, response.localVideoTrackStats)
                }));
            });
        });
    };
    /**
     * Restart the muted local media {@link Track}s and play inadvertently paused HTMLMediaElements
     * that are attached to local and remote media {@link Track}s. This method is useful mainly on
     * mobile browsers (Safari and Chrome on iOS), where there is a possibility that the muted local
     * media {@link Track}s are never unmuted and inadvertently paused HTMLMediaElements are never
     * played again, especially after handling an incoming phone call.
     * @returns {this}
     */ Room.prototype.refreshInactiveMedia = function() {
        var localTrackPublications = this.localParticipant.tracks;
        var localMediaTracks = Array.from(localTrackPublications.values()).filter(function(_a) {
            var kind = _a.track.kind;
            return kind !== 'data';
        }).map(function(_a) {
            var track = _a.track;
            return track;
        });
        var remoteMediaTracks = flatMap(this.participants, function(participants) {
            return Array.from(participants.tracks.values());
        }).filter(function(_a) {
            var track = _a.track;
            return track && track.kind !== 'data';
        }).map(function(_a) {
            var track = _a.track;
            return track;
        });
        var mediaTracks = localMediaTracks.concat(remoteMediaTracks);
        var unmuteEvent = new Event('unmute');
        localMediaTracks.forEach(function(_a) {
            var isMuted = _a.isMuted, mediaStreamTrack = _a.mediaStreamTrack;
            if (isMuted) {
                mediaStreamTrack.dispatchEvent(unmuteEvent);
            }
        });
        var pauseEvent = new Event('pause');
        mediaTracks.forEach(function(_a) {
            var attachments = _a._attachments, elShims = _a._elShims;
            return attachments.forEach(function(el) {
                var shim = elShims.get(el);
                var isInadvertentlyPaused = el.paused && shim && !shim.pausedIntentionally();
                if (isInadvertentlyPaused) {
                    el.dispatchEvent(pauseEvent);
                }
            });
        });
        return this;
    };
    Room.prototype.toJSON = function() {
        return valueToJSON(this);
    };
    return Room;
}(EventEmitter);
function verifyNoiseCancellation(room) {
    var allowedAudioProcessors = room.localParticipant._signaling.audioProcessors;
    room.localParticipant.audioTracks.forEach(function(_a) {
        var track = _a.track;
        var noiseCancellation = track.noiseCancellation;
        if (noiseCancellation && !allowedAudioProcessors.includes(noiseCancellation.vendor)) {
            room._log.warn(noiseCancellation.vendor + " is not supported in this room. disabling it permanently");
            noiseCancellation.disablePermanently();
        }
    });
}
function rewriteLocalTrackIds(room, trackStats) {
    var localParticipantSignaling = room.localParticipant._signaling;
    return trackStats.reduce(function(trackStats, trackStat) {
        var publication = localParticipantSignaling.tracks.get(trackStat.trackId);
        var trackSender = localParticipantSignaling.getSender(publication);
        return trackSender ? [
            Object.assign({}, trackStat, {
                trackId: trackSender.id
            })
        ].concat(trackStats) : trackStats;
    }, []);
}
/**
 * A {@link Room.SID} is a 34-character string starting with "RM"
 * that uniquely identifies a {@link Room}.
 * @type string
 * @typedef Room.SID
 */ /**
 * The Dominant Speaker in the {@link Room} changed. Either the Dominant Speaker
 * is a new {@link RemoteParticipant} or the Dominant Speaker has been reset and
 * is now null.
 * @param {?RemoteParticipant} dominantSpeaker - The Dominant Speaker in the
 *   {@link Room}, if any
 * @event Room#dominantSpeakerChanged
 */ /**
 * Your {@link LocalParticipant} was disconnected from the {@link Room} and all
 * other {@link RemoteParticipant}s.
 * @param {Room} room - The {@link Room} your
 *   {@link LocalParticipant} was disconnected from
 * @param {?TwilioError} error - Present when the {@link LocalParticipant} got
 *   disconnected from the {@link Room} unexpectedly
 * @event Room#disconnected
 * @example
 * myRoom.on('disconnected', function(room, error) {
 *   if (error) {
 *     console.log('Unexpectedly disconnected:', error);
 *   }
 *   myRoom.localParticipant.tracks.forEach(function(track) {
 *     track.stop();
 *     track.detach();
 *   });
 * });
 */ /**
 * Emitted when transcription data is received.
 * This event is only emitted when the {@link Room} is configured for transcription.
 * @param {TranscriptionEvent} transcriptionEvent - The transcription event data.
 * @param {string} transcriptionEvent.language_code - The BCP-47 language code (e.g., 'en-US') of the transcribed text.
 * @param {boolean} transcriptionEvent.partial_results - Whether the transcription is a final or a partial result.
 * @param {string} transcriptionEvent.participant - The SID of the speaking participant.
 * @param {number} transcriptionEvent.sequence_number - Starts with one and increments monotonically.
 * @param {string} transcriptionEvent.timestamp - ISO 8601 timestamp of when the transcription was generated.
 * @param {string} transcriptionEvent.track - The SID of the audio track being transcribed.
 * @param {string} transcriptionEvent.transcription - The transcribed text.
 * @param {string} transcriptionEvent.type - Constant 'extension_transcriptions'.
 * @event Room#transcription
 * @example
 * room.on('transcription', transcriptionEvent => {
 *   console.log(`${transcriptionEvent.participant}: ${transcriptionEvent.transcription}`);
 * });
 */ /**
 * A {@link RemoteParticipant} joined the {@link Room}. In Large Group Rooms (Maximum
 * Participants greater than 50), this event is raised only when a {@link RemoteParticipant}
 * publishes at least one {@link LocalTrack}.
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who joined
 * @event Room#participantConnected
 * @example
 * myRoom.on('participantConnected', function(participant) {
 *   console.log(participant.identity + ' joined the Room');
 * });
 */ /**
 * A {@link RemoteParticipant} left the {@link Room}. In Large Group Rooms (Maximum
 * Participants greater than 50), this event is raised only when a {@link RemoteParticipant}
 * unpublishes all its {@link LocalTrack}s.
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who left
 * @event Room#participantDisconnected
 * @example
 * myRoom.on('participantDisconnected', function(participant) {
 *   console.log(participant.identity + ' left the Room');
 *   participant.tracks.forEach(function(track) {
 *     track.detach().forEach(function(mediaElement) {
 *       mediaElement.remove();
 *     });
 *   });
 * });
 */ /**
 * A {@link RemoteParticipant} has reconnected to the {@link Room} after a signaling connection disruption.
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} that has reconnected.
 * @event Room#participantReconnected
 * @example
 * myRoom.on('participantReconnected', participant => {
 *   console.log(participant.identity + ' reconnected to the Room');
 * });
 */ /**
 * A {@link RemoteParticipant} is reconnecting to the {@link Room} after a signaling connection disruption.
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} that is reconnecting.
 * @event Room#participantReconnecting
 * @example
 * myRoom.on('participantReconnecting', participant => {
 *   console.log(participant.identity + ' is reconnecting to the Room');
 * });
 */ /**
 * Your application successfully reconnected to the {@link Room}. When this
 * event is emitted, the {@link Room} is in state "connected".
 * @event Room#reconnected
 * @example
 * myRoom.on('reconnected', () => {
 *   console.log('Reconnected!');
 * });
 */ /**
 * Your application is reconnecting to the {@link Room}. This happens when there
 * is a disruption in your signaling connection and/or your media connection. When
 * this event is emitted, the {@link Room} is in state "reconnecting". If reconnecting
 * succeeds, the {@link Room} will emit a "reconnected" event.
 * @param {MediaConnectionError|SignalingConnectionDisconnectedError} error - A
 *   {@link MediaConnectionError} if your application is reconnecting due to a
 *   disruption in your media connection, or a {@link SignalingConnectionDisconnectedError}
 *   if your application is reconnecting due to a disruption in your signaling connection
 * @event Room#reconnecting
 * @example
 * myRoom.on('reconnecting', error => {
 *   if (error.code === 53001) {
 *     console.log('Reconnecting your signaling connection!', error.message);
 *   } else if (error.code === 53405) {
 *     console.log('Reconnecting your media connection!', error.message);
 *   }
 * });
 */ /**
 * The {@link Room} is now being recorded
 * @event Room#recordingStarted
 */ /**
 * The {@link Room} is no longer being recorded
 * @event Room#recordingStopped
 */ /**
 * One of the {@link RemoteParticipant}'s {@link VideoTrack}'s dimensions changed.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} whose dimensions changed
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteVideoTrack}'s dimensions changed
 * @event Room#trackDimensionsChanged
 */ /**
 * A {@link RemoteTrack} was disabled by a {@link RemoteParticipant} in the {@link Room}.
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication} that represents disabled {@link RemoteTrack}
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who
 *   disabled the {@link RemoteTrack}
 * @event Room#trackDisabled
 */ /**
 * A {@link RemoteTrack} was enabled by a {@link RemoteParticipant} in the {@link Room}.
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication} that represents enabled {@link RemoteTrack}
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who
 *   enabled the {@link RemoteTrack}
 * @event Room#trackEnabled
 */ /**
 * A message was received over one of the {@link RemoteParticipant}'s
 * {@link RemoteDataTrack}'s.
 * @param {string|ArrayBuffer} data
 * @param {RemoteDataTrack} track - The {@link RemoteDataTrack} over which the
 *   message was received
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteDataTrack} received the message
 * @event Room#trackMessage
 */ /**
 * A {@link RemoteTrack} was published by a {@link RemoteParticipant} after
 * connecting to the {@link Room}. This event is not emitted for
 * {@link RemoteTrack}s that were published while the {@link RemoteParticipant}
 * was connecting to the {@link Room}.
 * @event Room#trackPublished
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   which represents the published {@link RemoteTrack}
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who
 *   published the {@link RemoteTrack}
 * @example
 * function trackPublished(publication, participant) {
 *   console.log(`RemoteParticipant ${participant.sid} published Track ${publication.trackSid}`);
 * }
 *
 * // Handle RemoteTracks published after connecting to the Room.
 * room.on('trackPublished', trackPublished);
 *
 * room.on('participantConnected', participant => {
 *   // Handle RemoteTracks published while connecting to the Room.
 *   participant.trackPublications.forEach(publication => trackPublished(publication, participant));
 * });
 */ /**
 * One of a {@link RemoteParticipant}'s {@link RemoteTrack}s in the {@link Room} started.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that started
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} started
 * @event Room#trackStarted
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was subscribed to.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was subscribed
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was subscribed to
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} was subscribed
 * @event Room#trackSubscribed
 * @example
 * room.on('trackSubscribed', function(track, publication, participant) {
 *   var participantView = document.getElementById('participant-view-' + participant.identity);
 *   participantView.appendChild(track.attach());
 * });
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was switched off.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was switched off
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was subscribed to
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} was switched off
 * @event Room#trackSwitchedOff
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was switched on.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was switched on
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was subscribed to
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} was switched on
 * @event Room#trackSwitchedOn
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} could not be subscribed to.
 * @param {TwilioError} error - The reason the {@link RemoteTrack} could not be
 *   subscribed to
 * @param {RemoteTrackPublication} publication - The
 *   {@link RemoteTrackPublication} for the {@link RemoteTrack} that could not
 *   be subscribed to
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} could not be subscribed to
 * @event Room#trackSubscriptionFailed
 */ /**
 * The {@link RemoteTrack}'s publish {@link Track.Priority} was changed by the
 * {@link RemoteParticipant}.
 * @param {Track.Priority} priority - the {@link RemoteTrack}'s new publish
 *   {@link Track.Priority};
 * @param {RemoteTrackPublication} publication - The
 *   {@link RemoteTrackPublication} for the {@link RemoteTrack} that changed priority
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} changed priority
 * @event Room#trackPublishPriorityChanged
 */ /**
 * A {@link RemoteTrack} was unpublished by a {@link RemoteParticipant} to the {@link Room}.
 * @event Room#trackUnpublished
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   which represents the unpublished {@link RemoteTrack}
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} who
 *   unpublished the {@link RemoteTrack}
 */ /**
 * A {@link RemoteParticipant}'s {@link RemoteTrack} was unsubscribed from.
 * @param {RemoteTrack} track - The {@link RemoteTrack} that was unsubscribed
 * @param {RemoteTrackPublication} publication - The {@link RemoteTrackPublication}
 *   for the {@link RemoteTrack} that was unsubscribed from
 * @param {RemoteParticipant} participant - The {@link RemoteParticipant} whose
 *   {@link RemoteTrack} was unsubscribed
 * @event Room#trackUnsubscribed
 * @example
 * room.on('trackUnsubscribed', function(track, publication, participant) {
 *   track.detach().forEach(function(mediaElement) {
 *     mediaElement.remove();
 *   });
 * });
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrackPublication}s in the {@link Room} encountered a warning.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @param {string} name - The warning that was raised.
 * @param {LocalTrackPublication} publication - The {@link LocalTrackPublication} that encountered the warning.
 * @param {LocalParticipant} participant - The {@link LocalParticipant}
 * @event Room#trackWarning
 * @example
 * room.on('trackWarning', (name, publication, participant) => {
 *   if (name === 'recording-media-lost') {
 *     log(`LocalTrack ${publication.track.name} is not recording media.`,
 *       name, publication, participant);
 *
 *     // Wait a reasonable amount of time to clear the warning.
 *     const timer = setTimeout(() => {
 *       // If the warning is not cleared, you can manually
 *       // reconnect to the room, or show a dialog to the user
 *     }, 5000);
 *
 *     room.once('trackWarningsCleared', (publication, participant) => {
 *       log('LocalTrack warnings have cleared!',
 *         publication, participant);
 *       clearTimeout(timer);
 *     });
 *   }
});
 */ /**
 * One of the {@link LocalParticipant}'s {@link LocalTrackPublication}s in the {@link Room} cleared all warnings.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @param {LocalTrackPublication} publication - The {@link LocalTrackPublication} that cleared all warnings.
 * @param {LocalParticipant} participant - The {@link LocalParticipant}
 * @event Room#trackWarningsCleared
 */ function connectParticipant(room, participantSignaling) {
    var log = room._log, clientTrackSwitchOffControl = room._clientTrackSwitchOffControl, contentPreferencesMode = room._contentPreferencesMode, _a = room._options, MediaStream = _a.MediaStream, mapMediaElement = _a.mapMediaElement, disposeMediaElement = _a.disposeMediaElement;
    var participant = new RemoteParticipant(participantSignaling, {
        log: log,
        clientTrackSwitchOffControl: clientTrackSwitchOffControl,
        contentPreferencesMode: contentPreferencesMode,
        MediaStream: MediaStream,
        mapMediaElement: mapMediaElement,
        disposeMediaElement: disposeMediaElement
    });
    log.info('A new RemoteParticipant connected:', participant);
    room._participants.set(participant.sid, participant);
    room.emit('participantConnected', participant);
    // Reemit Track and RemoteParticipant events.
    var eventListeners = [
        [
            'reconnected',
            'participantReconnected'
        ],
        [
            'reconnecting',
            'participantReconnecting'
        ],
        'trackDimensionsChanged',
        'trackDisabled',
        'trackEnabled',
        'trackMessage',
        'trackPublished',
        'trackPublishPriorityChanged',
        'trackStarted',
        'trackSubscribed',
        'trackSubscriptionFailed',
        'trackSwitchedOff',
        'trackSwitchedOn',
        'trackUnpublished',
        'trackUnsubscribed'
    ].map(function(eventOrPair) {
        var _a = __read(Array.isArray(eventOrPair) ? eventOrPair : [
            eventOrPair,
            eventOrPair
        ], 2), event = _a[0], participantEvent = _a[1];
        function reemit() {
            var args = [].slice.call(arguments);
            args.unshift(participantEvent);
            args.push(participant);
            room.emit.apply(room, __spreadArray([], __read(args)));
        }
        participant.on(event, reemit);
        return [
            event,
            reemit
        ];
    });
    participant.once('disconnected', function participantDisconnected() {
        var dominantSpeaker = room.dominantSpeaker;
        log.info('RemoteParticipant disconnected:', participant);
        room._participants.delete(participant.sid);
        eventListeners.forEach(function(args) {
            participant.removeListener(args[0], args[1]);
        });
        room.emit('participantDisconnected', participant);
        if (participant === dominantSpeaker) {
            room.emit('dominantSpeakerChanged', room.dominantSpeaker);
        }
    });
}
function handleLocalParticipantEvents(room, localParticipant) {
    var events = [
        'trackWarning',
        'trackWarningsCleared'
    ].map(function(event) {
        return {
            eventName: event,
            handler: function() {
                var args = [];
                for(var _i = 0; _i < arguments.length; _i++){
                    args[_i] = arguments[_i];
                }
                return room.emit.apply(room, __spreadArray([
                    event
                ], __read(__spreadArray(__spreadArray([], __read(args)), [
                    localParticipant
                ]))));
            }
        };
    });
    events.forEach(function(_a) {
        var eventName = _a.eventName, handler = _a.handler;
        return localParticipant.on(eventName, handler);
    });
    room.once('disconnected', function() {
        return events.forEach(function(_a) {
            var eventName = _a.eventName, handler = _a.handler;
            return localParticipant.removeListener(eventName, handler);
        });
    });
}
function handleRecordingEvents(room, recording) {
    recording.on('updated', function updated() {
        var started = recording.isEnabled;
        room._log.info("Recording " + (started ? 'started' : 'stopped'));
        room.emit("recording" + (started ? 'Started' : 'Stopped'));
    });
}
function handleSignalingEvents(room, signaling) {
    var log = room._log;
    // Reemit RemoteParticipant events from the RoomSignaling.
    log.debug('Creating a new RemoteParticipant for each ParticipantSignaling ' + 'in the RoomSignaling');
    signaling.participants.forEach(connectParticipant.bind(null, room));
    log.debug('Setting up RemoteParticipant creation for all subsequent ' + 'ParticipantSignalings that connect to the RoomSignaling');
    signaling.on('participantConnected', connectParticipant.bind(null, room));
    signaling.on('dominantSpeakerChanged', function() {
        return room.emit('dominantSpeakerChanged', room.dominantSpeaker);
    });
    signaling.on('transcription', function(data) {
        return room.emit('transcription', data);
    });
    // Reemit state transition events from the RoomSignaling.
    signaling.on('stateChanged', function stateChanged(state, error) {
        log.info('Transitioned to state:', state);
        switch(state){
            case 'disconnected':
                room.participants.forEach(function(participant) {
                    participant._unsubscribeTracks();
                });
                room.emit(state, room, error);
                room.localParticipant.tracks.forEach(function(publication) {
                    publication.unpublish();
                });
                signaling.removeListener('stateChanged', stateChanged);
                break;
            case 'reconnecting':
                // NOTE(mpatwardhan): `stateChanged` can get emitted with StateMachine locked.
                // Do not signal  public events synchronously with lock held.
                setTimeout(function() {
                    return room.emit('reconnecting', error);
                }, 0);
                break;
            default:
                // NOTE(mpatwardhan): `stateChanged` can get emitted with StateMachine locked.
                // Do not signal  public events synchronously with lock held.
                setTimeout(function() {
                    return room.emit('reconnected');
                }, 0);
        }
    });
}
module.exports = Room; //# sourceMappingURL=room.js.map
}}),
"[project]/node_modules/twilio-video/es5/connect.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var MediaStreamTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)").MediaStreamTrack;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), guessBrowser = _a.guessBrowser, guessBrowserVersion = _a.guessBrowserVersion, isCodecSupported = _a.isCodecSupported;
var createCancelableRoomPromise = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/cancelableroompromise.js [app-client] (ecmascript)");
var EncodingParametersImpl = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/encodingparameters.js [app-client] (ecmascript)");
var LocalParticipant = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/localparticipant.js [app-client] (ecmascript)");
var InsightsPublisher = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/insightspublisher/index.js [app-client] (ecmascript)");
var NullInsightsPublisher = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/insightspublisher/null.js [app-client] (ecmascript)");
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)"), LocalAudioTrack = _b.LocalAudioTrack, LocalDataTrack = _b.LocalDataTrack, LocalVideoTrack = _b.LocalVideoTrack;
var NetworkQualityConfigurationImpl = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/networkqualityconfiguration.js [app-client] (ecmascript)");
var Room = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/room.js [app-client] (ecmascript)");
var SignalingV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/index.js [app-client] (ecmascript)");
var _c = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), asLocalTrack = _c.asLocalTrack, buildLogLevels = _c.buildLogLevels, filterObject = _c.filterObject, isNonArrayObject = _c.isNonArrayObject;
var _d = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), DEFAULT_ENVIRONMENT = _d.DEFAULT_ENVIRONMENT, DEFAULT_LOG_LEVEL = _d.DEFAULT_LOG_LEVEL, DEFAULT_LOGGER_NAME = _d.DEFAULT_LOGGER_NAME, DEFAULT_REALM = _d.DEFAULT_REALM, DEFAULT_REGION = _d.DEFAULT_REGION, WS_SERVER = _d.WS_SERVER, SDK_NAME = _d.SDK_NAME, SDK_VERSION = _d.SDK_VERSION, E = _d.typeErrors;
var CancelablePromise = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/cancelablepromise.js [app-client] (ecmascript)");
var EventObserver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/eventobserver.js [app-client] (ecmascript)");
var DefaultLog = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var validateBandwidthProfile = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/validate.js [app-client] (ecmascript)").validateBandwidthProfile;
var safariVersion = guessBrowser() === 'safari' && guessBrowserVersion();
// This is used to make out which connect() call a particular Log statement
// belongs to. Each call to connect() increments this counter.
var connectCalls = 0;
var didPrintSafariWarning = false;
var isSafariWithoutVP8Support = false;
if (safariVersion) {
    var safariMajorVersion = safariVersion.major, safariMinorVersion = safariVersion.minor;
    isSafariWithoutVP8Support = safariMajorVersion < 12 || safariMajorVersion === 12 && safariMinorVersion < 1;
}
var deprecatedConnectOptionsProps = new Set([
    {
        didWarn: false,
        shouldDelete: true,
        name: 'abortOnIceServersTimeout'
    },
    {
        didWarn: false,
        shouldDelete: true,
        name: 'dscpTagging',
        newName: 'enableDscp'
    },
    {
        didWarn: false,
        shouldDelete: true,
        name: 'iceServersTimeout'
    },
    {
        didWarn: false,
        shouldDelete: false,
        name: 'eventListener',
        newName: 'Video.Logger'
    },
    {
        didWarn: false,
        shouldDelete: false,
        name: 'logLevel',
        newName: 'Video.Logger'
    }
]);
var deprecatedBandwidthProfileOptions = new Set([
    {
        didWarn: false,
        shouldDelete: false,
        name: 'maxTracks',
        newName: 'bandwidthProfile.video.clientTrackSwitchOffControl'
    },
    {
        didWarn: false,
        shouldDelete: false,
        name: 'renderDimensions',
        newName: 'bandwidthProfile.video.contentPreferencesMode'
    }
]);
/**
 * Connect to a {@link Room}.
 *   <br><br>
 *   By default, this will automatically acquire an array containing a
 *   {@link LocalAudioTrack} and {@link LocalVideoTrack} before connecting to
 *   the {@link Room}. These will be stopped when you disconnect from the
 *   {@link Room}.
 *   <br><br>
 *   You can override the default behavior by specifying
 *   <code>options</code>. For example, rather than acquiring a
 *   {@link LocalAudioTrack} and {@link LocalVideoTrack} automatically, you can
 *   pass your own array which you can stop yourself. See {@link ConnectOptions}
 *   for more information.
 * @alias module:twilio-video.connect
 * @param {string} token - The Access Token string
 * @param {ConnectOptions} [options] - Options to override the default behavior, invalid options are ignored.
 * @returns {CancelablePromise<Room>}
 * @throws {RangeError}
 * @throws {TwilioError}
 * @throws {TypeError}
 * @example
 * var Video = require('twilio-video');
 * var token = getAccessToken();
 * Video.connect(token, {
 *   name: 'my-cool-room'
 * }).then(function(room) {
 *   room.on('participantConnected', function(participant) {
 *     console.log(participant.identity + ' has connected');
 *   });

 *   room.once('disconnected', function() {
 *     console.log('You left the Room:', room.name);
 *   });
 * }).catch(error => {
 *   console.log('Could not connect to the Room:', error.message);
 * });
 * @example
 * var Video = require('twilio-video');
 * var token = getAccessToken();
 *
 * // Connect with audio-only
 * Video.connect(token, {
 *   name: 'my-cool-room',
 *   audio: true
 * }).then(function(room) {
 *   room.on('participantConnected', function(participant) {
 *     console.log(participant.identity + ' has connected');
 *   });
 *
 *   room.once('disconnected', function() {
 *     console.log('You left the Room:', room.name);
 *   });
 * }).catch(error => {
 *   console.log('Could not connect to the Room:', error.message);
 * });
 * @example
 * var Video = require('twilio-video');
 * var token = getAccessToken();
 *
 * // Connect with media acquired using getUserMedia()
 * navigator.mediaDevices.getUserMedia({
 *   audio: true,
 *   video: true
 * }).then(function(mediaStream) {
 *   return Video.connect(token, {
 *     name: 'my-cool-room',
 *     tracks: mediaStream.getTracks()
 *   });
 * }).then(function(room) {
 *   room.on('participantConnected', function(participant) {
 *     console.log(participant.identity + ' has connected');
 *   });
 *
 *   room.once('disconnected', function() {
 *     console.log('You left the Room:', room.name);
 *   });
 * }).catch(error => {
 *   console.log('Could not connect to the Room:', error.message);
 * });
 * @example
 * var Video = require('twilio-video');
 * var token = getAccessToken();
 *
 * // Connect with custom names for LocalAudioTrack and LocalVideoTrack
 * Video.connect(token, {
 *   name: 'my-cool-room'
 *   audio: { name: 'microphone' },
 *   video: { name: 'camera' }
 * }).then(function(room) {
 *   room.localParticipants.trackPublications.forEach(function(publication) {
 *     console.log('The LocalTrack "' + publication.trackName + '" was successfully published');
 *   });
 * }).catch(error => {
 *   console.log('Could not connect to the Room:', error.message);
 * });
 * @example
 * // Accessing the SDK logger
 * var { Logger, connect } = require('twilio-video');
 * var token = getAccessToken();
 *
 * var logger = Logger.getLogger('twilio-video');
 *
 * // Listen for logs
 * var originalFactory = logger.methodFactory;
 * logger.methodFactory = function (methodName, logLevel, loggerName) {
 *   var method = originalFactory(methodName, logLevel, loggerName);
 *
 *   return function (datetime, logLevel, component, message, data) {
 *     method(datetime, logLevel, component, message, data);
 *     // Send to your own server
 *     postDataToServer(arguments);
 *   };
 * };
 * logger.setLevel('debug');
 *
 * connect(token, {
 *   name: 'my-cool-room'
 * }).then(function(room) {
 *   room.on('participantConnected', function(participant) {
 *     console.log(participant.identity + ' has connected');
 *   });
 * }).catch(error => {
 *   console.log('Could not connect to the Room:', error.message);
 * });
 */ function connect(token, options) {
    if (typeof options === 'undefined') {
        options = {};
    }
    if (!isNonArrayObject(options)) {
        return CancelablePromise.reject(E.INVALID_TYPE('options', 'object'));
    }
    var Log = options.Log || DefaultLog;
    var loggerName = options.loggerName || DEFAULT_LOGGER_NAME;
    var logLevel = options.logLevel || DEFAULT_LOG_LEVEL;
    var logLevels = buildLogLevels(logLevel);
    var logComponentName = "[connect #" + ++connectCalls + "]";
    var log;
    try {
        log = new Log('default', logComponentName, logLevels, loggerName);
    } catch (error) {
        return CancelablePromise.reject(error);
    }
    // NOTE(csantos): Log a warning for the deprecated ConnectOptions properties.
    // The warning is displayed only for the first call to connect() per browser session.
    // Additionally, the options that are no longer needed will be removed.
    deprecateOptions(options, log, deprecatedConnectOptionsProps);
    var adaptiveSimulcast = options.preferredVideoCodecs === 'auto';
    if (adaptiveSimulcast) {
        // NOTE(mpatwardhan): enable adaptiveSimulcast.
        options.preferredVideoCodecs = [
            {
                codec: 'VP8',
                simulcast: true,
                adaptiveSimulcast: true
            }
        ];
    }
    if (options.maxVideoBitrate && adaptiveSimulcast) {
        log.error('ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"');
        return CancelablePromise.reject(E.ILLEGAL_INVOKE('connect', 'ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"'));
    }
    options = Object.assign({
        automaticSubscription: true,
        dominantSpeaker: false,
        enableDscp: false,
        receiveTranscriptions: false,
        environment: DEFAULT_ENVIRONMENT,
        eventListener: null,
        insights: true,
        LocalAudioTrack: LocalAudioTrack,
        LocalDataTrack: LocalDataTrack,
        LocalParticipant: LocalParticipant,
        LocalVideoTrack: LocalVideoTrack,
        Log: Log,
        MediaStreamTrack: MediaStreamTrack,
        loggerName: loggerName,
        logLevel: logLevel,
        maxAudioBitrate: null,
        maxVideoBitrate: null,
        name: null,
        networkMonitor: true,
        networkQuality: false,
        preferredAudioCodecs: [],
        preferredVideoCodecs: [],
        realm: DEFAULT_REALM,
        region: DEFAULT_REGION,
        signaling: SignalingV2
    }, filterObject(options));
    /* eslint new-cap:0 */ var eventPublisherOptions = {};
    if (typeof options.wsServerInsights === 'string') {
        eventPublisherOptions.gateway = options.wsServerInsights;
    }
    var EventPublisher = options.insights ? InsightsPublisher : NullInsightsPublisher;
    var eventPublisher = new EventPublisher(token, SDK_NAME, SDK_VERSION, options.environment, options.realm, eventPublisherOptions);
    var wsServer = WS_SERVER(options.environment, options.region);
    var eventObserver = new EventObserver(eventPublisher, Date.now(), log, options.eventListener);
    options = Object.assign({
        eventObserver: eventObserver,
        wsServer: wsServer
    }, options);
    options.log = log;
    // NOTE(mroberts): Print the Safari warning once if the log-level is at least
    // "warn", i.e. neither "error" nor "off".
    // NOTE(mmalavalli): Print the Safari warning only for versions 12.0 and below.
    if (isSafariWithoutVP8Support && !didPrintSafariWarning && log.logLevel !== 'error' && log.logLevel !== 'off') {
        didPrintSafariWarning = true;
        log.warn([
            'Support for Safari 12.0 and below is limited because it does not support VP8.',
            'This means you may experience codec issues in Group Rooms. You may also',
            'experience codec issues in Peer-to-Peer (P2P) Rooms containing Android- or',
            'iOS-based Participants who do not support H.264. However, P2P Rooms',
            'with browser-based Participants should work. For more information, please',
            'refer to this guide: https://www.twilio.com/docs/video/javascript-v2-developing-safari-11'
        ].join(' '));
    }
    if (typeof token !== 'string') {
        return CancelablePromise.reject(E.INVALID_TYPE('token', 'string'));
    }
    // NOTE(mmalavalli): The Room "name" in "options" was being used
    // as the LocalTrack name in asLocalTrack(). So we pass a copy of
    // "options" without the "name".
    var localTrackOptions = Object.assign({}, options);
    delete localTrackOptions.name;
    if ('tracks' in options) {
        if (!Array.isArray(options.tracks)) {
            return CancelablePromise.reject(E.INVALID_TYPE('options.tracks', 'Array of LocalAudioTrack, LocalVideoTrack or MediaStreamTrack'));
        }
        try {
            options.tracks = options.tracks.map(function(track) {
                return asLocalTrack(track, localTrackOptions);
            });
        } catch (error) {
            return CancelablePromise.reject(error);
        }
    }
    var error = validateBandwidthProfile(options.bandwidthProfile);
    if (error) {
        return CancelablePromise.reject(error);
    }
    // Note(mpatwardhan): "clientTrackSwitchOffControl" allows tracks to be switched off
    // and "contentPreferencesMode" allows track dimensions to be specified dynamically.
    // The properties can have one of the three values internally:
    // 1) "auto" = sdk will decide and send the hints.
    // 2) "manual" - app can use api to send the hints.
    // 3) "disabled" = do not enable this feature. (this is internal only value)
    // 'disabled' is needed because clientTrackSwitchOffControl and contentPreferencesMode are incompatible with
    // deprecated properties maxTracks and renderDimensions respectively. once we make @breaking_version_change
    // we can remove 'disabled' state along with maxTracks and renderDimensions.
    options.clientTrackSwitchOffControl = 'disabled'; // should sdk turn off idle tracks automatically?
    options.contentPreferencesMode = 'disabled'; // should sdk  use video element dimensions for content hints?
    if (options.bandwidthProfile) {
        options.clientTrackSwitchOffControl = 'auto';
        options.contentPreferencesMode = 'auto';
        if (options.bandwidthProfile.video) {
            // log any warnings about deprecated bwp options
            deprecateOptions(options.bandwidthProfile.video, log, deprecatedBandwidthProfileOptions);
            if ('maxTracks' in options.bandwidthProfile.video) {
                // when deprecated maxTracks is specified. disable clientTrackSwitchOffControl
                options.clientTrackSwitchOffControl = 'disabled';
            } else if (options.bandwidthProfile.video.clientTrackSwitchOffControl === 'manual') {
                options.clientTrackSwitchOffControl = 'manual';
            } else {
                options.clientTrackSwitchOffControl = 'auto';
            }
            if ('renderDimensions' in options.bandwidthProfile.video) {
                options.contentPreferencesMode = 'disabled';
            } else if (options.bandwidthProfile.video.contentPreferencesMode === 'manual') {
                options.contentPreferencesMode = 'manual';
            } else {
                options.contentPreferencesMode = 'auto';
            }
        }
    }
    var Signaling = options.signaling;
    var signaling = new Signaling(options.wsServer, options);
    log.info('Connecting to a Room');
    log.debug('Options:', options);
    var encodingParameters = new EncodingParametersImpl({
        maxAudioBitrate: options.maxAudioBitrate,
        maxVideoBitrate: options.maxVideoBitrate
    }, adaptiveSimulcast);
    var preferredCodecs = {
        audio: options.preferredAudioCodecs.map(normalizeCodecSettings),
        video: options.preferredVideoCodecs.map(normalizeCodecSettings)
    };
    var networkQualityConfiguration = new NetworkQualityConfigurationImpl(isNonArrayObject(options.networkQuality) ? options.networkQuality : {});
    // Log warnings for any unsupported preferred codecs.
    [
        'audio',
        'video'
    ].forEach(function(kind) {
        return preferredCodecs[kind].forEach(function(_a) {
            var codec = _a.codec;
            return isCodecSupported(codec, kind).then(function(isSupported) {
                return !isSupported && log.warn("The preferred " + kind + " codec \"" + codec + "\" will be ignored as it is not supported by the browser.");
            });
        });
    });
    // Create a CancelableRoomPromise<Room> that resolves after these steps:
    // 1 - Get the LocalTracks.
    // 2 - Create the LocalParticipant using options.tracks.
    // 3 - Connect to rtc-room-service and create the RoomSignaling.
    // 4 - Create the Room and then resolve the CancelablePromise.
    var cancelableRoomPromise = createCancelableRoomPromise(getLocalTracks.bind(null, options), createLocalParticipant.bind(null, signaling, log, encodingParameters, networkQualityConfiguration, options), createRoomSignaling.bind(null, token, options, signaling, encodingParameters, preferredCodecs), createRoom.bind(null, options));
    cancelableRoomPromise.then(function(room) {
        eventPublisher.connect(room.sid, room.localParticipant.sid);
        log.info('Connected to Room:', room.toString());
        log.info('Room name:', room.name);
        log.debug('Room:', room);
        room.once('disconnected', function() {
            return eventPublisher.disconnect();
        });
        return room;
    }, function(error) {
        eventPublisher.disconnect();
        if (cancelableRoomPromise._isCanceled) {
            log.info('Attempt to connect to a Room was canceled');
        } else {
            log.info('Error while connecting to a Room:', error);
        }
    });
    return cancelableRoomPromise;
}
/**
 * You may pass these options to {@link connect} in order to override the
 * default behavior.
 * @typedef {object} ConnectOptions
 * @property {boolean|CreateLocalTracksOptions|CreateLocalAudioTrackOptions} [audio=true] - Whether or not to
 *   get local audio with <code>getUserMedia</code> when <code>tracks</code>
 *   are not provided.
 * @property {boolean} [automaticSubscription=true] - By default, you will subscribe
 *   to all RemoteTracks shared by other Participants in a Room. You can now override this
 *   behavior by setting this flag to <code>false</code>. It will make sure that you will
 *   not subscribe to any RemoteTrack in a Group or Small Group Room. Setting it to
 *   <code>true</code>, or not setting it at all preserves the default behavior. This
 *   flag does not have any effect in a Peer-to-Peer Room.
 * @property {BandwidthProfileOptions} [bandwidthProfile] - You can optionally configure
 *   how your available downlink bandwidth is shared among the RemoteTracks you have subscribed
 *   to in a Group Room. By default, bandwidth is shared equally among the RemoteTracks.
 *   This has no effect in Peer-to-Peer Rooms.
 * @property {function} [disposeMediaElement] - Callback triggered after a media track is detached from an audio or video element.
 * @property {boolean} [dominantSpeaker=false] - Whether to enable the Dominant
 *   Speaker API or not. This only takes effect in Group Rooms.
 * @property {boolean} [dscpTagging=false] - <code>(deprecated: use "enableDscp" instead)</code>
 *   DSCP tagging allows you to request enhanced QoS treatment for RTP media packets from any
 *   firewall that the client may be behind. Setting this option to <code>true</code> will
 *   request DSCP tagging for media packets on supported browsers (only Chrome supports this
 *   as of now). Audio packets will be sent with DSCP header value set to 0xb8 which corresponds
 *   to Expedited Forwarding (EF). Video packets will be sent with DSCP header value set to 0x88
 *   which corresponds to Assured Forwarding (AF41).
 * @property {boolean} [enableDscp=false] - DSCP tagging allows you to request enhanced
 *   QoS treatment for RTP media packets from any firewall that the client may be behind.
 *   Setting this option to <code>true</code> will request DSCP tagging for media packets
 *   on supported browsers (only Chrome supports this as of now). Audio packets will be
 *   sent with DSCP header value set to 0xb8 which corresponds to Expedited Forwarding (EF).
 *   Video packets will be sent with DSCP header value set to 0x88 which corresponds to
 *   Assured Forwarding (AF41).
 * @property {function} [enumerateDevices] - Overrides the native MediaDevices.enumerateDevices API.
 * @property {EventListener} [eventListener] - <code>(deprecated: use [Video.Logger](module-twilio-video.html)</code>
 *   you can listen to fine-grained events related to signaling and media that are
 *   not available in the public APIs. These events might be useful for your own reporting
 *   and diagnostics.
 * @property {function} [getUserMedia] - Overrides the native MediaDevices.getUserMedia API.
 * @property {Array<RTCIceServer>} iceServers - Override the STUN and TURN
 *   servers used when connecting to {@link Room}s
 * @property {RTCIceTransportPolicy} [iceTransportPolicy="all"] - Override the
 *   ICE transport policy to be one of "relay" or "all"
 * @property {boolean} [insights=true] - Whether publishing events
 *   to the Insights gateway is enabled or not
  * @property {LogLevel|LogLevels} [logLevel='warn'] - <code>(deprecated: use [Video.Logger](module-twilio-video.html) instead.
 *   See [examples](module-twilio-video.html#.connect) for details)</code>
 *   Set the default log verbosity
 *   of logging. Passing a {@link LogLevel} string will use the same
 *   level for all components. Pass a {@link LogLevels} to set specific log
 *   levels.
 * @property {string} [loggerName='twilio-video'] - The name of the logger. Use this name when accessing the logger used by the SDK.
 *   See [examples](module-twilio-video.html#.connect) for details.
 * @property {function} [mapMediaElement] - Callback triggered after a media track is attached to an audio or video element.
 * @property {?number} [maxAudioBitrate=null] - Max outgoing audio bitrate (bps);
 *   A <code>null</code> or a <code>0</code> value does not set any bitrate limit;
 *   This value is set as a hint for variable bitrate codecs, but will not take
 *   effect for fixed bitrate codecs; Based on our tests, Chrome, Firefox and Safari
 *   support a bitrate range of 12000 bps to 256000 bps for Opus codec; This parameter
 *   has no effect on iSAC, PCMU and PCMA codecs
 * @property {?number} [maxVideoBitrate=null] - Max outgoing video bitrate (bps);
 *   A <code>null</code> or <code>0</code> value does not set any bitrate limit;
 *   This value is set as a hint for variable bitrate codecs, but will not take
 *   effect for fixed bitrate codecs; Based on our tests, Chrome, Firefox and Safari
 *   all seem to support an average bitrate range of 20000 bps (20 kbps) to
 *   8000000 bps (8 mbps) for a 720p VideoTrack
 *   This parameter must not be set when when preferredVideoCodecs is set to `auto`.
 * @property {MediaStream.constructor} [MediaStream] - Overrides the native MediaStream class.
 * @property {?string} [name=null] - Set to connect to a {@link Room} by name
 * @property {boolean|NetworkQualityConfiguration} [networkQuality=false] - Whether to enable the Network
 *   Quality API or not. This only takes effect in Group Rooms. Pass a {@link NetworkQualityConfiguration}
 *   to configure verbosity levels for network quality information for {@link LocalParticipant}
 *   and {@link RemoteParticipant}s. A <code>true</code> value will set the {@link NetworkQualityVerbosity}
 *   for the {@link LocalParticipant} to {@link NetworkQualityVerbosity}<code style="padding:0 0">#minimal</code>
 *   and the {@link NetworkQualityVerbosity} for {@link RemoteParticipant}s to
 *   {@link NetworkQualityVerbosity}<code style="padding:0 0">#none</code>.
 * @property {Array<string>} [notifyWarnings=[]] - The SDK raises warning events when it
 *   detects certain conditions. You can implement callbacks on these events to act on them, or to alert
 *   the user of an issue. Subsequently, "warningsCleared" event is raised when conditions have returned
 *   to normal. You can listen to these events by specifying an array of warning. By default,
 *   this array is empty and no warning events will be raised.
 *   Possible values include <code>recording-media-lost</code>, which is raised when the media server
 *   has not detected any media on the published track that is being recorded in the past 30 seconds.
 *   This usually happens when there are network interruptions or when the track has stopped.
 *   This warning is raised by {@link LocalTrackPublication}, {@link LocalParticipant}, and {@link Room} object.
 * @property {boolean} [receiveTranscriptions=false] - Whether to enable transcription in the {@link Room}.
 *   When set to <code>true</code>, the {@link Room} will emit <code>transcription</code> events containing transcription data.
 * @property {string} [region='gll'] - Preferred signaling region; By default, you will be connected to the
 *   nearest signaling server determined by latency based routing. Setting a value other
 *   than <code style="padding:0 0">gll</code> bypasses routing and guarantees that signaling traffic will be
 *   terminated in the region that you prefer. Please refer to this <a href="https://www.twilio.com/docs/video/ip-address-whitelisting#signaling-communication" target="_blank">table</a>
 *   for the list of supported signaling regions.
 * @property {RTCConfiguration} [rtcConfiguration] - An optional RTCConfiguration to pass to the RTCPeerConnection constructor.
 * This allows you to configure the WebRTC connection between your local machine and the remote peer.
 * @property {RTCPeerConnection.constructor} [RTCPeerConnection] - Allows overriding the native RTCPeerConnection class.
 * @property {Array<AudioCodec|AudioCodecSettings>} [preferredAudioCodecs=[]] - Preferred audio codecs;
 *  An empty array preserves the current audio codec preference order.
 * @property {Array<VideoCodec|VideoCodecSettings>|VideoEncodingMode} [preferredVideoCodecs=[]] -
 *  Preferred video codecs; when set to 'VideoEncodingMode.Auto', SDK manages the video codec,
 *  by preferring VP8 simulcast in group rooms. It also enables adaptive simulcast, which allows SDK
 *  to turn off simulcast layers that are not needed for efficient bandwidth and CPU usage.
 *  An empty array preserves the current video codec.
 *  preference order. If you want to set a preferred video codec on a Group Room,
 *  you will need to create the Room using the REST API and set the
 *  <code>VideoCodecs</code> property.
 *  See <a href="https://www.twilio.com/docs/api/video/rooms-resource#create-room">
 *  here</a> for more information.
 * @property {Array<LocalTrack|MediaStreamTrack>} [tracks] - The
 *   {@link LocalTrack}s or MediaStreamTracks with which to join the
 *   {@link Room}. These tracks can be obtained either by calling
 *   {@link createLocalTracks}, or by constructing them from the MediaStream
 *   obtained by calling <code>getUserMedia()</code>.
 * @property {boolean|CreateLocalTrackOptions} [video=true] - Whether or not to
 *   get local video with <code>getUserMedia</code> when <code>tracks</code>
 *   are not provided.
 */ /**
 * {@link BandwidthProfileOptions} allows you to configure how your available downlink
 * bandwidth is shared among the RemoteTracks you have subscribed to in a Group Room.
 * @typedef {object} BandwidthProfileOptions
 * @property {VideoBandwidthProfileOptions} [video] - Optional parameter to configure
 *   how your available downlink bandwidth is shared among the {@link RemoteVideoTrack}s you
 *   have subscribed to in a Group Room.
 */ /**
 * {@link VideoBandwidthProfileOptions} allows you to configure how your available downlink
 * bandwidth is shared among the {@link RemoteVideoTrack}s you have subscribed to in a Group Room.
 * @typedef {object} VideoBandwidthProfileOptions
 * @property {Track.Priority} [dominantSpeakerPriority="standard"] - Optional parameter to
 *   specify the minimum subscribe {@link Track.Priority} of the Dominant Speaker's {@link RemoteVideoTrack}s.
 *   This means that the Dominant Speaker's {@link RemoteVideoTrack}s that are published with
 *   lower {@link Track.Priority} will be subscribed to with the {@link Track.Priority} specified here.
 *   This has no effect on {@link RemoteVideoTrack}s published with higher {@link Track.Priority}, which will
 *   still be subscribed to with with the same {@link Track.Priority}. If not specified, this defaults to "standard".
 *   This parameter only applies to a Group Room Participant when {@link ConnectOptions}.dominantSpeaker is set to true.
 * @property {number} [maxSubscriptionBitrate] - Optional parameter to specify the maximum
 *   downlink video bandwidth in bits per second (bps). By default, there are no limits on
 *   the downlink video bandwidth.
 * @property {ClientTrackSwitchOffControl} [clientTrackSwitchOffControl="auto"] - Optional parameter that determines
 *    when to turn the {@link RemoteVideoTrack} on or off. When set to "auto", SDK will use the visibility of the
 *    attached elements to determine if the {@link RemoteVideoTrack} should be turned off or on. When the attached video elements become invisible the {@link RemoteVideoTrack} will
 *    be turned off, and when elements become visible they will be turned on. When set to "manual" you can turn the {@link RemoteVideoTrack}
 *    on and off using the api {@link RemoteVideoTrack#switchOn} and {@link RemoteVideoTrack#switchOff} respectively.
 * @property {VideoContentPreferencesMode} [contentPreferencesMode="auto"] - This Optional parameter configures
 *    the mode for specifying content preferences for the {@link RemoteVideoTrack}. When set to "auto" the
 *    SDK determines the render dimensions by inspecting the attached video elements. {@link RemoteVideoTrack}s rendered in smaller video elements
 *    will receive a lower resolution stream compared to the video rendered in larger video elements. When set to "manual" you can set
 *    the dimensions programmatically by calling {@link RemoteVideoTrack#setContentPreferences}.
 * @property {number} [maxTracks] - <code>(deprecated: use "clientTrackSwitchOffControl" instead)</code>. Optional
 *   parameter to specify the maximum number of visible {@link RemoteVideoTrack}s, which will be selected based on
 *   {@link Track.Priority} and an N-Loudest policy. By default there are no limits on the number of visible {@link RemoteVideoTrack}s.
 *   0 or a negative value will remove any limit on the maximum number of visible {@link RemoteVideoTrack}s.
 * @property {BandwidthProfileMode} [mode="grid"] - Optional parameter to specify how the {@link RemoteVideoTrack}s'
 *   TrackPriority values are mapped to bandwidth allocation in Group Rooms. This defaults to "grid",
 *   which results in equal bandwidth share allocation to all {@link RemoteVideoTrack}s.
 * @property {VideoRenderDimensions} [renderDimensions] - <code>(deprecated: use "contentPreferencesMode" instead)</code>. Optional
 * parameter to specify the desired render dimensions of {@link RemoteVideoTrack}s.
 * @property {TrackSwitchOffMode} [trackSwitchOffMode="predicted"] - Optional parameter to configure
 *   how {@link RemoteVideoTrack}s are switched off in response to bandwidth pressure. Defaults to "predicted".
 */ /**
 * @deprecated
 * {@link VideoRenderDimensions} allows you to specify the desired render dimensions of {@link RemoteVideoTrack}s.
 * You can specify 'auto' for this field - which is also default value -  based on {@link Track.Priority}. The bandwidth allocation algorithm will distribute the available downlink bandwidth
 * proportional to the requested render dimensions. This is just an input for calculating the bandwidth to be allocated
 * and does not affect the actual resolution of the {@link RemoteVideoTrack}s.
 * @typedef {object} VideoRenderDimensions
 * @property {VideoTrack.Dimensions} [high] - Optional parameter to specify the desired rendering dimensions of
 *   {@link RemoteVideoTrack} whose {@link Track.Priority} is "high". 0 or a negative value will result in the lowest
 *   possible resolution. This defaults to 1280 x 720 (HD).
 * @property {VideoTrack.Dimensions} [low] - Optional parameter to specify the desired rendering dimensions of
 *   {@link RemoteVideoTrack} whose {@link Track.Priority} is "low". 0 or a negative value will result in the lowest
 *   possible resolution. This defaults to 176 x 144 (QCIF).
 * @property {VideoTrack.Dimensions} [standard] - Optional parameter to specify the desired rendering dimensions of
 *   {@link RemoteVideoTrack} whose {@link Track.Priority} is "standard". 0 or a negative value will result in the lowest
 *   possible resolution. This defaults to 640 x 480 (VGA).
 */ /**
 * Configure verbosity levels for network quality information for
 * {@link LocalParticipant} and {@link RemoteParticipant}s.
 * @typedef {object} NetworkQualityConfiguration
 * @property {NetworkQualityVerbosity} [local=1] - Verbosity level for {@link LocalParticipant}
 * @property {NetworkQualityVerbosity} [remote=0] - Verbosity level for {@link RemoteParticipant}s
 */ /**
 * You may pass these levels to {@link ConnectOptions} to override
 * log levels for individual components.
 * @typedef {object} LogLevels
 * @property {LogLevel} [default='warn'] - Log level for 'default' modules.
 * @property {LogLevel} [media='warn'] - Log level for 'media' modules.
 * @property {LogLevel} [signaling='warn'] - Log level for 'signaling' modules.
 * @property {LogLevel} [webrtc='warn'] - Log level for 'webrtc' modules.
 */ /**
 * Audio codec settings.
 * @typedef {object} AudioCodecSettings
 * @property {AudioCodec} codec - Audio codec name
 */ /**
 * Opus codec settings.
 * @typedef {AudioCodecSettings} OpusCodecSettings
 * @property {AudioCodec} name - "opus"
 * @property {boolean} [dtx=true] - Enable/disable discontinuous transmission (DTX);
 *   If enabled all published {@link LocalAudioTrack}s will reduce the outgoing bitrate
 *   to near-zero whenever speech is not detected, resulting in bandwidth and CPU savings;
 *   It defaults to true.
 */ /**
 * Video codec settings.
 * @typedef {object} VideoCodecSettings
 * @property {VideoCodec} codec - Video codec name
 */ /**
 * VP8 codec settings.
 * @typedef {VideoCodecSettings} VP8CodecSettings
 * @property {VideoCodec} name - "VP8"
 * @property {boolean} [simulcast=false] - Enable/disable VP8 simulcast; If
 *   enabled, Twilio's Video SDK will send three video streams of different
 *   qualities
 */ /**
 * Names of the supported audio codecs.
 * @enum {string}
 */ // eslint-disable-next-line
var AudioCodec = {
    isac: 'isac',
    opus: 'opus',
    PCMA: 'PCMA',
    PCMU: 'PCMU'
};
/**
 * Names of the supported VideoEncodingMode.
 * @enum {string}
 */ // eslint-disable-next-line
var VideoEncodingMode = {
    Auto: 'auto'
};
/**
 * Names of the supported video codecs.
 * @enum {string}
 */ // eslint-disable-next-line
var VideoCodec = {
    H264: 'H264',
    VP8: 'VP8'
};
// VP9 is supported by most browsers, but backend doesn't at the moment.
// Hide it from public documentation until then.
VideoCodec.VP9 = 'VP9';
/**
 * Levels for logging verbosity.
 * @enum {string}
 */ // eslint-disable-next-line
var LogLevel = {
    debug: 'debug',
    info: 'info',
    warn: 'warn',
    error: 'error',
    off: 'off'
};
/**
 * The verbosity level of network quality information of a {@link Participant}.
 * @enum {number}
 */ // eslint-disable-next-line
var NetworkQualityVerbosity = {
    /**
     * Nothing is reported for the {@link Participant}. This has no effect and
     * defaults to {@link NetworkQualityVerbosity}<code style="padding:0 0">#minimal</code>
     * for the {@link LocalParticipant}.
     */ none: 0,
    /**
     * Reports {@link NetworkQualityLevel} for the {@link Participant}.
     */ minimal: 1,
    /**
     * Reports {@link NetworkQualityLevel} and {@link NetworkQualityStats} for the {@link Participant}.
     * {@link NetworkQualityStats} is populated with audio and video {@link NetworkQualityLevel}s
     * based on which the {@link Participant}'s {@link NetworkQualityLevel} is calculated.
     */ moderate: 2,
    /**
     * Reports {@link NetworkQualityLevel} and {@link NetworkQualityStats} for the {@link Participant}.
     * {@link NetworkQualityStats} is populated with audio and Video {@link NetworkQualityLevel}s
     * and their corresponding {@link NetworkQualityMediaStats} based on which the
     * {@link Participant}'s {@link NetworkQualityLevel} is calculated.
     */ detailed: 3
};
/**
 * {@link TrackSwitchOffMode} specifies when {@link RemoteVideoTrack}s' are switched off.
 * @enum {string}
 */ // eslint-disable-next-line
var TrackSwitchOffMode = {
    /**
     * In this mode, {@link RemoteVideoTrack}s are switched off only when network congestion
     * is detected.
     */ detected: 'detected',
    /**
     * In this mode, {@link RemoteVideoTrack}s are pro-actively switched off when network
     * congestion is predicted by the bandwidth estimation mechanism.
     */ predicted: 'predicted',
    /**
     * In this mode, {@link RemoteVideoTrack}s are not switched off. Instead in response to network
     * congestion, tracks will be adjusted to lower quality.
     */ disabled: 'disabled'
};
/**
 * {@link BandwidthProfileMode} specifies how {@link RemoteVideoTrack}s' {@link Track.Priority} values
 * are mapped to bandwidth allocation in Group Rooms.
 * @enum {string}
 */ // eslint-disable-next-line
var BandwidthProfileMode = {
    /**
     * This mode is for use cases where all the subscribed {@link RemoteVideoTrack}s are
     * equally important. The bandwidth allocation algorithm will share the available
     * downlink bandwidth equally among the subscribed {@link RemoteVideoTrack}s, irrespective
     * of their {@link Track.Priority}. In case of insufficient downlink bandwidth, the lower
     * priority {@link RemoteVideoTrack}s are switched off.
     */ grid: 'grid',
    /**
     * This mode is for use cases where some {@link RemoteVideoTrack}s are prioritized more than
     * others. However, the lower priority {@link RemoteVideoTrack}s still need to be visible.
     * The bandwidth allocation algorithm will share the available downlink bandwidth proportional
     * to the requested {@link VideoRenderDimensions} corresponding to their {@link Track.Priority}.
     * In case of insufficient downlink bandwidth, the quality of higher priority {@link RemoteVideoTrack}s
     * may be degraded to avoid switching off lower priority {@link RemoteVideoTrack}s.
     */ collaboration: 'collaboration',
    /**
     * This mode is for use cases where some {@link RemoteVideoTrack}s are deemed critical and must
     * be preserved at any cost over the other {@link RemoteVideoTrack}s. The bandwidth allocation
     * algorithm will allocate as big a share of the available downlink bandwidth as it possibly
     * can to the higher priority {@link RemoteVideoTrack}s, and only then consider the lower priority
     * {@link RemoteVideoTrack}s. In case of insufficient downlink bandwidth, the lower priority
     * {@link RemoteVideoTrack}s are switched off in order to preserve the quality of the higher
     * priority {@link RemoteVideoTrack}s.
     */ presentation: 'presentation'
};
/**
 * {@link VideoContentPreferencesMode} specifies how {@link RemoteVideoTrack}s' render dimensions are
 * decided by the SDK.
 * @enum {string}
 */ // eslint-disable-next-line
var VideoContentPreferencesMode = {
    /**
     * when set to auto, SDK uses the sizes of the video elements attached to the to the  {@link RemoteVideoTrack} dynamically to
     * decide the render dimensions. {@link RemoteVideoTrack}s rendered in smaller video elements will be given smaller bandwidth allocation
     * compared to the tracks rendered in large video elements.
     */ auto: 'auto',
    /**
     * When set to manual, application can use {@link RemoteVideoTrack#setContentPreference} to set the
     * desired render dimensions for the {@link RemoteVideoTrack}.
     */ manual: 'manual'
};
/**
 * {@link ClientTrackSwitchOffControl} specifies how {@link RemoteVideoTrack}s' turned on and off
 * @enum {string}
 */ // eslint-disable-next-line
var ClientTrackSwitchOffControl = {
    /**
     * when set to auto, SDK uses the visibility of the video elements attached to the to the  {@link RemoteVideoTrack} to decide.
     * on turning tracks on or off. The track that are not attached to any video elements or not visible on the screen will be turned
     * off automatically.
     */ auto: 'auto',
    /**
     * When set to manual, application can use {@link RemoteVideoTrack}s switchOff and switchOn apis to control turn the track on or off.
     */ manual: 'manual'
};
/**
 * Names of the supported levels for {@link EventListenerEvent}s.
 * @enum {string}
 */ // eslint-disable-next-line
var EventListenerLevel = {
    debug: 'debug',
    error: 'error',
    info: 'info',
    warning: 'warning'
};
/**
 * Names of the supported groups for {@link EventListenerEvent}s.
 * @enum {string}
 */ // eslint-disable-next-line
var EventListenerGroup = {
    /**
     * Events associated with the connection to Twilio's signaling server
     */ signaling: 'signaling'
};
/**
 * An {@link EventListener} allows you to listen to fine-grained {@link EventListenerEvent}s related
 * to signaling and media that are not available in the public APIs, which might be useful for your own
 * reporting and diagnostics.
 * @typedef {EventEmitter} EventListener
 * @example
 * const { EventEmitter } = require('events');
 * const { connect } = require('twilio-video');
 *
 * const eventListener = new EventEmitter();
 * eventListener.on('event', function(event) {
 *   console.log('The SDK raised an event:', event);
 * });
 *
 * connect('token', {
 *   eventListener: eventListener
 * });
 */ /**
 * The SDK raised an {@link EventListenerEvent}.
 * @event EventListener#event
 * @param {EventListenerEvent} event - Context about the event raised by the SDK.
 * This can be one of the following:
 *  * {@link EventListenerClosedEvent}
 *  * {@link EventListenerConnectingEvent}
 *  * {@link EventListenerEarlyEvent}
 *  * {@link EventListenerOpenEvent}
 *  * {@link EventListenerWaitingEvent}
 */ /**
 * An {@link EventListenerEvent} provides context about an event raised by the SDK on the
 * {@link EventListener}. Apart from the properties listed here, it may also include some
 * event-specific data within an optional "payload" property. The different types of
 * {@link EventListenerEvent}s are listed below:
 *  * {@link EventListenerClosedEvent}
 *  * {@link EventListenerConnectingEvent}
 *  * {@link EventListenerEarlyEvent}
 *  * {@link EventListenerOpenEvent}
 *  * {@link EventListenerWaitingEvent}
 * @typedef {object} EventListenerEvent
 * @property {number} elapsedTime - The time elapsed in milliseconds since connect() was called
 * @property {EventListenerGroup} group - The group under which the event is classified
 * @property {EventListenerLevel} level - The verbosity level of the event, which can be one of "debug", "error", "info", "warning"
 * @property {string} name - The name of the event
 * @property {*} [payload] - Optional event-specific data
 * @property {number} timestamp - The time in milliseconds relative to the Unix Epoch when the event was raised
 */ /**
 * The connection to Twilio's signaling server was closed.
 * @typedef {EventListenerEvent} EventListenerClosedEvent
 * @property {EventListenerGroup} group='signaling'
 * @property {EventListenerLevel} level - 'info' if the connection was closed by the client, 'error' otherwise
 * @property {string} name='closed'
 * @property {{reason: string}} payload - Reason for the connection being closed. It can be one of
 *   'busy', 'failed', 'local', 'remote' or 'timeout'
 */ /**
 * The SDK is connecting to Twilio's signaling server.
 * @typedef {EventListenerEvent} EventListenerConnectingEvent
 * @property {EventListenerGroup} group='signaling'
 * @property {EventListenerLevel} level='info'
 * @property {string} name='connecting'
 */ /**
 * The SDK is about to connect to Twilio's signaling server.
 * @typedef {EventListenerEvent} EventListenerEarlyEvent
 * @property {EventListenerGroup} group='signaling'
 * @property {EventListenerLevel} level='info'
 * @property {string} name='early'
 */ /**
 * The SDK has established a signaling connection to Twilio's signaling server.
 * @typedef {EventListenerEvent} EventListenerOpenEvent
 * @property {EventListenerGroup} group='signaling'
 * @property {EventListenerLevel} level='info'
 * @property {string} name='open'
 */ /**
 * The SDK is waiting to retry connecting th Twilio's signaling server. This can
 * happen if the server is busy with too many connection requests.
 * @typedef {EventListenerEvent} EventListenerWaitingEvent
 * @property {EventListenerGroup} group='signaling'
 * @property {EventListenerLevel} level='warning'
 * @property {string} name='waiting'
 */ function deprecateOptions(options, log, deprecationTable) {
    deprecationTable.forEach(function(prop) {
        var didWarn = prop.didWarn, name = prop.name, newName = prop.newName, shouldDelete = prop.shouldDelete;
        if (name in options && typeof options[name] !== 'undefined') {
            if (newName && shouldDelete) {
                options[newName] = options[name];
            }
            if (shouldDelete) {
                delete options[name];
            }
            if (!didWarn && ![
                'error',
                'off'
            ].includes(log.level)) {
                log.warn("The ConnectOptions \"" + name + "\" is " + (newName ? "deprecated and scheduled for removal. Please use \"" + newName + "\" instead." : 'no longer applicable and will be ignored.'));
                prop.didWarn = true;
            }
        }
    });
}
function createLocalParticipant(signaling, log, encodingParameters, networkQualityConfiguration, options, localTracks) {
    var localParticipantSignaling = signaling.createLocalParticipantSignaling(encodingParameters, networkQualityConfiguration);
    log.debug('Creating a new LocalParticipant:', localParticipantSignaling);
    return new options.LocalParticipant(localParticipantSignaling, localTracks, options);
}
function createRoom(options, localParticipant, roomSignaling) {
    var room = new Room(localParticipant, roomSignaling, options);
    var log = options.log;
    log.debug('Creating a new Room:', room);
    roomSignaling.on('stateChanged', function stateChanged(state) {
        if (state === 'disconnected') {
            log.info('Disconnected from Room:', room.toString());
            roomSignaling.removeListener('stateChanged', stateChanged);
        }
    });
    return room;
}
function createRoomSignaling(token, options, signaling, encodingParameters, preferredCodecs, localParticipant) {
    options.log.debug('Creating a new RoomSignaling');
    return signaling.connect(localParticipant._signaling, token, encodingParameters, preferredCodecs, options);
}
function getLocalTracks(options, handleLocalTracks) {
    var log = options.log;
    options.shouldStopLocalTracks = !options.tracks;
    if (options.shouldStopLocalTracks) {
        log.info('LocalTracks were not provided, so they will be acquired ' + 'automatically before connecting to the Room. LocalTracks will ' + 'be released if connecting to the Room fails or if the Room ' + 'is disconnected');
    } else {
        log.info('Getting LocalTracks');
        log.debug('Options:', options);
    }
    return options.createLocalTracks(options).then(function getLocalTracksSucceeded(localTracks) {
        var promise = handleLocalTracks(localTracks);
        promise.catch(function handleLocalTracksFailed() {
            if (options.shouldStopLocalTracks) {
                log.info('The automatically acquired LocalTracks will now be stopped');
                localTracks.forEach(function(track) {
                    track.stop();
                });
            }
        });
        return promise;
    });
}
function normalizeCodecSettings(nameOrSettings) {
    var settings = typeof nameOrSettings === 'string' ? {
        codec: nameOrSettings
    } : nameOrSettings;
    switch(settings.codec.toLowerCase()){
        case 'opus':
            {
                return Object.assign({
                    dtx: true
                }, settings);
            }
        case 'vp8':
            {
                return Object.assign({
                    simulcast: false
                }, settings);
            }
        default:
            {
                return settings;
            }
    }
}
module.exports = connect; //# sourceMappingURL=connect.js.map
}}),
"[project]/node_modules/twilio-video/es5/createlocaltrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), DEFAULT_LOG_LEVEL = _a.DEFAULT_LOG_LEVEL, DEFAULT_LOGGER_NAME = _a.DEFAULT_LOGGER_NAME;
/**
 * Request a {@link LocalAudioTrack} or {@link LocalVideoTrack}.
 * @param {Track.Kind} kind - "audio" or "video"
 * @param {CreateLocalTrackOptions} [options]
 * @returns {Promise<LocalAudioTrack|LocalVideoTrack>}
 * @private
 */ function createLocalTrack(kind, options) {
    options = Object.assign({
        loggerName: DEFAULT_LOGGER_NAME,
        logLevel: DEFAULT_LOG_LEVEL
    }, options);
    var createOptions = {};
    createOptions.loggerName = options.loggerName;
    createOptions.logLevel = options.logLevel;
    delete options.loggerName;
    delete options.logLevel;
    // NOTE(lrivas): These createLocalTrack options override the default media handling and should be passed as createOptions
    var customMediaHandling = [
        'getUserMedia',
        'enumerateDevices',
        'MediaStream',
        'mapMediaElement',
        'disposeMediaElement'
    ];
    customMediaHandling.forEach(function(override) {
        if (override in options) {
            createOptions[override] = options[override];
            delete options[override];
        }
    });
    var createLocalTracks = options.createLocalTracks;
    delete options.createLocalTracks;
    createOptions[kind] = Object.keys(options).length > 0 ? options : true;
    return createLocalTracks(createOptions).then(function(localTracks) {
        return localTracks[0];
    });
}
/**
 * Request a {@link LocalAudioTrack}.
 * @alias module:twilio-video.createLocalAudioTrack
 * @param {CreateLocalTracksOptions|CreateLocalAudioTrackOptions} [options] - Options for requesting a {@link LocalAudioTrack}
 * @returns {Promise<LocalAudioTrack>}
 * @example
 * var Video = require('twilio-video');
 *
 * // Connect to the Room with just video
 * Video.connect('my-token', {
 *   name: 'my-cool-room',
 *   video: true
 * }).then(function(room) {
 *   // Add audio after connecting to the Room
 *   Video.createLocalAudioTrack().then(function(localTrack) {
 *     room.localParticipant.publishTrack(localTrack);
 *   });
 * });
 * @example
 * var Video = require('twilio-video');
 *
 * // Request the LocalAudioTrack with a custom name
 * // and krisp noise cancellation
 * Video.createLocalAudioTrack({
 *   name: 'microphone',
 *   noiseCancellationOptions: {
 *      vendor: 'krisp',
 *      sdkAssetsPath: '/twilio-krisp-audio-plugin/1.0.0/dist'
 *   }
 * });
 */ function createLocalAudioTrack(options) {
    return createLocalTrack('audio', options);
}
/**
 * Request a {@link LocalVideoTrack}. Note that on mobile browsers,
 * the camera can be reserved by only one {@link LocalVideoTrack} at any given
 * time. If you attempt to create a second {@link LocalVideoTrack}, video frames
 * will no longer be supplied to the first {@link LocalVideoTrack}.
 * @alias module:twilio-video.createLocalVideoTrack
 * @param {CreateLocalTrackOptions} [options] - Options for requesting a {@link LocalVideoTrack}
 * @returns {Promise<LocalVideoTrack>}
 * @example
 * var Video = require('twilio-video');
 *
 * // Connect to the Room with just audio
 * Video.connect('my-token', {
 *   name: 'my-cool-room',
 *   audio: true
 * }).then(function(room) {
 *   // Add video after connecting to the Room
 *   Video.createLocalVideoTrack().then(function(localTrack) {
 *     room.localParticipant.publishTrack(localTrack);
 *   });
 * });
 * @example
 * var Video = require('twilio-video');
 *
 * // Request the default LocalVideoTrack with a custom name
 * Video.createLocalVideoTrack({ name: 'camera' }).then(function(localTrack) {
 *   console.log(localTrack.name); // 'camera'
 * });
 */ function createLocalVideoTrack(options) {
    return createLocalTrack('video', options);
}
/**
 * {@link NoiseCancellationVendor} specifies the 3rd party plugin to use for noise cancellation.
 * @enum {string}
 */ // eslint-disable-next-line
var NoiseCancellationVendor = {
    /**
     * This plugin can be found by requesting access with this form {@link https://forms.gle/eeFyoGJj1mgMrxN88}
     */ krisp: 'krisp'
};
/**
 * You can use 3rd party noise cancellation plugin when creating {@link LocalAudioTrack}
 * By specifying these options. This is a beta feature.
 * @typedef {object} NoiseCancellationOptions
 * @property {NoiseCancellationVendor} vendor - Specifies the vendor library to use
 *   You need to obtain and host the library files on your web server.
 * @property {string} sdkAssetsPath - Specifies path where vendor library files are
 *   hosted on your web server.
 */ /**
 * Create {@link LocalAudioTrack} options.
 * @typedef {CreateLocalTrackOptions} CreateLocalAudioTrackOptions
 * @property {boolean} [workaroundWebKitBug180748=false] - setting this
 *   attempts to workaround WebKit Bug 180748, where, in Safari, getUserMedia may return a silent audio
 *   MediaStreamTrack.
 * @property {DefaultDeviceCaptureMode} [defaultDeviceCaptureMode="auto"] - This optional property only applies if the
 *   {@link LocalAudioTrack} is capturing from the default audio input device connected to a desktop or laptop. When the
 *   property is set to "auto", the LocalAudioTrack restarts whenever the default audio input device changes, in order to
 *   capture audio from the new default audio input device. For example, when a bluetooth audio headset is connected to a
 *   Macbook, the LocalAudioTrack will start capturing audio from the headset microphone. When the headset is disconnected,
 *   the LocalAudioTrack will start capturing audio from the Macbook microphone. When the property is set to "manual", the
 *   LocalAudioTrack continues to capture from the same audio input device even after the default audio input device changes.
 *   When the property is not specified, it defaults to "auto".
 * @property {NoiseCancellationOptions} [noiseCancellationOptions] - This optional property enables using 3rd party plugins
 *   for noise cancellation.
 */ /**
 * Create {@link LocalTrack} options. Apart from the properties listed here, you can
 * also specify any of the <a href="https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints" target="_blank">MediaTrackConstraints</a>
 * properties.
 * @typedef {MediaTrackConstraints} CreateLocalTrackOptions
 * @property {LogLevel|LogLevels} [logLevel='warn'] - <code>(deprecated: use [Video.Logger](module-twilio-video.html) instead.
 *   See [examples](module-twilio-video.html#.connect) for details)</code>
 *   Set the default log verbosity
 *   of logging. Passing a {@link LogLevel} string will use the same
 *   level for all components. Pass a {@link LogLevels} to set specific log
 *   levels.
 * @property {string} [loggerName='twilio-video'] - The name of the logger. Use this name when accessing the logger used by the SDK.
 *   See [examples](module-twilio-video.html#.connect) for details.
 * @property {string} [name] - The {@link LocalTrack}'s name; by default,
 *   it is set to the {@link LocalTrack}'s ID.
 */ module.exports = {
    audio: createLocalAudioTrack,
    video: createLocalVideoTrack
}; //# sourceMappingURL=createlocaltrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
var createlocaltracks_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/createlocaltracks.js [app-client] (ecmascript)");
var preflighttest_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/preflight/preflighttest.js [app-client] (ecmascript)");
var internals = {
    connect: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/connect.js [app-client] (ecmascript)"),
    createLocalAudioTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/createlocaltrack.js [app-client] (ecmascript)").audio,
    createLocalVideoTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/createlocaltrack.js [app-client] (ecmascript)").video,
    isSupported: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/support.js [app-client] (ecmascript)")(),
    version: __turbopack_context__.r("[project]/node_modules/twilio-video/package.json (json)").version,
    Logger: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/vendor/loglevel.js [app-client] (ecmascript)"),
    LocalAudioTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)").LocalAudioTrack,
    LocalDataTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)").LocalDataTrack,
    LocalVideoTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)").LocalVideoTrack
};
function connect(token, options) {
    var internalOptions = __assign({
        createLocalTracks: createlocaltracks_1.createLocalTracks
    }, options);
    return internals.connect(token, internalOptions);
}
function createLocalAudioTrack(options) {
    var internalOptions = __assign({
        createLocalTracks: createlocaltracks_1.createLocalTracks
    }, options);
    return internals.createLocalAudioTrack(internalOptions);
}
function createLocalVideoTrack(options) {
    var internalOptions = __assign({
        createLocalTracks: createlocaltracks_1.createLocalTracks
    }, options);
    return internals.createLocalVideoTrack(internalOptions);
}
/**
 * @module twilio-video
 * @property {boolean} isSupported - true if the current browser is officially
 *   supported by twilio-video.js; In this context, "supported" means that
 *   twilio-video.js has been extensively tested with this browser; This
 *   <a href="https://www.twilio.com/docs/video/javascript#supported-browsers" target="_blank">table</a>
 *   specifies the list of officially supported browsers.
 *
 * @property {object} Logger - The <a href="https://www.npmjs.com/package/loglevel" target="_blank">loglevel</a>
 *    module used by the SDK. Use this object to access the internal loggers and perform actions as defined by the
 *   <a href="https://www.npmjs.com/package/loglevel" target="_blank">loglevel</a> APIs.
 *   See [connect](#.connect) for examples.
 *
 * @property {string} version - current version of twilio-video.js.
 */ var isSupported = internals.isSupported;
var version = internals.version;
var Logger = internals.Logger;
var LocalAudioTrack = internals.LocalAudioTrack;
var LocalVideoTrack = internals.LocalVideoTrack;
var LocalDataTrack = internals.LocalDataTrack;
module.exports = {
    connect: connect,
    createLocalAudioTrack: createLocalAudioTrack,
    createLocalVideoTrack: createLocalVideoTrack,
    createLocalTracks: createlocaltracks_1.createLocalTracks,
    runPreflight: preflighttest_1.runPreflight,
    isSupported: isSupported,
    version: version,
    Logger: Logger,
    LocalAudioTrack: LocalAudioTrack,
    LocalVideoTrack: LocalVideoTrack,
    LocalDataTrack: LocalDataTrack
}; //# sourceMappingURL=index.js.map
}}),
}]);

//# sourceMappingURL=node_modules_twilio-video_es5_d70e1825._.js.map