(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/twilio-video/es5/media/track/noisecancellationimpl.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.applyNoiseCancellation = exports.NoiseCancellationImpl = void 0;
var noisecancellationadapter_1 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/noisecancellationadapter.js [app-client] (ecmascript)");
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
/**
 * {@link NoiseCancellation} interface provides methods to control noise cancellation at runtime. This interface is exposed
 * on {@link LocalAudioTrack} property `noiseCancellation`. It is available only when {@link NoiseCancellationOptions} are
 * specified when creating a {@link LocalAudioTrack}, and the plugin is successfully loaded.
 * @alias NoiseCancellation
 * @interface
 *
 * @example
 * const { connect, createLocalAudioTrack } = require('twilio-video');
 *
 * // Create a LocalAudioTrack with Krisp noise cancellation enabled.
 * const localAudioTrack = await createLocalAudioTrack({
 *   noiseCancellationOptions: {
 *     sdkAssetsPath: 'path/to/hosted/twilio/krisp/audio/plugin/1.0.0/dist',
 *     vendor: 'krisp'
 *   }
 * });
 *
 * if (!localAudioTrack.noiseCancellation) {
 *   // If the Krisp audio plugin fails to load, then a warning message will be logged
 *   // in the browser console, and the "noiseCancellation" property will be set to null.
 *   // You can still use the LocalAudioTrack to join a Room. However, it will use the
 *   // browser's noise suppression instead of the Krisp noise cancellation. Make sure
 *   // the "sdkAssetsPath" provided in "noiseCancellationOptions" points to the correct
 *   // hosted path of the plugin assets.
 * } else {
 *   // Join a Room with the LocalAudioTrack.
 *   const room = await connect('token', {
 *     name: 'my-cool-room',
 *     tracks: [localAudioTrack]
 *   });
 *
 *   if (!localAudioTrack.noiseCancellation.isEnabled) {
 *     // Krisp noise cancellation is permanently disabled in Peer-to-Peer and Go Rooms.
 *   }
 * }
 *
 * //
 * // Enable/disable noise cancellation.
 * // @param {boolean} enable - whether noise cancellation should be enabled
 * //
 * function setNoiseCancellation(enable) {
 *   const { noiseCancellation } = localAudioTrack;
 *   if (noiseCancellation) {
 *     if (enable) {
 *       // If enabled, then the LocalAudioTrack will use the Krisp noise
 *       // cancellation instead of the browser's noise suppression.
 *       noiseCancellation.enable();
 *     } else {
 *       // If disabled, then the LocalAudioTrack will use the browser's
 *       // noise suppression instead of the Krisp noise cancellation.
 *       noiseCancellation.disable();
 *     }
 *   }
 * }
 */ var NoiseCancellationImpl = function() {
    function NoiseCancellationImpl(processor, originalTrack) {
        this._processor = processor;
        this._sourceTrack = originalTrack;
        this._disabledPermanent = false;
    }
    Object.defineProperty(NoiseCancellationImpl.prototype, "vendor", {
        /**
         * Name of the noise cancellation vendor.
         * @type {NoiseCancellationVendor}
         */ get: function() {
            return this._processor.vendor;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NoiseCancellationImpl.prototype, "sourceTrack", {
        /**
         * The underlying MediaStreamTrack of the {@link LocalAudioTrack}.
         * @type {MediaStreamTrack}
         */ get: function() {
            return this._sourceTrack;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NoiseCancellationImpl.prototype, "isEnabled", {
        /**
         * Whether noise cancellation is enabled.
         * @type {boolean}
         */ get: function() {
            return this._processor.isEnabled();
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Enable noise cancellation.
     * @returns {Promise<void>} Promise that resolves when the operation is complete
     * @throws {Error} Throws an error if noise cancellation is disabled permanently
     *   for the {@link LocalAudioTrack}
     */ NoiseCancellationImpl.prototype.enable = function() {
        if (this._disabledPermanent) {
            throw new Error(this.vendor + " noise cancellation is disabled permanently for this track");
        }
        this._processor.enable();
        return Promise.resolve();
    };
    /**
     * Disable noise cancellation.
     * @returns {Promise<void>} Promise that resolves when the operation is complete
     */ NoiseCancellationImpl.prototype.disable = function() {
        this._processor.disable();
        return Promise.resolve();
    };
    /**
     * @private
     */ NoiseCancellationImpl.prototype.reacquireTrack = function(reacquire) {
        return __awaiter(this, void 0, void 0, function() {
            var processorWasEnabled, track, processedTrack;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        processorWasEnabled = this._processor.isEnabled();
                        this._processor.disconnect();
                        return [
                            4 /*yield*/ ,
                            reacquire()
                        ];
                    case 1:
                        track = _a.sent();
                        this._sourceTrack = track;
                        return [
                            4 /*yield*/ ,
                            this._processor.connect(track)
                        ];
                    case 2:
                        processedTrack = _a.sent();
                        if (processorWasEnabled) {
                            this._processor.enable();
                        } else {
                            this._processor.disable();
                        }
                        return [
                            2 /*return*/ ,
                            processedTrack
                        ];
                }
            });
        });
    };
    /**
     * @private
     */ NoiseCancellationImpl.prototype.disablePermanently = function() {
        this._disabledPermanent = true;
        return this.disable();
    };
    /**
     * @private
     */ NoiseCancellationImpl.prototype.stop = function() {
        this._processor.disconnect();
        this._sourceTrack.stop();
    };
    return NoiseCancellationImpl;
}();
exports.NoiseCancellationImpl = NoiseCancellationImpl;
function applyNoiseCancellation(mediaStreamTrack, noiseCancellationOptions, log) {
    return __awaiter(this, void 0, void 0, function() {
        var processor, cleanTrack, noiseCancellation, ex_1;
        return __generator(this, function(_a) {
            switch(_a.label){
                case 0:
                    _a.trys.push([
                        0,
                        2,
                        ,
                        3
                    ]);
                    return [
                        4 /*yield*/ ,
                        noisecancellationadapter_1.createNoiseCancellationAudioProcessor(noiseCancellationOptions, log)
                    ];
                case 1:
                    processor = _a.sent();
                    cleanTrack = processor.connect(mediaStreamTrack);
                    noiseCancellation = new NoiseCancellationImpl(processor, mediaStreamTrack);
                    return [
                        2 /*return*/ ,
                        {
                            cleanTrack: cleanTrack,
                            noiseCancellation: noiseCancellation
                        }
                    ];
                case 2:
                    ex_1 = _a.sent();
                    // in case of failures to load noise cancellation library just return original media stream.
                    log.warn("Failed to create noise cancellation. Returning normal audio track: " + ex_1);
                    return [
                        2 /*return*/ ,
                        {
                            cleanTrack: mediaStreamTrack
                        }
                    ];
                case 3:
                    return [
                        2 /*return*/ 
                    ];
            }
        });
    });
}
exports.applyNoiseCancellation = applyNoiseCancellation; //# sourceMappingURL=noisecancellationimpl.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), buildLogLevels = _a.buildLogLevels, valueToJSON = _a.valueToJSON;
var DEFAULT_LOG_LEVEL = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_LOG_LEVEL;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var nInstances = 0;
/**
 * A {@link Track} represents a stream of audio, video, or data.
 * @extends EventEmitter
 * @property {Track.Kind} kind - The {@link Track}'s kind
 * @property {string} name - The {@link Track}'s name
 */ var Track = function(_super) {
    __extends(Track, _super);
    /**
     * Construct a {@link Track}.
     * @param {Track.ID} id - The {@link Track}'s ID
     * @param {Track.Kind} kind - The {@link Track}'s kind
     * @param {{ log: Log, name: ?string }} options
     */ function Track(id, kind, options) {
        var _this = this;
        options = Object.assign({
            name: id,
            log: null,
            logLevel: DEFAULT_LOG_LEVEL
        }, options);
        _this = _super.call(this) || this;
        var name = String(options.name);
        var logLevels = buildLogLevels(options.logLevel);
        var log = options.log ? options.log.createLog('media', _this) : new Log('media', _this, logLevels, options.loggerName);
        Object.defineProperties(_this, {
            _instanceId: {
                value: ++nInstances
            },
            _log: {
                value: log
            },
            kind: {
                enumerable: true,
                value: kind
            },
            name: {
                enumerable: true,
                value: name
            }
        });
        return _this;
    }
    Track.prototype.toJSON = function() {
        return valueToJSON(this);
    };
    return Track;
}(EventEmitter);
/**
 * The {@link Track} ID is a string identifier for the {@link Track}.
 * @typedef {string} Track.ID
 */ /**
 * The {@link Track} kind is either "audio", "video", or "data".
 * @typedef {string} Track.Kind
 */ /**
 * The {@link Track}'s priority can be "low", "standard", or "high".
 * @typedef {string} Track.Priority
 */ /**
 * The {@link Track} SID is a unique string identifier for the {@link Track}
 * that is published to a {@link Room}.
 * @typedef {string} Track.SID
 */ /**
 * A {@link DataTrack} is a {@link LocalDataTrack} or {@link RemoteDataTrack}.
 * @typedef {LocalDataTrack|RemoteDataTrack} DataTrack
 */ /**
 * A {@link LocalTrack} is a {@link LocalAudioTrack}, {@link LocalVideoTrack},
 * or {@link LocalDataTrack}.
 * @typedef {LocalAudioTrack|LocalVideoTrack|LocalDataTrack} LocalTrack
 */ /**
 * {@link LocalTrack} options
 * @typedef {object} LocalTrackOptions
 * @property {LogLevel|LogLevels} logLevel - Log level for 'media' modules
 * @property {string} [name] - The {@link LocalTrack}'s name; by default,
 *   it is set to the {@link LocalTrack}'s ID.
 */ /**
 * A {@link RemoteTrack} is a {@link RemoteAudioTrack},
 * {@link RemoteVideoTrack}, or {@link RemoteDataTrack}.
 * @typedef {RemoteAudioTrack|RemoteVideoTrack|RemoteDataTrack} RemoteTrack
 */ module.exports = Track; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/mediatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var isIOS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)").isIOS;
var MediaStream = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)").MediaStream;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), waitForEvent = _a.waitForEvent, waitForSometime = _a.waitForSometime;
var localMediaRestartDeferreds = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/localmediarestartdeferreds.js [app-client] (ecmascript)");
var Track = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/index.js [app-client] (ecmascript)");
/**
 * A {@link MediaTrack} represents audio or video that can be sent to or
 * received from a {@link Room}.
 * @extends Track
 * @property {Track.ID} id - This {@link Track}'s ID
 * @property {boolean} isStarted - Whether or not the {@link MediaTrack} has
 *   started
 * @property {boolean} isEnabled - Whether or not the {@link MediaTrack} is
 *   enabled (i.e., whether it is paused or muted)
 * @property {Track.Kind} kind - The kind of the underlying
 *   MediaStreamTrack, "audio" or "video"
 * @property {MediaStreamTrack} mediaStreamTrack - The underlying
 *   MediaStreamTrack
 * @emits MediaTrack#disabled
 * @emits MediaTrack#enabled
 * @emits MediaTrack#started
 */ var MediaTrack = function(_super) {
    __extends(MediaTrack, _super);
    /**
     * Construct a {@link MediaTrack}.
     * @param {MediaTrackTransceiver} mediaTrackTransceiver
     * @param {{log: Log}} options
     */ function MediaTrack(mediaTrackTransceiver, options) {
        var _this = this;
        options = Object.assign({
            playPausedElementsIfNotBackgrounded: isIOS() && typeof document === 'object' && typeof document.addEventListener === 'function' && typeof document.visibilityState === 'string'
        }, options);
        _this = _super.call(this, mediaTrackTransceiver.id, mediaTrackTransceiver.kind, options) || this;
        var isStarted = false;
        options = Object.assign({
            MediaStream: MediaStream
        }, options);
        if (typeof options.MediaStream !== 'function') {
            throw new Error('MediaTrack received an invalid MediaStream constructor: ' + options.MediaStream);
        }
        /* istanbul ignore next */ Object.defineProperties(_this, {
            _attachments: {
                value: new Set()
            },
            _dummyEl: {
                value: null,
                writable: true
            },
            _elShims: {
                value: new WeakMap()
            },
            _isStarted: {
                get: function() {
                    return isStarted;
                },
                set: function(_isStarted) {
                    isStarted = _isStarted;
                }
            },
            _playPausedElementsIfNotBackgrounded: {
                value: options.playPausedElementsIfNotBackgrounded
            },
            _shouldShimAttachedElements: {
                value: options.workaroundWebKitBug212780 || options.playPausedElementsIfNotBackgrounded
            },
            _unprocessedTrack: {
                value: null,
                writable: true
            },
            _MediaStream: {
                value: options.MediaStream
            },
            _mapMediaElement: {
                value: options.mapMediaElement
            },
            _disposeMediaElement: {
                value: options.disposeMediaElement
            },
            isStarted: {
                enumerable: true,
                get: function() {
                    return isStarted;
                }
            },
            mediaStreamTrack: {
                enumerable: true,
                get: function() {
                    return this._unprocessedTrack || mediaTrackTransceiver.track;
                }
            },
            processedTrack: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
        _this._initialize();
        return _this;
    }
    /**
     * @private
     */ MediaTrack.prototype._start = function() {
        this._log.debug('Started');
        this._isStarted = true;
        if (this._dummyEl) {
            this._dummyEl.oncanplay = null;
        }
        // eslint-disable-next-line no-use-before-define
        this.emit('started', this);
    };
    /**
     * @private
     */ MediaTrack.prototype._initialize = function() {
        var self = this;
        this._log.debug('Initializing');
        this._dummyEl = this._createElement();
        if (this.mediaStreamTrack && this.mediaStreamTrack.addEventListener) {
            this.mediaStreamTrack.addEventListener('ended', function onended() {
                self._end();
                self.mediaStreamTrack.removeEventListener('ended', onended);
            });
        } else if (this.mediaStreamTrack) {
            this.mediaStreamTrack.onended = function onended() {
                self._end();
                self.mediaStreamTrack.onended = null;
            };
        }
        if (this._dummyEl) {
            this._dummyEl.muted = true;
            this._dummyEl.oncanplay = this._start.bind(this, this._dummyEl);
            // NOTE(csantos): We always want to attach the original mediaStreamTrack for dummyEl
            this._attach(this._dummyEl, this.mediaStreamTrack);
            this._attachments.delete(this._dummyEl);
        }
    };
    /**
     * @private
     */ MediaTrack.prototype._end = function() {
        this._log.debug('Ended');
        if (this._dummyEl) {
            this._dummyEl.remove();
            if (this._disposeMediaElement) {
                this._disposeMediaElement(this._dummyEl);
            } else {
                this._dummyEl.srcObject = null;
            }
            this._dummyEl.oncanplay = null;
            this._dummyEl = null;
        }
    };
    MediaTrack.prototype.attach = function(el) {
        var _this = this;
        if (typeof el === 'string') {
            el = this._selectElement(el);
        } else if (!el) {
            el = this._createElement();
        }
        this._log.debug('Attempting to attach to element:', el);
        el = this._attach(el);
        if (this._shouldShimAttachedElements && !this._elShims.has(el)) {
            var onUnintentionallyPaused = this._playPausedElementsIfNotBackgrounded ? function() {
                return playIfPausedAndNotBackgrounded(el, _this._log);
            } : null;
            this._elShims.set(el, shimMediaElement(el, onUnintentionallyPaused));
        }
        return el;
    };
    /**
     * Attach the provided MediaStreamTrack to the media element.
     * @param el - The media element to attach to
     * @param mediaStreamTrack - The MediaStreamTrack to attach. If this is
     * not provided, it uses the processedTrack if it exists
     * or it defaults to the current mediaStreamTrack
     * @private
     */ MediaTrack.prototype._attach = function(el, mediaStreamTrack) {
        if (mediaStreamTrack === void 0) {
            mediaStreamTrack = this.processedTrack || this.mediaStreamTrack;
        }
        var mediaStream = el.srcObject;
        if (!(mediaStream instanceof this._MediaStream)) {
            mediaStream = new this._MediaStream();
        }
        var getTracks = mediaStreamTrack.kind === 'audio' ? 'getAudioTracks' : 'getVideoTracks';
        mediaStream[getTracks]().forEach(function(track) {
            mediaStream.removeTrack(track);
        });
        mediaStream.addTrack(mediaStreamTrack);
        // Map the element if a custom mapping function is provided
        if (this._mapMediaElement) {
            this._log.debug('Mapping element using mapMediaElement before attaching media to element');
            this._mapMediaElement(el);
        }
        // NOTE(mpatwardhan): resetting `srcObject` here, causes flicker (JSDK-2641), but it lets us
        // to sidestep the a chrome bug: https://bugs.chromium.org/p/chromium/issues/detail?id=1052353
        el.srcObject = mediaStream;
        el.autoplay = true;
        el.playsInline = true;
        if (!this._attachments.has(el)) {
            this._attachments.add(el);
        }
        return el;
    };
    /**
     * @private
     */ MediaTrack.prototype._selectElement = function(selector) {
        var el = document.querySelector(selector);
        if (!el) {
            throw new Error("Selector matched no element: " + selector);
        }
        return el;
    };
    /**
     * @private
     */ MediaTrack.prototype._updateElementsMediaStreamTrack = function() {
        var _this = this;
        this._log.debug('Reattaching all elements to update mediaStreamTrack');
        this._getAllAttachedElements().forEach(function(el) {
            return _this._attach(el);
        });
    };
    /**
     * @private
     */ MediaTrack.prototype._createElement = function() {
        return typeof document !== 'undefined' ? document.createElement(this.kind) : null;
    };
    MediaTrack.prototype.detach = function(el) {
        var els;
        if (typeof el === 'string') {
            els = [
                this._selectElement(el)
            ];
        } else if (!el) {
            els = this._getAllAttachedElements();
        } else {
            els = [
                el
            ];
        }
        this._log.debug('Attempting to detach from elements:', els);
        this._detachElements(els);
        return el ? els[0] : els;
    };
    /**
     * @private
     */ MediaTrack.prototype._detachElements = function(elements) {
        return elements.map(this._detachElement.bind(this));
    };
    /**
     * @private
     */ MediaTrack.prototype._detachElement = function(el) {
        if (!this._attachments.has(el)) {
            return el;
        }
        var mediaStream = el.srcObject;
        if (mediaStream instanceof this._MediaStream) {
            mediaStream.removeTrack(this.processedTrack || this.mediaStreamTrack);
        }
        if (this._disposeMediaElement) {
            this._log.debug('Disposing element using disposeMediaElement after removing media from element');
            this._disposeMediaElement(el);
        }
        this._attachments.delete(el);
        if (this._shouldShimAttachedElements && this._elShims.has(el)) {
            var shim = this._elShims.get(el);
            shim.unShim();
            this._elShims.delete(el);
        }
        return el;
    };
    /**
     * @private
     */ MediaTrack.prototype._getAllAttachedElements = function() {
        var els = [];
        this._attachments.forEach(function(el) {
            els.push(el);
        });
        return els;
    };
    return MediaTrack;
}(Track);
/**
 * Play an HTMLMediaElement if it is paused and not backgrounded.
 * @private
 * @param {HTMLMediaElement} el
 * @param {Log} log
 * @returns {void}
 */ function playIfPausedAndNotBackgrounded(el, log) {
    var tag = el.tagName.toLowerCase();
    log.warn('Unintentionally paused:', el);
    // NOTE(mmalavalli): When the element is unintentionally paused, we wait one
    // second for the "onvisibilitychange" event on the HTMLDocument to see if the
    // app will be backgrounded. If not, then the element can be safely played.
    Promise.race([
        waitForEvent(document, 'visibilitychange'),
        waitForSometime(1000)
    ]).then(function() {
        if (document.visibilityState === 'visible') {
            // NOTE(mmalavalli): We play the inadvertently paused elements only after
            // the LocalAudioTrack is unmuted to work around WebKit Bug 213853.
            //
            // Bug: https://bugs.webkit.org/show_bug.cgi?id=213853
            //
            localMediaRestartDeferreds.whenResolved('audio').then(function() {
                log.info("Playing unintentionally paused <" + tag + "> element");
                log.debug('Element:', el);
                return el.play();
            }).then(function() {
                log.info("Successfully played unintentionally paused <" + tag + "> element");
                log.debug('Element:', el);
            }).catch(function(error) {
                log.warn("Error while playing unintentionally paused <" + tag + "> element:", {
                    error: error,
                    el: el
                });
            });
        }
    });
}
/**
 * Shim the pause() and play() methods of the given HTMLMediaElement so that
 * we can detect if it was paused unintentionally.
 * @param {HTMLMediaElement} el
 * @param {?function} [onUnintentionallyPaused=null]
 * @returns {{pausedIntentionally: function, unShim: function}}
 */ function shimMediaElement(el, onUnintentionallyPaused) {
    if (onUnintentionallyPaused === void 0) {
        onUnintentionallyPaused = null;
    }
    var origPause = el.pause;
    var origPlay = el.play;
    var pausedIntentionally = false;
    el.pause = function() {
        pausedIntentionally = true;
        return origPause.call(el);
    };
    el.play = function() {
        pausedIntentionally = false;
        return origPlay.call(el);
    };
    var onPause = onUnintentionallyPaused ? function() {
        if (!pausedIntentionally) {
            onUnintentionallyPaused();
        }
    } : null;
    if (onPause) {
        el.addEventListener('pause', onPause);
    }
    return {
        pausedIntentionally: function() {
            return pausedIntentionally;
        },
        unShim: function() {
            el.pause = origPause;
            el.play = origPlay;
            if (onPause) {
                el.removeEventListener('pause', onPause);
            }
        }
    };
}
module.exports = MediaTrack; //# sourceMappingURL=mediatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/audiotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/mediatrack.js [app-client] (ecmascript)");
/**
 * An {@link AudioTrack} is a {@link Track} representing audio.
 * @extends Track
 * @property {boolean} isStarted - Whether or not the {@link AudioTrack} has
 *   started; if the {@link AudioTrack} started, there is enough audio data to
 *   begin playback
 * @property {boolean} isEnabled - Whether or not the {@link AudioTrack} is
 *   enabled; if the {@link AudioTrack} is not enabled, it is "muted"
 * @property {Track.Kind} kind - "audio"
 * @property {MediaStreamTrack} mediaStreamTrack - An audio MediaStreamTrack
 * @property {?MediaStreamTrack} processedTrack - The source of processed audio samples.
 * It is always null as audio processing is not currently supported.
 * @emits AudioTrack#disabled
 * @emits AudioTrack#enabled
 * @emits AudioTrack#started
 */ var AudioTrack = function(_super) {
    __extends(AudioTrack, _super);
    /**
     * Construct an {@link AudioTrack}.
     * @param {MediaTrackTransceiver} mediaTrackTransceiver
     * @param {{log: Log}} options
     */ function AudioTrack(mediaTrackTransceiver, options) {
        return _super.call(this, mediaTrackTransceiver, options) || this;
    }
    /**
     * Create an HTMLAudioElement and attach the {@link AudioTrack} to it.
     *
     * The HTMLAudioElement's <code>srcObject</code> will be set to a new
     * MediaStream containing the {@link AudioTrack}'s MediaStreamTrack.
     *
     * @returns {HTMLAudioElement} audioElement
     * @example
     * const Video = require('twilio-video');
     *
     * Video.createLocalAudioTrack().then(function(audioTrack) {
     *   const audioElement = audioTrack.attach();
     *   document.body.appendChild(audioElement);
     * });
    */ /**
     * Attach the {@link AudioTrack} to an existing HTMLMediaElement. The
     * HTMLMediaElement could be an HTMLAudioElement or an HTMLVideoElement.
     *
     * If the HTMLMediaElement's <code>srcObject</code> is not set to a MediaStream,
     * this method sets it to a new MediaStream containing the {@link AudioTrack}'s
     * MediaStreamTrack; otherwise, it adds the {@link MediaTrack}'s
     * MediaStreamTrack to the existing MediaStream. Finally, if there are any other
     * MediaStreamTracks of the same kind on the MediaStream, this method removes
     * them.
     *
     * @param {HTMLMediaElement} mediaElement - The HTMLMediaElement to attach to
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const Video = require('twilio-video');
     *
     * const videoElement = document.createElement('video');
     * document.body.appendChild(videoElement);
     *
     * Video.createLocalAudioTrack().then(function(audioTrack) {
     *   audioTrack.attach(videoElement);
     * });
    */ /**
     * Attach the {@link AudioTrack} to an HTMLMediaElement selected by
     * <code>document.querySelector</code>. The HTMLMediaElement could be an
     * HTMLAudioElement or an HTMLVideoElement.
     *
     * If the HTMLMediaElement's <code>srcObject</code> is not set to a MediaStream,
     * this method sets it to a new MediaStream containing the {@link AudioTrack}'s
     * MediaStreamTrack; otherwise, it adds the {@link AudioTrack}'s
     * MediaStreamTrack to the existing MediaStream. Finally, if there are any other
     * MediaStreamTracks of the same kind on the MediaStream, this method removes
     * them.
     *
     * @param {string} selector - A query selector for the HTMLMediaElement to
     *   attach to
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const Video = require('twilio-video');
     *
     * const videoElement = document.createElement('video');
     * videoElement.id = 'my-video-element';
     * document.body.appendChild(videoElement);
     *
     * Video.createLocalAudioTrack().then(function(track) {
     *   track.attach('#my-video-element');
     * });
     */ AudioTrack.prototype.attach = function() {
        return _super.prototype.attach.apply(this, arguments);
    };
    /**
     * Detach the {@link AudioTrack} from all previously attached HTMLMediaElements.
     * @returns {Array<HTMLMediaElement>} mediaElements
     * @example
     * const mediaElements = audioTrack.detach();
     * mediaElements.forEach(mediaElement => mediaElement.remove());
    */ /**
     * Detach the {@link AudioTrack} from a previously attached HTMLMediaElement.
     * @param {HTMLMediaElement} mediaElement - One of the HTMLMediaElements to
     *   which the {@link AudioTrack} is attached
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const videoElement = document.getElementById('my-video-element');
     * audioTrack.detach(videoElement).remove();
    */ /**
     * Detach the {@link AudioTrack} from a previously attached HTMLMediaElement
     *   specified by <code>document.querySelector</code>.
     * @param {string} selector - The query selector of HTMLMediaElement to which
     *    the {@link AudioTrack} is attached
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * audioTrack.detach('#my-video-element').remove();
     */ AudioTrack.prototype.detach = function() {
        return _super.prototype.detach.apply(this, arguments);
    };
    return AudioTrack;
}(MediaTrack);
/**
 * The {@link AudioTrack} was disabled, i.e. "muted".
 * @param {AudioTrack} track - The {@link AudioTrack} that was disabled
 * @event AudioTrack#disabled
 */ /**
 * The {@link AudioTrack} was enabled, i.e. "unmuted".
 * @param {AudioTrack} track - The {@link AudioTrack} that was enabled
 * @event AudioTrack#enabled
 */ /**
 * The {@link AudioTrack} started. This means there is enough audio data to
 * begin playback.
 * @param {AudioTrack} track - The {@link AudioTrack} that started
 * @event AudioTrack#started
 */ module.exports = AudioTrack; //# sourceMappingURL=audiotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/transceiver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/transceiver.js [app-client] (ecmascript)");
/**
 * A {@link MediaTrackTransceiver} represents either one or more local
 * RTCRtpSenders, or a single RTCRtpReceiver.
 * @extends TrackTransceiver
 * @property {MediaStreamTrack} track
 */ var MediaTrackTransceiver = function(_super) {
    __extends(MediaTrackTransceiver, _super);
    /**
     * Construct a {@link MediaTrackTransceiver}.
     * @param {Track.ID} id - The MediaStreamTrack ID signaled through RSP/SDP
     * @param {MediaStreamTrack} mediaStreamTrack
     */ function MediaTrackTransceiver(id, mediaStreamTrack) {
        var _this = _super.call(this, id, mediaStreamTrack.kind) || this;
        Object.defineProperties(_this, {
            _track: {
                value: mediaStreamTrack,
                writable: true
            },
            enabled: {
                enumerable: true,
                get: function() {
                    return this._track.enabled;
                }
            },
            readyState: {
                enumerable: true,
                get: function() {
                    return this._track.readyState;
                }
            },
            track: {
                enumerable: true,
                get: function() {
                    return this._track;
                }
            }
        });
        return _this;
    }
    MediaTrackTransceiver.prototype.stop = function() {
        this.track.stop();
        _super.prototype.stop.call(this);
    };
    return MediaTrackTransceiver;
}(TrackTransceiver);
module.exports = MediaTrackTransceiver; //# sourceMappingURL=transceiver.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/sender.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var MediaTrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/transceiver.js [app-client] (ecmascript)");
/**
 * A {@link MediaTrackSender} represents one or more local RTCRtpSenders.
 * @extends MediaTrackTransceiver
 * @emits MediaTrackSender#replaced
 */ var MediaTrackSender = function(_super) {
    __extends(MediaTrackSender, _super);
    /**
     * Construct a {@link MediaTrackSender}.
     * @param {MediaStreamTrack} mediaStreamTrack
     */ function MediaTrackSender(mediaStreamTrack) {
        var _this = _super.call(this, mediaStreamTrack.id, mediaStreamTrack) || this;
        Object.defineProperties(_this, {
            _clones: {
                value: new Set()
            },
            _eventsToReemitters: {
                value: new Map([
                    [
                        'mute',
                        function() {
                            return _this.queue('muted');
                        }
                    ],
                    [
                        'unmute',
                        function() {
                            return _this.queue('unmuted');
                        }
                    ]
                ])
            },
            _senders: {
                value: new Set()
            },
            _senderToPublisherHintCallbacks: {
                value: new Map()
            },
            isPublishing: {
                enumerable: true,
                get: function() {
                    return !!this._clones.size;
                }
            },
            muted: {
                enumerable: true,
                get: function() {
                    return this._track.muted;
                }
            }
        });
        _this._reemitMediaStreamTrackEvents();
        return _this;
    }
    /**
     * @private
     */ MediaTrackSender.prototype._reemitMediaStreamTrackEvents = function(mediaStreamTrack) {
        if (mediaStreamTrack === void 0) {
            mediaStreamTrack = this._track;
        }
        var _a = this, eventsToReemitters = _a._eventsToReemitters, track = _a._track;
        if (mediaStreamTrack.addEventListener) {
            eventsToReemitters.forEach(function(reemitter, event) {
                return mediaStreamTrack.addEventListener(event, reemitter);
            });
        } else {
            eventsToReemitters.forEach(function(reemitter, event) {
                mediaStreamTrack["on" + event] = reemitter;
            });
        }
        if (track !== mediaStreamTrack) {
            eventsToReemitters.forEach(function(reemitter, event) {
                return track.removeEventListener(event, reemitter);
            });
            if (track.muted !== mediaStreamTrack.muted) {
                var reemitter = eventsToReemitters.get(mediaStreamTrack.muted ? 'mute' : 'unmute');
                reemitter();
            }
        }
    };
    /**
     * Return a new {@link MediaTrackSender} containing a clone of the underlying
     * MediaStreamTrack. No RTCRtpSenders are copied.
     * @returns {MediaTrackSender}
     */ MediaTrackSender.prototype.clone = function() {
        var clone = new MediaTrackSender(this.track.clone());
        this._clones.add(clone);
        return clone;
    };
    /**
     * Remove a cloned {@link MediaTrackSender}.
     * @returns {void}
     */ MediaTrackSender.prototype.removeClone = function(clone) {
        this._clones.delete(clone);
    };
    /**
     * Set the given MediaStreamTrack.
     * @param {MediaStreamTrack} mediaStreamTrack
     * @returns {Promise<void>}
     */ MediaTrackSender.prototype.setMediaStreamTrack = function(mediaStreamTrack) {
        var _this = this;
        var clones = Array.from(this._clones);
        var senders = Array.from(this._senders);
        return Promise.all(clones.map(function(clone) {
            return clone.setMediaStreamTrack(mediaStreamTrack.clone());
        }).concat(senders.map(function(sender) {
            return _this._replaceTrack(sender, mediaStreamTrack);
        }))).finally(function() {
            _this._reemitMediaStreamTrackEvents(mediaStreamTrack);
            _this._track = mediaStreamTrack;
        });
    };
    /**
     * Add an RTCRtpSender.
     * @param {RTCRtpSender} sender
     * @param {?()=>Promise<string>} publisherHintCallback
     * @returns {this}
     */ MediaTrackSender.prototype.addSender = function(sender, publisherHintCallback) {
        this._senders.add(sender);
        if (publisherHintCallback) {
            this._senderToPublisherHintCallbacks.set(sender, publisherHintCallback);
        }
        return this;
    };
    /**
     * Remove an RTCRtpSender.
     * @param {RTCRtpSender} sender
     * @returns {this}
     */ MediaTrackSender.prototype.removeSender = function(sender) {
        this._senders.delete(sender);
        this._senderToPublisherHintCallbacks.delete(sender);
        return this;
    };
    /**
     * Applies given encodings, or resets encodings if none specified.
     * @param {Array<{enabled: boolean, layer_index: number}>|null} encodings
     * @returns {Promise<string>}
     */ MediaTrackSender.prototype.setPublisherHint = function(encodings) {
        // Note(mpatwardhan): since publisher hint applies only to group rooms we only look at 1st call callback.
        var _a = __read(Array.from(this._senderToPublisherHintCallbacks.values()), 1), publisherHintCallback = _a[0];
        return publisherHintCallback ? publisherHintCallback(encodings) : Promise.resolve('COULD_NOT_APPLY_HINT');
    };
    MediaTrackSender.prototype._replaceTrack = function(sender, mediaStreamTrack) {
        var _this = this;
        return sender.replaceTrack(mediaStreamTrack).then(function(replaceTrackResult) {
            // clear any publisherHints and apply default encodings.
            _this.setPublisherHint(null).catch(function() {});
            _this.emit('replaced');
            return replaceTrackResult;
        });
    };
    return MediaTrackSender;
}(MediaTrackTransceiver);
/**
 * The {@link MediaTrackSender}'s underlying MediaStreamTrack was muted.
 * @event MediaTrackSender#muted
 */ /**
 * The {@link MediaTrackSender} replaced the underlying MediaStreamTrack.
 * @event MediaTrackSender#replaced
 */ /**
 * The {@link MediaTrackSender}'s underlying MediaStreamTrack was unmuted.
 * @event MediaTrackSender#unmuted
 */ module.exports = MediaTrackSender; //# sourceMappingURL=sender.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localmediatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint new-cap:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var getUserMedia = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)").getUserMedia;
var isIOS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)").isIOS;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), capitalize = _a.capitalize, defer = _a.defer, waitForSometime = _a.waitForSometime, waitForEvent = _a.waitForEvent;
var ILLEGAL_INVOKE = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").typeErrors.ILLEGAL_INVOKE;
var detectSilentAudio = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/detectsilentaudio.js [app-client] (ecmascript)");
var detectSilentVideo = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/detectsilentvideo.js [app-client] (ecmascript)");
var documentVisibilityMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/documentvisibilitymonitor.js [app-client] (ecmascript)");
var localMediaRestartDeferreds = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/localmediarestartdeferreds.js [app-client] (ecmascript)");
var gUMSilentTrackWorkaround = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/workaround180748.js [app-client] (ecmascript)");
var MediaTrackSender = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/sender.js [app-client] (ecmascript)");
function mixinLocalMediaTrack(AudioOrVideoTrack) {
    /**
     * A {@link LocalMediaTrack} represents audio or video that your
     * {@link LocalParticipant} is sending to a {@link Room}. As such, it can be
     * enabled and disabled with {@link LocalMediaTrack#enable} and
     * {@link LocalMediaTrack#disable} or stopped completely with
     * {@link LocalMediaTrack#stop}.
     * @emits LocalMediaTrack#muted
     * @emits LocalMediaTrack#stopped
     * @emits LocalMediaTrack#unmuted
     */ return function(_super) {
        __extends(LocalMediaTrack, _super);
        /**
         * Construct a {@link LocalMediaTrack} from a MediaStreamTrack.
         * @param {MediaStreamTrack} mediaStreamTrack - The underlying MediaStreamTrack
         * @param {LocalTrackOptions} [options] - {@link LocalTrack} options
         */ function LocalMediaTrack(mediaStreamTrack, options) {
            var _this = this;
            var workaroundWebKitBug1208516 = isIOS() && typeof document === 'object' && typeof document.addEventListener === 'function' && typeof document.visibilityState === 'string';
            options = Object.assign({
                getUserMedia: getUserMedia,
                isCreatedByCreateLocalTracks: false,
                workaroundWebKitBug1208516: workaroundWebKitBug1208516,
                gUMSilentTrackWorkaround: gUMSilentTrackWorkaround
            }, options);
            var mediaTrackSender = new MediaTrackSender(mediaStreamTrack);
            var kind = mediaTrackSender.kind;
            _this = _super.call(this, mediaTrackSender, options) || this;
            Object.defineProperties(_this, {
                _constraints: {
                    value: typeof options[kind] === 'object' ? options[kind] : {},
                    writable: true
                },
                _getUserMedia: {
                    value: options.getUserMedia
                },
                _gUMSilentTrackWorkaround: {
                    value: options.gUMSilentTrackWorkaround
                },
                _eventsToReemitters: {
                    value: new Map([
                        [
                            'muted',
                            function() {
                                return _this.emit('muted', _this);
                            }
                        ],
                        [
                            'unmuted',
                            function() {
                                return _this.emit('unmuted', _this);
                            }
                        ]
                    ])
                },
                _workaroundWebKitBug1208516: {
                    value: options.workaroundWebKitBug1208516
                },
                _workaroundWebKitBug1208516Cleanup: {
                    value: null,
                    writable: true
                },
                _didCallEnd: {
                    value: false,
                    writable: true
                },
                _isCreatedByCreateLocalTracks: {
                    value: options.isCreatedByCreateLocalTracks
                },
                _noiseCancellation: {
                    value: options.noiseCancellation || null
                },
                _trackSender: {
                    value: mediaTrackSender
                },
                id: {
                    enumerable: true,
                    value: mediaTrackSender.id
                },
                isEnabled: {
                    enumerable: true,
                    get: function() {
                        return mediaTrackSender.enabled;
                    }
                },
                isMuted: {
                    enumerable: true,
                    get: function() {
                        return mediaTrackSender.muted;
                    }
                },
                isStopped: {
                    enumerable: true,
                    get: function() {
                        return mediaTrackSender.readyState === 'ended';
                    }
                }
            });
            // NOTE(mpatwardhan): As a workaround for WebKit bug: https://bugs.webkit.org/show_bug.cgi?id=208516,
            // upon foregrounding, re-acquire new MediaStreamTrack if the existing one is ended or muted.
            if (_this._workaroundWebKitBug1208516) {
                _this._workaroundWebKitBug1208516Cleanup = restartWhenInadvertentlyStopped(_this);
            }
            _this._reemitTrackSenderEvents();
            return _this;
        }
        /**
         * @private
         */ LocalMediaTrack.prototype._end = function() {
            var _this = this;
            if (this._didCallEnd) {
                return;
            }
            _super.prototype._end.call(this);
            this._didCallEnd = true;
            this._eventsToReemitters.forEach(function(reemitter, event) {
                return _this._trackSender.removeListener(event, reemitter);
            });
            this.emit('stopped', this);
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._initialize = function() {
            if (this._didCallEnd) {
                this._didCallEnd = false;
            }
            if (this._eventsToReemitters) {
                this._reemitTrackSenderEvents();
            }
            _super.prototype._initialize.call(this);
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._reacquireTrack = function(constraints) {
            var _a;
            var _b = this, getUserMedia = _b._getUserMedia, gUMSilentTrackWorkaround = _b._gUMSilentTrackWorkaround, log = _b._log, kind = _b.mediaStreamTrack.kind;
            log.info('Re-acquiring the MediaStreamTrack');
            log.debug('Constraints:', constraints);
            var gUMConstraints = Object.assign({
                audio: false,
                video: false
            }, (_a = {}, _a[kind] = constraints, _a));
            var gUMPromise = this._workaroundWebKitBug1208516Cleanup ? gUMSilentTrackWorkaround(log, getUserMedia, gUMConstraints) : getUserMedia(gUMConstraints);
            return gUMPromise.then(function(mediaStream) {
                return mediaStream.getTracks()[0];
            });
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._reemitTrackSenderEvents = function() {
            var _this = this;
            this._eventsToReemitters.forEach(function(reemitter, event) {
                return _this._trackSender.on(event, reemitter);
            });
            this._trackSender.dequeue('muted');
            this._trackSender.dequeue('unmuted');
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._restart = function(constraints) {
            var _this = this;
            var log = this._log;
            constraints = constraints || this._constraints;
            // NOTE(mmalavalli): If we try and restart a silent MediaStreamTrack
            // without stopping it first, then a NotReadableError is raised in case of
            // video, or the restarted audio will still be silent. Hence, we stop the
            // MediaStreamTrack here.
            this._stop();
            return this._reacquireTrack(constraints).catch(function(error) {
                log.error('Failed to re-acquire the MediaStreamTrack:', {
                    error: error,
                    constraints: constraints
                });
                throw error;
            }).then(function(newMediaStreamTrack) {
                log.info('Re-acquired the MediaStreamTrack');
                log.debug('MediaStreamTrack:', newMediaStreamTrack);
                _this._constraints = Object.assign({}, constraints);
                return _this._setMediaStreamTrack(newMediaStreamTrack);
            });
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._setMediaStreamTrack = function(mediaStreamTrack) {
            var _this = this;
            // NOTE(mpatwardhan): Preserve the value of the "enabled" flag.
            mediaStreamTrack.enabled = this.mediaStreamTrack.enabled;
            // NOTE(mmalavalli): Stop the current MediaStreamTrack. If not already
            // stopped, this should fire a "stopped" event.
            this._stop();
            // NOTE(csantos): If there's an unprocessedTrack, this means RTCRtpSender has
            // the processedTrack already set, we don't want to replace that.
            return (this._unprocessedTrack ? Promise.resolve().then(function() {
                _this._unprocessedTrack = mediaStreamTrack;
            }) : this._trackSender.setMediaStreamTrack(mediaStreamTrack).catch(function(error) {
                _this._log.warn('setMediaStreamTrack failed:', {
                    error: error,
                    mediaStreamTrack: mediaStreamTrack
                });
            })).then(function() {
                _this._initialize();
                _this._getAllAttachedElements().forEach(function(el) {
                    return _this._attach(el);
                });
            });
        };
        /**
         * @private
         */ LocalMediaTrack.prototype._stop = function() {
            this.mediaStreamTrack.stop();
            this._end();
            return this;
        };
        LocalMediaTrack.prototype.enable = function(enabled) {
            enabled = typeof enabled === 'boolean' ? enabled : true;
            if (enabled !== this.mediaStreamTrack.enabled) {
                this._log.info((enabled ? 'En' : 'Dis') + "abling");
                this.mediaStreamTrack.enabled = enabled;
                this.emit(enabled ? 'enabled' : 'disabled', this);
            }
            return this;
        };
        LocalMediaTrack.prototype.disable = function() {
            return this.enable(false);
        };
        LocalMediaTrack.prototype.restart = function(constraints) {
            var _this = this;
            var kind = this.kind;
            if (!this._isCreatedByCreateLocalTracks) {
                return Promise.reject(ILLEGAL_INVOKE('restart', 'can only be called on a' + (" Local" + capitalize(kind) + "Track that is created using createLocalTracks") + (" or createLocal" + capitalize(kind) + "Track.")));
            }
            if (this._workaroundWebKitBug1208516Cleanup) {
                this._workaroundWebKitBug1208516Cleanup();
                this._workaroundWebKitBug1208516Cleanup = null;
            }
            var promise = this._restart(constraints);
            if (this._workaroundWebKitBug1208516) {
                promise = promise.finally(function() {
                    _this._workaroundWebKitBug1208516Cleanup = restartWhenInadvertentlyStopped(_this);
                });
            }
            return promise;
        };
        LocalMediaTrack.prototype.stop = function() {
            this._log.info('Stopping');
            if (this._workaroundWebKitBug1208516Cleanup) {
                this._workaroundWebKitBug1208516Cleanup();
                this._workaroundWebKitBug1208516Cleanup = null;
            }
            return this._stop();
        };
        return LocalMediaTrack;
    }(AudioOrVideoTrack);
}
/**
 * Restart the given {@link LocalMediaTrack} if it has been inadvertently stopped.
 * @private
 * @param {LocalAudioTrack|LocalVideoTrack} localMediaTrack
 * @returns {function} Clean up listeners attached by the workaround
 */ function restartWhenInadvertentlyStopped(localMediaTrack) {
    var log = localMediaTrack._log, kind = localMediaTrack.kind, noiseCancellation = localMediaTrack._noiseCancellation;
    var detectSilence = {
        audio: detectSilentAudio,
        video: detectSilentVideo
    }[kind];
    var getSourceMediaStreamTrack = function() {
        return noiseCancellation ? noiseCancellation.sourceTrack : localMediaTrack.mediaStreamTrack;
    };
    var el = localMediaTrack._dummyEl;
    var mediaStreamTrack = getSourceMediaStreamTrack();
    var trackChangeInProgress = null;
    function checkSilence() {
        // The dummy element is paused, so play it and then detect silence.
        return el.play().then(function() {
            return detectSilence(el);
        }).then(function(isSilent) {
            if (isSilent) {
                log.warn('Silence detected');
            } else {
                log.info('Non-silence detected');
            }
            return isSilent;
        }).catch(function(error) {
            log.warn('Failed to detect silence:', error);
        }).finally(function() {
            // Pause the dummy element again, if there is no processed track.
            if (!localMediaTrack.processedTrack) {
                el.pause();
            }
        });
    }
    function shouldReacquireTrack() {
        var _workaroundWebKitBug1208516Cleanup = localMediaTrack._workaroundWebKitBug1208516Cleanup, isStopped = localMediaTrack.isStopped;
        var isInadvertentlyStopped = isStopped && !!_workaroundWebKitBug1208516Cleanup;
        var muted = getSourceMediaStreamTrack().muted;
        // NOTE(mmalavalli): Restart the LocalMediaTrack if:
        // 1. The app is foregrounded, and
        // 2. A restart is not already in progress, and
        // 3. The LocalMediaTrack is either muted, inadvertently stopped or silent
        return Promise.resolve().then(function() {
            return document.visibilityState === 'visible' && !trackChangeInProgress && (muted || isInadvertentlyStopped || checkSilence());
        });
    }
    function maybeRestart() {
        return Promise.race([
            waitForEvent(mediaStreamTrack, 'unmute'),
            waitForSometime(50)
        ]).then(function() {
            return shouldReacquireTrack();
        }).then(function(shouldReacquire) {
            if (shouldReacquire && !trackChangeInProgress) {
                trackChangeInProgress = defer();
                localMediaTrack._restart().finally(function() {
                    el = localMediaTrack._dummyEl;
                    removeMediaStreamTrackListeners();
                    mediaStreamTrack = getSourceMediaStreamTrack();
                    addMediaStreamTrackListeners();
                    trackChangeInProgress.resolve();
                    trackChangeInProgress = null;
                }).catch(function(error) {
                    log.error('failed to restart track: ', error);
                });
            }
            // NOTE(mmalavalli): If the MediaStreamTrack ends before the DOM is visible,
            // then this makes sure that visibility callback for phase 2 is called only
            // after the MediaStreamTrack is re-acquired.
            var promise = trackChangeInProgress && trackChangeInProgress.promise || Promise.resolve();
            return promise.finally(function() {
                return localMediaRestartDeferreds.resolveDeferred(kind);
            });
        }).catch(function(ex) {
            log.error("error in maybeRestart: " + ex.message);
        });
    }
    function onMute() {
        var log = localMediaTrack._log, kind = localMediaTrack.kind;
        log.info('Muted');
        log.debug('LocalMediaTrack:', localMediaTrack);
        // NOTE(mmalavalli): When a LocalMediaTrack is muted without the app being
        // backgrounded, and the inadvertently paused elements are played before it
        // is restarted, it never gets unmuted due to the WebKit Bug 213853. Hence,
        // setting this Deferred will make sure that the inadvertently paused elements
        // are played only after the LocalMediaTrack is unmuted.
        //
        // Bug: https://bugs.webkit.org/show_bug.cgi?id=213853
        //
        localMediaRestartDeferreds.startDeferred(kind);
    }
    function addMediaStreamTrackListeners() {
        if (mediaStreamTrack.addEventListener) {
            mediaStreamTrack.addEventListener('ended', maybeRestart);
            mediaStreamTrack.addEventListener('mute', onMute);
            mediaStreamTrack.addEventListener('unmute', maybeRestart);
        } else {
            mediaStreamTrack.onended = maybeRestart;
            mediaStreamTrack.onmute = onMute;
            mediaStreamTrack.onunmute = maybeRestart;
        }
    }
    function removeMediaStreamTrackListeners() {
        if (mediaStreamTrack.removeEventListener) {
            mediaStreamTrack.removeEventListener('ended', maybeRestart);
            mediaStreamTrack.removeEventListener('mute', onMute);
            mediaStreamTrack.removeEventListener('unmute', maybeRestart);
        } else {
            mediaStreamTrack.onended = null;
            mediaStreamTrack.onmute = null;
            mediaStreamTrack.onunmute = null;
        }
    }
    // NOTE(mpatwardhan): listen for document visibility callback on phase 1.
    // this ensures that we acquire media tracks before RemoteMediaTrack
    // tries to `play` them (in phase 2). This order is important because
    // play can fail on safari if audio is not being captured.
    var onVisibilityChange = function(isVisible) {
        return isVisible ? maybeRestart() : false;
    };
    documentVisibilityMonitor.onVisibilityChange(1, onVisibilityChange);
    addMediaStreamTrackListeners();
    return function() {
        documentVisibilityMonitor.offVisibilityChange(1, onVisibilityChange);
        removeMediaStreamTrackListeners();
    };
}
module.exports = mixinLocalMediaTrack; //# sourceMappingURL=localmediatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localaudiotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var isIOS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)").isIOS;
var detectSilentAudio = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/detectsilentaudio.js [app-client] (ecmascript)");
var isIOSChrome = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").isIOSChrome;
var AudioTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/audiotrack.js [app-client] (ecmascript)");
var mixinLocalMediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localmediatrack.js [app-client] (ecmascript)");
var LocalMediaAudioTrack = mixinLocalMediaTrack(AudioTrack);
/**
 * A {@link LocalAudioTrack} is an {@link AudioTrack} representing audio that
 * your {@link LocalParticipant} can publish to a {@link Room}. It can be
 * enabled and disabled with {@link LocalAudioTrack#enable} and
 * {@link LocalAudioTrack#disable} or stopped completely with
 * {@link LocalAudioTrack#stop}.
 * @extends AudioTrack
 * @property {Track.ID} id - The {@link LocalAudioTrack}'s ID
 * @property {boolean} isMuted - Whether or not the audio source has stopped sending samples to the
 *   {@link LocalAudioTrack}; This can happen when the microphone is taken over by another application,
 *   mainly on mobile devices; When this property toggles, then <code>muted</code> and <code>unmuted</code>
 *   events are fired appropriately
 * @property {boolean} isStopped - Whether or not the {@link LocalAudioTrack} is
 *   stopped
 * @property {NoiseCancellation?} noiseCancellation - When a LocalAudioTrack is created
 *   with {@link NoiseCancellationOptions}, this property provides interface
 *   to enable or disable the noise cancellation at runtime.
 * @emits LocalAudioTrack#disabled
 * @emits LocalAudioTrack#enabled
 * @emits LocalAudioTrack#muted
 * @emits LocalAudioTrack#started
 * @emits LocalAudioTrack#stopped
 * @emits LocalAudioTrack#unmuted
 */ var LocalAudioTrack = function(_super) {
    __extends(LocalAudioTrack, _super);
    /**
     * Construct a {@link LocalAudioTrack} from a MediaStreamTrack.
     * @param {MediaStreamTrack} mediaStreamTrack - An audio MediaStreamTrack
     * @param {LocalTrackOptions} [options] - {@link LocalTrack} options
     */ function LocalAudioTrack(mediaStreamTrack, options) {
        var _this = this;
        var noiseCancellation = (options === null || options === void 0 ? void 0 : options.noiseCancellation) || null;
        _this = _super.call(this, mediaStreamTrack, options) || this;
        var log = _this._log;
        var _a = mediaStreamTrack.label, defaultDeviceLabel = _a === void 0 ? '' : _a;
        var _b = mediaStreamTrack.getSettings(), _c = _b.deviceId, defaultDeviceId = _c === void 0 ? '' : _c, _d = _b.groupId, defaultGroupId = _d === void 0 ? '' : _d;
        Object.defineProperties(_this, {
            _currentDefaultDeviceInfo: {
                value: {
                    deviceId: defaultDeviceId,
                    groupId: defaultGroupId,
                    label: defaultDeviceLabel
                },
                writable: true
            },
            _enumerateDevices: {
                value: typeof (options === null || options === void 0 ? void 0 : options.enumerateDevices) === 'function' ? options.enumerateDevices : navigator.mediaDevices.enumerateDevices
            },
            _defaultDeviceCaptureMode: {
                value: (!isIOS() || !!noiseCancellation) && _this._isCreatedByCreateLocalTracks && typeof navigator === 'object' && typeof navigator.mediaDevices === 'object' && typeof navigator.mediaDevices.addEventListener === 'function' && (typeof (options === null || options === void 0 ? void 0 : options.enumerateDevices) === 'function' || typeof navigator.mediaDevices.enumerateDevices === 'function') ? (options === null || options === void 0 ? void 0 : options.defaultDeviceCaptureMode) || 'auto' : 'manual'
            },
            _onDeviceChange: {
                value: function() {
                    _this._enumerateDevices().then(function(deviceInfos) {
                        // NOTE(mmalavalli): In Chrome, when the default device changes, and we restart the LocalAudioTrack with
                        // device ID "default", it will not switch to the new default device unless all LocalAudioTracks capturing
                        // from the old default device are stopped. So, we restart the LocalAudioTrack with the actual device ID of
                        // the new default device instead.
                        var defaultDeviceInfo = deviceInfos.find(function(_a) {
                            var deviceId = _a.deviceId, kind = _a.kind;
                            return kind === 'audioinput' && deviceId !== 'default';
                        });
                        if (defaultDeviceInfo && [
                            'deviceId',
                            'groupId'
                        ].some(function(prop) {
                            return defaultDeviceInfo[prop] !== _this._currentDefaultDeviceInfo[prop];
                        })) {
                            log.info('Default device changed, restarting the LocalAudioTrack');
                            log.debug("Old default device: \"" + _this._currentDefaultDeviceInfo.deviceId + "\" => \"" + _this._currentDefaultDeviceInfo.label + "\"");
                            log.debug("New default device: \"" + defaultDeviceInfo.deviceId + "\" => \"" + defaultDeviceInfo.label + "\"");
                            _this._currentDefaultDeviceInfo = defaultDeviceInfo;
                            _this._restartDefaultDevice().catch(function(error) {
                                return log.warn("Failed to restart: " + error.message);
                            });
                        }
                    }, function(error) {
                        log.warn("Failed to run enumerateDevices(): " + error.message);
                    });
                }
            },
            _restartOnDefaultDeviceChangeCleanup: {
                value: null,
                writable: true
            },
            noiseCancellation: {
                enumerable: true,
                value: noiseCancellation,
                writable: false
            }
        });
        log.debug('defaultDeviceCaptureMode:', _this._defaultDeviceCaptureMode);
        _this._maybeRestartOnDefaultDeviceChange();
        return _this;
    }
    LocalAudioTrack.prototype.toString = function() {
        return "[LocalAudioTrack #" + this._instanceId + ": " + this.id + "]";
    };
    LocalAudioTrack.prototype.attach = function(el) {
        el = _super.prototype.attach.call(this, el);
        el.muted = true;
        return el;
    };
    /**
     * @private
     */ LocalAudioTrack.prototype._end = function() {
        return _super.prototype._end.apply(this, arguments);
    };
    /**
     * @private
     */ LocalAudioTrack.prototype._maybeRestartOnDefaultDeviceChange = function() {
        var _this = this;
        var _a = this, constraints = _a._constraints, defaultDeviceCaptureMode = _a._defaultDeviceCaptureMode, log = _a._log;
        var mediaStreamTrack = this.noiseCancellation ? this.noiseCancellation.sourceTrack : this.mediaStreamTrack;
        var deviceId = mediaStreamTrack.getSettings().deviceId;
        var isNotEqualToCapturedDeviceIdOrEqualToDefault = function(requestedDeviceId) {
            return requestedDeviceId !== deviceId || requestedDeviceId === 'default';
        };
        var isCapturingFromDefaultDevice = function checkIfCapturingFromDefaultDevice(deviceIdConstraint) {
            if (deviceIdConstraint === void 0) {
                deviceIdConstraint = {};
            }
            if (typeof deviceIdConstraint === 'string') {
                return isNotEqualToCapturedDeviceIdOrEqualToDefault(deviceIdConstraint);
            } else if (Array.isArray(deviceIdConstraint)) {
                return deviceIdConstraint.every(isNotEqualToCapturedDeviceIdOrEqualToDefault);
            } else if (deviceIdConstraint.exact) {
                return checkIfCapturingFromDefaultDevice(deviceIdConstraint.exact);
            } else if (deviceIdConstraint.ideal) {
                return checkIfCapturingFromDefaultDevice(deviceIdConstraint.ideal);
            }
            return true;
        }(constraints.deviceId);
        if (defaultDeviceCaptureMode === 'auto' && isCapturingFromDefaultDevice) {
            if (!this._restartOnDefaultDeviceChangeCleanup) {
                log.info('LocalAudioTrack will be restarted if the default device changes');
                navigator.mediaDevices.addEventListener('devicechange', this._onDeviceChange);
                this._restartOnDefaultDeviceChangeCleanup = function() {
                    log.info('Cleaning up the listener to restart the LocalAudioTrack if the default device changes');
                    navigator.mediaDevices.removeEventListener('devicechange', _this._onDeviceChange);
                    _this._restartOnDefaultDeviceChangeCleanup = null;
                };
            }
        } else {
            log.info('LocalAudioTrack will NOT be restarted if the default device changes');
            if (this._restartOnDefaultDeviceChangeCleanup) {
                this._restartOnDefaultDeviceChangeCleanup();
            }
        }
    };
    /**
     * @private
     */ LocalAudioTrack.prototype._reacquireTrack = function(constraints) {
        var _this = this;
        this._log.debug('_reacquireTrack: ', constraints);
        if (this.noiseCancellation) {
            return this.noiseCancellation.reacquireTrack(function() {
                return _super.prototype._reacquireTrack.call(_this, constraints);
            });
        }
        return _super.prototype._reacquireTrack.call(this, constraints);
    };
    /**
     * @private
     */ LocalAudioTrack.prototype._restartDefaultDevice = function() {
        var _this = this;
        var constraints = Object.assign({}, this._constraints);
        var restartConstraints = Object.assign({}, constraints, {
            deviceId: this._currentDefaultDeviceInfo.deviceId
        });
        return this.restart(restartConstraints).then(function() {
            // NOTE(mmalavalli): Since we used the new default device's ID while restarting the LocalAudioTrack,
            // we reset the constraints to the original constraints so that the default device detection logic in
            // _maybeRestartOnDefaultDeviceChange() still works.
            _this._constraints = constraints;
            _this._maybeRestartOnDefaultDeviceChange();
        });
    };
    /**
     * NOTE(mmalavalli): On iOS 17 Chrome, a LocalAudioTrack with Krisp Noise Cancellation
     * enabled that is restarted due to foregrounding the browser is silent for as-of-yet
     * unknown reason. We work around this by discarding the Krisp MediaStreamTrack and using
     * the source MediaStreamTrack. (VIDEO-13006)
     * @private
     */ LocalAudioTrack.prototype._setMediaStreamTrack = function(mediaStreamTrack) {
        var _this = this;
        var _a = this, log = _a._log, noiseCancellation = _a.noiseCancellation;
        var promise = _super.prototype._setMediaStreamTrack.call(this, mediaStreamTrack);
        if (isIOSChrome() && !!noiseCancellation) {
            log.debug('iOS Chrome detected, checking if the restarted Krisp audio is silent');
            promise = promise.then(function() {
                return detectSilentAudio(_this._dummyEl);
            }).then(function(isSilent) {
                log.debug("Krisp audio is " + (isSilent ? 'silent, using source audio' : 'not silent'));
                return isSilent && noiseCancellation.disablePermanently().then(function() {
                    return _super.prototype._setMediaStreamTrack.call(_this, noiseCancellation.sourceTrack);
                });
            });
        }
        return promise;
    };
    /**
     * Disable the {@link LocalAudioTrack}. This is equivalent to muting the audio source.
     * @returns {this}
     * @fires LocalAudioTrack#disabled
     */ LocalAudioTrack.prototype.disable = function() {
        return _super.prototype.disable.apply(this, arguments);
    };
    /**
     * Enable the {@link LocalAudioTrack}. This is equivalent to unmuting the audio source.
     * @returns {this}
     * @fires LocalAudioTrack#enabled
    */ /**
     * Enable or disable the {@link LocalAudioTrack}. This is equivalent to unmuting or muting
     * the audio source respectively.
     * @param {boolean} [enabled] - Specify false to disable the
     *   {@link LocalAudioTrack}
     * @returns {this}
     * @fires LocalAudioTrack#disabled
     * @fires LocalAudioTrack#enabled
     */ LocalAudioTrack.prototype.enable = function() {
        return _super.prototype.enable.apply(this, arguments);
    };
    /**
     * Restart the {@link LocalAudioTrack}. This stops the existing MediaStreamTrack
     * and creates a new MediaStreamTrack. If the {@link LocalAudioTrack} is being published
     * to a {@link Room}, then all the {@link RemoteParticipant}s will start receiving media
     * from the newly created MediaStreamTrack. You can access the new MediaStreamTrack via
     * the <code>mediaStreamTrack</code> property. If you want to listen to events on
     * the MediaStreamTrack directly, please do so in the "started" event handler. Also,
     * the {@link LocalAudioTrack}'s ID is no longer guaranteed to be the same as the
     * underlying MediaStreamTrack's ID.
     * @param {MediaTrackConstraints} [constraints] - The optional <a href="https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints" target="_blank">MediaTrackConstraints</a>
     *   for restarting the {@link LocalAudioTrack}; If not specified, then the current MediaTrackConstraints
     *   will be used; If <code>{}</code> (empty object) is specified, then the default MediaTrackConstraints
     *   will be used
     * @returns {Promise<void>} Rejects with a TypeError if the {@link LocalAudioTrack} was not created
     *   using an one of <code>createLocalAudioTrack</code>, <code>createLocalTracks</code> or <code>connect</code>;
     *   Also rejects with the <a href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Exceptions" target="_blank">DOMException</a>
     *   raised by <code>getUserMedia</code> when it fails
     * @fires LocalAudioTrack#stopped
     * @fires LocalAudioTrack#started
     * @example
     * const { connect, createLocalAudioTrack } = require('twilio-video');
     *
     * // Create a LocalAudioTrack that captures audio from a USB microphone.
     * createLocalAudioTrack({ deviceId: 'usb-mic-id' }).then(function(localAudioTrack) {
     *   return connect('token', {
     *     name: 'my-cool-room',
     *     tracks: [localAudioTrack]
     *   });
     * }).then(function(room) {
     *   // Restart the LocalAudioTrack to capture audio from the default microphone.
     *   const localAudioTrack = Array.from(room.localParticipant.audioTracks.values())[0].track;
     *   return localAudioTrack.restart({ deviceId: 'default-mic-id' });
     * });
     */ LocalAudioTrack.prototype.restart = function() {
        return _super.prototype.restart.apply(this, arguments);
    };
    /**
     * Calls stop on the underlying MediaStreamTrack. If you choose to stop a
     * {@link LocalAudioTrack}, you should unpublish it after stopping.
     * @returns {this}
     * @fires LocalAudioTrack#stopped
     */ LocalAudioTrack.prototype.stop = function() {
        if (this.noiseCancellation) {
            this.noiseCancellation.stop();
        }
        if (this._restartOnDefaultDeviceChangeCleanup) {
            this._restartOnDefaultDeviceChangeCleanup();
        }
        return _super.prototype.stop.apply(this, arguments);
    };
    return LocalAudioTrack;
}(LocalMediaAudioTrack);
/**
 * The {@link LocalAudioTrack} was disabled, i.e. the audio source was muted by the user.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that was
 *   disabled
 * @event LocalAudioTrack#disabled
 */ /**
 * The {@link LocalAudioTrack} was enabled, i.e. the audio source was unmuted by the user.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that was enabled
 * @event LocalAudioTrack#enabled
 */ /**
 * The {@link LocalAudioTrack} was muted because the audio source stopped sending samples, most
 * likely due to another application taking said audio source, especially on mobile devices.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that was muted
 * @event LocalAudioTrack#muted
 */ /**
 * The {@link LocalAudioTrack} started. This means there is enough audio data to
 * begin playback.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that started
 * @event LocalAudioTrack#started
 */ /**
 * The {@link LocalAudioTrack} stopped, either because {@link LocalAudioTrack#stop}
 * or {@link LocalAudioTrack#restart} was called or because the underlying
 * MediaStreamTrack ended.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that stopped
 * @event LocalAudioTrack#stopped
 */ /**
 * The {@link LocalAudioTrack} was unmuted because the audio source resumed sending samples,
 * most likely due to the application that took over the said audio source has released it
 * back to the application, especially on mobile devices. This event is also fired when
 * {@link LocalAudioTrack#restart} is called on a muted {@link LocalAudioTrack} with a
 * new audio source.
 * @param {LocalAudioTrack} track - The {@link LocalAudioTrack} that was unmuted
 * @event LocalAudioTrack#unmuted
 */ module.exports = LocalAudioTrack; //# sourceMappingURL=localaudiotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/es5/localaudiotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// eslint-disable-next-line no-warning-comments
// TODO(mroberts): Remove this when we go to the next major version. This is
// only in place so that we can support ES6 classes without requiring `new`.
'use strict';
var inherits = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/vendor/inherits.js [app-client] (ecmascript)");
var LocalAudioTrackClass = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localaudiotrack.js [app-client] (ecmascript)");
function LocalAudioTrack(mediaStreamTrack, options) {
    var track = new LocalAudioTrackClass(mediaStreamTrack, options);
    Object.setPrototypeOf(track, LocalAudioTrack.prototype);
    return track;
}
inherits(LocalAudioTrack, LocalAudioTrackClass);
module.exports = LocalAudioTrack; //# sourceMappingURL=localaudiotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/capturevideoframes.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals MediaStreamTrackGenerator, MediaStreamTrackProcessor, TransformStream */ 'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var DEFAULT_FRAME_RATE = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_FRAME_RATE;
function captureVideoFramesSetInterval(videoEl, processVideoFrame) {
    var _a = __read(videoEl.srcObject.getVideoTracks(), 1), track = _a[0];
    var _b = track.getSettings().frameRate, frameRate = _b === void 0 ? DEFAULT_FRAME_RATE : _b;
    var sampleInterval;
    var readable = new ReadableStream({
        start: function(controller) {
            sampleInterval = setInterval(function() {
                return controller.enqueue();
            }, 1000 / frameRate);
        }
    });
    var transformer = new TransformStream({
        transform: function() {
            return processVideoFrame();
        }
    });
    readable.pipeThrough(transformer).pipeTo(new WritableStream()).then(function() {});
    return function() {
        clearInterval(sampleInterval);
    };
}
function captureVideoFramesInsertableStreams(videoEl, processVideoFrame, videoFrameType) {
    var _a = __read(videoEl.srcObject.getVideoTracks(), 1), track = _a[0];
    var readable = new MediaStreamTrackProcessor({
        track: track
    }).readable;
    var generator = new MediaStreamTrackGenerator({
        kind: 'video'
    });
    var shouldStop = false;
    var transformer = new TransformStream({
        transform: function(videoFrame, controller) {
            var promise = videoFrameType === 'videoframe' ? processVideoFrame(videoFrame) : Promise.resolve(videoFrame.close()).then(processVideoFrame);
            return promise.finally(function() {
                if (shouldStop) {
                    controller.terminate();
                }
            });
        }
    });
    readable.pipeThrough(transformer).pipeTo(generator.writable).then(function() {});
    return function() {
        shouldStop = true;
    };
}
module.exports = typeof MediaStreamTrackGenerator === 'function' && typeof MediaStreamTrackProcessor === 'function' ? captureVideoFramesInsertableStreams : captureVideoFramesSetInterval; //# sourceMappingURL=capturevideoframes.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/videoprocessoreventobserver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS;
/**
 * VideoProcessorEventObserver listens to {@link VideoProcessor} related events
 * and re-emits them as a generic event with some additional information.
 * @extends EventEmitter
 * @emits VideoProcessorEventObserver#event
 */ var VideoProcessorEventObserver = function(_super) {
    __extends(VideoProcessorEventObserver, _super);
    /**
     * Constructor.
     * @param {Log} log
     */ function VideoProcessorEventObserver(log) {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _lastStatsSaveTime: {
                value: null,
                writable: true
            },
            _lastStatsPublishTime: {
                value: null,
                writable: true
            },
            _log: {
                value: log
            },
            _processorInfo: {
                value: null,
                writable: true
            },
            _stats: {
                value: null,
                writable: true
            }
        });
        _this.on('add', function(info) {
            _this._lastStatsSaveTime = Date.now();
            _this._lastStatsPublishTime = Date.now();
            _this._processorInfo = info;
            _this._stats = [];
            _this._reemitEvent('add', _this._getEventData());
        });
        _this.on('remove', function() {
            var data = _this._getEventData();
            _this._lastStatsSaveTime = null;
            _this._lastStatsPublishTime = null;
            _this._processorInfo = null;
            _this._stats = null;
            _this._reemitEvent('remove', data);
        });
        _this.on('start', function() {
            _this._reemitEvent('start', _this._getEventData());
        });
        _this.on('stop', function(message) {
            _this._reemitEvent('stop', Object.assign({
                message: message
            }, _this._getEventData()));
        });
        _this.on('stats', function() {
            return _this._maybeEmitStats();
        });
        return _this;
    }
    /**
     * @private
     */ VideoProcessorEventObserver.prototype._getEventData = function() {
        if (!this._processorInfo) {
            return {};
        }
        var _a = this._processorInfo, processor = _a.processor, captureHeight = _a.captureHeight, captureWidth = _a.captureWidth, inputFrameRate = _a.inputFrameRate, isRemoteVideoTrack = _a.isRemoteVideoTrack, inputFrameBufferType = _a.inputFrameBufferType, outputFrameBufferContextType = _a.outputFrameBufferContextType;
        var data = {
            captureHeight: captureHeight,
            captureWidth: captureWidth,
            inputFrameRate: inputFrameRate,
            isRemoteVideoTrack: isRemoteVideoTrack,
            inputFrameBufferType: inputFrameBufferType,
            outputFrameBufferContextType: outputFrameBufferContextType
        };
        data.name = processor._name || 'VideoProcessor';
        [
            'assetsPath',
            'blurFilterRadius',
            'debounce',
            'fitType',
            'isSimdEnabled',
            'maskBlurRadius',
            'pipeline',
            'version'
        ].forEach(function(prop) {
            var val = processor["_" + prop];
            if (typeof val !== 'undefined') {
                data[prop] = val;
            }
        });
        Object.keys(data).forEach(function(prop) {
            var val = data[prop];
            if (typeof val === 'boolean') {
                data[prop] = val ? 'true' : 'false';
            }
        });
        return data;
    };
    /**
     * Save stats every second. If a specific time interval has elapsed,
     * the stats event will be emitted
     * @private
     */ VideoProcessorEventObserver.prototype._maybeEmitStats = function() {
        if (!this._stats || !this._processorInfo) {
            return;
        }
        var benchmark = this._processorInfo.processor._benchmark;
        if (!benchmark) {
            return;
        }
        var now = Date.now();
        if (now - this._lastStatsSaveTime < 1000) {
            return;
        }
        var entry = {
            outputFrameRate: benchmark.getRate('totalProcessingDelay')
        };
        [
            'captureFrameDelay',
            'imageCompositionDelay',
            'inputImageResizeDelay',
            'processFrameDelay',
            'segmentationDelay'
        ].forEach(function(name) {
            entry[name] = benchmark.getAverageDelay(name);
        });
        this._lastStatsSaveTime = now;
        this._stats.push(entry);
        if (now - this._lastStatsPublishTime < DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS) {
            return;
        }
        this._lastStatsPublishTime = now;
        var stats = this._stats.splice(0);
        var averages = stats.reduce(function(averages, current, n) {
            Object.keys(entry).forEach(function(name) {
                if (!averages[name]) {
                    averages[name] = 0;
                }
                averages[name] = (averages[name] * n + current[name]) / (n + 1);
            });
            return averages;
        }, {});
        Object.keys(averages).forEach(function(name) {
            averages[name] = parseFloat(averages[name].toFixed(2));
        });
        this._reemitEvent('stats', Object.assign({}, averages, this._getEventData()));
    };
    /**
     * @private
     */ VideoProcessorEventObserver.prototype._reemitEvent = function(name, data) {
        this._log.debug("VideoProcessor:" + name, data);
        this.emit('event', {
            name: name,
            data: data
        });
    };
    return VideoProcessorEventObserver;
}(EventEmitter);
module.exports = VideoProcessorEventObserver; //# sourceMappingURL=videoprocessoreventobserver.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/videotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/mediatrack.js [app-client] (ecmascript)");
var captureVideoFrames = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/capturevideoframes.js [app-client] (ecmascript)");
var VideoProcessorEventObserver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/videoprocessoreventobserver.js [app-client] (ecmascript)");
var guessBrowser = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").guessBrowser;
var DEFAULT_FRAME_RATE = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_FRAME_RATE;
/**
 * A {@link VideoTrack} is a {@link Track} representing video.
 * @extends Track
 * @property {boolean} isStarted - Whether or not the {@link VideoTrack} has
 *   started; if the {@link VideoTrack} started, there is enough video data to
 *   begin playback
 * @property {boolean} isEnabled - Whether or not the {@link VideoTrack} is
 *   enabled; if the {@link VideoTrack} is not enabled, it is "paused"
 * @property {VideoTrack.Dimensions} dimensions - The {@link VideoTrack}'s
 *   {@link VideoTrack.Dimensions}
 * @property {Track.Kind} kind - "video"
 * @property {MediaStreamTrack} mediaStreamTrack - A video MediaStreamTrack
 * @property {?MediaStreamTrack} processedTrack - The source of processed video frames.
 * It is null if no VideoProcessor has been added.
 * @property {?VideoProcessor} processor - A {@link VideoProcessor} that is currently
 *   processing video frames. It is null if video frames are not being processed.
 * @emits VideoTrack#dimensionsChanged
 * @emits VideoTrack#disabled
 * @emits VideoTrack#enabled
 * @emits VideoTrack#started
 */ var VideoTrack = function(_super) {
    __extends(VideoTrack, _super);
    /**
     * Construct a {@link VideoTrack}.
     * @param {MediaTrackTransceiver} mediaTrackTransceiver
     * @param {{log: Log}} options
     */ function VideoTrack(mediaTrackTransceiver, options) {
        var _this = _super.call(this, mediaTrackTransceiver, options) || this;
        Object.defineProperties(_this, {
            _isCapturing: {
                value: false,
                writable: true
            },
            _inputFrame: {
                value: null,
                writable: true
            },
            _outputFrame: {
                value: null,
                writable: true
            },
            _processorEventObserver: {
                value: null,
                writable: true
            },
            _processorOptions: {
                value: {},
                writable: true
            },
            _stopCapture: {
                value: function() {},
                writable: true
            },
            _unmuteHandler: {
                value: null,
                writable: true
            },
            dimensions: {
                enumerable: true,
                value: {
                    width: null,
                    height: null
                }
            },
            processor: {
                enumerable: true,
                value: null,
                writable: true
            }
        });
        _this._processorEventObserver = new (options.VideoProcessorEventObserver || VideoProcessorEventObserver)(_this._log);
        return _this;
    }
    /**
     * @private
     */ VideoTrack.prototype._checkIfCanCaptureFrames = function(isPublishing) {
        if (isPublishing === void 0) {
            isPublishing = false;
        }
        var canCaptureFrames = true;
        var message = '';
        var _a = this.mediaStreamTrack, enabled = _a.enabled, readyState = _a.readyState;
        if (!enabled) {
            canCaptureFrames = false;
            message = 'MediaStreamTrack is disabled';
        }
        if (readyState === 'ended') {
            canCaptureFrames = false;
            message = 'MediaStreamTrack is ended';
        }
        if (!this.processor) {
            canCaptureFrames = false;
            message = 'VideoProcessor not detected.';
        }
        if (!this._attachments.size && !isPublishing) {
            canCaptureFrames = false;
            message = 'VideoTrack is not publishing and there is no attached element.';
        }
        if (message) {
            this._log.debug(message);
        }
        return {
            canCaptureFrames: canCaptureFrames,
            message: message
        };
    };
    /**
     * @private
     */ VideoTrack.prototype._captureFrames = function() {
        var _this = this;
        if (this._isCapturing) {
            this._log.debug('Ignoring captureFrames call. Capture is already in progress');
            return;
        }
        if (!this._checkIfCanCaptureFrames().canCaptureFrames) {
            this._isCapturing = false;
            this._log.debug('Cannot capture frames. Ignoring captureFrames call.');
            return;
        }
        this._isCapturing = true;
        this._processorEventObserver.emit('start');
        this._log.debug('Start capturing frames');
        var inputFrameBufferType = this._processorOptions.inputFrameBufferType;
        this._dummyEl.play().then(function() {
            var process = function(videoFrame) {
                var checkResult = _this._checkIfCanCaptureFrames();
                if (!checkResult.canCaptureFrames) {
                    if (videoFrame) {
                        videoFrame.close();
                    }
                    _this._isCapturing = false;
                    _this._stopCapture();
                    _this._processorEventObserver.emit('stop', checkResult.message);
                    _this._log.debug('Cannot capture frames. Stopping capturing frames.');
                    return Promise.resolve();
                }
                var _a = _this.mediaStreamTrack.getSettings(), _b = _a.width, width = _b === void 0 ? 0 : _b, _c = _a.height, height = _c === void 0 ? 0 : _c;
                // Setting the canvas' dimension triggers a redraw.
                // Only set it if it has changed.
                if (_this._outputFrame && _this._outputFrame.width !== width) {
                    _this._outputFrame.width = width;
                    _this._outputFrame.height = height;
                }
                if (_this._inputFrame) {
                    if (_this._inputFrame.width !== width) {
                        _this._inputFrame.width = width;
                        _this._inputFrame.height = height;
                    }
                    _this._inputFrame.getContext('2d').drawImage(_this._dummyEl, 0, 0, width, height);
                }
                var input = videoFrame || ([
                    'video',
                    'videoframe'
                ].includes(inputFrameBufferType) ? _this._dummyEl : _this._inputFrame);
                var result = null;
                try {
                    result = _this.processor.processFrame(input, _this._outputFrame);
                } catch (ex) {
                    _this._log.debug('Exception detected after calling processFrame.', ex);
                }
                return (result instanceof Promise ? result : Promise.resolve(result)).then(function() {
                    if (_this._outputFrame) {
                        if (typeof _this.processedTrack.requestFrame === 'function') {
                            _this.processedTrack.requestFrame();
                        }
                        _this._processorEventObserver.emit('stats');
                    }
                });
            };
            _this._stopCapture = captureVideoFrames(_this._dummyEl, process, inputFrameBufferType);
        }).catch(function(error) {
            return _this._log.error('Video element cannot be played', {
                error: error,
                track: _this
            });
        });
    };
    /**
     * @private
     */ VideoTrack.prototype._initialize = function() {
        var _this = this;
        _super.prototype._initialize.call(this);
        if (this._dummyEl) {
            this._dummyEl.onloadedmetadata = function() {
                if (dimensionsChanged(_this, _this._dummyEl)) {
                    _this.dimensions.width = _this._dummyEl.videoWidth;
                    _this.dimensions.height = _this._dummyEl.videoHeight;
                }
            };
            this._dummyEl.onresize = function() {
                if (dimensionsChanged(_this, _this._dummyEl)) {
                    _this.dimensions.width = _this._dummyEl.videoWidth;
                    _this.dimensions.height = _this._dummyEl.videoHeight;
                    if (_this.isStarted) {
                        _this._log.debug('Dimensions changed:', _this.dimensions);
                        _this.emit(VideoTrack.DIMENSIONS_CHANGED, _this);
                    }
                }
            };
        }
    };
    /**
     * @private
     */ VideoTrack.prototype._restartProcessor = function() {
        var processor = this.processor;
        if (processor) {
            var processorOptions = Object.assign({}, this._processorOptions);
            this.removeProcessor(processor);
            this.addProcessor(processor, processorOptions);
        }
    };
    /**
     * @private
     */ VideoTrack.prototype._start = function(dummyEl) {
        this.dimensions.width = dummyEl.videoWidth;
        this.dimensions.height = dummyEl.videoHeight;
        this._log.debug('Dimensions:', this.dimensions);
        this.emit(VideoTrack.DIMENSIONS_CHANGED, this);
        return _super.prototype._start.call(this, dummyEl);
    };
    /**
     * Add a {@link VideoProcessor} to allow for custom processing of video frames belonging to a VideoTrack.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to use.
     * @param {AddProcessorOptions} [options] - {@link AddProcessorOptions} to provide.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * Video.createLocalVideoTrack().then(function(videoTrack) {
     *   videoTrack.addProcessor(new GrayScaleProcessor(100));
     * });
     */ VideoTrack.prototype.addProcessor = function(processor, options) {
        var _this = this;
        if (!processor || typeof processor.processFrame !== 'function') {
            throw new Error('Received an invalid VideoProcessor from addProcessor.');
        }
        if (this.processor) {
            throw new Error('A VideoProcessor has already been added.');
        }
        if (!this._dummyEl) {
            throw new Error('VideoTrack has not been initialized.');
        }
        this._log.debug('Adding VideoProcessor to the VideoTrack', processor);
        if (!this._unmuteHandler) {
            this._unmuteHandler = function() {
                _this._log.debug('mediaStreamTrack unmuted');
                // NOTE(csantos): On certain scenarios where mediaStreamTrack is coming from muted to unmuted state,
                // the processedTrack doesn't unmutes automatically although enabled is already set to true.
                // This is a terminal state for the processedTrack and should be restarted. (VIDEO-4176)
                if (_this.processedTrack.muted) {
                    _this._log.debug('mediaStreamTrack is unmuted but processedTrack is muted. Restarting processor.');
                    _this._restartProcessor();
                }
            };
            if (this.mediaStreamTrack.addEventListener) {
                this.mediaStreamTrack.addEventListener('unmute', this._unmuteHandler);
            } else {
                this.mediaStreamTrack.onunmute = this._unmuteHandler.bind(this);
            }
        }
        this._processorOptions = options || {};
        var _a = this._processorOptions, inputFrameBufferType = _a.inputFrameBufferType, outputFrameBufferContextType = _a.outputFrameBufferContextType;
        if (typeof OffscreenCanvas === 'undefined' && inputFrameBufferType === 'offscreencanvas') {
            throw new Error('OffscreenCanvas is not supported by this browser.');
        }
        if (inputFrameBufferType && inputFrameBufferType !== 'videoframe' && inputFrameBufferType !== 'video' && inputFrameBufferType !== 'canvas' && inputFrameBufferType !== 'offscreencanvas') {
            throw new Error("Invalid inputFrameBufferType of " + inputFrameBufferType);
        }
        if (!inputFrameBufferType) {
            inputFrameBufferType = typeof OffscreenCanvas === 'undefined' ? 'canvas' : 'offscreencanvas';
        }
        var _b = this.mediaStreamTrack.getSettings(), _c = _b.width, width = _c === void 0 ? 0 : _c, _d = _b.height, height = _d === void 0 ? 0 : _d, _e = _b.frameRate, frameRate = _e === void 0 ? DEFAULT_FRAME_RATE : _e;
        if (inputFrameBufferType === 'offscreencanvas') {
            this._inputFrame = new OffscreenCanvas(width, height);
        }
        if (inputFrameBufferType === 'canvas') {
            this._inputFrame = document.createElement('canvas');
        }
        if (this._inputFrame) {
            this._inputFrame.width = width;
            this._inputFrame.height = height;
        }
        this._outputFrame = document.createElement('canvas');
        this._outputFrame.width = width;
        this._outputFrame.height = height;
        // NOTE(csantos): Initialize the rendering context for future renders. This also ensures
        // that the correct type is used and on Firefox, it throws an exception if you try to capture
        // frames prior calling getContext https://bugzilla.mozilla.org/show_bug.cgi?id=1572422
        outputFrameBufferContextType = outputFrameBufferContextType || '2d';
        var ctx = this._outputFrame.getContext(outputFrameBufferContextType);
        if (!ctx) {
            throw new Error("Cannot get outputFrameBufferContextType: " + outputFrameBufferContextType + ".");
        }
        // NOTE(csantos): Zero FPS means we can control when to render the next frame by calling requestFrame.
        // Some browsers such as Firefox doesn't support requestFrame so we will use default, which is an undefined value.
        // This means, the browser will use the highest FPS available.
        var targetFps = typeof CanvasCaptureMediaStreamTrack !== 'undefined' && CanvasCaptureMediaStreamTrack.prototype && // eslint-disable-next-line
        typeof CanvasCaptureMediaStreamTrack.prototype.requestFrame === 'function' ? 0 : undefined;
        this.processedTrack = this._outputFrame.captureStream(targetFps).getTracks()[0];
        this.processedTrack.enabled = this.mediaStreamTrack.enabled;
        this.processor = processor;
        this._processorEventObserver.emit('add', {
            processor: processor,
            captureHeight: height,
            captureWidth: width,
            inputFrameRate: frameRate,
            isRemoteVideoTrack: this.toString().includes('RemoteVideoTrack'),
            inputFrameBufferType: inputFrameBufferType,
            outputFrameBufferContextType: outputFrameBufferContextType
        });
        this._updateElementsMediaStreamTrack();
        this._captureFrames();
        return this;
    };
    /**
     * Create an HTMLVideoElement and attach the {@link VideoTrack} to it.
     *
     * The HTMLVideoElement's <code>srcObject</code> will be set to a new
     * MediaStream containing the {@link VideoTrack}'s MediaStreamTrack.
     *
     * @returns {HTMLVideoElement} videoElement
     * @example
     * const Video = require('twilio-video');
     *
     * Video.createLocalVideoTrack().then(function(videoTrack) {
     *   const videoElement = videoTrack.attach();
     *   document.body.appendChild(videoElement);
     * });
    */ /**
     * Attach the {@link VideoTrack} to an existing HTMLMediaElement. The
     * HTMLMediaElement could be an HTMLAudioElement or an HTMLVideoElement.
     *
     * If the HTMLMediaElement's <code>srcObject</code> is not set to a MediaStream,
     * this method sets it to a new MediaStream containing the {@link VideoTrack}'s
     * MediaStreamTrack; otherwise, it adds the {@link MediaTrack}'s
     * MediaStreamTrack to the existing MediaStream. Finally, if there are any other
     * MediaStreamTracks of the same kind on the MediaStream, this method removes
     * them.
     *
     * @param {HTMLMediaElement} mediaElement - The HTMLMediaElement to attach to
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const Video = require('twilio-video');
     *
     * const videoElement = document.createElement('video');
     * document.body.appendChild(videoElement);
     *
     * Video.createLocalVideoTrack().then(function(videoTrack) {
     *   videoTrack.attach(videoElement);
     * });
    */ /**
     * Attach the {@link VideoTrack} to an HTMLMediaElement selected by
     * <code>document.querySelector</code>. The HTMLMediaElement could be an
     * HTMLAudioElement or an HTMLVideoElement.
     *
     * If the HTMLMediaElement's <code>srcObject</code> is not set to a MediaStream,
     * this method sets it to a new MediaStream containing the {@link VideoTrack}'s
     * MediaStreamTrack; otherwise, it adds the {@link VideoTrack}'s
     * MediaStreamTrack to the existing MediaStream. Finally, if there are any other
     * MediaStreamTracks of the same kind on the MediaStream, this method removes
     * them.
     *
     * @param {string} selector - A query selector for the HTMLMediaElement to
     *   attach to
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const Video = require('twilio-video');
     *
     * const videoElement = document.createElement('video');
     * videoElement.id = 'my-video-element';
     * document.body.appendChild(videoElement);
     *
     * Video.createLocalVideoTrack().then(function(track) {
     *   track.attach('#my-video-element');
     * });
     */ VideoTrack.prototype.attach = function() {
        var result = _super.prototype.attach.apply(this, arguments);
        if (this.processor) {
            this._captureFrames();
        }
        return result;
    };
    /**
     * Detach the {@link VideoTrack} from all previously attached HTMLMediaElements.
     * @returns {Array<HTMLMediaElement>} mediaElements
     * @example
     * const mediaElements = videoTrack.detach();
     * mediaElements.forEach(mediaElement => mediaElement.remove());
    */ /**
     * Detach the {@link VideoTrack} from a previously attached HTMLMediaElement.
     * @param {HTMLMediaElement} mediaElement - One of the HTMLMediaElements to
     *   which the {@link VideoTrack} is attached
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * const videoElement = document.getElementById('my-video-element');
     * videoTrack.detach(videoElement).remove();
    */ /**
     * Detach the {@link VideoTrack} from a previously attached HTMLMediaElement
     *   specified by <code>document.querySelector</code>.
     * @param {string} selector - The query selector of HTMLMediaElement to which
     *    the {@link VideoTrack} is attached
     * @returns {HTMLMediaElement} mediaElement
     * @example
     * videoTrack.detach('#my-video-element').remove();
     */ VideoTrack.prototype.detach = function() {
        return _super.prototype.detach.apply(this, arguments);
    };
    /**
     * Remove the previously added {@link VideoProcessor} using `addProcessor` API.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to remove.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * Video.createLocalVideoTrack().then(function(videoTrack) {
     *   const grayScaleProcessor = new GrayScaleProcessor(100);
     *   videoTrack.addProcessor(grayScaleProcessor);
     *   document.getElementById('remove-button').onclick = () => videoTrack.removeProcessor(grayScaleProcessor);
     * });
     */ VideoTrack.prototype.removeProcessor = function(processor) {
        if (!processor) {
            throw new Error('Received an invalid VideoProcessor from removeProcessor.');
        }
        if (!this.processor) {
            throw new Error('No existing VideoProcessor detected.');
        }
        if (processor !== this.processor) {
            throw new Error('The provided VideoProcessor is different than the existing one.');
        }
        this._processorEventObserver.emit('remove');
        this._log.debug('Removing VideoProcessor from the VideoTrack', processor);
        this._stopCapture();
        this._stopCapture = function() {};
        this.mediaStreamTrack.removeEventListener('unmute', this._unmuteHandler);
        this._processorOptions = {};
        this._unmuteHandler = null;
        this._isCapturing = false;
        this.processor = null;
        this.processedTrack = null;
        this._inputFrame = null;
        this._outputFrame = null;
        this._updateElementsMediaStreamTrack();
        return this;
    };
    return VideoTrack;
}(MediaTrack);
VideoTrack.DIMENSIONS_CHANGED = 'dimensionsChanged';
function dimensionsChanged(track, elem) {
    return track.dimensions.width !== elem.videoWidth || track.dimensions.height !== elem.videoHeight;
}
/**
 * A {@link VideoTrack}'s width and height.
 * @typedef {object} VideoTrack.Dimensions
 * @property {?number} width - The {@link VideoTrack}'s width or null if the
 *   {@link VideoTrack} has not yet started
 * @property {?number} height - The {@link VideoTrack}'s height or null if the
 *   {@link VideoTrack} has not yet started
 */ /**
 * A {@link VideoProcessor}, when added via {@link VideoTrack#addProcessor},
 * is used to process incoming video frames before
 * sending to the encoder or renderer.
 * @typedef {object} VideoProcessor
 * @property {function} processFrame - A callback to receive input and output frame buffers for processing.
 * The input frame buffer contains the original video frame which can be used for additional processing
 * such as applying filters to it. The output frame buffer is used to receive the processed video frame
 * before sending to the encoder or renderer.
 *
 * Any exception raised (either synchronously or asynchronously) in `processFrame` will result in the frame being dropped.
 * This callback has the following signature:<br/><br/>
 * <code>processFrame(</code><br/>
 * &nbsp;&nbsp;<code>inputFrameBuffer: OffscreenCanvas | HTMLCanvasElement | HTMLVideoElement | VideoFrame,</code><br/>
 * &nbsp;&nbsp;<code>outputFrameBuffer: HTMLCanvasElement</code><br/>
 * <code>): Promise&lt;void&gt; | void;</code>
 *
 * @example
 * class GrayScaleProcessor {
 *   constructor(percentage) {
 *     this.percentage = percentage;
 *   }
 *   processFrame(inputFrameBuffer, outputFrameBuffer) {
 *     const context = outputFrameBuffer.getContext('2d');
 *     context.filter = `grayscale(${this.percentage}%)`;
 *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
 *   }
 * }
 */ /**
 * Possible options to provide to {@link LocalVideoTrack#addProcessor} and {@link RemoteVideoTrack#addProcessor}.
 * @typedef {object} AddProcessorOptions
 * @property {string} [inputFrameBufferType="offscreencanvas"] - This option allows you to specify what kind of input you want to receive in your
 * Video Processor. The default is `offscreencanvas` and will fallback to a regular `canvas` if the browser does not support it.
 * Possible values include the following.
 * <br/>
 * <br/>
 * `videoframe` - Your Video Processor will receive a [VideoFrame](https://developer.mozilla.org/en-US/docs/Web/API/VideoFrame).
 * On browsers that do not support `VideoFrame`, it will receive an [HTMLVideoElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLVideoElement) instead.
 * <br/>
 * <br/>
 * `offscreencanvas` - Your Video Processor will receive an [OffscreenCanvas](https://developer.mozilla.org/en-US/docs/Web/API/OffscreenCanvas)
 * which is good for canvas-related processing that can be rendered off screen.
 * <br/>
 * <br/>
 * `canvas` - Your Video Processor will receive an [HTMLCanvasElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement).
 * This is recommended on browsers that doesn't support `OffscreenCanvas`, or if you need to render the frame on the screen.
 * <br/>
 * <br/>
 * `video` - Your Video Processor will receive an [HTMLVideoElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLVideoElement).
 * Use this option if you are processing the frame using WebGL or if you only need to [draw](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage)
 * the frame directly to your output canvas.
 * @property {string} [outputFrameBufferContextType="2d"] - The SDK needs the [context type](https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/getContext)
 * that your Video Processor uses in order to properly generate the processed track. For example, if your Video Processor uses WebGL2 (`canvas.getContext('webgl2')`),
 * you should set `outputFrameBufferContextType` to `webgl2`. If you're using Canvas 2D processing (`canvas.getContext('2d')`),
 * you should set `outputFrameBufferContextType` to `2d`. If the output frame is an [ImageBitmap](https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap),
 * you should set `outputFrameBufferContextType` to `bitmaprenderer`.
 */ /**
 * The {@link VideoTrack}'s dimensions changed.
 * @param {VideoTrack} track - The {@link VideoTrack} whose dimensions changed
 * @event VideoTrack#dimensionsChanged
 */ /**
 * The {@link VideoTrack} was disabled, i.e. "paused".
 * @param {VideoTrack} track - The {@link VideoTrack} that was disabled
 * @event VideoTrack#disabled
 */ /**
 * The {@link VideoTrack} was enabled, i.e. "unpaused".
 * @param {VideoTrack} track - The {@link VideoTrack} that was enabled
 * @event VideoTrack#enabled
 */ /**
 * The {@link VideoTrack} started. This means there is enough video data to
 * begin playback.
 * @param {VideoTrack} track - The {@link VideoTrack} that started
 * @event VideoTrack#started
 */ module.exports = VideoTrack; //# sourceMappingURL=videotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localvideotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var isIOS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)").isIOS;
var detectSilentVideo = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/detectsilentvideo.js [app-client] (ecmascript)");
var mixinLocalMediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localmediatrack.js [app-client] (ecmascript)");
var VideoTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/videotrack.js [app-client] (ecmascript)");
var LocalMediaVideoTrack = mixinLocalMediaTrack(VideoTrack);
/**
 * A {@link LocalVideoTrack} is a {@link VideoTrack} representing video that
 * your {@link LocalParticipant} can publish to a {@link Room}. It can be
 * enabled and disabled with {@link LocalVideoTrack#enable} and
 * {@link LocalVideoTrack#disable} or stopped completely with
 * {@link LocalVideoTrack#stop}.
 * @extends VideoTrack
 * @property {Track.ID} id - The {@link LocalVideoTrack}'s ID
 * @property {boolean} isMuted - Whether or not the video source has stopped sending frames to the
 *   {@link LocalVideoTrack}; This can happen when the camera is taken over by another application,
 *   mainly on mobile devices; When this property toggles, then <code>muted</code> and <code>unmuted</code>
 *   events are fired appropriately
 * @property {boolean} isStopped - Whether or not the {@link LocalVideoTrack} is
 *   stopped
 * @emits LocalVideoTrack#disabled
 * @emits LocalVideoTrack#enabled
 * @emits LocalVideoTrack#muted
 * @emits LocalVideoTrack#started
 * @emits LocalVideoTrack#stopped
 * @emits LocalVideoTrack#unmuted
 */ var LocalVideoTrack = function(_super) {
    __extends(LocalVideoTrack, _super);
    /**
     * Construct a {@link LocalVideoTrack} from a MediaStreamTrack.
     * @param {MediaStreamTrack} mediaStreamTrack - The underlying MediaStreamTrack
     * @param {LocalTrackOptions} [options] - {@link LocalTrack} options
     */ function LocalVideoTrack(mediaStreamTrack, options) {
        var _this = this;
        options = Object.assign({
            workaroundSilentLocalVideo: isIOS() && typeof document !== 'undefined' && typeof document.createElement === 'function'
        }, options);
        _this = _super.call(this, mediaStreamTrack, options) || this;
        Object.defineProperties(_this, {
            _workaroundSilentLocalVideo: {
                value: options.workaroundSilentLocalVideo ? workaroundSilentLocalVideo : null
            },
            _workaroundSilentLocalVideoCleanup: {
                value: null,
                writable: true
            }
        });
        // NOTE(mmalavalli): In iOS Safari, we work around a bug where local video
        // MediaStreamTracks are silent (even though they are enabled, live and unmuted)
        // after accepting/rejecting a phone call.
        if (_this._workaroundSilentLocalVideo) {
            _this._workaroundSilentLocalVideoCleanup = _this._workaroundSilentLocalVideo(_this, document);
        }
        return _this;
    }
    LocalVideoTrack.prototype.toString = function() {
        return "[LocalVideoTrack #" + this._instanceId + ": " + this.id + "]";
    };
    /**
     * @private
     */ LocalVideoTrack.prototype._checkIfCanCaptureFrames = function() {
        return _super.prototype._checkIfCanCaptureFrames.call(this, this._trackSender.isPublishing);
    };
    /**
     * @private
     */ LocalVideoTrack.prototype._end = function() {
        return _super.prototype._end.apply(this, arguments);
    };
    /**
     * @private
     */ LocalVideoTrack.prototype._setSenderMediaStreamTrack = function(useProcessed) {
        var _this = this;
        var unprocessedTrack = this.mediaStreamTrack;
        var mediaStreamTrack = useProcessed ? this.processedTrack : unprocessedTrack;
        return this._trackSender.setMediaStreamTrack(mediaStreamTrack).catch(function(error) {
            return _this._log.warn('setMediaStreamTrack failed on LocalVideoTrack RTCRtpSender', {
                error: error,
                mediaStreamTrack: mediaStreamTrack
            });
        }).then(function() {
            _this._unprocessedTrack = useProcessed ? unprocessedTrack : null;
        });
    };
    /**
     * Add a {@link VideoProcessor} to allow for custom processing of video frames belonging to a VideoTrack.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to use.
     * @param {AddProcessorOptions} [options] - {@link AddProcessorOptions} to provide.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * const localVideoTrack = Array.from(room.localParticipant.videoTracks.values())[0].track;
     * localVideoTrack.addProcessor(new GrayScaleProcessor(100));
     */ LocalVideoTrack.prototype.addProcessor = function() {
        this._log.debug('Adding VideoProcessor to the LocalVideoTrack');
        var result = _super.prototype.addProcessor.apply(this, arguments);
        if (!this.processedTrack) {
            return this._log.warn('Unable to add a VideoProcessor to the LocalVideoTrack');
        }
        this._log.debug('Updating LocalVideoTrack\'s MediaStreamTrack with the processed MediaStreamTrack', this.processedTrack);
        this._setSenderMediaStreamTrack(true);
        return result;
    };
    /**
     * Remove the previously added {@link VideoProcessor} using `addProcessor` API.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to remove.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * const localVideoTrack = Array.from(room.localParticipant.videoTracks.values())[0].track;
     * const grayScaleProcessor = new GrayScaleProcessor(100);
     * localVideoTrack.addProcessor(grayScaleProcessor);
     *
     * document.getElementById('remove-button').onclick = () => localVideoTrack.removeProcessor(grayScaleProcessor);
     */ LocalVideoTrack.prototype.removeProcessor = function() {
        var _this = this;
        this._log.debug('Removing VideoProcessor from the LocalVideoTrack');
        var result = _super.prototype.removeProcessor.apply(this, arguments);
        this._log.debug('Updating LocalVideoTrack\'s MediaStreamTrack with the original MediaStreamTrack');
        this._setSenderMediaStreamTrack().then(function() {
            return _this._updateElementsMediaStreamTrack();
        });
        return result;
    };
    /**
     * Disable the {@link LocalVideoTrack}. This is equivalent to pausing a video source.
     * If a {@link VideoProcessor} is added, then <code>processedTrack</code> is also disabled.
     * @returns {this}
     * @fires VideoTrack#disabled
     */ LocalVideoTrack.prototype.disable = function() {
        var result = _super.prototype.disable.apply(this, arguments);
        if (this.processedTrack) {
            this.processedTrack.enabled = false;
        }
        return result;
    };
    /**
     * Enable the {@link LocalVideoTrack}. This is equivalent to unpausing the video source.
     * If a {@link VideoProcessor} is added, then <code>processedTrack</code> is also enabled.
     * @returns {this}
     * @fires VideoTrack#enabled
    */ /**
     * Enable or disable the {@link LocalVideoTrack}. This is equivalent to unpausing or pausing
     * the video source respectively. If a {@link VideoProcessor} is added, then <code>processedTrack</code>
     * is also enabled or disabled.
     * @param {boolean} [enabled] - Specify false to disable the
     *   {@link LocalVideoTrack}
     * @returns {this}
     * @fires VideoTrack#disabled
     * @fires VideoTrack#enabled
     */ LocalVideoTrack.prototype.enable = function(enabled) {
        if (enabled === void 0) {
            enabled = true;
        }
        var result = _super.prototype.enable.apply(this, arguments);
        if (this.processedTrack) {
            this.processedTrack.enabled = enabled;
            if (enabled) {
                this._captureFrames();
                this._log.debug('Updating LocalVideoTrack\'s MediaStreamTrack with the processed MediaStreamTrack', this.processedTrack);
                this._setSenderMediaStreamTrack(true);
            }
        }
        return result;
    };
    /**
     * Restart the {@link LocalVideoTrack}. This stops the existing MediaStreamTrack
     * and creates a new MediaStreamTrack. If the {@link LocalVideoTrack} is being published
     * to a {@link Room}, then all the {@link RemoteParticipant}s will start receiving media
     * from the newly created MediaStreamTrack. You can access the new MediaStreamTrack via
     * the <code>mediaStreamTrack</code> property. If you want to listen to events on
     * the MediaStreamTrack directly, please do so in the "started" event handler. Also,
     * the {@link LocalVideoTrack}'s ID is no longer guaranteed to be the same as the
     * underlying MediaStreamTrack's ID.
     * @param {MediaTrackConstraints} [constraints] - The optional <a href="https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints" target="_blank">MediaTrackConstraints</a>
     *   for restarting the {@link LocalVideoTrack}; If not specified, then the current MediaTrackConstraints
     *   will be used; If <code>{}</code> (empty object) is specified, then the default MediaTrackConstraints
     *   will be used
     * @returns {Promise<void>} Rejects with a TypeError if the {@link LocalVideoTrack} was not created
     *   using an one of <code>createLocalVideoTrack</code>, <code>createLocalTracks</code> or <code>connect</code>;
     *   Also rejects with the <a href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Exceptions" target="_blank">DOMException</a>
     *   raised by <code>getUserMedia</code> when it fails
     * @fires LocalVideoTrack#stopped
     * @fires LocalVideoTrack#started
     * @example
     * const { connect, createLocalVideoTrack } = require('twilio-video');
     *
     * // Create a LocalVideoTrack that captures video from the front-facing camera.
     * createLocalVideoTrack({ facingMode: 'user' }).then(function(localVideoTrack) {
     *   return connect('token', {
     *     name: 'my-cool-room',
     *     tracks: [localVideoTrack]
     *   });
     * }).then(function(room) {
     *   // Restart the LocalVideoTrack to capture video from the back-facing camera.
     *   const localVideoTrack = Array.from(room.localParticipant.videoTracks.values())[0].track;
     *   return localVideoTrack.restart({ facingMode: 'environment' });
     * });
     */ LocalVideoTrack.prototype.restart = function() {
        var _this = this;
        if (this._workaroundSilentLocalVideoCleanup) {
            this._workaroundSilentLocalVideoCleanup();
            this._workaroundSilentLocalVideoCleanup = null;
        }
        var promise = _super.prototype.restart.apply(this, arguments);
        if (this.processor) {
            promise.then(function() {
                _this._restartProcessor();
            });
        }
        if (this._workaroundSilentLocalVideo) {
            promise.finally(function() {
                _this._workaroundSilentLocalVideoCleanup = _this._workaroundSilentLocalVideo(_this, document);
            });
        }
        return promise;
    };
    /**
     * Calls stop on the underlying MediaStreamTrack. If you choose to stop a
     * {@link LocalVideoTrack}, you should unpublish it after stopping.
     * @returns {this}
     * @fires LocalVideoTrack#stopped
     */ LocalVideoTrack.prototype.stop = function() {
        if (this._workaroundSilentLocalVideoCleanup) {
            this._workaroundSilentLocalVideoCleanup();
            this._workaroundSilentLocalVideoCleanup = null;
        }
        return _super.prototype.stop.apply(this, arguments);
    };
    return LocalVideoTrack;
}(LocalMediaVideoTrack);
/**
 * Work around a bug where local video MediaStreamTracks are silent (even though
 * they are enabled, live and unmuted) after accepting/rejecting a phone call.
 * @private
 * @param {LocalVideoTrack} localVideoTrack
 * @param {HTMLDocument} doc
 * @returns {function} Cleans up listeners attached by the workaround
 */ function workaroundSilentLocalVideo(localVideoTrack, doc) {
    var log = localVideoTrack._log;
    var el = localVideoTrack._dummyEl, mediaStreamTrack = localVideoTrack.mediaStreamTrack;
    function onUnmute() {
        if (!localVideoTrack.isEnabled) {
            return;
        }
        log.info('Unmuted, checking silence');
        // The dummy element is paused, so play it and then detect silence.
        el.play().then(function() {
            return detectSilentVideo(el, doc);
        }).then(function(isSilent) {
            if (!isSilent) {
                log.info('Non-silent frames detected, so no need to restart');
                return;
            }
            log.warn('Silence detected, restarting');
            // NOTE(mmalavalli): If we try and restart a silent MediaStreamTrack
            // without stopping it first, then a NotReadableError is raised. Hence,
            // we stop the MediaStreamTrack here.
            localVideoTrack._stop();
            // Restart the LocalVideoTrack.
            // eslint-disable-next-line consistent-return
            return localVideoTrack._restart();
        }).catch(function(error) {
            log.warn('Failed to detect silence and restart:', error);
        }).finally(function() {
            // If silent frames were not detected, then pause the dummy element again,
            // if there is no processed track.
            el = localVideoTrack._dummyEl;
            if (el && !el.paused && !localVideoTrack.processedTrack) {
                el.pause();
            }
            // Reset the unmute handler.
            mediaStreamTrack.removeEventListener('unmute', onUnmute);
            mediaStreamTrack = localVideoTrack.mediaStreamTrack;
            if (mediaStreamTrack.addEventListener) {
                mediaStreamTrack.addEventListener('unmute', onUnmute);
            } else {
                mediaStreamTrack.onunmute = onUnmute;
            }
        });
    }
    // Set the unmute handler.
    if (mediaStreamTrack.addEventListener) {
        mediaStreamTrack.addEventListener('unmute', onUnmute);
    } else {
        mediaStreamTrack.onunmute = onUnmute;
    }
    return function() {
        if (mediaStreamTrack.removeEventListener) {
            mediaStreamTrack.removeEventListener('unmute', onUnmute);
        } else {
            mediaStreamTrack.onunmute = null;
        }
    };
}
/**
 * The {@link LocalVideoTrack} was disabled, i.e. the video source was paused by the user.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that was
 *   disabled
 * @event LocalVideoTrack#disabled
 */ /**
 * The {@link LocalVideoTrack} was enabled, i.e. the video source was unpaused by the user.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that was enabled
 * @event LocalVideoTrack#enabled
 */ /**
 * The {@link LocalVideoTrack} was muted because the video source stopped sending frames, most
 * likely due to another application taking said video source, especially on mobile devices.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that was muted
 * @event LocalVideoTrack#muted
 */ /**
 * The {@link LocalVideoTrack} started. This means there is enough video data
 * to begin playback.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that started
 * @event LocalVideoTrack#started
 */ /**
 * The {@link LocalVideoTrack} stopped, either because {@link LocalVideoTrack#stop}
 * or {@link LocalVideoTrack#restart} was called or because the underlying
 * MediaStreamTrack ended.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that stopped
 * @event LocalVideoTrack#stopped
 */ /**
 * The {@link LocalVideoTrack} was unmuted because the video source resumed sending frames,
 * most likely due to the application that took over the said video source has released it
 * back to the application, especially on mobile devices. This event is also fired when
 * {@link LocalVideoTrack#restart} is called on a muted {@link LocalVideoTrack} with a
 * new video source.
 * @param {LocalVideoTrack} track - The {@link LocalVideoTrack} that was unmuted
 * @event LocalVideoTrack#unmuted
 */ module.exports = LocalVideoTrack; //# sourceMappingURL=localvideotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/es5/localvideotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// eslint-disable-next-line no-warning-comments
// TODO(mroberts): Remove this when we go to the next major version. This is
// only in place so that we can support ES6 classes without requiring `new`.
'use strict';
var inherits = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/vendor/inherits.js [app-client] (ecmascript)");
var LocalVideoTrackClass = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localvideotrack.js [app-client] (ecmascript)");
function LocalVideoTrack(mediaStreamTrack, options) {
    var track = new LocalVideoTrackClass(mediaStreamTrack, options);
    Object.setPrototypeOf(track, LocalVideoTrack.prototype);
    return track;
}
inherits(LocalVideoTrack, LocalVideoTrackClass);
module.exports = LocalVideoTrack; //# sourceMappingURL=localvideotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localdatatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var Track = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/index.js [app-client] (ecmascript)");
var DefaultDataTrackSender = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/data/sender.js [app-client] (ecmascript)");
/**
 * A {@link LocalDataTrack} is a {@link Track} representing data that your
 * {@link LocalParticipant} can publish to a {@link Room}.
 * @extends Track
 * @property {Track.ID} id - The {@link LocalDataTrack}'s ID
 * @property {Track.Kind} kind - "data"
 * @property {?number} maxPacketLifeTime - If non-null, this represents a time
 *   limit (in milliseconds) during which the {@link LocalDataTrack} will send
 *   or re-send data if not acknowledged on the underlying RTCDataChannel(s).
 * @property {?number} maxRetransmits - If non-null, this represents the number
 *   of times the {@link LocalDataTrack} will resend data if not successfully
 *   delivered on the underlying RTCDataChannel(s).
 * @property {boolean} ordered - true if data on the {@link LocalDataTrack} is
 *   guaranteed to be sent in order.
 * @property {boolean} reliable - This is true if both
 *   <code>maxPacketLifeTime</code> and <code>maxRetransmits</code> are set to
 *   null. In other words, if this is true, there is no bound on packet lifetime
 *   or the number of times the {@link LocalDataTrack} will attempt to send
 *   data, ensuring "reliable" transmission.
 * @example
 * var Video = require('twilio-video');
 *
 * var localDataTrack = new Video.LocalDataTrack();
 * window.addEventListener('mousemove', function(event) {
 *   localDataTrack.send(JSON.stringify({
 *     x: e.clientX,
 *     y: e.clientY
 *   }));
 * });
 *
 * var token1 = getAccessToken();
 * Video.connect(token1, {
 *   name: 'my-cool-room',
 *   tracks: [localDataTrack]
 * });
 *
 * var token2 = getAccessToken();
 * Video.connect(token2, {
 *   name: 'my-cool-room',
 *   tracks: []
 * }).then(function(room) {
 *   room.on('trackSubscribed', function(track) {
 *     track.on('message', function(message) {
 *       console.log(JSON.parse(message)); // { x: <number>, y: <number> }
 *     });
 *   });
 * });
 */ var LocalDataTrack = function(_super) {
    __extends(LocalDataTrack, _super);
    /**
     * Construct a {@link LocalDataTrack}.
     * @param {LocalDataTrackOptions} [options] - {@link LocalDataTrack} options
     */ function LocalDataTrack(options) {
        var _this = this;
        options = Object.assign({
            DataTrackSender: DefaultDataTrackSender,
            maxPacketLifeTime: null,
            maxRetransmits: null,
            ordered: true
        }, options);
        var DataTrackSender = options.DataTrackSender;
        var dataTrackSender = new DataTrackSender(options.maxPacketLifeTime, options.maxRetransmits, options.ordered);
        _this = _super.call(this, dataTrackSender.id, 'data', options) || this;
        Object.defineProperties(_this, {
            _trackSender: {
                value: dataTrackSender
            },
            id: {
                enumerable: true,
                value: dataTrackSender.id
            },
            maxPacketLifeTime: {
                enumerable: true,
                value: options.maxPacketLifeTime
            },
            maxRetransmits: {
                enumerable: true,
                value: options.maxRetransmits
            },
            ordered: {
                enumerable: true,
                value: options.ordered
            },
            reliable: {
                enumerable: true,
                value: options.maxPacketLifeTime === null && options.maxRetransmits === null
            }
        });
        return _this;
    }
    /**
     * Send a message over the {@link LocalDataTrack}.
     * @param {string|Blob|ArrayBuffer|ArrayBufferView} data
     * @returns {void}
     */ LocalDataTrack.prototype.send = function(data) {
        this._trackSender.send(data);
    };
    return LocalDataTrack;
}(Track);
/**
 * {@link LocalDataTrack} options
 * @typedef {LocalTrackOptions} LocalDataTrackOptions
 * @property {?number} [maxPacketLifeTime=null] - Set this to limit the time
 *   (in milliseconds) during which the LocalDataTrack will send or re-send data
 *   if not successfully delivered on the underlying RTCDataChannel(s). It is an
 *   error to specify both this and <code>maxRetransmits</code>.
 * @property {?number} [maxRetransmits=null] - Set this to limit the number of
 *   times the {@link LocalDataTrack} will send or re-send data if not
 *   acknowledged on the underlying RTCDataChannel(s). It is an error to specify
 *   both this and <code>maxPacketLifeTime</code>.
 * @property {boolean} [ordered=true] - Set this to false to allow data on the
 *   LocalDataTrack to be sent out-of-order.
 */ module.exports = LocalDataTrack; //# sourceMappingURL=localdatatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/es5/localdatatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// eslint-disable-next-line no-warning-comments
// TODO(mroberts): Remove this when we go to the next major version. This is
// only in place so that we can support ES6 classes without requiring `new`.
'use strict';
var inherits = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/vendor/inherits.js [app-client] (ecmascript)");
var LocalDataTrackClass = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localdatatrack.js [app-client] (ecmascript)");
function LocalDataTrack(options) {
    var track = new LocalDataTrackClass(options);
    Object.setPrototypeOf(track, LocalDataTrack.prototype);
    return track;
}
inherits(LocalDataTrack, LocalDataTrackClass);
module.exports = LocalDataTrack; //# sourceMappingURL=localdatatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/es5/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = {
    LocalAudioTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/localaudiotrack.js [app-client] (ecmascript)"),
    LocalVideoTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/localvideotrack.js [app-client] (ecmascript)"),
    LocalDataTrack: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/es5/localdatatrack.js [app-client] (ecmascript)")
}; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/trackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventemitter.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), buildLogLevels = _a.buildLogLevels, valueToJSON = _a.valueToJSON;
var DEFAULT_LOG_LEVEL = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_LOG_LEVEL;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var nInstances = 0;
/**
 * A {@link TrackPublication} represents a {@link Track} that
 * has been published to a {@link Room}.
 * @property {string} trackName - the published {@link Track}'s name
 * @property {Track.SID} trackSid - SID assigned to the published {@link Track}
 * @emits TrackPublication#trackDisabled
 * @emits TrackPublication#trackEnabled
 */ var TrackPublication = function(_super) {
    __extends(TrackPublication, _super);
    /**
     * Construct a {@link TrackPublication}.
     * @param {string} trackName - the published {@link Track}'s name
     * @param {Track.SID} trackSid - SID assigned to the {@link Track}
     * @param {TrackPublicationOptions} options - {@link TrackPublication} options
     */ function TrackPublication(trackName, trackSid, options) {
        var _this = _super.call(this) || this;
        options = Object.assign({
            logLevel: DEFAULT_LOG_LEVEL
        }, options);
        var logLevels = buildLogLevels(options.logLevel);
        Object.defineProperties(_this, {
            _instanceId: {
                value: nInstances++
            },
            _log: {
                value: options.log ? options.log.createLog('default', _this) : new Log('default', _this, logLevels, options.loggerName)
            },
            trackName: {
                enumerable: true,
                value: trackName
            },
            trackSid: {
                enumerable: true,
                value: trackSid
            }
        });
        return _this;
    }
    TrackPublication.prototype.toJSON = function() {
        return valueToJSON(this);
    };
    TrackPublication.prototype.toString = function() {
        return "[TrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return TrackPublication;
}(EventEmitter);
/**
 * The published {@link Track} was disabled.
 * @event TrackPublication#trackDisabled
 */ /**
 * The published {@link Track} was enabled.
 * @event TrackPublication#trackEnabled
 */ /**
 * A {@link LocalAudioTrackPublication} or a {@link RemoteAudioTrackPublication}.
 * @typedef {LocalAudioTrackPublication|RemoteAudioTrackPublication} AudioTrackPublication
 */ /**
 * A {@link LocalDataTrackPublication} or a {@link RemoteDataTrackPublication}.
 * @typedef {LocalDataTrackPublication|RemoteDataTrackPublication} DataTrackPublication
 */ /**
 * A {@link LocalVideoTrackPublication} or a {@link RemoteVideoTrackPublication}.
 * @typedef {LocalVideoTrackPublication|RemoteVideoTrackPublication} VideoTrackPublication
 */ /**
 * {@link TrackPublication} options
 * @typedef {object} TrackPublicationOptions
 * @property {LogLevel|LogLevels} logLevel - Log level for 'media' modules
 */ module.exports = TrackPublication; //# sourceMappingURL=trackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localtrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint new-cap:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var TrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/trackpublication.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), E = _a.typeErrors, trackPriority = _a.trackPriority;
/**
 * A {@link LocalTrackPublication} is a {@link LocalTrack} that has been
 * published to a {@link Room}.
 * @extends TrackPublication
 * @property {boolean} isTrackEnabled - whether the published {@link LocalTrack}
 *   is enabled
 * @property {Track.Kind} kind - kind of the published {@link LocalTrack}
 * @property {Track.Priority} priority - the publish priority of the {@link LocalTrack}
 * @property {LocalTrack} track - the {@link LocalTrack}
 * @emits LocalTrackPublication#warning
 * @emits LocalTrackPublication#warningsCleared
 */ var LocalTrackPublication = function(_super) {
    __extends(LocalTrackPublication, _super);
    /**
     * Construct a {@link LocalTrackPublication}.
     * @param {LocalTrackPublicationSignaling} signaling - The corresponding
     *   {@link LocalTrackPublicationSignaling}
     * @param {LocalTrack} track - The {@link LocalTrack}
     * @param {function(LocalTrackPublication): void} unpublish - The callback
     *   that unpublishes the {@link LocalTrackPublication}
     * @param {TrackPublicationOptions} options - {@link LocalTrackPublication}
     *   options
     */ function LocalTrackPublication(signaling, track, unpublish, options) {
        var _this = _super.call(this, track.name, signaling.sid, options) || this;
        Object.defineProperties(_this, {
            _reemitSignalingEvent: {
                value: function() {
                    var args = [];
                    for(var _i = 0; _i < arguments.length; _i++){
                        args[_i] = arguments[_i];
                    }
                    return _this.emit.apply(_this, __spreadArray([
                        args && args.length ? 'warning' : 'warningsCleared'
                    ], __read(args)));
                }
            },
            _reemitTrackEvent: {
                value: function() {
                    return _this.emit(_this.isTrackEnabled ? 'trackEnabled' : 'trackDisabled');
                }
            },
            _signaling: {
                value: signaling
            },
            _unpublish: {
                value: unpublish
            },
            isTrackEnabled: {
                enumerable: true,
                get: function() {
                    return this.track.kind === 'data' ? true : this.track.isEnabled;
                }
            },
            kind: {
                enumerable: true,
                value: track.kind
            },
            priority: {
                enumerable: true,
                get: function() {
                    return signaling.updatedPriority;
                }
            },
            track: {
                enumerable: true,
                value: track
            }
        });
        [
            'disabled',
            'enabled'
        ].forEach(function(name) {
            return track.on(name, _this._reemitTrackEvent);
        });
        [
            'warning',
            'warningsCleared'
        ].forEach(function(name) {
            return signaling.on(name, _this._reemitSignalingEvent);
        });
        return _this;
    }
    LocalTrackPublication.prototype.toString = function() {
        return "[LocalTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    /**
     * Update the {@link Track.Priority} of the published {@link LocalTrack}.
     * @param {Track.Priority} priority - the new {@link Track.priority}
     * @returns {this}
     * @throws {RangeError}
     */ LocalTrackPublication.prototype.setPriority = function(priority) {
        var priorityValues = Object.values(trackPriority);
        if (!priorityValues.includes(priority)) {
            throw E.INVALID_VALUE('priority', priorityValues);
        }
        this._signaling.setPriority(priority);
        return this;
    };
    /**
     * Unpublish a {@link LocalTrackPublication}. This means that the media
     * from this {@link LocalTrackPublication} is no longer available to the
     * {@link Room}'s {@link RemoteParticipant}s.
     * @returns {this}
     */ LocalTrackPublication.prototype.unpublish = function() {
        var _this = this;
        [
            'disabled',
            'enabled'
        ].forEach(function(name) {
            return _this.track.removeListener(name, _this._reemitTrackEvent);
        });
        [
            'warning',
            'warningsCleared'
        ].forEach(function(name) {
            return _this._signaling.removeListener(name, _this._reemitSignalingEvent);
        });
        this._unpublish(this);
        return this;
    };
    return LocalTrackPublication;
}(TrackPublication);
/**
 * The published {@link LocalTrack} encountered a warning.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @event LocalTrackPublication#warning
 * @param {string} name - The warning that was raised.
 */ /**
 * The published {@link LocalTrack} cleared all warnings.
 * This event is only raised if you enabled warnings using <code>notifyWarnings</code> in <code>ConnectOptions</code>.
 * @event LocalTrackPublication#warningsCleared
 */ module.exports = LocalTrackPublication; //# sourceMappingURL=localtrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localaudiotrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localtrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link LocalAudioTrackPublication} is a {@link LocalAudioTrack} that has
 * been published to a {@link Room}.
 * @extends LocalTrackPublication
 * @property {Track.Kind} kind - "audio"
 * @property {LocalAudioTrack} track - the {@link LocalAudioTrack}
 */ var LocalAudioTrackPublication = function(_super) {
    __extends(LocalAudioTrackPublication, _super);
    /**
     * Construct a {@link LocalAudioTrackPublication}.
     * @param {LocalTrackPublicationSignaling} signaling - The corresponding
     *   {@link LocalTrackPublicationSignaling}
     * @param {LocalAudioTrack} track - the {@link LocalAudioTrack}
     * @param {function(LocalTrackPublication): void} unpublish - The callback
     *    that unpublishes the {@link LocalTrackPublication}
     * @param {TrackPublicationOptions} options - {@link LocalTrackPublication} options
     */ function LocalAudioTrackPublication(signaling, track, unpublish, options) {
        return _super.call(this, signaling, track, unpublish, options) || this;
    }
    LocalAudioTrackPublication.prototype.toString = function() {
        return "[LocalAudioTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return LocalAudioTrackPublication;
}(LocalTrackPublication);
module.exports = LocalAudioTrackPublication; //# sourceMappingURL=localaudiotrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localdatatrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localtrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link LocalDataTrackPublication} is a {@link LocalDataTrack} that has been
 * published to a {@link Room}.
 * @extends LocalTrackPublication
 * @property {Track.Kind} kind - "data"
 * @property {LocalDataTrack} track - the {@link LocalDataTrack}
 */ var LocalDataTrackPublication = function(_super) {
    __extends(LocalDataTrackPublication, _super);
    /**
     * Construct a {@link LocalDataTrackPublication}.
     * @param {LocalTrackPublicationSignaling} signaling - The corresponding
     *   {@link LocalTrackPublicationSignaling}
     * @param {LocalDataTrack} track - the {@link LocalDataTrack}
     * @param {function(LocalTrackPublication): void} unpublish - The callback
     *    that unpublishes the {@link LocalTrackPublication}
     * @param {TrackPublicationOptions} options - {@link LocalTrackPublication} options
     */ function LocalDataTrackPublication(signaling, track, unpublish, options) {
        return _super.call(this, signaling, track, unpublish, options) || this;
    }
    LocalDataTrackPublication.prototype.toString = function() {
        return "[LocalDataTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return LocalDataTrackPublication;
}(LocalTrackPublication);
module.exports = LocalDataTrackPublication; //# sourceMappingURL=localdatatrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/localvideotrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/localtrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link LocalVideoTrackPublication} is a {@link LocalVideoTrack} that has
 * been published to a {@link Room}.
 * @extends LocalTrackPublication
 * @property {Track.Kind} kind - "video"
 * @property {LocalVideoTrack} track - the {@link LocalVideoTrack}
 */ var LocalVideoTrackPublication = function(_super) {
    __extends(LocalVideoTrackPublication, _super);
    /**
     * Construct a {@link LocalVideoTrackPublication}.
     * @param {LocalTrackPublicationSignaling} signaling - The corresponding
     *   {@link LocalTrackPublicationSignaling}
     * @param {LocalVideoTrack} track - the {@link LocalVideoTrack}
     * @param {function(LocalTrackPublication): void} unpublish - The callback
     *    that unpublishes the {@link LocalTrackPublication}
     * @param {TrackPublicationOptions} options - {@link LocalTrackPublication} options
     */ function LocalVideoTrackPublication(signaling, track, unpublish, options) {
        return _super.call(this, signaling, track, unpublish, options) || this;
    }
    LocalVideoTrackPublication.prototype.toString = function() {
        return "[LocalVideoTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return LocalVideoTrackPublication;
}(LocalTrackPublication);
module.exports = LocalVideoTrackPublication; //# sourceMappingURL=localvideotrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotemediatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), E = _a.typeErrors, trackPriority = _a.trackPriority;
var isIOS = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)").isIOS;
var documentVisibilityMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/documentvisibilitymonitor.js [app-client] (ecmascript)");
function mixinRemoteMediaTrack(AudioOrVideoTrack) {
    /**
     * A {@link RemoteMediaTrack} represents a {@link MediaTrack} published to a
     * {@link Room} by a {@link RemoteParticipant}.
     * @property {boolean} isEnabled - Whether the {@link RemoteMediaTrack} is enabled
     * @property {boolean} isSwitchedOff - Whether the {@link RemoteMediaTrack} is switched off
     * @property {Track.SID} sid - The SID assigned to the {@link RemoteMediaTrack}
     * @property {?Track.Priority} priority - The subscribe priority of the {@link RemoteMediaTrack}
     * @emits RemoteMediaTrack#disabled
     * @emits RemoteMediaTrack#enabled
     * @emits RemoteMediaTrack#switchedOff
     * @emits RemoteMediaTrack#switchedOn
     */ return function(_super) {
        __extends(RemoteMediaTrack, _super);
        /**
         * Construct a {@link RemoteMediaTrack}.
         * @param {Track.SID} sid
         * @param {MediaTrackReceiver} mediaTrackReceiver
         * @param {boolean} isEnabled
          @param {boolean} isSwitchedOff
         * @param {function(?Track.Priority): void} setPriority - Set or clear the subscribe
         *  {@link Track.Priority} of the {@link RemoteMediaTrack}
         * @param {function(ClientRenderHint): void} setRenderHint - Set render hints.
         * @param {{log: Log, name: ?string}} options
         */ function RemoteMediaTrack(sid, mediaTrackReceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options) {
            var _this = this;
            options = Object.assign({
                // NOTE(mpatwardhan): WebKit bug: 212780 sometimes causes the audio/video elements to stay paused when safari
                // regains foreground. To workaround it, when safari gains foreground - we will play any elements that were
                // playing before safari lost foreground.
                workaroundWebKitBug212780: isIOS() && typeof document === 'object' && typeof document.addEventListener === 'function' && typeof document.visibilityState === 'string'
            }, options);
            _this = _super.call(this, mediaTrackReceiver, options) || this;
            Object.defineProperties(_this, {
                _isEnabled: {
                    value: isEnabled,
                    writable: true
                },
                _isSwitchedOff: {
                    value: isSwitchedOff,
                    writable: true
                },
                _priority: {
                    value: null,
                    writable: true
                },
                _setPriority: {
                    value: setPriority
                },
                _setRenderHint: {
                    value: function(renderHint) {
                        _this._log.debug('updating render hint:', renderHint);
                        setRenderHint(renderHint);
                    }
                },
                isEnabled: {
                    enumerable: true,
                    get: function() {
                        return this._isEnabled;
                    }
                },
                isSwitchedOff: {
                    enumerable: true,
                    get: function() {
                        return this._isSwitchedOff;
                    }
                },
                priority: {
                    enumerable: true,
                    get: function() {
                        return this._priority;
                    }
                },
                sid: {
                    enumerable: true,
                    value: sid
                },
                _workaroundWebKitBug212780: {
                    value: options.workaroundWebKitBug212780
                },
                _workaroundWebKitBug212780Cleanup: {
                    value: null,
                    writable: true
                }
            });
            return _this;
        }
        /**
         * Update the subscribe {@link Track.Priority} of the {@link RemoteMediaTrack}.
         * @param {?Track.Priority} priority - the new subscribe {@link Track.Priority};
         *   If <code>null</code>, then the subscribe {@link Track.Priority} is cleared, which
         *   means the {@link Track.Priority} set by the publisher is now the effective priority.
         * @returns {this}
         * @throws {RangeError}
         */ RemoteMediaTrack.prototype.setPriority = function(priority) {
            var priorityValues = __spreadArray([
                null
            ], __read(Object.values(trackPriority)));
            if (!priorityValues.includes(priority)) {
                // eslint-disable-next-line new-cap
                throw E.INVALID_VALUE('priority', priorityValues);
            }
            if (this._priority !== priority) {
                this._priority = priority;
                this._setPriority(priority);
            }
            return this;
        };
        /**
         * @private
         * @param {boolean} isEnabled
         */ RemoteMediaTrack.prototype._setEnabled = function(isEnabled) {
            if (this._isEnabled !== isEnabled) {
                this._isEnabled = isEnabled;
                this.emit(this._isEnabled ? 'enabled' : 'disabled', this);
            }
        };
        /**
         * @private
         * @param {boolean} isSwitchedOff
         */ RemoteMediaTrack.prototype._setSwitchedOff = function(isSwitchedOff) {
            if (this._isSwitchedOff !== isSwitchedOff) {
                this._isSwitchedOff = isSwitchedOff;
                this.emit(isSwitchedOff ? 'switchedOff' : 'switchedOn', this);
            }
        };
        RemoteMediaTrack.prototype.attach = function(el) {
            var result = _super.prototype.attach.call(this, el);
            if (this.mediaStreamTrack.enabled !== true) {
                // NOTE(mpatwardhan): we disable mediaStreamTrack when there
                // are no attachments to it (see notes below). Now that there
                // are attachments re-enable the track.
                this.mediaStreamTrack.enabled = true;
                if (this.processedTrack) {
                    this.processedTrack.enabled = true;
                }
                // NOTE(csantos): since remote tracks disables/enables the mediaStreamTrack,
                // captureFrames stops along with it. We need to start it again after re-enabling.
                // See attach/detach methods in this class and in VideoTrack class.
                if (this.processor) {
                    this._captureFrames();
                }
            }
            if (this._workaroundWebKitBug212780) {
                this._workaroundWebKitBug212780Cleanup = this._workaroundWebKitBug212780Cleanup || playIfPausedWhileInBackground(this);
            }
            return result;
        };
        RemoteMediaTrack.prototype.detach = function(el) {
            var result = _super.prototype.detach.call(this, el);
            if (this._attachments.size === 0) {
                // NOTE(mpatwardhan): chrome continues playing webrtc audio
                // track even after audio element is removed from the DOM.
                // https://bugs.chromium.org/p/chromium/issues/detail?id=749928
                // to workaround: here disable the track when
                // there are no elements attached to it.
                this.mediaStreamTrack.enabled = false;
                if (this.processedTrack) {
                    this.processedTrack.enabled = false;
                }
                if (this._workaroundWebKitBug212780Cleanup) {
                    // unhook visibility change
                    this._workaroundWebKitBug212780Cleanup();
                    this._workaroundWebKitBug212780Cleanup = null;
                }
            }
            return result;
        };
        return RemoteMediaTrack;
    }(AudioOrVideoTrack);
}
function playIfPausedWhileInBackground(remoteMediaTrack) {
    var log = remoteMediaTrack._log, kind = remoteMediaTrack.kind;
    function onVisibilityChanged(isVisible) {
        if (!isVisible) {
            return;
        }
        remoteMediaTrack._attachments.forEach(function(el) {
            var shim = remoteMediaTrack._elShims.get(el);
            var isInadvertentlyPaused = el.paused && shim && !shim.pausedIntentionally();
            if (isInadvertentlyPaused) {
                log.info("Playing inadvertently paused <" + kind + "> element");
                log.debug('Element:', el);
                log.debug('RemoteMediaTrack:', remoteMediaTrack);
                el.play().then(function() {
                    log.info("Successfully played inadvertently paused <" + kind + "> element");
                    log.debug('Element:', el);
                    log.debug('RemoteMediaTrack:', remoteMediaTrack);
                }).catch(function(err) {
                    log.warn("Error while playing inadvertently paused <" + kind + "> element:", {
                        err: err,
                        el: el,
                        remoteMediaTrack: remoteMediaTrack
                    });
                });
            }
        });
    }
    // NOTE(mpatwardhan): listen for document visibility callback on phase 2.
    // this ensures that any LocalMediaTrack's restart (which listen on phase 1) gets executed
    // first. This order is important because we `play` tracks in the callback, and
    // play can fail on safari if audio is not being captured.
    documentVisibilityMonitor.onVisibilityChange(2, onVisibilityChanged);
    return function() {
        documentVisibilityMonitor.offVisibilityChange(2, onVisibilityChanged);
    };
}
/**
 * A {@link RemoteMediaTrack} was disabled.
 * @param {RemoteMediaTrack} track - The {@link RemoteMediaTrack} that was
 *   disabled
 * @event RemoteMediaTrack#disabled
 */ /**
 * A {@link RemoteMediaTrack} was enabled.
 * @param {RemoteMediaTrack} track - The {@link RemoteMediaTrack} that was
 *   enabled
 * @event RemoteMediaTrack#enabled
 */ /**
 * A {@link RemoteMediaTrack} was switched off.
 * @param {RemoteMediaTrack} track - The {@link RemoteMediaTrack} that was
 *   switched off
 * @event RemoteMediaTrack#switchedOff
 */ /**
 * A {@link RemoteMediaTrack} was switched on.
 * @param {RemoteMediaTrack} track - The {@link RemoteMediaTrack} that was
 *   switched on
 * @event RemoteMediaTrack#switchedOn
 */ /**
 * A {@link ClientRenderHint} object specifies track dimensions and /enabled disable state.
 * This state will be used by the server(SFU) to determine bandwidth allocation for the track,
 * and turn it on or off as needed.
 * @typedef {object} ClientRenderHint
 * @property {boolean} [enabled] - track is enabled or disabled. defaults to disabled.
 * @property {VideoTrack.Dimensions} [renderDimensions] - Optional parameter to specify the desired
 *   render dimensions of {@link RemoteVideoTrack}s. This property must be specified if enabled=true
 */ module.exports = mixinRemoteMediaTrack; //# sourceMappingURL=remotemediatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remoteaudiotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var AudioTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/audiotrack.js [app-client] (ecmascript)");
var mixinRemoteMediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotemediatrack.js [app-client] (ecmascript)");
var RemoteMediaAudioTrack = mixinRemoteMediaTrack(AudioTrack);
/**
 * A {@link RemoteAudioTrack} represents an {@link AudioTrack} published to a
 * {@link Room} by a {@link RemoteParticipant}.
 * @extends AudioTrack
 * @property {boolean} isEnabled - Whether the {@link RemoteAudioTrack} is enabled
 * @property {boolean} isSwitchedOff - Whether the {@link RemoteAudioTrack} is switched off
 * @property {Track.SID} sid - The {@link RemoteAudioTrack}'s SID
 * @property {?Track.Priority} priority - The subscribe priority of the {@link RemoteAudioTrack}
 * @emits RemoteAudioTrack#disabled
 * @emits RemoteAudioTrack#enabled
 * @emits RemoteAudioTrack#started
 * @emits RemoteAudioTrack#switchedOff
 * @emits RemoteAudioTrack#switchedOn
 */ var RemoteAudioTrack = function(_super) {
    __extends(RemoteAudioTrack, _super);
    /**
     * Construct a {@link RemoteAudioTrack}.
     * @param {Track.SID} sid - The {@link RemoteAudioTrack}'s SID
     * @param {MediaTrackReceiver} mediaTrackReceiver - An audio MediaStreamTrack container
     * @param {boolean} isEnabled - Whether the {@link RemoteAudioTrack} is enabled
     * @param {boolean} isSwitchedOff - Whether the {@link RemoteAudioTrack} is switched off
     * @param {function(?Track.Priority): void} setPriority - Set or clear the subscribe
     *  {@link Track.Priority} of the {@link RemoteAudioTrack}
     * @param {function(ClientRenderHint): void} setRenderHint - Set render hints.
     * @param {{log: Log}} options - The {@link RemoteTrack} options
     */ function RemoteAudioTrack(sid, mediaTrackReceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options) {
        return _super.call(this, sid, mediaTrackReceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options) || this;
    }
    RemoteAudioTrack.prototype.toString = function() {
        return "[RemoteAudioTrack #" + this._instanceId + ": " + this.sid + "]";
    };
    /**
     * @private
     */ RemoteAudioTrack.prototype._start = function() {
        _super.prototype._start.call(this);
        if (this._dummyEl) {
            // NOTE(mpatwardhan): To fix VIDEO-6336, clear dummy element after the
            // RemoteAudioTrack has started.
            if (this._disposeMediaElement) {
                this._disposeMediaElement(this._dummyEl);
            } else {
                this._dummyEl.srcObject = null;
            }
            this._dummyEl = null;
        }
    };
    /**
     * Update the subscribe {@link Track.Priority} of the {@link RemoteAudioTrack}.
     * @param {?Track.Priority} priority - the new subscribe {@link Track.Priority};
     *   Currently setPriority has no effect on audio tracks.
     * @returns {this}
     * @throws {RangeError}
     */ RemoteAudioTrack.prototype.setPriority = function(priority) {
        return _super.prototype.setPriority.call(this, priority);
    };
    return RemoteAudioTrack;
}(RemoteMediaAudioTrack);
/**
 * The {@link RemoteAudioTrack} was disabled, i.e. "muted".
 * @param {RemoteAudioTrack} track - The {@link RemoteAudioTrack} that was
 *   disabled
 * @event RemoteAudioTrack#disabled
 */ /**
 * The {@link RemoteAudioTrack} was enabled, i.e. "unmuted".
 * @param {RemoteAudioTrack} track - The {@link RemoteAudioTrack} that was
 *   enabled
 * @event RemoteAudioTrack#enabled
 */ /**
 * The {@link RemoteAudioTrack} started. This means there is enough audio data
 * to begin playback.
 * @param {RemoteAudioTrack} track - The {@link RemoteAudioTrack} that started
 * @event RemoteAudioTrack#started
 */ /**
 * A {@link RemoteAudioTrack} was switched off.
 * @param {RemoteAudioTrack} track - The {@link RemoteAudioTrack} that was
 *   switched off
 * @event RemoteAudioTrack#switchedOff
 */ /**
 * A {@link RemoteAudioTrack} was switched on.
 * @param {RemoteAudioTrack} track - The {@link RemoteAudioTrack} that was
 *   switched on
 * @event RemoteAudioTrack#switchedOn
 */ module.exports = RemoteAudioTrack; //# sourceMappingURL=remoteaudiotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotetrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/trackpublication.js [app-client] (ecmascript)");
/**
 * A {@link RemoteTrackPublication} represents a {@link RemoteTrack} that has
 * been published to a {@link Room}.
 * @extends TrackPublication
 * @property {boolean} isSubscribed - whether the published {@link RemoteTrack}
 *   is subscribed to
 * @property {boolean} isTrackEnabled - whether the published
 *   {@link RemoteTrack} is enabled
 * @property {Track.Kind} kind - kind of the published {@link RemoteTrack}
 * @property {Track.Priority} publishPriority - the {@link Track.Priority} of the published
 *   {@link RemoteTrack} set by the {@link RemoteParticipant}
 * @property {?RemoteTrack} track - Unless you have subscribed to the
 *   {@link RemoteTrack}, this property is null
 * @emits RemoteTrackPublication#publishPriorityChanged
 * @emits RemoteTrackPublication#subscribed
 * @emits RemoteTrackPublication#subscriptionFailed
 * @emits RemoteTrackPublication#trackDisabled
 * @emits RemoteTrackPublication#trackEnabled
 * @emits RemoteTrackPublication#trackSwitchedOff
 * @emits RemoteTrackPublication#trackSwitchedOn
 * @emits RemoteTrackPublication#unsubscribed
 *
 */ var RemoteTrackPublication = function(_super) {
    __extends(RemoteTrackPublication, _super);
    /**
     * Construct a {@link RemoteTrackPublication}.
     * @param {RemoteTrackPublicationSignaling} signaling - {@link RemoteTrackPublication} signaling
     * @param {RemoteTrackPublicationOptions} options - {@link RemoteTrackPublication}
     *   options
     */ function RemoteTrackPublication(signaling, options) {
        var _this = _super.call(this, signaling.name, signaling.sid, options) || this;
        Object.defineProperties(_this, {
            _signaling: {
                value: signaling
            },
            _track: {
                value: null,
                writable: true
            },
            isSubscribed: {
                enumerable: true,
                get: function() {
                    return !!this._track;
                }
            },
            isTrackEnabled: {
                enumerable: true,
                get: function() {
                    return signaling.isEnabled;
                }
            },
            kind: {
                enumerable: true,
                value: signaling.kind
            },
            publishPriority: {
                enumerable: true,
                get: function() {
                    return signaling.priority;
                }
            },
            track: {
                enumerable: true,
                get: function() {
                    return this._track;
                }
            }
        });
        // remember original state, and fire events only on change.
        var error = signaling.error, isEnabled = signaling.isEnabled, isSwitchedOff = signaling.isSwitchedOff, priority = signaling.priority;
        signaling.on('updated', function() {
            if (error !== signaling.error) {
                error = signaling.error;
                _this.emit('subscriptionFailed', signaling.error);
                return;
            }
            if (isEnabled !== signaling.isEnabled) {
                isEnabled = signaling.isEnabled;
                if (_this.track) {
                    _this.track._setEnabled(signaling.isEnabled);
                }
                _this.emit(signaling.isEnabled ? 'trackEnabled' : 'trackDisabled');
            }
            if (isSwitchedOff !== signaling.isSwitchedOff) {
                _this._log.debug(_this.trackSid + ": " + (isSwitchedOff ? 'OFF' : 'ON') + " => " + (signaling.isSwitchedOff ? 'OFF' : 'ON'));
                isSwitchedOff = signaling.isSwitchedOff;
                if (_this.track) {
                    _this.track._setSwitchedOff(signaling.isSwitchedOff);
                    _this.emit(isSwitchedOff ? 'trackSwitchedOff' : 'trackSwitchedOn', _this.track);
                } else if (isSwitchedOff) {
                    _this._log.warn('Track was not subscribed when switched Off.');
                }
            }
            if (priority !== signaling.priority) {
                priority = signaling.priority;
                _this.emit('publishPriorityChanged', priority);
            }
        });
        return _this;
    }
    RemoteTrackPublication.prototype.toString = function() {
        return "[RemoteTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    /**
     * @private
     * @param {RemoteTrack} track
     */ RemoteTrackPublication.prototype._subscribed = function(track) {
        if (!this._track && track) {
            this._track = track;
            this.emit('subscribed', track);
        }
    };
    /**
     * @private
     */ RemoteTrackPublication.prototype._unsubscribe = function() {
        if (this._track) {
            var track = this._track;
            this._track = null;
            this.emit('unsubscribed', track);
        }
    };
    return RemoteTrackPublication;
}(TrackPublication);
/**
 * The {@link RemoteTrack}'s publish {@link Track.Priority} was changed by the
 * {@link RemoteParticipant}.
 * @param {Track.Priority} priority - the {@link RemoteTrack}'s new publish
 *   {@link Track.Priority}; RemoteTrackPublication#publishPriority is also
 *   updated accordingly
 * @event RemoteTrackPublication#publishPriorityChanged
 */ /**
 * Your {@link LocalParticipant} subscribed to the {@link RemoteTrack}.
 * @param {RemoteTrack} track - the {@link RemoteTrack} that was subscribed to
 * @event RemoteTrackPublication#subscribed
 */ /**
 * Your {@link LocalParticipant} failed to subscribe to the {@link RemoteTrack}.
 * @param {TwilioError} error - the reason the {@link RemoteTrack} could not be
 *   subscribed to
 * @event RemoteTrackPublication#subscriptionFailed
 */ /**
 * The {@link RemoteTrack} was disabled.
 * @event RemoteTrackPublication#trackDisabled
 */ /**
 * The {@link RemoteTrack} was enabled.
 * @event RemoteTrackPublication#trackEnabled
 */ /**
 * The {@link RemoteTrack} was switched off.
 * @param {RemoteTrack} track - the {@link RemoteTrack} that was switched off
 * @event RemoteTrackPublication#trackSwitchedOff
 */ /**
 * The {@link RemoteTrack} was switched on.
 * @param {RemoteTrack} track - the {@link RemoteTrack} that was switched on
 * @event RemoteTrackPublication#trackSwitchedOn
 */ /**
 * Your {@link LocalParticipant} unsubscribed from the {@link RemoteTrack}.
 * @param {RemoteTrack} track - the {@link RemoteTrack} that was unsubscribed from
 * @event RemoteTrackPublication#unsubscribed
 */ /**
 * {@link RemoteTrackPublication} options
 * @typedef {object} RemoteTrackPublicationOptions
 * @property {LogLevel|LogLevels} logLevel - Log level for 'media' modules
 */ module.exports = RemoteTrackPublication; //# sourceMappingURL=remotetrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remoteaudiotrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotetrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link RemoteAudioTrackPublication} represents a {@link RemoteAudioTrack}
 * that has been published to a {@link Room}.
 * @property {Track.Kind} kind - "audio"
 * @property {?RemoteAudioTrack} track - unless you have subscribed to the
 *   {@link RemoteAudioTrack}, this property is null
 * @emits RemoteAudioTrackPublication#subscribed
 * @emits RemoteAudioTrackPublication#subscriptionFailed
 * @emits RemoteAudioTrackPublication#trackDisabled
 * @emits RemoteAudioTrackPublication#trackEnabled
 * @emits RemoteAudioTrackPublication#unsubscribed
 */ var RemoteAudioTrackPublication = function(_super) {
    __extends(RemoteAudioTrackPublication, _super);
    /**
     * Construct a {@link RemoteAudioTrackPublication}.
     * @param {RemoteTrackPublicationSignaling} signaling - {@link RemoteTrackPublication} signaling
     * @param {RemoteTrackPublicationOptions} options - {@link RemoteTrackPublication}
     *   options
     */ function RemoteAudioTrackPublication(signaling, options) {
        return _super.call(this, signaling, options) || this;
    }
    RemoteAudioTrackPublication.prototype.toString = function() {
        return "[RemoteAudioTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return RemoteAudioTrackPublication;
}(RemoteTrackPublication);
/**
 * Your {@link LocalParticipant} subscribed to the {@link RemoteAudioTrack}.
 * @param {RemoteAudioTrack} track - the {@link RemoteAudioTrack} that was subscribed to
 * @event RemoteAudioTrackPublication#subscribed
 */ /**
 * Your {@link LocalParticipant} failed to subscribe to the {@link RemoteAudioTrack}.
 * @param {TwilioError} error - the reason the {@link RemoteAudioTrack} could not be
 *   subscribed to
 * @event RemoteAudioTrackPublication#subscriptionFailed
 */ /**
 * The {@link RemoteAudioTrack} was disabled.
 * @event RemoteAudioTrackPublication#trackDisabled
 */ /**
 * The {@link RemoteAudioTrack} was enabled.
 * @event RemoteAudioTrackPublication#trackEnabled
 */ /**
 * Your {@link LocalParticipant} unsubscribed from the {@link RemoteAudioTrack}.
 * @param {RemoteAudioTrack} track - the {@link RemoteAudioTrack} that was unsubscribed from
 * @event RemoteAudioTrackPublication#unsubscribed
 */ module.exports = RemoteAudioTrackPublication; //# sourceMappingURL=remoteaudiotrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotedatatrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var Track = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/index.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), E = _a.typeErrors, trackPriority = _a.trackPriority;
/**
 * A {@link RemoteDataTrack} represents data published to a {@link Room} by a
 * {@link RemoteParticipant}.
 * @extends Track
 * @property {boolean} isEnabled - true
 * @property {boolean} isSubscribed - Whether the {@link RemoteDataTrack} is
 *   subscribed to
 * @property {boolean} isSwitchedOff - Whether the {@link RemoteDataTrack} is
 *   switched off
 * @property {Track.Kind} kind - "data"
 * @property {?number} maxPacketLifeTime - If non-null, this represents a time
 *   limit (in milliseconds) during which data will be transmitted or
 *   retransmitted if not acknowledged on the underlying RTCDataChannel.
 * @property {?number} maxRetransmits - If non-null, this represents the number
 *   of times the data will be retransmitted if not successfully received on the
 *   underlying RTCDataChannel.
 * @property {boolean} ordered - true if data on the {@link RemoteDataTrack} can
 *   be received out-of-order.
 * @property {?Track.Priority} priority - The subscribe priority of the {@link RemoteDataTrack}
 * @property {boolean} reliable - This is true if both
 *   <code>maxPacketLifeTime</code> and <code>maxRetransmits</code> are set to
 *   null. In other words, if this is true, there is no bound on packet lifetime
 *   or the number of retransmits that will be attempted, ensuring "reliable"
 *   transmission.
 * @property {Track.SID} sid - The SID assigned to the {@link RemoteDataTrack}
 * @emits RemoteDataTrack#message
 * @emits RemoteDataTrack#switchedOff
 * @emits RemoteDataTrack#switchedOn
 */ var RemoteDataTrack = function(_super) {
    __extends(RemoteDataTrack, _super);
    /**
     * Construct a {@link RemoteDataTrack} from a {@link DataTrackReceiver}.
     * @param {Track.SID} sid
     * @param {DataTrackReceiver} dataTrackReceiver
     * @param {{log: Log, name: ?string}} options
     */ function RemoteDataTrack(sid, dataTrackReceiver, options) {
        var _this = _super.call(this, dataTrackReceiver.id, 'data', options) || this;
        Object.defineProperties(_this, {
            _isSwitchedOff: {
                value: false,
                writable: true
            },
            _priority: {
                value: null,
                writable: true
            },
            isEnabled: {
                enumerable: true,
                value: true
            },
            isSwitchedOff: {
                enumerable: true,
                get: function() {
                    return this._isSwitchedOff;
                }
            },
            maxPacketLifeTime: {
                enumerable: true,
                value: dataTrackReceiver.maxPacketLifeTime
            },
            maxRetransmits: {
                enumerable: true,
                value: dataTrackReceiver.maxRetransmits
            },
            ordered: {
                enumerable: true,
                value: dataTrackReceiver.ordered
            },
            priority: {
                enumerable: true,
                get: function() {
                    return this._priority;
                }
            },
            reliable: {
                enumerable: true,
                value: dataTrackReceiver.maxPacketLifeTime === null && dataTrackReceiver.maxRetransmits === null
            },
            sid: {
                enumerable: true,
                value: sid
            }
        });
        dataTrackReceiver.on('message', function(data) {
            _this.emit('message', data, _this);
        });
        return _this;
    }
    /**
     * Update the subscriber {@link Track.Priority} of the {@link RemoteDataTrack}.
     * @param {?Track.Priority} priority - the new {@link Track.priority};
     *   Currently setPriority has no effect on data tracks.
     * @returns {this}
     * @throws {RangeError}
     */ RemoteDataTrack.prototype.setPriority = function(priority) {
        var priorityValues = __spreadArray([
            null
        ], __read(Object.values(trackPriority)));
        if (!priorityValues.includes(priority)) {
            // eslint-disable-next-line new-cap
            throw E.INVALID_VALUE('priority', priorityValues);
        }
        // Note: priority has no real effect on the data tracks.
        this._priority = priority;
        return this;
    };
    /**
     * @private
     */ RemoteDataTrack.prototype._setEnabled = function() {
    // Do nothing.
    };
    /**
     * @private
     * @param {boolean} isSwitchedOff
     */ RemoteDataTrack.prototype._setSwitchedOff = function(isSwitchedOff) {
        if (this._isSwitchedOff !== isSwitchedOff) {
            this._isSwitchedOff = isSwitchedOff;
            this.emit(isSwitchedOff ? 'switchedOff' : 'switchedOn', this);
        }
    };
    return RemoteDataTrack;
}(Track);
/**
 * A message was received over the {@link RemoteDataTrack}.
 * @event RemoteDataTrack#message
 * @param {string|ArrayBuffer} data
 * @param {RemoteDataTrack} track - The {@link RemoteDataTrack} that received
 *   the message
 */ /**
 * A {@link RemoteDataTrack} was switched off.
 * @param {RemoteDataTrack} track - The {@link RemoteDataTrack} that was
 *   switched off
 * @event RemoteDataTrack#switchedOff
 */ /**
 * A {@link RemoteDataTrack} was switched on.
 * @param {RemoteDataTrack} track - The {@link RemoteDataTrack} that was
 *   switched on
 * @event RemoteDataTrack#switchedOn
 */ module.exports = RemoteDataTrack; //# sourceMappingURL=remotedatatrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotedatatrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotetrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link RemoteDataTrackPublication} represents a {@link RemoteDataTrack}
 * that has been published to a {@link Room}.
 * @property {Track.Kind} kind - "data"
 * @property {?RemoteDataTrack} track - unless you have subscribed to the
 *   {@link RemoteDataTrack}, this property is null
 * @emits RemoteDataTrackPublication#subscribed
 * @emits RemoteDataTrackPublication#subscriptionFailed
 * @emits RemoteDataTrackPublication#unsubscribed
 */ var RemoteDataTrackPublication = function(_super) {
    __extends(RemoteDataTrackPublication, _super);
    /**
     * Construct a {@link RemoteDataTrackPublication}.
     * @param {RemoteTrackPublicationSignaling} signaling - {@link RemoteTrackPublication} signaling
     * @param {RemoteTrackPublicationOptions} options - {@link RemoteTrackPublication}
     *   options
     */ function RemoteDataTrackPublication(signaling, options) {
        return _super.call(this, signaling, options) || this;
    }
    RemoteDataTrackPublication.prototype.toString = function() {
        return "[RemoteDataTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return RemoteDataTrackPublication;
}(RemoteTrackPublication);
/**
 * Your {@link LocalParticipant} subscribed to the {@link RemoteDataTrack}.
 * @param {RemoteDataTrack} track - the {@link RemoteDataTrack} that was subscribed to
 * @event RemoteDataTrackPublication#subscribed
 */ /**
 * Your {@link LocalParticipant} failed to subscribe to the {@link RemoteDataTrack}.
 * @param {TwilioError} error - the reason the {@link RemoteDataTrack} could not be
 *   subscribed to
 * @event RemoteDataTrackPublication#subscriptionFailed
 */ /**
 * Your {@link LocalParticipant} unsubscribed from the {@link RemoteDataTrack}.
 * @param {RemoteDataTrack} track - the {@link RemoteDataTrack} that was unsubscribed from
 * @event RemoteDataTrackPublication#unsubscribed
 */ module.exports = RemoteDataTrackPublication; //# sourceMappingURL=remotedatatrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotevideotrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var mixinRemoteMediaTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotemediatrack.js [app-client] (ecmascript)");
var VideoTrack = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/videotrack.js [app-client] (ecmascript)");
var documentVisibilityMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/documentvisibilitymonitor.js [app-client] (ecmascript)");
var NullObserver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/nullobserver.js [app-client] (ecmascript)").NullObserver;
var Timeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var RemoteMediaVideoTrack = mixinRemoteMediaTrack(VideoTrack);
var TRACK_TURN_OF_DELAY_MS = 50;
/**
 * A {@link RemoteVideoTrack} represents a {@link VideoTrack} published to a
 * {@link Room} by a {@link RemoteParticipant}.
 * @extends VideoTrack
 * @property {boolean} isEnabled - Whether the {@link RemoteVideoTrack} is enabled
 * @property {boolean} isSwitchedOff - Whether the {@link RemoteVideoTrack} is switched off
 * @property {Track.SID} sid - The {@link RemoteVideoTrack}'s SID
 * @property {?Track.Priority} priority - The subscribe priority of the {@link RemoteVideoTrack}
 * @emits RemoteVideoTrack#dimensionsChanged
 * @emits RemoteVideoTrack#disabled
 * @emits RemoteVideoTrack#enabled
 * @emits RemoteVideoTrack#started
 * @emits RemoteVideoTrack#switchedOff
 * @emits RemoteVideoTrack#switchedOn
 */ var RemoteVideoTrack = function(_super) {
    __extends(RemoteVideoTrack, _super);
    /**
     * Construct a {@link RemoteVideoTrack}.
     * @param {Track.SID} sid - The {@link RemoteVideoTrack}'s SID
     * @param {MediaTrackReceiver} mediaTrackReceiver - A video MediaStreamTrack container
     * @param {boolean} isEnabled - whether the {@link RemoteVideoTrack} is enabled
     * @param {boolean} isSwitchedOff - Whether the {@link RemoteVideoTrack} is switched off
     * @param {function(?Track.Priority): void} setPriority - Set or clear the subscribe
     *  {@link Track.Priority} of the {@link RemoteVideoTrack}
     * @param {function(ClientRenderHint): void} setRenderHint - Set render hints.
     * @param {{log: Log}} options - The {@link RemoteTrack} options
     */ function RemoteVideoTrack(sid, mediaTrackReceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options) {
        var _this = this;
        options = Object.assign({
            clientTrackSwitchOffControl: 'auto',
            contentPreferencesMode: 'auto',
            enableDocumentVisibilityTurnOff: true
        }, options);
        options = Object.assign({
            IntersectionObserver: typeof IntersectionObserver === 'undefined' || options.clientTrackSwitchOffControl !== 'auto' ? NullObserver : IntersectionObserver,
            ResizeObserver: typeof ResizeObserver === 'undefined' || options.contentPreferencesMode !== 'auto' ? NullObserver : ResizeObserver
        }, options);
        _this = _super.call(this, sid, mediaTrackReceiver, isEnabled, isSwitchedOff, setPriority, setRenderHint, options) || this;
        Object.defineProperties(_this, {
            _enableDocumentVisibilityTurnOff: {
                value: options.enableDocumentVisibilityTurnOff === true && options.clientTrackSwitchOffControl === 'auto'
            },
            _documentVisibilityTurnOffCleanup: {
                value: null,
                writable: true
            },
            _clientTrackSwitchOffControl: {
                value: options.clientTrackSwitchOffControl
            },
            _contentPreferencesMode: {
                value: options.contentPreferencesMode
            },
            _invisibleElements: {
                value: new WeakSet()
            },
            _elToPipCallbacks: {
                value: new WeakMap()
            },
            _elToPipWindows: {
                value: new WeakMap()
            },
            _turnOffTimer: {
                value: new Timeout(function() {
                    _this._setRenderHint({
                        enabled: false
                    });
                }, TRACK_TURN_OF_DELAY_MS, false)
            },
            _resizeObserver: {
                value: new options.ResizeObserver(function(entries) {
                    // NOTE(mpatwardhan): we ignore elements in _invisibleElements
                    // to ensure that ResizeObserver does not end-up turning off a track when a fresh Video element is
                    // attached and IntersectionObserver has not had its callback executed yet.
                    var visibleElementResized = entries.find(function(entry) {
                        return !_this._invisibleElements.has(entry.target);
                    });
                    if (visibleElementResized) {
                        maybeUpdateDimensionHint(_this);
                    }
                })
            },
            _intersectionObserver: {
                value: new options.IntersectionObserver(function(entries) {
                    var shouldSetRenderHint = false;
                    entries.forEach(function(entry) {
                        var wasVisible = !_this._invisibleElements.has(entry.target);
                        if (wasVisible !== entry.isIntersecting) {
                            if (entry.isIntersecting) {
                                _this._log.debug('intersectionObserver detected: Off => On');
                                _this._invisibleElements.delete(entry.target);
                            } else {
                                _this._log.debug('intersectionObserver detected: On => Off');
                                _this._invisibleElements.add(entry.target);
                            }
                            shouldSetRenderHint = true;
                        }
                    });
                    if (shouldSetRenderHint) {
                        maybeUpdateEnabledHint(_this);
                        // when visibility of an element changes that may cause the "biggest" element to change,
                        // update dimensions as well. since dimensions are cached and de-duped at signaling layer,
                        // its okay if they got  resent.
                        maybeUpdateDimensionHint(_this);
                    }
                }, {
                    threshold: 0.25
                })
            }
        });
        return _this;
    }
    /**
     * @private
     */ RemoteVideoTrack.prototype._start = function(dummyEl) {
        var result = _super.prototype._start.call(this, dummyEl);
        // NOTE(mpatwardhan): after emitting started, update turn off track if not visible.
        maybeUpdateEnabledHint(this);
        return result;
    };
    /**
     * Request to switch on a {@link RemoteVideoTrack}, This method is applicable only for the group rooms and only when connected with
     * clientTrackSwitchOffControl in video bandwidth profile options set to 'manual'
     * @returns {this}
     */ RemoteVideoTrack.prototype.switchOn = function() {
        if (this._clientTrackSwitchOffControl !== 'manual') {
            throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');
        }
        this._setRenderHint({
            enabled: true
        });
        return this;
    };
    /**
     * Request to switch off a {@link RemoteVideoTrack}, This method is applicable only for the group rooms and only when connected with
     * clientTrackSwitchOffControl in video bandwidth profile options set to 'manual'
     * @returns {this}
     */ RemoteVideoTrack.prototype.switchOff = function() {
        if (this._clientTrackSwitchOffControl !== 'manual') {
            throw new Error('Invalid state. You can call switchOff only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');
        }
        this._setRenderHint({
            enabled: false
        });
        return this;
    };
    /**
     * Set the {@link RemoteVideoTrack}'s content preferences. This method is applicable only for the group rooms and only when connected with
     * videoContentPreferencesMode in video bandwidth profile options set to 'manual'
     * @param {VideoContentPreferences} contentPreferences - requested preferences.
     * @returns {this}
     */ RemoteVideoTrack.prototype.setContentPreferences = function(contentPreferences) {
        if (this._contentPreferencesMode !== 'manual') {
            throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.contentPreferencesMode is set to "manual"');
        }
        if (contentPreferences.renderDimensions) {
            this._setRenderHint({
                renderDimensions: contentPreferences.renderDimensions
            });
        }
        return this;
    };
    RemoteVideoTrack.prototype._unObservePip = function(el) {
        var pipCallbacks = this._elToPipCallbacks.get(el);
        if (pipCallbacks) {
            el.removeEventListener('enterpictureinpicture', pipCallbacks.onEnterPip);
            el.removeEventListener('leavepictureinpicture', pipCallbacks.onLeavePip);
            this._elToPipCallbacks.delete(el);
        }
    };
    RemoteVideoTrack.prototype._observePip = function(el) {
        var _this = this;
        var pipCallbacks = this._elToPipCallbacks.get(el);
        if (!pipCallbacks) {
            var onEnterPip = function(event) {
                return _this._onEnterPip(event, el);
            };
            var onLeavePip = function(event) {
                return _this._onLeavePip(event, el);
            };
            var onResizePip = function(event) {
                return _this._onResizePip(event, el);
            };
            el.addEventListener('enterpictureinpicture', onEnterPip);
            el.addEventListener('leavepictureinpicture', onLeavePip);
            this._elToPipCallbacks.set(el, {
                onEnterPip: onEnterPip,
                onLeavePip: onLeavePip,
                onResizePip: onResizePip
            });
        }
    };
    RemoteVideoTrack.prototype._onEnterPip = function(event, videoEl) {
        this._log.debug('onEnterPip');
        var pipWindow = event.pictureInPictureWindow;
        this._elToPipWindows.set(videoEl, pipWindow);
        var onResizePip = this._elToPipCallbacks.get(videoEl).onResizePip;
        pipWindow.addEventListener('resize', onResizePip);
        maybeUpdateEnabledHint(this);
    };
    RemoteVideoTrack.prototype._onLeavePip = function(event, videoEl) {
        this._log.debug('onLeavePip');
        this._elToPipWindows.delete(videoEl);
        var onResizePip = this._elToPipCallbacks.get(videoEl).onResizePip;
        var pipWindow = event.pictureInPictureWindow;
        pipWindow.removeEventListener('resize', onResizePip);
        maybeUpdateEnabledHint(this);
    };
    RemoteVideoTrack.prototype._onResizePip = function() {
        maybeUpdateDimensionHint(this);
    };
    RemoteVideoTrack.prototype.attach = function(el) {
        var result = _super.prototype.attach.call(this, el);
        if (this._clientTrackSwitchOffControl === 'auto') {
            // start off the element as invisible. will mark it
            // visible (and update render hints) once intersection observer calls back.
            this._invisibleElements.add(result);
        }
        this._intersectionObserver.observe(result);
        this._resizeObserver.observe(result);
        if (this._enableDocumentVisibilityTurnOff) {
            this._documentVisibilityTurnOffCleanup = this._documentVisibilityTurnOffCleanup || setupDocumentVisibilityTurnOff(this);
        }
        this._observePip(result);
        return result;
    };
    RemoteVideoTrack.prototype.detach = function(el) {
        var _this = this;
        var result = _super.prototype.detach.call(this, el);
        var elements = Array.isArray(result) ? result : [
            result
        ];
        elements.forEach(function(element) {
            _this._intersectionObserver.unobserve(element);
            _this._resizeObserver.unobserve(element);
            _this._invisibleElements.delete(element);
            _this._unObservePip(element);
        });
        if (this._attachments.size === 0) {
            if (this._documentVisibilityTurnOffCleanup) {
                this._documentVisibilityTurnOffCleanup();
                this._documentVisibilityTurnOffCleanup = null;
            }
        }
        maybeUpdateEnabledHint(this);
        maybeUpdateDimensionHint(this);
        return result;
    };
    /**
     * Add a {@link VideoProcessor} to allow for custom processing of video frames belonging to a VideoTrack.
     * When a Participant un-publishes and re-publishes a VideoTrack, a new RemoteVideoTrack is created and
     * any VideoProcessors attached to the previous RemoteVideoTrack would have to be re-added again.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to use.
     * @param {AddProcessorOptions} [options] - {@link AddProcessorOptions} to provide.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * const grayscaleProcessor = new GrayScaleProcessor(100);
     *
     * Array.from(room.participants.values()).forEach(participant => {
     *   const remoteVideoTrack = Array.from(participant.videoTracks.values())[0].track;
     *   remoteVideoTrack.addProcessor(grayscaleProcessor);
     * });
     */ RemoteVideoTrack.prototype.addProcessor = function() {
        return _super.prototype.addProcessor.apply(this, arguments);
    };
    /**
     * Remove the previously added {@link VideoProcessor} using `addProcessor` API.
     * @param {VideoProcessor} processor - The {@link VideoProcessor} to remove.
     * @returns {this}
     * @example
     * class GrayScaleProcessor {
     *   constructor(percentage) {
     *     this.percentage = percentage;
     *   }
     *   processFrame(inputFrameBuffer, outputFrameBuffer) {
     *     const context = outputFrameBuffer.getContext('2d');
     *     context.filter = `grayscale(${this.percentage}%)`;
     *     context.drawImage(inputFrameBuffer, 0, 0, inputFrameBuffer.width, inputFrameBuffer.height);
     *   }
     * }
     *
     * const grayscaleProcessor = new GrayScaleProcessor(100);
     *
     * Array.from(room.participants.values()).forEach(participant => {
     *   const remoteVideoTrack = Array.from(participant.videoTracks.values())[0].track;
     *   remoteVideoTrack.addProcessor(grayscaleProcessor);
     * });
     *
     * document.getElementById('remove-button').onclick = () => {
     *   Array.from(room.participants.values()).forEach(participant => {
     *     const remoteVideoTrack = Array.from(participant.videoTracks.values())[0].track;
     *     remoteVideoTrack.removeProcessor(grayscaleProcessor);
     *   });
     * }
     */ RemoteVideoTrack.prototype.removeProcessor = function() {
        return _super.prototype.removeProcessor.apply(this, arguments);
    };
    RemoteVideoTrack.prototype.toString = function() {
        return "[RemoteVideoTrack #" + this._instanceId + ": " + this.sid + "]";
    };
    /**
     * Update the subscribe {@link Track.Priority} of the {@link RemoteVideoTrack}.
     * @param {?Track.Priority} priority - the new subscribe {@link Track.Priority};
     *   If <code>null</code>, then the subscribe {@link Track.Priority} is cleared, which
     *   means the {@link Track.Priority} set by the publisher is now the effective priority.
     * @returns {this}
     * @throws {RangeError}
     */ RemoteVideoTrack.prototype.setPriority = function(priority) {
        return _super.prototype.setPriority.call(this, priority);
    };
    return RemoteVideoTrack;
}(RemoteMediaVideoTrack);
function isAttachedToDocumentPip(remoteVideoTrack) {
    if (!('documentPictureInPicture' in globalThis)) {
        return false;
    }
    var pipWindow = globalThis.documentPictureInPicture.window;
    if (!pipWindow) {
        return false;
    }
    var pipEls = new WeakSet(pipWindow.document.querySelectorAll('video'));
    return remoteVideoTrack._getAllAttachedElements().some(function(el) {
        return pipEls.has(el);
    });
}
function maybeUpdateEnabledHint(remoteVideoTrack) {
    if (remoteVideoTrack._clientTrackSwitchOffControl !== 'auto') {
        return;
    }
    var visibleElements = remoteVideoTrack._getAllAttachedElements().filter(function(el) {
        return !remoteVideoTrack._invisibleElements.has(el);
    });
    var pipWindows = remoteVideoTrack._getAllAttachedElements().filter(function(el) {
        return remoteVideoTrack._elToPipWindows.has(el);
    });
    // even when document is invisible we may have track playing in pip window.
    var enabled = pipWindows.length > 0 || isAttachedToDocumentPip(remoteVideoTrack) || document.visibilityState === 'visible' && visibleElements.length > 0;
    if (enabled === true) {
        remoteVideoTrack._turnOffTimer.clear();
        remoteVideoTrack._setRenderHint({
            enabled: true
        });
    } else if (!remoteVideoTrack._turnOffTimer.isSet) {
        // set the track to be turned off after some delay.
        remoteVideoTrack._turnOffTimer.start();
    }
}
function maybeUpdateDimensionHint(remoteVideoTrack) {
    if (remoteVideoTrack._contentPreferencesMode !== 'auto') {
        return;
    }
    var visibleElements = remoteVideoTrack._getAllAttachedElements().filter(function(el) {
        return !remoteVideoTrack._invisibleElements.has(el);
    });
    var pipElements = remoteVideoTrack._getAllAttachedElements().map(function(el) {
        var pipWindow = remoteVideoTrack._elToPipWindows.get(el);
        return pipWindow ? {
            clientHeight: pipWindow.height,
            clientWidth: pipWindow.width
        } : {
            clientHeight: 0,
            clientWidth: 0
        };
    });
    var totalElements = visibleElements.concat(pipElements);
    if (totalElements.length > 0) {
        var _a = __read(totalElements.sort(function(el1, el2) {
            return el2.clientHeight + el2.clientWidth - el1.clientHeight - el1.clientWidth - 1;
        }), 1), _b = _a[0], clientHeight = _b.clientHeight, clientWidth = _b.clientWidth;
        var renderDimensions = {
            height: clientHeight,
            width: clientWidth
        };
        remoteVideoTrack._setRenderHint({
            renderDimensions: renderDimensions
        });
    }
}
function setupDocumentVisibilityTurnOff(removeVideoTrack) {
    function onVisibilityChanged() {
        maybeUpdateEnabledHint(removeVideoTrack);
    }
    documentVisibilityMonitor.onVisibilityChange(1, onVisibilityChanged);
    return function() {
        documentVisibilityMonitor.offVisibilityChange(1, onVisibilityChanged);
    };
}
/**
 * @typedef {object} VideoContentPreferences
 * @property {VideoTrack.Dimensions} [renderDimensions] - Render Dimensions to request for the {@link RemoteVideoTrack}.
 */ /**
 * The {@link RemoteVideoTrack}'s dimensions changed.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} whose
 *   dimensions changed
 * @event RemoteVideoTrack#dimensionsChanged
 */ /**
 * The {@link RemoteVideoTrack} was disabled, i.e. "paused".
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} that was
 *   disabled
 * @event RemoteVideoTrack#disabled
 */ /**
 * The {@link RemoteVideoTrack} was enabled, i.e. "resumed".
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} that was
 *   enabled
 * @event RemoteVideoTrack#enabled
 */ /**
 * The {@link RemoteVideoTrack} started. This means there is enough video data
 * to begin playback.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} that started
 * @event RemoteVideoTrack#started
 */ /**
 * A {@link RemoteVideoTrack} was switched off.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} that was
 *   switched off
 * @event RemoteVideoTrack#switchedOff
 */ /**
 * A {@link RemoteVideoTrack} was switched on.
 * @param {RemoteVideoTrack} track - The {@link RemoteVideoTrack} that was
 *   switched on
 * @event RemoteVideoTrack#switchedOn
 */ module.exports = RemoteVideoTrack; //# sourceMappingURL=remotevideotrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/remotevideotrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackPublication = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/remotetrackpublication.js [app-client] (ecmascript)");
/**
 * A {@link RemoteVideoTrackPublication} represents a {@link RemoteVideoTrack}
 * that has been published to a {@link Room}.
 * @property {Track.Kind} kind - "video"
 * @property {?RemoteVideoTrack} track - unless you have subscribed to the
 *   {@link RemoteVideoTrack}, this property is null
 * @emits RemoteVideoTrackPublication#subscribed
 * @emits RemoteVideoTrackPublication#subscriptionFailed
 * @emits RemoteVideoTrackPublication#trackDisabled
 * @emits RemoteVideoTrackPublication#trackEnabled
 * @emits RemoteVideoTrackPublication#unsubscribed
 */ var RemoteVideoTrackPublication = function(_super) {
    __extends(RemoteVideoTrackPublication, _super);
    /**
     * Construct a {@link RemoteVideoTrackPublication}.
     * @param {RemoteTrackPublicationSignaling} signaling - {@link RemoteTrackPublication} signaling
     * @param {RemoteTrackPublicationOptions} options - {@link RemoteTrackPublication}
     *   options
     */ function RemoteVideoTrackPublication(signaling, options) {
        return _super.call(this, signaling, options) || this;
    }
    RemoteVideoTrackPublication.prototype.toString = function() {
        return "[RemoteVideoTrackPublication #" + this._instanceId + ": " + this.trackSid + "]";
    };
    return RemoteVideoTrackPublication;
}(RemoteTrackPublication);
/**
 * Your {@link LocalParticipant} subscribed to the {@link RemoteVideoTrack}.
 * @param {RemoteVideoTrack} track - the {@link RemoteVideoTrack} that was subscribed to
 * @event RemoteVideoTrackPublication#subscribed
 */ /**
 * Your {@link LocalParticipant} failed to subscribe to the {@link RemoteVideoTrack}.
 * @param {TwilioError} error - the reason the {@link RemoteVideoTrack} could not be
 *   subscribed to
 * @event RemoteVideoTrackPublication#subscriptionFailed
 */ /**
 * The {@link RemoteVideoTrack} was disabled.
 * @event RemoteVideoTrackPublication#trackDisabled
 */ /**
 * The {@link RemoteVideoTrack} was enabled.
 * @event RemoteVideoTrackPublication#trackEnabled
 */ /**
 * Your {@link LocalParticipant} unsubscribed from the {@link RemoteVideoTrack}.
 * @param {RemoteVideoTrack} track - the {@link RemoteVideoTrack} that was unsubscribed from
 * @event RemoteVideoTrackPublication#unsubscribed
 */ module.exports = RemoteVideoTrackPublication; //# sourceMappingURL=remotevideotrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/media/track/receiver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaTrackTransceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/transceiver.js [app-client] (ecmascript)");
/**
 * A {@link MediaTrackReceiver} represents a remote MediaStreamTrack.
 * @extends MediaTrackTransceiver
 */ var MediaTrackReceiver = function(_super) {
    __extends(MediaTrackReceiver, _super);
    /**
     * Construct a {@link MediaTrackReceiver}.
     * @param {Track.ID} id - The MediaStreamTrack ID signaled through RSP/SDP
     * @param {MediaStreamTrack} mediaStreamTrack - The remote MediaStreamTrack
     */ function MediaTrackReceiver(id, mediaStreamTrack) {
        return _super.call(this, id, mediaStreamTrack) || this;
    }
    return MediaTrackReceiver;
}(MediaTrackTransceiver);
module.exports = MediaTrackReceiver; //# sourceMappingURL=receiver.js.map
}}),
}]);

//# sourceMappingURL=node_modules_twilio-video_es5_media_track_94300d3f._.js.map