(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/twilio-video/es5/signaling/v2/icebox.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Filter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/filter.js [app-client] (ecmascript)");
/**
 * An {@link IceBox} stores trickled ICE candidates. Candidates added to the
 * {@link IceBox} via {@link IceBox#update} are compared against previously
 * trickled candidates and only new candidates will be returned (assuming they
 * match the current ICE username fragment set by {@link IceBox#setUfrag}).
 * @property {?string} ufrag
 */ var IceBox = function() {
    /**
     * Construct an {@link IceBox}.
     */ function IceBox() {
        Object.defineProperties(this, {
            _filter: {
                value: new Filter({
                    getKey: function getKey(iceState) {
                        return iceState.ufrag;
                    },
                    isLessThanOrEqualTo: function isLessThanOrEqualTo(a, b) {
                        return a.revision <= b.revision;
                    }
                })
            },
            _ufrag: {
                writable: true,
                value: null
            },
            ufrag: {
                enumerable: true,
                get: function() {
                    return this._ufrag;
                }
            }
        });
    }
    /**
     * Set the ICE username fragment on the {@link IceBox}. This method returns any
     * ICE candidates associated with the username fragment.
     * @param {string} ufrag
     * @returns {Array<RTCIceCandidateInit>}
     */ IceBox.prototype.setUfrag = function(ufrag) {
        this._ufrag = ufrag;
        var ice = this._filter.toMap().get(ufrag);
        return ice ? ice.candidates : [];
    };
    /**
     * Update the {@link IceBox}. This method returns any new ICE candidates
     * associated with the current username fragment.
     * @param {object} iceState
     * @returns {Array<RTCIceCandidateInit>}
     */ IceBox.prototype.update = function(iceState) {
        // NOTE(mroberts): The Server sometimes does not set the candidates property.
        iceState.candidates = iceState.candidates || [];
        var oldIceState = this._filter.toMap().get(iceState.ufrag);
        var oldCandidates = oldIceState ? oldIceState.candidates : [];
        return this._filter.update(iceState) && this._ufrag === iceState.ufrag ? iceState.candidates.slice(oldCandidates.length) : [];
    };
    return IceBox;
}();
module.exports = IceBox; //# sourceMappingURL=icebox.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/iceconnectionmonitor.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), ICE_ACTIVITY_CHECK_PERIOD_MS = _a.ICE_ACTIVITY_CHECK_PERIOD_MS, ICE_INACTIVITY_THRESHOLD_MS = _a.ICE_INACTIVITY_THRESHOLD_MS;
/**
 * Monitors a {@link RTCPeerConnection}'s stats and notifies
 * caller when inactivity is detected.
 */ var IceConnectionMonitor = function() {
    /**
     * Construct an {@link IceConnectionMonitor}.
     * @param {RTCPeerConnection} peerConnection
     * @param {object} [options]
     */ function IceConnectionMonitor(peerConnection, options) {
        options = Object.assign({
            activityCheckPeriodMs: ICE_ACTIVITY_CHECK_PERIOD_MS,
            inactivityThresholdMs: ICE_INACTIVITY_THRESHOLD_MS
        }, options);
        Object.defineProperties(this, {
            _activityCheckPeriodMs: {
                value: options.activityCheckPeriodMs
            },
            _inactivityThresholdMs: {
                value: options.inactivityThresholdMs
            },
            _lastActivity: {
                value: null,
                writable: true
            },
            _peerConnection: {
                value: peerConnection
            },
            _timer: {
                value: null,
                writable: true
            },
            _onIceConnectionStateChanged: {
                value: null,
                writable: true
            }
        });
    }
    IceConnectionMonitor.prototype._getActivePairStat = function(stats) {
        var statsArray = Array.from(stats.values());
        var activePairStats = statsArray.find(function(stat) {
            return stat.type === 'candidate-pair' && stat.nominated;
        });
        // NOTE(mpatwardhan): sometimes (JSDK-2667) after getting disconnected while switching network
        // we may not find active pair. Treat this as 0 bytesReceived so that we count it towards inactivity.
        return activePairStats || {
            bytesReceived: 0,
            timestamp: Math.round(new Date().getTime())
        };
    };
    /**
     * Get ICE connection stats, and extract received and send bytes.
     * @returns Promise<?RTCIceCandidatePairStats>
     */ IceConnectionMonitor.prototype._getIceConnectionStats = function() {
        var _this = this;
        return this._peerConnection.getStats().then(function(stats) {
            return _this._getActivePairStat(stats);
        }).catch(function() {
            return null;
        });
    };
    /**
     * schedules/un-schedules inactivity callback.
     */ IceConnectionMonitor.prototype._scheduleInactivityCallback = function(callback) {
        var _this = this;
        if (callback && this._onIceConnectionStateChanged === null) {
            // schedule callback
            this._onIceConnectionStateChanged = function() {
                if (_this._peerConnection.iceConnectionState === 'disconnected') {
                    // eslint-disable-next-line callback-return
                    callback();
                }
            };
            if (this._peerConnection.addEventListener) {
                this._peerConnection.addEventListener('iceconnectionstatechange', this._onIceConnectionStateChanged);
            } else {
                this._peerConnection.oniceconnectionstatechange = this._onIceConnectionStateChanged;
            }
        } else if (!callback && this._onIceConnectionStateChanged) {
            // unschedule callback
            if (this._peerConnection.removeEventListener) {
                this._peerConnection.removeEventListener('iceconnectionstatechange', this._onIceConnectionStateChanged);
            } else {
                this._peerConnection.oniceconnectionstatechange = null;
            }
            this._onIceConnectionStateChanged = null;
        }
    };
    /**
     * Start monitoring the ICE connection.
     * Monitors bytes received on active ice connection pair,
     * invokes onIceConnectionInactive when inactivity is detected.
     * @param {function} onIceConnectionInactive
     */ IceConnectionMonitor.prototype.start = function(onIceConnectionInactive) {
        var _this = this;
        this.stop();
        this._timer = setInterval(function() {
            _this._getIceConnectionStats().then(function(iceStats) {
                if (!iceStats) {
                    return;
                }
                // NOTE(mpatwardhan): We look at bytesReceived on active candidate pair as an indication of active ice connection.
                // As per spec (https://w3c.github.io/webrtc-stats/#dom-rtcicecandidatepairstats-bytesreceived) this value
                // includes RTCP traffic and is +ve even when there are no tracks subscribed to.
                if (!_this._lastActivity || _this._lastActivity.bytesReceived !== iceStats.bytesReceived) {
                    _this._lastActivity = iceStats;
                    // detected activity, cancel scheduled callback if any.
                    _this._scheduleInactivityCallback(null);
                }
                if (iceStats.timestamp - _this._lastActivity.timestamp >= _this._inactivityThresholdMs) {
                    // detected inactivity.
                    if (_this._peerConnection.iceConnectionState === 'disconnected') {
                        onIceConnectionInactive();
                    } else if (_this._onIceConnectionStateChanged === null) {
                        _this._scheduleInactivityCallback(onIceConnectionInactive);
                    }
                }
            });
        }, this._activityCheckPeriodMs);
    };
    /**
     * Stop monitoring the ICE connection state.
     * @returns {void}
     */ IceConnectionMonitor.prototype.stop = function() {
        this._scheduleInactivityCallback(null);
        if (this._timer !== null) {
            clearInterval(this._timer);
            this._timer = null;
            this._lastActivity = null;
        }
    };
    return IceConnectionMonitor;
}();
module.exports = IceConnectionMonitor; //# sourceMappingURL=iceconnectionmonitor.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/peerconnection.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var DefaultBackoff = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/backoff.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)"), DefaultRTCIceCandidate = _a.RTCIceCandidate, DefaultRTCPeerConnection = _a.RTCPeerConnection, DefaultRTCSessionDescription = _a.RTCSessionDescription, getStatistics = _a.getStats;
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)");
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), DEFAULT_ICE_GATHERING_TIMEOUT_MS = _b.DEFAULT_ICE_GATHERING_TIMEOUT_MS, DEFAULT_LOG_LEVEL = _b.DEFAULT_LOG_LEVEL, DEFAULT_SESSION_TIMEOUT_SEC = _b.DEFAULT_SESSION_TIMEOUT_SEC, iceRestartBackoffConfig = _b.iceRestartBackoffConfig;
var _c = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/index.js [app-client] (ecmascript)"), addOrRewriteNewTrackIds = _c.addOrRewriteNewTrackIds, addOrRewriteTrackIds = _c.addOrRewriteTrackIds, createCodecMapForMediaSection = _c.createCodecMapForMediaSection, disableRtx = _c.disableRtx, enableDtxForOpus = _c.enableDtxForOpus, filterLocalCodecs = _c.filterLocalCodecs, getMediaSections = _c.getMediaSections, removeSSRCAttributes = _c.removeSSRCAttributes, revertSimulcast = _c.revertSimulcast, setCodecPreferences = _c.setCodecPreferences, setSimulcast = _c.setSimulcast;
var DefaultTimeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var _d = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), MediaClientLocalDescFailedError = _d.MediaClientLocalDescFailedError, MediaClientRemoteDescFailedError = _d.MediaClientRemoteDescFailedError;
var _e = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), buildLogLevels = _e.buildLogLevels, getPlatform = _e.getPlatform, isChromeScreenShareTrack = _e.isChromeScreenShareTrack, oncePerTick = _e.oncePerTick, defer = _e.defer;
var IceBox = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/icebox.js [app-client] (ecmascript)");
var DefaultIceConnectionMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/iceconnectionmonitor.js [app-client] (ecmascript)");
var DataTrackReceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/data/receiver.js [app-client] (ecmascript)");
var MediaTrackReceiver = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/receiver.js [app-client] (ecmascript)");
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var TrackMatcher = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/trackmatcher.js [app-client] (ecmascript)");
var workaroundIssue8329 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/issue8329.js [app-client] (ecmascript)");
var guess = util.guessBrowser();
var platform = getPlatform();
var isAndroid = /android/.test(platform);
var isChrome = guess === 'chrome';
var isFirefox = guess === 'firefox';
var isSafari = guess === 'safari';
var nInstances = 0;
/*
PeerConnectionV2 States
-----------------------

    +------+    +--------+
    |      |    |        |
    | open |--->| closed |
    |      |    |        |
    +------+    +--------+
      |  ^          ^
      |  |          |
      |  |          |
      v  |          |
  +----------+      |
  |          |      |
  | updating |------+
  |          |
  +----------+

*/ var states = {
    open: [
        'closed',
        'updating'
    ],
    updating: [
        'closed',
        'open'
    ],
    closed: []
};
/**
 * @extends StateMachine
 * @property {id}
 * @emits PeerConnectionV2#connectionStateChanged
 * @emits PeerConnectionV2#iceConnectionStateChanged
 * @emits PeerConnectionV2#candidates
 * @emits PeerConnectionV2#description
 */ var PeerConnectionV2 = function(_super) {
    __extends(PeerConnectionV2, _super);
    /**
     * Construct a {@link PeerConnectionV2}.
     * @param {string} id
     * @param {EncodingParametersImpl} encodingParameters
     * @param {PreferredCodecs} preferredCodecs
     * @param {object} [options]
     */ function PeerConnectionV2(id, encodingParameters, preferredCodecs, options) {
        var _this = _super.call(this, 'open', states) || this;
        options = Object.assign({
            enableDscp: false,
            dummyAudioMediaStreamTrack: null,
            isChromeScreenShareTrack: isChromeScreenShareTrack,
            iceServers: [],
            logLevel: DEFAULT_LOG_LEVEL,
            offerOptions: {},
            revertSimulcast: revertSimulcast,
            sessionTimeout: DEFAULT_SESSION_TIMEOUT_SEC * 1000,
            setCodecPreferences: setCodecPreferences,
            setSimulcast: setSimulcast,
            Backoff: DefaultBackoff,
            IceConnectionMonitor: DefaultIceConnectionMonitor,
            RTCIceCandidate: DefaultRTCIceCandidate,
            RTCPeerConnection: DefaultRTCPeerConnection,
            RTCSessionDescription: DefaultRTCSessionDescription,
            Timeout: DefaultTimeout
        }, options);
        // NOTE(lrivas): We intentionally include the options object for backward compatibility.
        // The ConnectOptions 'iceServers' and 'iceTransportPolicy' are part of the 'RTCConfiguration' interface and should be passed
        // as 'rtcConfiguration.iceServers' and 'rtcConfiguration.iceTransportPolicy' respectively.
        var configuration = getConfiguration(Object.assign({}, options, options.rtcConfiguration));
        var logLevels = buildLogLevels(options.logLevel);
        var RTCPeerConnection = options.RTCPeerConnection;
        if (options.enableDscp === true) {
            options.chromeSpecificConstraints = options.chromeSpecificConstraints || {};
            options.chromeSpecificConstraints.optional = options.chromeSpecificConstraints.optional || [];
            options.chromeSpecificConstraints.optional.push({
                googDscp: true
            });
        }
        var log = options.log ? options.log.createLog('webrtc', _this) : new Log('webrtc', _this, logLevels, options.loggerName);
        var peerConnection = new RTCPeerConnection(configuration, options.chromeSpecificConstraints);
        if (options.dummyAudioMediaStreamTrack) {
            peerConnection.addTrack(options.dummyAudioMediaStreamTrack);
        }
        Object.defineProperties(_this, {
            _appliedTrackIdsToAttributes: {
                value: new Map(),
                writable: true
            },
            _dataChannels: {
                value: new Map()
            },
            _dataTrackReceivers: {
                value: new Set()
            },
            _descriptionRevision: {
                writable: true,
                value: 0
            },
            _didGenerateLocalCandidates: {
                writable: true,
                value: false
            },
            _enableDscp: {
                value: options.enableDscp
            },
            _encodingParameters: {
                value: encodingParameters
            },
            _isChromeScreenShareTrack: {
                value: options.isChromeScreenShareTrack
            },
            _iceGatheringFailed: {
                value: false,
                writable: true
            },
            _iceGatheringTimeout: {
                value: new options.Timeout(function() {
                    return _this._handleIceGatheringTimeout();
                }, DEFAULT_ICE_GATHERING_TIMEOUT_MS, false)
            },
            _iceRestartBackoff: {
                // eslint-disable-next-line new-cap
                value: new options.Backoff(iceRestartBackoffConfig)
            },
            _instanceId: {
                value: ++nInstances
            },
            _isIceConnectionInactive: {
                writable: true,
                value: false
            },
            _isIceLite: {
                writable: true,
                value: false
            },
            _isIceRestartBackoffInProgress: {
                writable: true,
                value: false
            },
            _isRestartingIce: {
                writable: true,
                value: false
            },
            _lastIceConnectionState: {
                writable: true,
                value: null
            },
            _lastStableDescriptionRevision: {
                writable: true,
                value: 0
            },
            _localCandidates: {
                writable: true,
                value: []
            },
            _localCodecs: {
                value: new Set()
            },
            _localCandidatesRevision: {
                writable: true,
                value: 1
            },
            _localDescriptionWithoutSimulcast: {
                writable: true,
                value: null
            },
            _localDescription: {
                writable: true,
                value: null
            },
            _localUfrag: {
                writable: true,
                value: null
            },
            _log: {
                value: log
            },
            _eventObserver: {
                value: options.eventObserver
            },
            _remoteCodecMaps: {
                value: new Map()
            },
            _rtpSenders: {
                value: new Map()
            },
            _rtpNewSenders: {
                value: new Set()
            },
            _iceConnectionMonitor: {
                value: new options.IceConnectionMonitor(peerConnection)
            },
            _mediaTrackReceivers: {
                value: new Set()
            },
            _needsAnswer: {
                writable: true,
                value: false
            },
            _negotiationRole: {
                writable: true,
                value: null
            },
            _offerOptions: {
                writable: true,
                value: options.offerOptions
            },
            _onEncodingParametersChanged: {
                value: oncePerTick(function() {
                    if (!_this._needsAnswer) {
                        updateEncodingParameters(_this);
                    }
                })
            },
            _peerConnection: {
                value: peerConnection
            },
            _preferredAudioCodecs: {
                value: preferredCodecs.audio
            },
            _preferredVideoCodecs: {
                value: preferredCodecs.video
            },
            _shouldApplyDtx: {
                value: preferredCodecs.audio.every(function(_a) {
                    var codec = _a.codec;
                    return codec !== 'opus';
                }) || preferredCodecs.audio.some(function(_a) {
                    var codec = _a.codec, dtx = _a.dtx;
                    return codec === 'opus' && dtx;
                })
            },
            _queuedDescription: {
                writable: true,
                value: null
            },
            _iceReconnectTimeout: {
                value: new options.Timeout(function() {
                    log.debug('ICE reconnect timed out');
                    _this.close();
                }, options.sessionTimeout, false)
            },
            _recycledTransceivers: {
                value: {
                    audio: [],
                    video: []
                }
            },
            _replaceTrackPromises: {
                value: new Map()
            },
            _remoteCandidates: {
                writable: true,
                value: new IceBox()
            },
            _setCodecPreferences: {
                // NOTE(mmalavalli): Re-ordering payload types in order to make sure a non-H264
                // preferred codec is selected does not work on Android Firefox due to this behavior:
                // https://bugzilla.mozilla.org/show_bug.cgi?id=1683258. So, we work around this by
                // not applying any non-H264 preferred video codec.
                value: isFirefox && isAndroid && preferredCodecs.video[0] && preferredCodecs.video[0].codec.toLowerCase() !== 'h264' ? function(sdp) {
                    return sdp;
                } : options.setCodecPreferences
            },
            _setSimulcast: {
                value: options.setSimulcast
            },
            _revertSimulcast: {
                value: options.revertSimulcast
            },
            _RTCIceCandidate: {
                value: options.RTCIceCandidate
            },
            _RTCPeerConnection: {
                value: options.RTCPeerConnection
            },
            _RTCSessionDescription: {
                value: options.RTCSessionDescription
            },
            _shouldOffer: {
                writable: true,
                value: false
            },
            _shouldRestartIce: {
                writable: true,
                value: false
            },
            _trackIdsToAttributes: {
                value: new Map(),
                writable: true
            },
            _trackMatcher: {
                writable: true,
                value: null
            },
            _mediaTrackSenderToPublisherHints: {
                value: new Map()
            },
            id: {
                enumerable: true,
                value: id
            }
        });
        encodingParameters.on('changed', _this._onEncodingParametersChanged);
        peerConnection.addEventListener('connectionstatechange', _this._handleConnectionStateChange.bind(_this));
        peerConnection.addEventListener('datachannel', _this._handleDataChannelEvent.bind(_this));
        peerConnection.addEventListener('icecandidate', _this._handleIceCandidateEvent.bind(_this));
        peerConnection.addEventListener('iceconnectionstatechange', _this._handleIceConnectionStateChange.bind(_this));
        peerConnection.addEventListener('icegatheringstatechange', _this._handleIceGatheringStateChange.bind(_this));
        peerConnection.addEventListener('signalingstatechange', _this._handleSignalingStateChange.bind(_this));
        peerConnection.addEventListener('track', _this._handleTrackEvent.bind(_this));
        var self = _this;
        _this.on('stateChanged', function stateChanged(state) {
            if (state !== 'closed') {
                return;
            }
            self.removeListener('stateChanged', stateChanged);
            self._dataChannels.forEach(function(dataChannel, dataTrackSender) {
                self.removeDataTrackSender(dataTrackSender);
            });
        });
        return _this;
    }
    PeerConnectionV2.prototype.toString = function() {
        return "[PeerConnectionV2 #" + this._instanceId + ": " + this.id + "]";
    };
    PeerConnectionV2.prototype.setEffectiveAdaptiveSimulcast = function(effectiveAdaptiveSimulcast) {
        this._log.debug('Setting setEffectiveAdaptiveSimulcast: ', effectiveAdaptiveSimulcast);
        // clear adaptive simulcast from codec preferences if it was set.
        this._preferredVideoCodecs.forEach(function(cs) {
            if ('adaptiveSimulcast' in cs) {
                cs.adaptiveSimulcast = effectiveAdaptiveSimulcast;
            }
        });
    };
    Object.defineProperty(PeerConnectionV2.prototype, "_shouldApplySimulcast", {
        get: function() {
            if (!isChrome && !isSafari) {
                return false;
            }
            // adaptiveSimulcast is set to false after connected message is received if other party does not support it.
            var simulcast = this._preferredVideoCodecs.some(function(cs) {
                return cs.codec.toLowerCase() === 'vp8' && cs.simulcast && cs.adaptiveSimulcast !== false;
            });
            return simulcast;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PeerConnectionV2.prototype, "connectionState", {
        /**
         * The {@link PeerConnectionV2}'s underlying RTCPeerConnection's RTCPeerConnectionState
         * if supported by the browser, its RTCIceConnectionState otherwise.
         * @property {RTCPeerConnectionState}
         */ get: function() {
            return this.iceConnectionState === 'failed' ? 'failed' : this._peerConnection.connectionState || this.iceConnectionState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PeerConnectionV2.prototype, "iceConnectionState", {
        /**
         * The {@link PeerConnectionV2}'s underlying RTCPeerConnection's
         * RTCIceConnectionState.
         * @property {RTCIceConnectionState}
         */ get: function() {
            return this._isIceConnectionInactive && this._peerConnection.iceConnectionState === 'disconnected' || this._iceGatheringFailed ? 'failed' : this._peerConnection.iceConnectionState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PeerConnectionV2.prototype, "isApplicationSectionNegotiated", {
        /**
         * Whether the {@link PeerConnectionV2} has negotiated or is in the process
         * of negotiating the application m= section.
         * @returns {boolean}
         */ get: function() {
            if (this._peerConnection.signalingState !== 'closed') {
                // accessing .localDescription in 'closed' state causes it throw exceptions.
                return this._peerConnection.localDescription ? getMediaSections(this._peerConnection.localDescription.sdp, 'application').length > 0 : false;
            }
            return true;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PeerConnectionV2.prototype, "_isAdaptiveSimulcastEnabled", {
        /**
         * Whether adaptive simulcast is enabled.
         * @returns {boolean}
         */ get: function() {
            var adaptiveSimulcastEntry = this._preferredVideoCodecs.find(function(cs) {
                return 'adaptiveSimulcast' in cs;
            });
            return adaptiveSimulcastEntry && adaptiveSimulcastEntry.adaptiveSimulcast === true;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * @param {MediaStreamTrack} track
     * @param {Array<RTCRtpEncodingParameters>} encodings
     * @param {boolean} trackReplaced
     * @returns {boolean} true if encodings were updated.
     */ PeerConnectionV2.prototype._maybeUpdateEncodings = function(track, encodings, trackReplaced) {
        if (trackReplaced === void 0) {
            trackReplaced = false;
        }
        if (track.kind !== 'video' || track.readyState === 'ended') {
            return false;
        }
        // NOTE(mmalavalli): There is no guarantee that CanvasCaptureMediaStreamTracks will always have "width" and "height"
        // in their settings. So, we don't update the encodings if they are not present.
        // Chromium bug: https://bugs.chromium.org/p/chromium/issues/detail?id=1367082
        var _a = track.getSettings(), height = _a.height, width = _a.width;
        if (typeof height !== 'number' || typeof width !== 'number') {
            return false;
        }
        // Note(mpatwardhan): always configure encodings for safari.
        // for chrome only when adaptive simulcast enabled.
        var browser = util.guessBrowser();
        if (browser === 'safari' || browser === 'chrome' && this._isAdaptiveSimulcastEnabled) {
            this._updateEncodings(track, encodings, trackReplaced);
            return true;
        }
        return false;
    };
    /**
     * Configures with default encodings depending on track type and resolution.
     * Default configuration sets some encodings to disabled, and for others set scaleResolutionDownBy
     * values. When trackReplaced is set to true, it will clear 'active' for any encodings that
     * needs to be enabled.
     * @param {MediaStreamTrack} track
     * @param {Array<RTCRtpEncodingParameters>} encodings
     * @param {boolean} trackReplaced
     */ PeerConnectionV2.prototype._updateEncodings = function(track, encodings, trackReplaced) {
        if (this._isChromeScreenShareTrack(track)) {
            var screenShareActiveLayerConfig_1 = [
                {
                    scaleResolutionDownBy: 1
                },
                {
                    scaleResolutionDownBy: 1
                }
            ];
            encodings.forEach(function(encoding, i) {
                var activeLayerConfig = screenShareActiveLayerConfig_1[i];
                if (activeLayerConfig) {
                    encoding.scaleResolutionDownBy = activeLayerConfig.scaleResolutionDownBy;
                    if (trackReplaced) {
                        delete encoding.active;
                    }
                } else {
                    encoding.active = false;
                    delete encoding.scaleResolutionDownBy;
                }
            });
        } else {
            var _a = track.getSettings(), width = _a.width, height = _a.height;
            // NOTE(mpatwardhan): for non-screen share tracks
            // enable layers depending on track resolutions
            var pixelsToMaxActiveLayers = [
                {
                    pixels: 960 * 540,
                    maxActiveLayers: 3
                },
                {
                    pixels: 480 * 270,
                    maxActiveLayers: 2
                },
                {
                    pixels: 0,
                    maxActiveLayers: 1
                }
            ];
            var trackPixels_1 = width * height;
            var activeLayersInfo = pixelsToMaxActiveLayers.find(function(layer) {
                return trackPixels_1 >= layer.pixels;
            });
            var activeLayers_1 = Math.min(encodings.length, activeLayersInfo.maxActiveLayers);
            encodings.forEach(function(encoding, i) {
                var enabled = i < activeLayers_1;
                if (enabled) {
                    encoding.scaleResolutionDownBy = 1 << activeLayers_1 - i - 1;
                    if (trackReplaced) {
                        encoding.active = true;
                    }
                } else {
                    encoding.active = false;
                    delete encoding.scaleResolutionDownBy;
                }
            });
        }
        this._log.debug('_updateEncodings:', encodings.map(function(_a, i) {
            var active = _a.active, scaleResolutionDownBy = _a.scaleResolutionDownBy;
            return "[" + i + ": " + active + ", " + (scaleResolutionDownBy || 0) + "]";
        }).join(', '));
    };
    /**
     * Add an ICE candidate to the {@link PeerConnectionV2}.
     * @private
     * @param {object} candidate
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._addIceCandidate = function(candidate) {
        var _this = this;
        return Promise.resolve().then(function() {
            candidate = new _this._RTCIceCandidate(candidate);
            return _this._peerConnection.addIceCandidate(candidate);
        }).catch(function(error) {
            // NOTE(mmalavalli): Firefox 68+ now generates an RTCIceCandidate with an
            // empty candidate string to signal end-of-candidates, followed by a null
            // candidate. As of now, Chrome and Safari reject this RTCIceCandidate. Since
            // this does not affect the media connection between Firefox 68+ and Chrome/Safari
            // in Peer-to-Peer Rooms, we suppress the Error and log a warning message.
            //
            // Chrome bug: https://bugs.chromium.org/p/chromium/issues/detail?id=978582
            //
            _this._log.warn("Failed to add RTCIceCandidate " + (candidate ? "\"" + candidate.candidate + "\"" : 'null') + ": " + error.message);
        });
    };
    /**
     * Add ICE candidates to the {@link PeerConnectionV2}.
     * @private
     * @param {Array<object>} candidates
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._addIceCandidates = function(candidates) {
        return Promise.all(candidates.map(this._addIceCandidate, this)).then(function() {});
    };
    /**
     * Add a new RTCRtpTransceiver or update an existing RTCRtpTransceiver for the
     * given MediaStreamTrack.
     * @private
     * @param {MediaStreamTrack} track
     * @returns {RTCRtpTransceiver}
     */ PeerConnectionV2.prototype._addOrUpdateTransceiver = function(track) {
        var _this = this;
        var transceiver = takeRecycledTransceiver(this, track.kind);
        if (transceiver && transceiver.sender) {
            var oldTrackId = transceiver.sender.track ? transceiver.sender.track.id : null;
            if (oldTrackId) {
                this._log.warn("Reusing transceiver: " + transceiver.mid + "] " + oldTrackId + " => " + track.id);
            }
            // NOTE(mpatwardhan):remember this transceiver while we replace track.
            // we recycle transceivers that are not in use after 'negotiationCompleted', but we want to prevent
            // this one from getting recycled while replaceTrack is pending.
            this._replaceTrackPromises.set(transceiver, transceiver.sender.replaceTrack(track).then(function() {
                transceiver.direction = 'sendrecv';
            }, function() {
            // Do nothing.
            }).finally(function() {
                _this._replaceTrackPromises.delete(transceiver);
            }));
            return transceiver;
        }
        // TODO(lrivas): Review with Charlie, the second argument is required by VDI environment
        return this._peerConnection.addTransceiver(track, {});
    };
    /**
     * Check the {@link IceBox}.
     * @private
     * @param {RTCSessionDescriptionInit} description
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._checkIceBox = function(description) {
        var ufrag = getUfrag(description);
        if (!ufrag) {
            return Promise.resolve();
        }
        var candidates = this._remoteCandidates.setUfrag(ufrag);
        return this._addIceCandidates(candidates);
    };
    /**
     * Create an answer and set it on the {@link PeerConnectionV2}.
     * @private
     * @param {RTCSessionDescriptionInit} offer
     * @returns {Promise<boolean>}
     */ PeerConnectionV2.prototype._answer = function(offer) {
        var _this = this;
        return Promise.resolve().then(function() {
            if (!_this._negotiationRole) {
                _this._negotiationRole = 'answerer';
            }
            return _this._setRemoteDescription(offer);
        }).catch(function() {
            throw new MediaClientRemoteDescFailedError();
        }).then(function() {
            return _this._peerConnection.createAnswer();
        }).then(function(answer) {
            if (isFirefox) {
                // NOTE(mmalavalli): We work around Chromium bug 1106157 by disabling
                // RTX in Firefox 79+. For more details about the bug, please go here:
                // https://bugs.chromium.org/p/chromium/issues/detail?id=1106157
                answer = new _this._RTCSessionDescription({
                    sdp: disableRtx(answer.sdp),
                    type: answer.type
                });
            } else {
                answer = workaroundIssue8329(answer);
            }
            // NOTE(mpatwardhan): Upcoming chrome versions are going to remove ssrc attributes
            // mslabel and label. See this bug https://bugs.chromium.org/p/webrtc/issues/detail?id=7110
            // and PSA: https://groups.google.com/forum/#!searchin/discuss-webrtc/PSA%7Csort:date/discuss-webrtc/jcZO-Wj0Wus/k2XvPCvoAwAJ
            // We are not referencing those attributes, but this changes goes ahead and removes them to see if it works.
            // this also helps reduce bytes on wires
            var updatedSdp = removeSSRCAttributes(answer.sdp, [
                'mslabel',
                'label'
            ]);
            if (_this._shouldApplySimulcast) {
                var sdpWithoutSimulcast = updatedSdp;
                updatedSdp = _this._setSimulcast(sdpWithoutSimulcast, _this._trackIdsToAttributes);
                // NOTE(syerrapragada): VMS does not support H264 simulcast. So,
                // unset simulcast for sections in local offer where corresponding
                // sections in answer doesn't have vp8 as preferred codec and reapply offer.
                updatedSdp = _this._revertSimulcast(updatedSdp, sdpWithoutSimulcast, offer.sdp);
            }
            // NOTE(mmalavalli): Work around Chromium bug 1074421.
            // https://bugs.chromium.org/p/chromium/issues/detail?id=1074421
            updatedSdp = updatedSdp.replace(/42e015/g, '42e01f');
            return _this._setLocalDescription({
                type: answer.type,
                sdp: updatedSdp
            });
        }).then(function() {
            return _this._checkIceBox(offer);
        }).then(function() {
            return _this._queuedDescription && _this._updateDescription(_this._queuedDescription);
        }).then(function() {
            _this._queuedDescription = null;
            return _this._maybeReoffer(_this._peerConnection.localDescription);
        }).catch(function(error) {
            var errorToThrow = error instanceof MediaClientRemoteDescFailedError ? error : new MediaClientLocalDescFailedError();
            _this._publishMediaWarning({
                message: 'Failed to _answer',
                code: errorToThrow.code,
                error: error
            });
            throw errorToThrow;
        });
    };
    /**
     * Close the underlying RTCPeerConnection. Returns false if the
     * RTCPeerConnection was already closed.
     * @private
     * @returns {boolean}
     */ PeerConnectionV2.prototype._close = function() {
        this._iceConnectionMonitor.stop();
        if (this._peerConnection.signalingState !== 'closed') {
            this._peerConnection.close();
            this.preempt('closed');
            this._encodingParameters.removeListener('changed', this._onEncodingParametersChanged);
            return true;
        }
        return false;
    };
    /**
     * Handle a "connectionstatechange" event.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._handleConnectionStateChange = function() {
        this.emit('connectionStateChanged');
    };
    /**
     * Handle a "datachannel" event.
     * @private
     * @param {RTCDataChannelEvent} event
     * @returns {void}
     */ PeerConnectionV2.prototype._handleDataChannelEvent = function(event) {
        var _this = this;
        var dataChannel = event.channel;
        var dataTrackReceiver = new DataTrackReceiver(dataChannel);
        this._dataTrackReceivers.add(dataTrackReceiver);
        dataChannel.addEventListener('close', function() {
            _this._dataTrackReceivers.delete(dataTrackReceiver);
        });
        this.emit('trackAdded', dataTrackReceiver);
    };
    /**
     * Handle a glare scenario on the {@link PeerConnectionV2}.
     * @private
     * @param {RTCSessionDescriptionInit} offer
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._handleGlare = function(offer) {
        var _this = this;
        this._log.debug('Glare detected; rolling back');
        if (this._isRestartingIce) {
            this._log.debug('An ICE restart was in progress; we\'ll need to restart ICE again after rolling back');
            this._isRestartingIce = false;
            this._shouldRestartIce = true;
        }
        return Promise.resolve().then(function() {
            _this._trackIdsToAttributes = new Map(_this._appliedTrackIdsToAttributes);
            return _this._setLocalDescription({
                type: 'rollback'
            });
        }).then(function() {
            _this._needsAnswer = false;
            return _this._answer(offer);
        }).then(function(didReoffer) {
            return didReoffer ? Promise.resolve() : _this._offer();
        });
    };
    PeerConnectionV2.prototype._publishMediaWarning = function(_a) {
        var message = _a.message, code = _a.code, error = _a.error, sdp = _a.sdp;
        this._eventObserver.emit('event', {
            level: 'warning',
            name: 'error',
            group: 'media',
            payload: {
                message: message,
                code: code,
                context: JSON.stringify({
                    error: error.message,
                    sdp: sdp
                })
            }
        });
    };
    /**
     * Handle an ICE candidate event.
     * @private
     * @param {Event} event
     * @returns {void}
     */ PeerConnectionV2.prototype._handleIceCandidateEvent = function(event) {
        if (event.candidate) {
            this._log.debug('Clearing ICE gathering timeout');
            this._didGenerateLocalCandidates = true;
            this._iceGatheringTimeout.clear();
            this._localCandidates.push(event.candidate);
        }
        var peerConnectionState = {
            ice: {
                candidates: this._isIceLite ? [] : this._localCandidates.slice(),
                ufrag: this._localUfrag
            },
            id: this.id
        };
        if (!event.candidate) {
            peerConnectionState.ice.complete = true;
        }
        if (!(this._isIceLite && event.candidate)) {
            peerConnectionState.ice.revision = this._localCandidatesRevision++;
            this.emit('candidates', peerConnectionState);
        }
    };
    /**
     * Handle an ICE connection state change event.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._handleIceConnectionStateChange = function() {
        var _this = this;
        var iceConnectionState = this._peerConnection.iceConnectionState;
        var isIceConnectedOrComplete = [
            'connected',
            'completed'
        ].includes(iceConnectionState);
        var log = this._log;
        log.debug("ICE connection state is \"" + iceConnectionState + "\"");
        if (isIceConnectedOrComplete) {
            this._iceReconnectTimeout.clear();
            this._iceRestartBackoff.reset();
        }
        if (this._lastIceConnectionState !== 'failed' && iceConnectionState === 'failed' && !this._shouldRestartIce && !this._isRestartingIce) {
            // Case 1: Transition to "failed".
            log.warn('ICE failed');
            this._initiateIceRestartBackoff();
        } else if ([
            'disconnected',
            'failed'
        ].includes(this._lastIceConnectionState) && isIceConnectedOrComplete) {
            // Case 2: Transition from "disconnected" or "failed".
            log.debug('ICE reconnected');
        }
        // start monitor media when connected, and continue to monitor while state is complete-disconnected-connected.
        if (iceConnectionState === 'connected') {
            this._isIceConnectionInactive = false;
            this._iceConnectionMonitor.start(function() {
                // note: iceConnection monitor waits for iceConnectionState=disconnected for
                // detecting inactivity. Its possible that it may know about disconnected before _handleIceConnectionStateChange
                _this._iceConnectionMonitor.stop();
                if (!_this._shouldRestartIce && !_this._isRestartingIce) {
                    log.warn('ICE Connection Monitor detected inactivity');
                    _this._isIceConnectionInactive = true;
                    _this._initiateIceRestartBackoff();
                    _this.emit('iceConnectionStateChanged');
                    _this.emit('connectionStateChanged');
                }
            });
        } else if (![
            'disconnected',
            'completed'
        ].includes(iceConnectionState)) {
            this._iceConnectionMonitor.stop();
            this._isIceConnectionInactive = false;
        }
        this._lastIceConnectionState = iceConnectionState;
        this.emit('iceConnectionStateChanged');
    };
    /**
     * Handle ICE gathering timeout.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._handleIceGatheringTimeout = function() {
        this._log.warn('ICE failed to gather any local candidates');
        this._iceGatheringFailed = true;
        this._initiateIceRestartBackoff();
        this.emit('iceConnectionStateChanged');
        this.emit('connectionStateChanged');
    };
    /**
     * Handle an ICE gathering state change event.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._handleIceGatheringStateChange = function() {
        var iceGatheringState = this._peerConnection.iceGatheringState;
        var log = this._log;
        log.debug("ICE gathering state is \"" + iceGatheringState + "\"");
        // NOTE(mmalavalli): Start the ICE gathering timeout only if the RTCPeerConnection
        // has started gathering candidates for the first time since the initial offer/answer
        // or an offer/answer with ICE restart.
        var _a = this._iceGatheringTimeout, delay = _a.delay, isSet = _a.isSet;
        if (iceGatheringState === 'gathering' && !this._didGenerateLocalCandidates && !isSet) {
            log.debug("Starting ICE gathering timeout: " + delay);
            this._iceGatheringFailed = false;
            this._iceGatheringTimeout.start();
        }
    };
    /**
     * Handle a signaling state change event.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._handleSignalingStateChange = function() {
        if (this._peerConnection.signalingState === 'stable') {
            this._appliedTrackIdsToAttributes = new Map(this._trackIdsToAttributes);
        }
    };
    /**
     * Handle a track event.
     * @private
     * @param {RTCTrackEvent} event
     * @returns {void}
     */ PeerConnectionV2.prototype._handleTrackEvent = function(event) {
        var _this = this;
        var sdp = this._peerConnection.remoteDescription ? this._peerConnection.remoteDescription.sdp : null;
        this._trackMatcher = this._trackMatcher || new TrackMatcher();
        this._trackMatcher.update(sdp);
        var mediaStreamTrack = event.track;
        var signaledTrackId = this._trackMatcher.match(event) || mediaStreamTrack.id;
        var mediaTrackReceiver = new MediaTrackReceiver(signaledTrackId, mediaStreamTrack);
        // NOTE(mmalavalli): "ended" is not fired on the remote MediaStreamTrack when
        // the remote peer removes a track. So, when this MediaStreamTrack is re-used
        // for a different track due to the remote peer calling RTCRtpSender.replaceTrack(),
        // we delete the previous MediaTrackReceiver that owned this MediaStreamTrack
        // before adding the new MediaTrackReceiver.
        this._mediaTrackReceivers.forEach(function(trackReceiver) {
            if (trackReceiver.track.id === mediaTrackReceiver.track.id) {
                _this._mediaTrackReceivers.delete(trackReceiver);
            }
        });
        this._mediaTrackReceivers.add(mediaTrackReceiver);
        if (mediaStreamTrack.addEventListener) {
            mediaStreamTrack.addEventListener('ended', function() {
                return _this._mediaTrackReceivers.delete(mediaTrackReceiver);
            });
        } else {
            mediaStreamTrack.onended = function() {
                return _this._mediaTrackReceivers.delete(mediaTrackReceiver);
            };
        }
        this.emit('trackAdded', mediaTrackReceiver);
    };
    /**
     * Initiate ICE Restart.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._initiateIceRestart = function() {
        if (this._peerConnection.signalingState === 'closed') {
            return;
        }
        var log = this._log;
        log.warn('Attempting to restart ICE');
        this._didGenerateLocalCandidates = false;
        this._isIceRestartBackoffInProgress = false;
        this._shouldRestartIce = true;
        var _a = this._iceReconnectTimeout, delay = _a.delay, isSet = _a.isSet;
        if (!isSet) {
            log.debug("Starting ICE reconnect timeout: " + delay);
            this._iceReconnectTimeout.start();
        }
        this.offer().catch(function(ex) {
            log.error("offer failed in _initiateIceRestart with: " + ex.message);
        });
    };
    /**
     * Schedule an ICE Restart.
     * @private
     * @returns {void}
     */ PeerConnectionV2.prototype._initiateIceRestartBackoff = function() {
        var _this = this;
        if (this._peerConnection.signalingState === 'closed' || this._isIceRestartBackoffInProgress) {
            return;
        }
        this._log.warn('An ICE restart has been scheduled');
        this._isIceRestartBackoffInProgress = true;
        this._iceRestartBackoff.backoff(function() {
            return _this._initiateIceRestart();
        });
    };
    /**
     * Conditionally re-offer.
     * @private
     * @param {?RTCSessionDescriptionInit} localDescription
     * @returns {Promise<boolean>}
     */ PeerConnectionV2.prototype._maybeReoffer = function(localDescription) {
        var shouldReoffer = this._shouldOffer;
        if (localDescription && localDescription.sdp) {
            // NOTE(mmalavalli): If the local RTCSessionDescription has fewer audio and/or
            // video send* m= lines than the corresponding RTCRtpSenders with non-null
            // MediaStreamTracks, it means that the newly added RTCRtpSenders require
            // renegotiation.
            var senders_1 = this._peerConnection.getSenders().filter(function(sender) {
                return sender.track;
            });
            shouldReoffer = [
                'audio',
                'video'
            ].reduce(function(shouldOffer, kind) {
                var mediaSections = getMediaSections(localDescription.sdp, kind, '(sendrecv|sendonly)');
                var sendersOfKind = senders_1.filter(isSenderOfKind.bind(null, kind));
                return shouldOffer || mediaSections.length < sendersOfKind.length;
            }, shouldReoffer);
            // NOTE(mroberts): We also need to re-offer if we have a DataTrack to share
            // but no m= application section.
            var hasDataTrack = this._dataChannels.size > 0;
            var hasApplicationMediaSection = getMediaSections(localDescription.sdp, 'application').length > 0;
            var needsApplicationMediaSection = hasDataTrack && !hasApplicationMediaSection;
            shouldReoffer = shouldReoffer || needsApplicationMediaSection;
        }
        var promise = shouldReoffer ? this._offer() : Promise.resolve();
        return promise.then(function() {
            return shouldReoffer;
        });
    };
    /**
     * Create an offer and set it on the {@link PeerConnectionV2}.
     * @private
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._offer = function() {
        var _this = this;
        var offerOptions = Object.assign({}, this._offerOptions);
        this._needsAnswer = true;
        if (this._shouldRestartIce) {
            this._shouldRestartIce = false;
            this._isRestartingIce = true;
            offerOptions.iceRestart = true;
        }
        return Promise.all(this._replaceTrackPromises.values()).then(function() {
            return _this._peerConnection.createOffer(offerOptions);
        }).catch(function(error) {
            var errorToThrow = new MediaClientLocalDescFailedError();
            _this._publishMediaWarning({
                message: 'Failed to create offer',
                code: errorToThrow.code,
                error: error
            });
            throw errorToThrow;
        }).then(function(offer) {
            if (isFirefox) {
                // NOTE(mmalavalli): We work around Chromium bug 1106157 by disabling
                // RTX in Firefox 79+. For more details about the bug, please go here:
                // https://bugs.chromium.org/p/chromium/issues/detail?id=1106157
                offer = new _this._RTCSessionDescription({
                    sdp: disableRtx(offer.sdp),
                    type: offer.type
                });
            } else {
                offer = workaroundIssue8329(offer);
            }
            // NOTE(mpatwardhan): upcoming chrome versions are going to remove ssrc attributes
            // mslabel and label. See this bug https://bugs.chromium.org/p/webrtc/issues/detail?id=7110
            // and PSA: https://groups.google.com/forum/#!searchin/discuss-webrtc/PSA%7Csort:date/discuss-webrtc/jcZO-Wj0Wus/k2XvPCvoAwAJ
            // Looks like we are not referencing those attributes, but this changes goes ahead and removes them to see if it works.
            // this also helps reduce bytes on wires
            var sdp = removeSSRCAttributes(offer.sdp, [
                'mslabel',
                'label'
            ]);
            sdp = _this._peerConnection.remoteDescription ? filterLocalCodecs(sdp, _this._peerConnection.remoteDescription.sdp) : sdp;
            var updatedSdp = _this._setCodecPreferences(sdp, _this._preferredAudioCodecs, _this._preferredVideoCodecs);
            _this._shouldOffer = false;
            if (!_this._negotiationRole) {
                _this._negotiationRole = 'offerer';
            }
            if (_this._shouldApplySimulcast) {
                _this._localDescriptionWithoutSimulcast = {
                    type: 'offer',
                    sdp: updatedSdp
                };
                updatedSdp = _this._setSimulcast(updatedSdp, _this._trackIdsToAttributes);
            }
            return _this._setLocalDescription({
                type: 'offer',
                sdp: updatedSdp
            });
        });
    };
    /**
     * Get the MediaTrackSender ID of the given MediaStreamTrack ID.
     * Since a MediaTrackSender's underlying MediaStreamTrack can be
     * replaced, the corresponding IDs can mismatch.
     * @private
     * @param {Track.ID} id
     * @returns {Track.ID}
     */ PeerConnectionV2.prototype._getMediaTrackSenderId = function(trackId) {
        var mediaTrackSender = Array.from(this._rtpSenders.keys()).find(function(_a) {
            var id = _a.track.id;
            return id === trackId;
        });
        return mediaTrackSender ? mediaTrackSender.id : trackId;
    };
    /**
     * Add or rewrite local MediaStreamTrack IDs in the given RTCSessionDescription.
     * @private
     * @param {RTCSessionDescription} description
     * @return {RTCSessionDescription}
     */ PeerConnectionV2.prototype._addOrRewriteLocalTrackIds = function(description) {
        var _this = this;
        var transceivers = this._peerConnection.getTransceivers();
        var activeTransceivers = transceivers.filter(function(_a) {
            var sender = _a.sender, stopped = _a.stopped;
            return !stopped && sender && sender.track;
        });
        // NOTE(mmalavalli): There is no guarantee that MediaStreamTrack IDs will be present in
        // SDPs, and even if they are, there is no guarantee that they will be the same as the
        // actual MediaStreamTrack IDs. So, we add or re-write the actual MediaStreamTrack IDs
        // to the assigned m= sections here.
        var assignedTransceivers = activeTransceivers.filter(function(_a) {
            var mid = _a.mid;
            return mid;
        });
        var midsToTrackIds = new Map(assignedTransceivers.map(function(_a) {
            var mid = _a.mid, sender = _a.sender;
            return [
                mid,
                _this._getMediaTrackSenderId(sender.track.id)
            ];
        }));
        var sdp1 = addOrRewriteTrackIds(description.sdp, midsToTrackIds);
        // NOTE(mmalavalli): Chrome and Safari do not apply the offer until they get an answer.
        // So, we add or re-write the actual MediaStreamTrack IDs to the unassigned m= sections here.
        var unassignedTransceivers = activeTransceivers.filter(function(_a) {
            var mid = _a.mid;
            return !mid;
        });
        var newTrackIdsByKind = new Map([
            'audio',
            'video'
        ].map(function(kind) {
            return [
                kind,
                unassignedTransceivers.filter(function(_a) {
                    var sender = _a.sender;
                    return sender.track.kind === kind;
                }).map(function(_a) {
                    var sender = _a.sender;
                    return _this._getMediaTrackSenderId(sender.track.id);
                })
            ];
        }));
        var sdp2 = addOrRewriteNewTrackIds(sdp1, midsToTrackIds, newTrackIdsByKind);
        return new this._RTCSessionDescription({
            sdp: sdp2,
            type: description.type
        });
    };
    /**
     * Rollback and apply the given offer.
     * @private
     * @param {RTCSessionDescriptionInit} offer
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._rollbackAndApplyOffer = function(offer) {
        var _this = this;
        return this._setLocalDescription({
            type: 'rollback'
        }).then(function() {
            return _this._setLocalDescription(offer);
        });
    };
    /**
     * Set a local description on the {@link PeerConnectionV2}.
     * @private
     * @param {RTCSessionDescription|RTCSessionDescriptionInit} description
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._setLocalDescription = function(description) {
        var _this = this;
        if (description.type !== 'rollback' && this._shouldApplyDtx) {
            description = new this._RTCSessionDescription({
                sdp: enableDtxForOpus(description.sdp),
                type: description.type
            });
        }
        return this._peerConnection.setLocalDescription(description).catch(function(error) {
            _this._log.warn("Calling setLocalDescription with an RTCSessionDescription of type \"" + description.type + "\" failed with the error \"" + error.message + "\".", error);
            var errorToThrow = new MediaClientLocalDescFailedError();
            var publishWarning = {
                message: "Calling setLocalDescription with an RTCSessionDescription of type \"" + description.type + "\" failed",
                code: errorToThrow.code,
                error: error
            };
            if (description.sdp) {
                _this._log.warn("The SDP was " + description.sdp);
                publishWarning.sdp = description.sdp;
            }
            _this._publishMediaWarning(publishWarning);
            throw errorToThrow;
        }).then(function() {
            if (description.type !== 'rollback') {
                _this._localDescription = _this._addOrRewriteLocalTrackIds(description);
                // NOTE(mmalavalli): In order for this feature to be backward compatible with older
                // SDK versions which to not support opus DTX, we append "usedtx=1" to the local SDP
                // only while applying it. We will not send it over the wire to prevent inadvertent
                // enabling of opus DTX in older SDKs. Newer SDKs will append "usedtx=1" by themselves
                // if the developer has requested opus DTX to be enabled. (JSDK-3063)
                if (_this._shouldApplyDtx) {
                    _this._localDescription = new _this._RTCSessionDescription({
                        sdp: enableDtxForOpus(_this._localDescription.sdp, []),
                        type: _this._localDescription.type
                    });
                }
                _this._localCandidates = [];
                if (description.type === 'offer') {
                    _this._descriptionRevision++;
                } else if (description.type === 'answer') {
                    _this._lastStableDescriptionRevision = _this._descriptionRevision;
                    negotiationCompleted(_this);
                }
                _this._localUfrag = getUfrag(description);
                _this.emit('description', _this.getState());
            }
        });
    };
    /**
     * Set a remote RTCSessionDescription on the {@link PeerConnectionV2}.
     * @private
     * @param {RTCSessionDescriptionInit} description
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._setRemoteDescription = function(description) {
        var _this = this;
        if (description.sdp) {
            description.sdp = this._setCodecPreferences(description.sdp, this._preferredAudioCodecs, this._preferredVideoCodecs);
            if (this._shouldApplyDtx) {
                description.sdp = enableDtxForOpus(description.sdp);
            } else {
                // NOTE(mmalavalli): Remove "usedtx=1" from opus's fmtp line if present
                // since DTX is disabled.
                description.sdp = enableDtxForOpus(description.sdp, []);
            }
            if (isFirefox) {
                // NOTE(mroberts): Do this to reduce our MediaStream count in Firefox. By
                // mapping MediaStream IDs in the SDP to "-", we ensure the "track" event
                // doesn't include any new MediaStreams in Firefox. Its `streams` member
                // will always be the empty Array.
                description.sdp = filterOutMediaStreamIds(description.sdp);
            }
            if (!this._peerConnection.remoteDescription) {
                this._isIceLite = /a=ice-lite/.test(description.sdp);
            }
        }
        description = new this._RTCSessionDescription(description);
        // eslint-disable-next-line consistent-return
        return Promise.resolve().then(function() {
            // NOTE(syerrapragada): VMS does not support H264 simulcast. So,
            // unset simulcast for sections in local offer where corresponding
            // sections in answer doesn't have vp8 as preferred codec and reapply offer.
            if (description.type === 'answer' && _this._localDescriptionWithoutSimulcast) {
                // NOTE(mpatwardhan):if we were using adaptive simulcast, and if its not supported by server
                // revert simulcast even for vp8.
                var adaptiveSimulcastEntry = _this._preferredVideoCodecs.find(function(cs) {
                    return 'adaptiveSimulcast' in cs;
                });
                var revertForAll = !!adaptiveSimulcastEntry && adaptiveSimulcastEntry.adaptiveSimulcast === false;
                var sdpWithoutSimulcastForNonVP8MediaSections = _this._revertSimulcast(_this._localDescription.sdp, _this._localDescriptionWithoutSimulcast.sdp, description.sdp, revertForAll);
                _this._localDescriptionWithoutSimulcast = null;
                if (sdpWithoutSimulcastForNonVP8MediaSections !== _this._localDescription.sdp) {
                    return _this._rollbackAndApplyOffer({
                        type: _this._localDescription.type,
                        sdp: sdpWithoutSimulcastForNonVP8MediaSections
                    });
                }
            }
        }).then(function() {
            return _this._peerConnection.setRemoteDescription(description);
        }).then(function() {
            if (description.type === 'answer') {
                if (_this._isRestartingIce) {
                    _this._log.debug('An ICE restart was in-progress and is now completed');
                    _this._isRestartingIce = false;
                }
                negotiationCompleted(_this);
            }
        }, function(error) {
            _this._log.warn("Calling setRemoteDescription with an RTCSessionDescription of type \"" + description.type + "\" failed with the error \"" + error.message + "\".", error);
            if (description.sdp) {
                _this._log.warn("The SDP was " + description.sdp);
            }
            throw error;
        });
    };
    /**
     * Update the {@link PeerConnectionV2}'s description.
     * @private
     * @param {RTCSessionDescriptionInit} description
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._updateDescription = function(description) {
        var _this = this;
        switch(description.type){
            case 'answer':
            case 'pranswer':
                if (description.revision !== this._descriptionRevision || this._peerConnection.signalingState !== 'have-local-offer') {
                    return Promise.resolve();
                }
                this._descriptionRevision = description.revision;
                break;
            case 'close':
                return this._close();
            case 'create-offer':
                if (description.revision <= this._lastStableDescriptionRevision) {
                    return Promise.resolve();
                } else if (this._needsAnswer) {
                    this._queuedDescription = description;
                    return Promise.resolve();
                }
                this._descriptionRevision = description.revision;
                return this._offer();
            case 'offer':
                if (description.revision <= this._lastStableDescriptionRevision || this._peerConnection.signalingState === 'closed') {
                    return Promise.resolve();
                }
                if (this._peerConnection.signalingState === 'have-local-offer') {
                    // NOTE(mpatwardhan): For a peer connection
                    // 1) createOffer always generate SDP with `setup:actpass`
                    // 2) when remote description is set `setup:active`  - the answer generated selects the dtls role of setup:passive
                    // 3) when remote description is set `setup:passive` - the answer generated selects the dtls role of setup:active
                    // 4) when remote description is set `setup:actpass` - the answer generated uses the previously negotiated role (if not negotiated previously setup:active is used)
                    // This test shows the  behavior: https://github.com/twilio/twilio-webrtc.js/blob/master/test/integration/spec/rtcpeerconnection.js#L936
                    // with glare handling (if dtls role was not negotiated before ) the generated answer will set setup:active.
                    // we do not want that. lets wait for "initial negotiation" before attempting glare handling.
                    if (this._needsAnswer && this._lastStableDescriptionRevision === 0) {
                        this._queuedDescription = description;
                        return Promise.resolve();
                    }
                    this._descriptionRevision = description.revision;
                    return this._handleGlare(description);
                }
                this._descriptionRevision = description.revision;
                return this._answer(description).then(function() {});
            default:
        }
        // Handle answer or pranswer.
        var revision = description.revision;
        return Promise.resolve().then(function() {
            return _this._setRemoteDescription(description);
        }).catch(function(error) {
            var errorToThrow = new MediaClientRemoteDescFailedError();
            _this._publishMediaWarning({
                message: "Calling setRemoteDescription with an RTCSessionDescription of type \"" + description.type + "\" failed",
                code: errorToThrow.code,
                error: error,
                sdp: description.sdp
            });
            throw errorToThrow;
        }).then(function() {
            _this._lastStableDescriptionRevision = revision;
            _this._needsAnswer = false;
            return _this._checkIceBox(description);
        }).then(function() {
            return _this._queuedDescription && _this._updateDescription(_this._queuedDescription);
        }).then(function() {
            _this._queuedDescription = null;
            return _this._maybeReoffer(_this._peerConnection.localDescription).then(function() {});
        });
    };
    /**
     * Update the {@link PeerConnectionV2}'s ICE candidates.
     * @private
     * @param {object} iceState
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype._updateIce = function(iceState) {
        var candidates = this._remoteCandidates.update(iceState);
        return this._addIceCandidates(candidates);
    };
    /**
     * Add a {@link DataTrackSender} to the {@link PeerConnectionV2}.
     * @param {DataTrackSender} dataTrackSender
     * @returns {void}
     */ PeerConnectionV2.prototype.addDataTrackSender = function(dataTrackSender) {
        if (this._dataChannels.has(dataTrackSender)) {
            return;
        }
        try {
            var dataChannelDict = {
                ordered: dataTrackSender.ordered
            };
            if (dataTrackSender.maxPacketLifeTime !== null) {
                dataChannelDict.maxPacketLifeTime = dataTrackSender.maxPacketLifeTime;
            }
            if (dataTrackSender.maxRetransmits !== null) {
                dataChannelDict.maxRetransmits = dataTrackSender.maxRetransmits;
            }
            var dataChannel = this._peerConnection.createDataChannel(dataTrackSender.id, dataChannelDict);
            dataTrackSender.addDataChannel(dataChannel);
            this._dataChannels.set(dataTrackSender, dataChannel);
        } catch (error) {
            this._log.warn("Error creating an RTCDataChannel for DataTrack \"" + dataTrackSender.id + "\": " + error.message);
        }
    };
    PeerConnectionV2.prototype._handleQueuedPublisherHints = function() {
        var _this = this;
        if (this._peerConnection.signalingState === 'stable') {
            this._mediaTrackSenderToPublisherHints.forEach(function(_a, mediaTrackSender) {
                var deferred = _a.deferred, encodings = _a.encodings;
                _this._mediaTrackSenderToPublisherHints.delete(mediaTrackSender);
                _this._setPublisherHint(mediaTrackSender, encodings).then(function(result) {
                    return deferred.resolve(result);
                }).catch(function(error) {
                    return deferred.reject(error);
                });
            });
        }
    };
    /**
     * updates encodings for simulcast layers of given sender.
     * @param {RTCRtpSender} sender
     * @param {Array<{enabled: boolean, layer_index: number}>|null} encodings
     * @returns {Promise<string>} string indicating result of the operation. can be one of
     *  "OK", "INVALID_HINT", "COULD_NOT_APPLY_HINT", "UNKNOWN_TRACK"
     */ PeerConnectionV2.prototype._setPublisherHint = function(mediaTrackSender, encodings) {
        var _this = this;
        if (isFirefox) {
            return Promise.resolve('COULD_NOT_APPLY_HINT');
        }
        if (this._mediaTrackSenderToPublisherHints.has(mediaTrackSender)) {
            // skip any stale hint associated with the mediaTrackSender.
            var queuedHint = this._mediaTrackSenderToPublisherHints.get(mediaTrackSender);
            queuedHint.deferred.resolve('REQUEST_SKIPPED');
            this._mediaTrackSenderToPublisherHints.delete(mediaTrackSender);
        }
        var sender = this._rtpSenders.get(mediaTrackSender);
        if (!sender) {
            this._log.warn('Could not apply publisher hint because RTCRtpSender was not found');
            return Promise.resolve('UNKNOWN_TRACK');
        }
        if (this._peerConnection.signalingState === 'closed') {
            this._log.warn('Could not apply publisher hint because signalingState was "closed"');
            return Promise.resolve('COULD_NOT_APPLY_HINT');
        }
        if (this._peerConnection.signalingState !== 'stable') {
            // enqueue this hint to be applied when pc becomes stable.
            this._log.debug('Queuing up publisher hint because signalingState:', this._peerConnection.signalingState);
            var deferred = defer();
            this._mediaTrackSenderToPublisherHints.set(mediaTrackSender, {
                deferred: deferred,
                encodings: encodings
            });
            return deferred.promise;
        }
        var parameters = sender.getParameters();
        if (encodings !== null) {
            encodings.forEach(function(_a) {
                var enabled = _a.enabled, layerIndex = _a.layer_index;
                if (parameters.encodings.length > layerIndex) {
                    _this._log.debug("layer:" + layerIndex + ", active:" + parameters.encodings[layerIndex].active + " => " + enabled);
                    parameters.encodings[layerIndex].active = enabled;
                } else {
                    _this._log.warn("invalid layer:" + layerIndex + ", active:" + enabled);
                }
            });
        }
        // Note(mpatwardhan): after publisher hints are applied, overwrite with default encodings
        // to disable any encoding that shouldn't have been enabled by publisher_hints.
        // When encodings===null (that is we are asked to reset encodings for replaceTrack)
        // along with disabling encodings, clear active flag for encodings that should not be disabled
        this._maybeUpdateEncodings(sender.track, parameters.encodings, encodings === null);
        return sender.setParameters(parameters).then(function() {
            return 'OK';
        }).catch(function(error) {
            _this._log.error('Failed to apply publisher hints:', error);
            return 'COULD_NOT_APPLY_HINT';
        });
    };
    /**
     * Add the {@link MediaTrackSender} to the {@link PeerConnectionV2}.
     * @param {MediaTrackSender} mediaTrackSender
     * @returns {void}
     */ PeerConnectionV2.prototype.addMediaTrackSender = function(mediaTrackSender) {
        var _this = this;
        if (this._peerConnection.signalingState === 'closed' || this._rtpSenders.has(mediaTrackSender)) {
            return;
        }
        var transceiver = this._addOrUpdateTransceiver(mediaTrackSender.track);
        var sender = transceiver.sender;
        mediaTrackSender.addSender(sender, function(encodings) {
            return _this._setPublisherHint(mediaTrackSender, encodings);
        });
        this._rtpNewSenders.add(sender);
        this._rtpSenders.set(mediaTrackSender, sender);
    };
    /**
     * Close the {@link PeerConnectionV2}.
     * @returns {void}
     */ PeerConnectionV2.prototype.close = function() {
        if (this._close()) {
            this._descriptionRevision++;
            this._localDescription = {
                type: 'close'
            };
            this.emit('description', this.getState());
        }
    };
    /**
     * Get the {@link DataTrackReceiver}s and the {@link MediaTrackReceiver}s on the
     * {@link PeerConnectionV2}.
     * @returns {Array<DataTrackReceiver|MediaTrackReceiver>} trackReceivers
     */ PeerConnectionV2.prototype.getTrackReceivers = function() {
        return Array.from(this._dataTrackReceivers).concat(Array.from(this._mediaTrackReceivers));
    };
    /**
     * Get the {@link PeerConnectionV2}'s state (specifically, its description).
     * @returns {?object}
     */ PeerConnectionV2.prototype.getState = function() {
        if (!this._localDescription) {
            return null;
        }
        // NOTE(mpatwardhan): Return most recent localDescription. If the most recent local description is an
        // answer, and this method is called for sending a "sync" message while the next remote offer is being processed,
        // we need to send the most recent stable description revision instead of the current description revision,
        // which is supposed to be for the next local answer.
        var localDescriptionRevision = this._localDescription.type === 'answer' ? this._lastStableDescriptionRevision : this._descriptionRevision;
        var localDescription = {
            type: this._localDescription.type,
            revision: localDescriptionRevision
        };
        if (this._localDescription.sdp) {
            localDescription.sdp = this._localDescription.sdp;
        }
        return {
            description: localDescription,
            id: this.id
        };
    };
    /**
     * Create an offer and set it on the {@link PeerConnectionV2}.
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype.offer = function() {
        var _this = this;
        if (this._needsAnswer || this._isRestartingIce) {
            this._shouldOffer = true;
            return Promise.resolve();
        }
        return this.bracket('offering', function(key) {
            _this.transition('updating', key);
            var promise = _this._needsAnswer || _this._isRestartingIce ? Promise.resolve() : _this._offer();
            return promise.then(function() {
                _this.tryTransition('open', key);
            }, function(error) {
                _this.tryTransition('open', key);
                throw error;
            });
        });
    };
    /**
     * Remove a {@link DataTrackSender} from the {@link PeerConnectionV2}.
     * @param {DataTrackSender} dataTrackSender
     * @returns {void}
     */ PeerConnectionV2.prototype.removeDataTrackSender = function(dataTrackSender) {
        var dataChannel = this._dataChannels.get(dataTrackSender);
        if (dataChannel) {
            dataTrackSender.removeDataChannel(dataChannel);
            this._dataChannels.delete(dataTrackSender);
            dataChannel.close();
        }
    };
    /**
     * Remove the {@link MediaTrackSender} from the {@link PeerConnectionV2}.
     * @param {MediaTrackSender} mediaTrackSender
     * @returns {void}
     */ PeerConnectionV2.prototype.removeMediaTrackSender = function(mediaTrackSender) {
        var sender = this._rtpSenders.get(mediaTrackSender);
        if (!sender) {
            return;
        }
        if (this._peerConnection.signalingState !== 'closed') {
            this._peerConnection.removeTrack(sender);
        }
        mediaTrackSender.removeSender(sender);
        // clean up any pending publisher hints associated with this mediaTrackSender.
        if (this._mediaTrackSenderToPublisherHints.has(mediaTrackSender)) {
            var queuedHint = this._mediaTrackSenderToPublisherHints.get(mediaTrackSender);
            queuedHint.deferred.resolve('UNKNOWN_TRACK');
            this._mediaTrackSenderToPublisherHints.delete(mediaTrackSender);
        }
        this._rtpNewSenders.delete(sender);
        this._rtpSenders.delete(mediaTrackSender);
    };
    /**
     * Set the RTCConfiguration on the underlying RTCPeerConnection.
     * @param {RTCConfiguration} configuration
     * @returns {void}
     */ PeerConnectionV2.prototype.setConfiguration = function(configuration) {
        if (typeof this._peerConnection.setConfiguration === 'function') {
            this._peerConnection.setConfiguration(getConfiguration(configuration));
        }
    };
    /**
     * Set the ICE reconnect timeout period.
     * @param {number} period - Period in milliseconds.
     * @returns {this}
     */ PeerConnectionV2.prototype.setIceReconnectTimeout = function(period) {
        this._iceReconnectTimeout.setDelay(period);
        this._log.debug('Updated ICE reconnection timeout period:', this._iceReconnectTimeout.delay);
        return this;
    };
    /**
     * Update the {@link PeerConnectionV2}.
     * @param {object} peerConnectionState
     * @returns {Promise<void>}
     */ PeerConnectionV2.prototype.update = function(peerConnectionState) {
        var _this = this;
        return this.bracket('updating', function(key) {
            if (_this.state === 'closed') {
                return Promise.resolve();
            }
            _this.transition('updating', key);
            var updates = [];
            if (peerConnectionState.ice) {
                updates.push(_this._updateIce(peerConnectionState.ice));
            }
            if (peerConnectionState.description) {
                updates.push(_this._updateDescription(peerConnectionState.description));
            }
            return Promise.all(updates).then(function() {
                _this.tryTransition('open', key);
            }, function(error) {
                _this.tryTransition('open', key);
                throw error;
            });
        });
    };
    /**
     * Get the {@link PeerConnectionV2}'s media statistics.
     * @returns {Promise<StandardizedStatsResponse>}
     */ PeerConnectionV2.prototype.getStats = function() {
        var _this = this;
        return getStatistics(this._peerConnection, {
            log: this._log
        }).then(function(response) {
            return rewriteTrackIds(_this, response);
        });
    };
    return PeerConnectionV2;
}(StateMachine);
function rewriteLocalTrackId(pcv2, stats) {
    var trackId = pcv2._getMediaTrackSenderId(stats.trackId);
    return Object.assign(stats, {
        trackId: trackId
    });
}
function rewriteTrackId(pcv2, stats) {
    var receiver = __spreadArray([], __read(pcv2._mediaTrackReceivers)).find(function(receiver) {
        return receiver.track.id === stats.trackId;
    });
    var trackId = receiver ? receiver.id : null;
    return Object.assign(stats, {
        trackId: trackId
    });
}
function rewriteTrackIds(pcv2, response) {
    return Object.assign(response, {
        remoteAudioTrackStats: response.remoteAudioTrackStats.map(function(stats) {
            return rewriteTrackId(pcv2, stats);
        }),
        remoteVideoTrackStats: response.remoteVideoTrackStats.map(function(stats) {
            return rewriteTrackId(pcv2, stats);
        }),
        localAudioTrackStats: response.localAudioTrackStats.map(function(stats) {
            return rewriteLocalTrackId(pcv2, stats);
        }),
        localVideoTrackStats: response.localVideoTrackStats.map(function(stats) {
            return rewriteLocalTrackId(pcv2, stats);
        })
    });
}
/**
 * @event PeerConnectionV2#candidates
 * @param {object} candidates
 */ /**
 * @event PeerConnectionV2#connectionStateChanged
 */ /**
 * @event PeerConnectionV2#description
 * @param {object} description
 */ /**
 * @event PeerConnectionV2#iceConnectionStateChanged
 */ /**
 * @event PeerConnectionV2#trackAdded
 * @param {DataTrackReceiver|MediaTrackReceiver} trackReceiver
 */ function getUfrag(description) {
    if (description.sdp) {
        var match = description.sdp.match(/^a=ice-ufrag:([a-zA-Z0-9+/]+)/m);
        if (match) {
            return match[1];
        }
    }
    return null;
}
/**
 * Construct a {@link RTCConfiguration} for the {@link PeerConnectionV2} combining the provided configuration object with the default values.
 * Default values are:
 * - bundlePolicy: 'max-bundle'
 * - rtcpMuxPolicy: 'require'
 * @param {RTCConfiguration} [configuration={}] - Optional RTCConfiguration object
 * @returns {RTCConfiguration}
 */ function getConfiguration(configuration) {
    if (configuration === void 0) {
        configuration = {};
    }
    return Object.assign({
        bundlePolicy: 'max-bundle',
        rtcpMuxPolicy: 'require'
    }, configuration);
}
/**
 * Whether the MediaStreamTrack of the given RTCRTPSender is a non-ended
 * MediaStreamTrack of a given kind.
 * @private
 * @param {string} kind
 * @param {RTCRtpSender} sender
 * @return {boolean}
 */ function isSenderOfKind(kind, sender) {
    var track = sender.track;
    return track && track.kind === kind && track.readyState !== 'ended';
}
/**
 * Preferred codecs.
 * @typedef {object} PreferredCodecs
 * @property {Array<AudioCodec>} audio
 * @property {Array<VideoCodec>} video
 */ function filterOutMediaStreamIds(sdp) {
    return sdp.replace(/a=msid:[^ ]+ /g, 'a=msid:- ');
}
/**
 * Whether an RTCRtpTransceiver can be recycled.
 * @param {RTCRtpTransceiver} transceiver
 * @returns {boolean}
 */ function shouldRecycleTransceiver(transceiver, pcv2) {
    return !transceiver.stopped && !pcv2._replaceTrackPromises.has(transceiver) && [
        'inactive',
        'recvonly'
    ].includes(transceiver.direction);
}
/**
 * Take a recycled RTCRtpTransceiver if available.
 * @param {PeerConnectionV2} pcv2
 * @param {Track.Kind} kind
 * @returns {?RTCRtpTransceiver}
 */ function takeRecycledTransceiver(pcv2, kind) {
    var preferredCodecs = {
        audio: pcv2._preferredAudioCodecs.map(function(_a) {
            var codec = _a.codec;
            return codec.toLowerCase();
        }),
        video: pcv2._preferredVideoCodecs.map(function(_a) {
            var codec = _a.codec;
            return codec.toLowerCase();
        })
    }[kind];
    var recycledTransceivers = pcv2._recycledTransceivers[kind];
    var localCodec = preferredCodecs.find(function(codec) {
        return pcv2._localCodecs.has(codec);
    });
    if (!localCodec) {
        return recycledTransceivers.shift();
    }
    var transceiver = recycledTransceivers.find(function(transceiver) {
        var remoteCodecMap = pcv2._remoteCodecMaps.get(transceiver.mid);
        return remoteCodecMap && remoteCodecMap.has(localCodec);
    });
    if (transceiver) {
        recycledTransceivers.splice(recycledTransceivers.indexOf(transceiver), 1);
    }
    return transceiver;
}
/**
 * Update the set of locally supported {@link Codec}s.
 * @param pcv2
 * @returns {void}
 */ function updateLocalCodecs(pcv2) {
    var description = pcv2._peerConnection.localDescription;
    if (!description || !description.sdp) {
        return;
    }
    getMediaSections(description.sdp).forEach(function(section) {
        var codecMap = createCodecMapForMediaSection(section);
        codecMap.forEach(function(pts, codec) {
            return pcv2._localCodecs.add(codec);
        });
    });
}
/**
 * Update the {@link Codec} maps for all m= sections in the remote {@link RTCSessionDescription}s.
 * @param {PeerConnectionV2} pcv2
 * @returns {void}
 */ function updateRemoteCodecMaps(pcv2) {
    var description = pcv2._peerConnection.remoteDescription;
    if (!description || !description.sdp) {
        return;
    }
    getMediaSections(description.sdp).forEach(function(section) {
        var matched = section.match(/^a=mid:(.+)$/m);
        if (!matched || !matched[1]) {
            return;
        }
        var mid = matched[1];
        var codecMap = createCodecMapForMediaSection(section);
        pcv2._remoteCodecMaps.set(mid, codecMap);
    });
}
/**
 * Update the list of recycled RTCRtpTransceivers.
 * @param {PeerConnectionV2} pcv2
 */ function updateRecycledTransceivers(pcv2) {
    pcv2._recycledTransceivers.audio = [];
    pcv2._recycledTransceivers.video = [];
    pcv2._peerConnection.getTransceivers().forEach(function(transceiver) {
        if (shouldRecycleTransceiver(transceiver, pcv2)) {
            var track = transceiver.receiver.track;
            pcv2._recycledTransceivers[track.kind].push(transceiver);
        }
    });
}
/**
 * Perform certain updates after an SDP negotiation is completed.
 * @param {PeerConnectionV2} pcv2
 * @returns {void}
 */ function negotiationCompleted(pcv2) {
    updateRecycledTransceivers(pcv2);
    updateLocalCodecs(pcv2);
    updateRemoteCodecMaps(pcv2);
    updateEncodingParameters(pcv2).then(function() {
        // if there any any publisher hints queued, apply them now.
        pcv2._handleQueuedPublisherHints();
    });
}
/**
 * Update the RTCRtpEncodingParameters of all active RTCRtpSenders.
 * @param {PeerConnectionV2} pcv2
 * @returns {void}
 */ function updateEncodingParameters(pcv2) {
    var _a = pcv2._encodingParameters, maxAudioBitrate = _a.maxAudioBitrate, maxVideoBitrate = _a.maxVideoBitrate;
    var maxBitrates = new Map([
        [
            'audio',
            maxAudioBitrate
        ],
        [
            'video',
            maxVideoBitrate
        ]
    ]);
    var promises = [];
    pcv2._peerConnection.getSenders().filter(function(sender) {
        return sender.track;
    }).forEach(function(sender) {
        var maxBitrate = maxBitrates.get(sender.track.kind);
        var params = sender.getParameters();
        if (maxBitrate === null || maxBitrate === 0) {
            removeMaxBitrate(params);
        } else if (pcv2._isChromeScreenShareTrack(sender.track)) {
            // NOTE(mpatwardhan): Sometimes (JSDK-2557) chrome does not send any bytes on screen track if MaxBitRate is set on it via setParameters,
            // To workaround this issue we will not apply maxBitrate if the track appears to be a screen share track created by chrome
            pcv2._log.warn("Not setting maxBitrate for " + sender.track.kind + " Track " + sender.track.id + " because it appears to be screen share track: " + sender.track.label);
        } else {
            setMaxBitrate(params, maxBitrate);
        }
        if (!isFirefox && params.encodings.length > 0) {
            if (sender.track.kind === 'audio') {
                // NOTE(mmalavalli): "priority" is a per-sender property and not
                // a per-encoding-layer property. So, we set the value only on the first
                // encoding layer. Any attempt to set the value on subsequent encoding
                // layers (in the case of simulcast) will result in the Promise returned
                // by RTCRtpSender.setParameters() being rejected. With this, audio encoding
                // is prioritized the most.
                params.encodings[0].priority = 'high';
            } else if (pcv2._isChromeScreenShareTrack(sender.track)) {
                // NOTE(mmalavalli): Screen share encodings are prioritized more than those
                // of the camera.
                params.encodings[0].priority = 'medium';
            }
            if (pcv2._enableDscp) {
                // NOTE(mmalavalli): "networkPriority" is a per-sender property and not
                // a per-encoding-layer property. So, we set the value only on the first
                // encoding layer. Any attempt to set the value on subsequent encoding
                // layers (in the case of simulcast) will result in the Promise returned
                // by RTCRtpSender.setParameters() being rejected.
                params.encodings[0].networkPriority = 'high';
            }
        }
        // when a sender is reused, delete any active encodings set by server.
        var trackReplaced = pcv2._rtpNewSenders.has(sender);
        pcv2._maybeUpdateEncodings(sender.track, params.encodings, trackReplaced);
        pcv2._rtpNewSenders.delete(sender);
        var promise = sender.setParameters(params).catch(function(error) {
            pcv2._log.warn("Error while setting encodings parameters for " + sender.track.kind + " Track " + sender.track.id + ": " + (error.message || error.name));
        });
        promises.push(promise);
    });
    return Promise.all(promises);
}
/**
 * Remove maxBitrate from the RTCRtpSendParameters' encodings.
 * @param {RTCRtpSendParameters} params
 * @returns {void}
 */ function removeMaxBitrate(params) {
    if (Array.isArray(params.encodings)) {
        params.encodings.forEach(function(encoding) {
            return delete encoding.maxBitrate;
        });
    }
}
/**
 * Set the given maxBitrate in the RTCRtpSendParameters' encodings.
 * @param {RTCRtpSendParameters} params
 * @param {number} maxBitrate
 * @returns {void}
 */ function setMaxBitrate(params, maxBitrate) {
    if (isFirefox) {
        params.encodings = [
            {
                maxBitrate: maxBitrate
            }
        ];
    } else {
        params.encodings.forEach(function(encoding) {
            encoding.maxBitrate = maxBitrate;
        });
    }
}
module.exports = PeerConnectionV2; //# sourceMappingURL=peerconnection.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/peerconnectionmanager.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var guessBrowser = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").guessBrowser;
var PeerConnectionV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/peerconnection.js [app-client] (ecmascript)");
var MediaTrackSender = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/media/track/sender.js [app-client] (ecmascript)");
var QueueingEventEmitter = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/queueingeventemitter.js [app-client] (ecmascript)");
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)");
var MediaConnectionError = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)").MediaConnectionError;
var isFirefox = guessBrowser() === 'firefox';
/**
 * {@link PeerConnectionManager} manages multiple {@link PeerConnectionV2}s.
 * @extends QueueingEventEmitter
 * @emits PeerConnectionManager#candidates
 * @emits PeerConnectionManager#connectionStateChanged
 * @emits PeerConnectionManager#description
 * @emits PeerConnectionManager#iceConnectionStateChanged
 * @emits PeerConnectionManager#trackAdded
 */ var PeerConnectionManager = function(_super) {
    __extends(PeerConnectionManager, _super);
    /**
     * Construct {@link PeerConnectionManager}.
     * @param {EncodingParametersImpl} encodingParameters
     * @param {PreferredCodecs} preferredCodecs
     * @param {object} options
     */ function PeerConnectionManager(encodingParameters, preferredCodecs, options) {
        var _this = _super.call(this) || this;
        options = Object.assign({
            audioContextFactory: isFirefox ? __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/audiocontext.js [app-client] (ecmascript)") : null,
            PeerConnectionV2: PeerConnectionV2
        }, options);
        var audioContext = options.audioContextFactory ? options.audioContextFactory.getOrCreate(_this) : null;
        // NOTE(mroberts): If we're using an AudioContext, we don't need to specify
        // `offerToReceiveAudio` in RTCOfferOptions.
        var offerOptions = audioContext ? {
            offerToReceiveVideo: true
        } : {
            offerToReceiveAudio: true,
            offerToReceiveVideo: true
        };
        Object.defineProperties(_this, {
            _audioContextFactory: {
                value: options.audioContextFactory
            },
            _closedPeerConnectionIds: {
                value: new Set()
            },
            _configuration: {
                writable: true,
                value: null
            },
            _configurationDeferred: {
                writable: true,
                value: util.defer()
            },
            _connectionState: {
                value: 'new',
                writable: true
            },
            _dummyAudioTrackSender: {
                value: audioContext ? new MediaTrackSender(createDummyAudioMediaStreamTrack(audioContext)) : null
            },
            _encodingParameters: {
                value: encodingParameters
            },
            _iceConnectionState: {
                writable: true,
                value: 'new'
            },
            _dataTrackSenders: {
                writable: true,
                value: new Set()
            },
            _lastConnectionState: {
                value: 'new',
                writable: true
            },
            _lastIceConnectionState: {
                writable: true,
                value: 'new'
            },
            _mediaTrackSenders: {
                writable: true,
                value: new Set()
            },
            _offerOptions: {
                value: offerOptions
            },
            _peerConnections: {
                value: new Map()
            },
            _preferredCodecs: {
                value: preferredCodecs
            },
            _sessionTimeout: {
                value: null,
                writable: true
            },
            _PeerConnectionV2: {
                value: options.PeerConnectionV2
            }
        });
        return _this;
    }
    PeerConnectionManager.prototype.setEffectiveAdaptiveSimulcast = function(effectiveAdaptiveSimulcast) {
        this._peerConnections.forEach(function(pc) {
            return pc.setEffectiveAdaptiveSimulcast(effectiveAdaptiveSimulcast);
        });
        this._preferredCodecs.video.forEach(function(cs) {
            if ('adaptiveSimulcast' in cs) {
                cs.adaptiveSimulcast = effectiveAdaptiveSimulcast;
            }
        });
    };
    Object.defineProperty(PeerConnectionManager.prototype, "connectionState", {
        /**
         * A summarized RTCPeerConnectionState across all the
         * {@link PeerConnectionManager}'s underlying {@link PeerConnectionV2}s.
         * @property {RTCPeerConnectionState}
         */ get: function() {
            return this._connectionState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PeerConnectionManager.prototype, "iceConnectionState", {
        /**
         * A summarized RTCIceConnectionState across all the
         * {@link PeerConnectionManager}'s underlying {@link PeerConnectionV2}s.
         * @property {RTCIceConnectionState}
         */ get: function() {
            return this._iceConnectionState;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Close the {@link PeerConnectionV2}s which are no longer relevant.
     * @param {Array<object>} peerConnectionStates
     * @returns {this}
     */ PeerConnectionManager.prototype._closeAbsentPeerConnections = function(peerConnectionStates) {
        var peerConnectionIds = new Set(peerConnectionStates.map(function(peerConnectionState) {
            return peerConnectionState.id;
        }));
        this._peerConnections.forEach(function(peerConnection) {
            if (!peerConnectionIds.has(peerConnection.id)) {
                peerConnection._close();
            }
        });
        return this;
    };
    /**
     * Get the {@link PeerConnectionManager}'s configuration.
     * @private
     * @returns {Promise<object>}
     */ PeerConnectionManager.prototype._getConfiguration = function() {
        return this._configurationDeferred.promise;
    };
    /**
     * Get or create a {@link PeerConnectionV2}.
     * @private
     * @param {string} id
     * @param {object} [configuration]
     * @returns {PeerConnectionV2}
     */ PeerConnectionManager.prototype._getOrCreate = function(id, configuration) {
        var _this = this;
        var self = this;
        var peerConnection = this._peerConnections.get(id);
        if (!peerConnection) {
            var PeerConnectionV2_1 = this._PeerConnectionV2;
            var options = Object.assign({
                dummyAudioMediaStreamTrack: this._dummyAudioTrackSender ? this._dummyAudioTrackSender.track : null,
                offerOptions: this._offerOptions
            }, this._sessionTimeout ? {
                sessionTimeout: this._sessionTimeout
            } : {}, configuration);
            try {
                peerConnection = new PeerConnectionV2_1(id, this._encodingParameters, this._preferredCodecs, options);
            } catch (e) {
                throw new MediaConnectionError();
            }
            this._peerConnections.set(peerConnection.id, peerConnection);
            peerConnection.on('candidates', this.queue.bind(this, 'candidates'));
            peerConnection.on('description', this.queue.bind(this, 'description'));
            peerConnection.on('trackAdded', this.queue.bind(this, 'trackAdded'));
            peerConnection.on('stateChanged', function stateChanged(state) {
                if (state === 'closed') {
                    peerConnection.removeListener('stateChanged', stateChanged);
                    self._dataTrackSenders.forEach(function(sender) {
                        return peerConnection.removeDataTrackSender(sender);
                    });
                    self._mediaTrackSenders.forEach(function(sender) {
                        return peerConnection.removeMediaTrackSender(sender);
                    });
                    self._peerConnections.delete(peerConnection.id);
                    self._closedPeerConnectionIds.add(peerConnection.id);
                    updateConnectionState(self);
                    updateIceConnectionState(self);
                }
            });
            peerConnection.on('connectionStateChanged', function() {
                return updateConnectionState(_this);
            });
            peerConnection.on('iceConnectionStateChanged', function() {
                return updateIceConnectionState(_this);
            });
            this._dataTrackSenders.forEach(peerConnection.addDataTrackSender, peerConnection);
            this._mediaTrackSenders.forEach(peerConnection.addMediaTrackSender, peerConnection);
            updateIceConnectionState(this);
        }
        return peerConnection;
    };
    /**
     * Close all the {@link PeerConnectionV2}s in this {@link PeerConnectionManager}.
     * @returns {this}
     */ PeerConnectionManager.prototype.close = function() {
        this._peerConnections.forEach(function(peerConnection) {
            peerConnection.close();
        });
        if (this._dummyAudioTrackSender) {
            this._dummyAudioTrackSender.stop();
        }
        if (this._audioContextFactory) {
            this._audioContextFactory.release(this);
        }
        updateIceConnectionState(this);
        return this;
    };
    /**
     * Create a new {@link PeerConnectionV2} on this {@link PeerConnectionManager}.
     * Then, create a new offer with the newly-created {@link PeerConnectionV2}.
     * @return {Promise<this>}
     */ PeerConnectionManager.prototype.createAndOffer = function() {
        var _this = this;
        return this._getConfiguration().then(function(configuration) {
            var id;
            do {
                id = util.makeUUID();
            }while (_this._peerConnections.has(id))
            return _this._getOrCreate(id, configuration);
        }).then(function(peerConnection) {
            return peerConnection.offer();
        }).then(function() {
            return _this;
        });
    };
    /**
     * Get the {@link DataTrackReceiver}s and {@link MediaTrackReceiver}s of all
     * the {@link PeerConnectionV2}s.
     * @returns {Array<DataTrackReceiver|MediaTrackReceiver>} trackReceivers
     */ PeerConnectionManager.prototype.getTrackReceivers = function() {
        return util.flatMap(this._peerConnections, function(peerConnection) {
            return peerConnection.getTrackReceivers();
        });
    };
    /**
     * Get the states of all {@link PeerConnectionV2}s.
     * @returns {Array<object>}
     */ PeerConnectionManager.prototype.getStates = function() {
        var peerConnectionStates = [];
        this._peerConnections.forEach(function(peerConnection) {
            var peerConnectionState = peerConnection.getState();
            if (peerConnectionState) {
                peerConnectionStates.push(peerConnectionState);
            }
        });
        return peerConnectionStates;
    };
    /**
     * Set the {@link PeerConnectionManager}'s configuration.
     * @param {object} configuration
     * @returns {this}
     */ PeerConnectionManager.prototype.setConfiguration = function(configuration) {
        if (this._configuration) {
            this._configurationDeferred = util.defer();
            this._peerConnections.forEach(function(peerConnection) {
                peerConnection.setConfiguration(configuration);
            });
        }
        this._configuration = configuration;
        this._configurationDeferred.resolve(configuration);
        return this;
    };
    /**
     * Set the ICE reconnect timeout period for all {@link PeerConnectionV2}s.
     * @param {number} period - Period in milliseconds.
     * @returns {this}
     */ PeerConnectionManager.prototype.setIceReconnectTimeout = function(period) {
        if (this._sessionTimeout === null) {
            this._peerConnections.forEach(function(peerConnection) {
                peerConnection.setIceReconnectTimeout(period);
            });
            this._sessionTimeout = period;
        }
        return this;
    };
    /**
     * Set the {@link DataTrackSender}s and {@link MediaTrackSender}s on the
     * {@link PeerConnectionManager}'s underlying {@link PeerConnectionV2}s.
     * @param {Array<DataTrackSender|MediaTrackSender>} trackSenders
     * @returns {this}
     */ PeerConnectionManager.prototype.setTrackSenders = function(trackSenders) {
        var dataTrackSenders = new Set(trackSenders.filter(function(trackSender) {
            return trackSender.kind === 'data';
        }));
        var mediaTrackSenders = new Set(trackSenders.filter(function(trackSender) {
            return trackSender && (trackSender.kind === 'audio' || trackSender.kind === 'video');
        }));
        var changes = getTrackSenderChanges(this, dataTrackSenders, mediaTrackSenders);
        this._dataTrackSenders = dataTrackSenders;
        this._mediaTrackSenders = mediaTrackSenders;
        applyTrackSenderChanges(this, changes);
        return this;
    };
    /**
     * Update the {@link PeerConnectionManager}.
     * @param {Array<object>} peerConnectionStates
     * @param {boolean} [synced=false]
     * @returns {Promise<this>}
     */ PeerConnectionManager.prototype.update = function(peerConnectionStates, synced) {
        var _this = this;
        if (synced === void 0) {
            synced = false;
        }
        if (synced) {
            this._closeAbsentPeerConnections(peerConnectionStates);
        }
        return this._getConfiguration().then(function(configuration) {
            return Promise.all(peerConnectionStates.map(function(peerConnectionState) {
                if (_this._closedPeerConnectionIds.has(peerConnectionState.id)) {
                    return null;
                }
                var peerConnection = _this._getOrCreate(peerConnectionState.id, configuration);
                return peerConnection.update(peerConnectionState);
            }));
        }).then(function() {
            return _this;
        });
    };
    /**
     * Get the {@link PeerConnectionManager}'s media statistics.
     * @returns {Promise.<Map<PeerConnectionV2#id, StandardizedStatsResponse>>}
     */ PeerConnectionManager.prototype.getStats = function() {
        var peerConnections = Array.from(this._peerConnections.values());
        return Promise.all(peerConnections.map(function(peerConnection) {
            return peerConnection.getStats().then(function(response) {
                return [
                    peerConnection.id,
                    response
                ];
            });
        })).then(function(responses) {
            return new Map(responses);
        });
    };
    return PeerConnectionManager;
}(QueueingEventEmitter);
/**
 * Create a dummy audio MediaStreamTrack with the given AudioContext.
 * @private
 * @param {AudioContext} audioContext
 * @return {MediaStreamTrack}
 */ function createDummyAudioMediaStreamTrack(audioContext) {
    var mediaStreamDestination = audioContext.createMediaStreamDestination();
    return mediaStreamDestination.stream.getAudioTracks()[0];
}
/**
 * @event {PeerConnectionManager#candidates}
 * @param {object} candidates
 */ /**
 * @event {PeerConnectionManager#connectionStateChanged}
 */ /**
 * @event {PeerConnectionManager#description}
 * @param {object} description
 */ /**
 * @event {PeerConnectionManager#iceConnectionStateChanged}
 */ /**
 * @event {PeerConnectionManager#trackAdded}
 * @param {MediaStreamTrack|DataTrackReceiver} mediaStreamTrackOrDataTrackReceiver
 */ /**
 * Apply {@link TrackSenderChanges}.
 * @param {PeerConnectionManager} peerConnectionManager
 * @param {TrackSenderChanges} changes
 * @returns {void}
 */ function applyTrackSenderChanges(peerConnectionManager, changes) {
    if (changes.data.add.size || changes.data.remove.size || changes.media.add.size || changes.media.remove.size) {
        peerConnectionManager._peerConnections.forEach(function(peerConnection) {
            changes.data.remove.forEach(peerConnection.removeDataTrackSender, peerConnection);
            changes.media.remove.forEach(peerConnection.removeMediaTrackSender, peerConnection);
            changes.data.add.forEach(peerConnection.addDataTrackSender, peerConnection);
            changes.media.add.forEach(peerConnection.addMediaTrackSender, peerConnection);
            if (changes.media.add.size || changes.media.remove.size || changes.data.add.size && !peerConnection.isApplicationSectionNegotiated) {
                peerConnection.offer();
            }
        });
    }
}
/**
 * @interface DataTrackSenderChanges
 * @property {Set<DataTrackSender>} add
 * @property {Set<DataTrackSender>} remove
 */ /**
 * Get the {@Link DataTrackSender} changes.
 * @param {PeerConnectionManager} peerConnectionManager
 * @param {Array<DataTrackSender>} dataTrackSenders
 * @returns {DataTrackSenderChanges} changes
 */ function getDataTrackSenderChanges(peerConnectionManager, dataTrackSenders) {
    var dataTrackSendersToAdd = util.difference(dataTrackSenders, peerConnectionManager._dataTrackSenders);
    var dataTrackSendersToRemove = util.difference(peerConnectionManager._dataTrackSenders, dataTrackSenders);
    return {
        add: dataTrackSendersToAdd,
        remove: dataTrackSendersToRemove
    };
}
/**
 * @interface TrackSenderChanges
 * @property {DataTrackSenderChanges} data
 * @property {MediaTrackSenderChanges} media
 */ /**
 * Get {@link DataTrackSender} and {@link MediaTrackSender} changes.
 * @param {PeerConnectionManager} peerConnectionManager
 * @param {Array<DataTrackSender>} dataTrackSenders
 * @param {Array<MediaTrackSender>} mediaTrackSenders
 * @returns {TrackSenderChanges} changes
 */ function getTrackSenderChanges(peerConnectionManager, dataTrackSenders, mediaTrackSenders) {
    return {
        data: getDataTrackSenderChanges(peerConnectionManager, dataTrackSenders),
        media: getMediaTrackSenderChanges(peerConnectionManager, mediaTrackSenders)
    };
}
/**
 * @interface MediaTrackSenderChanges
 * @property {Set<MediaTrackSender>} add
 * @property {Set<MediaTrackSender>} remove
 */ /**
 * Get the {@link MediaTrackSender} changes.
 * @param {PeerConnectionManager} peerConnectionManager
 * @param {Array<MediaTrackSender>} mediaTrackSenders
 * @returns {MediaTrackSenderChanges} changes
 */ function getMediaTrackSenderChanges(peerConnectionManager, mediaTrackSenders) {
    var mediaTrackSendersToAdd = util.difference(mediaTrackSenders, peerConnectionManager._mediaTrackSenders);
    var mediaTrackSendersToRemove = util.difference(peerConnectionManager._mediaTrackSenders, mediaTrackSenders);
    return {
        add: mediaTrackSendersToAdd,
        remove: mediaTrackSendersToRemove
    };
}
/**
 * This object maps RTCIceConnectionState and RTCPeerConnectionState values to a "rank".
 */ var toRank = {
    new: 0,
    checking: 1,
    connecting: 2,
    connected: 3,
    completed: 4,
    disconnected: -1,
    failed: -2,
    closed: -3
};
/**
 * This object maps "rank" back to RTCIceConnectionState or RTCPeerConnectionState values.
 */ var fromRank;
/**
 * `Object.keys` is not supported in older browsers, so we can't just
 * synchronously call it in this module; we need to defer invoking it until we
 * know we're in a modern environment (i.e., anything that supports WebRTC).
 * @returns {object} fromRank
 */ function createFromRank() {
    return Object.keys(toRank).reduce(function(fromRank, state) {
        var _a;
        return Object.assign(fromRank, (_a = {}, _a[toRank[state]] = state, _a));
    }, {});
}
/**
 * Summarize RTCIceConnectionStates or RTCPeerConnectionStates.
 * @param {Array<RTCIceConnectionState>|Array<RTCPeerConnectionState>} states
 * @returns {RTCIceConnectionState|RTCPeerConnectionState} summary
 */ function summarizeIceOrPeerConnectionStates(states) {
    if (!states.length) {
        return 'new';
    }
    fromRank = fromRank || createFromRank();
    return states.reduce(function(state1, state2) {
        return fromRank[Math.max(toRank[state1], toRank[state2])];
    });
}
/**
 * Update the {@link PeerConnectionManager}'s `iceConnectionState`, and emit an
 * "iceConnectionStateChanged" event, if necessary.
 * @param {PeerConnectionManager} pcm
 * @returns {void}
 */ function updateIceConnectionState(pcm) {
    pcm._lastIceConnectionState = pcm.iceConnectionState;
    pcm._iceConnectionState = summarizeIceOrPeerConnectionStates(__spreadArray([], __read(pcm._peerConnections.values())).map(function(pcv2) {
        return pcv2.iceConnectionState;
    }));
    if (pcm.iceConnectionState !== pcm._lastIceConnectionState) {
        pcm.emit('iceConnectionStateChanged');
    }
}
/**
 * Update the {@link PeerConnectionManager}'s `connectionState`, and emit a
 * "connectionStateChanged" event, if necessary.
 * @param {PeerConnectionManager} pcm
 * @returns {void}
 */ function updateConnectionState(pcm) {
    pcm._lastConnectionState = pcm.connectionState;
    pcm._connectionState = summarizeIceOrPeerConnectionStates(__spreadArray([], __read(pcm._peerConnections.values())).map(function(pcv2) {
        return pcv2.connectionState;
    }));
    if (pcm.connectionState !== pcm._lastConnectionState) {
        pcm.emit('connectionStateChanged');
    }
}
module.exports = PeerConnectionManager; //# sourceMappingURL=peerconnectionmanager.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint callback-return:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)");
var nInstances = 0;
var MediaSignaling = function(_super) {
    __extends(MediaSignaling, _super);
    /**
     * Construct a {@link MediaSignaling}.
     * @param {Promise<DataTrackReceiver>} getReceive
     * @param {string} channel
     */ function MediaSignaling(getReceiver, channel, options) {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _instanceId: {
                value: nInstances++
            },
            channel: {
                value: channel
            },
            _log: {
                value: options.log.createLog('default', _this)
            },
            _getReceiver: {
                value: getReceiver
            },
            _receiverPromise: {
                value: null,
                writable: true
            },
            _transport: {
                value: null,
                writable: true
            }
        });
        return _this;
    }
    Object.defineProperty(MediaSignaling.prototype, "isSetup", {
        get: function() {
            return !!this._receiverPromise;
        },
        enumerable: false,
        configurable: true
    });
    MediaSignaling.prototype.toString = function() {
        return "[MediaSignaling #" + this._instanceId + ":" + this.channel + "]";
    };
    MediaSignaling.prototype.setup = function(id) {
        var _this = this;
        this._teardown();
        this._log.info('setting up msp transport for id:', id);
        var receiverPromise = this._getReceiver(id).then(function(receiver) {
            if (receiver.kind !== 'data') {
                _this._log.error('Expected a DataTrackReceiver');
            }
            if (_this._receiverPromise !== receiverPromise) {
                return;
            }
            try {
                _this._transport = receiver.toDataTransport();
                _this.emit('ready', _this._transport);
            } catch (ex) {
                _this._log.error("Failed to toDataTransport: " + ex.message);
            }
            receiver.once('close', function() {
                return _this._teardown();
            });
        });
        this._receiverPromise = receiverPromise;
    };
    MediaSignaling.prototype._teardown = function() {
        if (this._transport) {
            this._log.info('Tearing down');
            this._transport = null;
            this._receiverPromise = null;
            this.emit('teardown');
        }
    };
    return MediaSignaling;
}(EventEmitter);
module.exports = MediaSignaling; //# sourceMappingURL=mediasignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/dominantspeakersignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
/**
 * @property {?Track.SID} loudestParticipantSid
 * @emits DominantSpeakerSignaling#updated
 */ var DominantSpeakerSignaling = function(_super) {
    __extends(DominantSpeakerSignaling, _super);
    /**
     * Construct an {@link DominantSpeakerSignaling}.
     */ function DominantSpeakerSignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'active_speaker', options) || this;
        Object.defineProperties(_this, {
            _loudestParticipantSid: {
                value: null,
                writable: true
            }
        });
        _this.on('ready', function(transport) {
            transport.on('message', function(message) {
                switch(message.type){
                    case 'active_speaker':
                        _this._setLoudestParticipantSid(message.participant);
                        break;
                    default:
                        break;
                }
            });
        });
        return _this;
    }
    Object.defineProperty(DominantSpeakerSignaling.prototype, "loudestParticipantSid", {
        /**
         * Get the loudest {@link Track.SID}, if known.
         * @returns {?Track.SID}
         */ get: function() {
            return this._loudestParticipantSid;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * @private
     * @param {Track.SID} loudestParticipantSid
     * @returns {void}
     */ DominantSpeakerSignaling.prototype._setLoudestParticipantSid = function(loudestParticipantSid) {
        if (this.loudestParticipantSid === loudestParticipantSid) {
            return;
        }
        this._loudestParticipantSid = loudestParticipantSid;
        this.emit('updated');
    };
    return DominantSpeakerSignaling;
}(MediaSignaling);
/**
 * @event DominantSpeakerSignaling#updated
 */ module.exports = DominantSpeakerSignaling; //# sourceMappingURL=dominantspeakersignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/transcriptionsignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
/**
 * @emits TranscriptionSignaling#transcription
 */ var TranscriptionSignaling = function(_super) {
    __extends(TranscriptionSignaling, _super);
    /**
     * Construct an {@link TranscriptionSignaling}.
     */ function TranscriptionSignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'extension_transcriptions', options) || this;
        _this.on('ready', function(transport) {
            transport.on('message', function(message) {
                switch(message.type){
                    case 'extension_transcriptions':
                        _this.emit('transcription', message);
                        break;
                    default:
                        break;
                }
            });
        });
        return _this;
    }
    return TranscriptionSignaling;
}(MediaSignaling);
/**
 * @event TranscriptionSignaling#transcription
 */ module.exports = TranscriptionSignaling; //# sourceMappingURL=transcriptionsignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/networkqualitymonitor.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint callback-return:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)");
var PeerConnectionReportFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/peerconnectionreportfactory.js [app-client] (ecmascript)");
/**
 * @emits NetworkQualityMonitor#updated
 */ var NetworkQualityMonitor = function(_super) {
    __extends(NetworkQualityMonitor, _super);
    /**
     * Construct a {@link NetworkQualityMonitor}.
     * @param {PeerConnectionManager} manager
     * @param {NetworkQualitySignaling} signaling
     */ function NetworkQualityMonitor(manager, signaling) {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _factories: {
                value: new WeakMap()
            },
            _manager: {
                value: manager
            },
            _signaling: {
                value: signaling
            }
        });
        signaling.on('updated', function() {
            return _this.emit('updated');
        });
        return _this;
    }
    Object.defineProperty(NetworkQualityMonitor.prototype, "level", {
        /**
         * Get the current {@link NetworkQualityLevel}, if any.
         * @returns {?NetworkQualityLevel} level - initially null
         */ get: function() {
            return this._signaling.level;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NetworkQualityMonitor.prototype, "levels", {
        /**
         * Get the current {@link NetworkQualityLevels}, if any.
         * @returns {?NetworkQualityLevels} levels - initially null
         */ get: function() {
            return this._signaling.levels;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NetworkQualityMonitor.prototype, "remoteLevels", {
        /**
         * Get the current {@link NetworkQualityLevels} of remote participants, if any.
         * @returns {Map<String, NetworkQualityLevels>} remoteLevels
         */ get: function() {
            return this._signaling.remoteLevels;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Start monitoring.
     * @returns {void}
     */ NetworkQualityMonitor.prototype.start = function() {
        var _this = this;
        this.stop();
        var timeout = setTimeout(function() {
            if (_this._timeout !== timeout) {
                return;
            }
            next(_this).then(function(reports) {
                if (_this._timeout !== timeout) {
                    return;
                }
                if (reports.length) {
                    var _a = __read(reports, 1), report = _a[0];
                    _this._signaling.put(report);
                }
                _this.start();
            });
        }, 200);
        this._timeout = timeout;
    };
    /**
     * Stop monitoring.
     * @returns {void}
     */ NetworkQualityMonitor.prototype.stop = function() {
        clearTimeout(this._timeout);
        this._timeout = null;
    };
    return NetworkQualityMonitor;
}(EventEmitter);
/**
 * @param {NetworkQualityMonitor}
 * @returns {Promise<NetworkQualityInputs>}
 */ function next(monitor) {
    var pcv2s = monitor._manager._peerConnections ? Array.from(monitor._manager._peerConnections.values()) : [];
    var pcs = pcv2s.map(function(pcv2) {
        return pcv2._peerConnection;
    }).filter(function(pc) {
        return pc.signalingState !== 'closed';
    });
    var factories = pcs.map(function(pc) {
        if (monitor._factories.has(pc)) {
            return monitor._factories.get(pc);
        }
        var factory = new PeerConnectionReportFactory(pc);
        monitor._factories.set(pc, factory);
        return factory;
    });
    var reportsOrNullPromises = factories.map(function(factory) {
        return factory.next().catch(function() {
            return null;
        });
    });
    return Promise.all(reportsOrNullPromises).then(function(reportsOrNull) {
        return reportsOrNull.filter(function(reportOrNull) {
            return reportOrNull;
        }).map(function(report) {
            return report.summarize();
        });
    });
}
/**
 * The {@link NetworkQualityLevel} changed.
 * @event NetworkQualityMonitor#updated
 */ module.exports = NetworkQualityMonitor; //# sourceMappingURL=networkqualitymonitor.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/networkqualitysignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
var AsyncVar = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/asyncvar.js [app-client] (ecmascript)");
var Timeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var NETWORK_QUALITY_RESPONSE_TIME_MS = 5000;
/**
 * @interface MediaSignalingTransport
 * @property {function(object): boolean} send
 * @emits MediaSignalingTransport#message
 */ /**
 * The {@link MediaSignalingTransport} received a message.
 * @event MediaSignalingTransport#message
 * @param {object} message
 */ /**
 * @interface LatencyStats
 * @property {number} jitter
 * @property {number} rtt
 * @property {number} level
 */ /**
 * @interface FractionLostStats
 * @property {number} fractionLost
 * @property {number} level
 */ /**
 * @interface BandwidthStats
 * @property {number} actual
 * @property {number} available
 * @property {number} level
 */ /**
 * @interface SendOrRecvStats
 * @property {BandwidthStats} bandwidth
 * @property {FractionLostStats} fractionLost
 * @property {LatencyStats} latency
 */ /**
 * @interface MediaLevels
 * @property {number} send
 * @property {SendOrRecvStats} sendStats
 * @property {number} recv
 * @property {SendOrRecvStats} recvStats
 */ /**
 * @interface NetworkQualityLevels
 * @property {number} level
 * @property {MediaLevels} audio
 * @property {MediaLevels} video
 */ /**
 * @typedef {PeerConnectionSummary} NetworkQualityInputs
 */ /**
 * @classdesc The {@link NetworkQualitySignaling} class allows submitting
 *   {@link NetworkQualityInputs} for computing {@link NetworkQualityLevel}. It
 *   does so by sending and receiving messages over a
 *   {@link MediaSignalingTransport}. The exact transport used depends on the
 *   topology of the {@link Room} that {@link NetworkQualitySignaling} is being
 *   used within: for P2P Rooms, we re-use the {@link TransportV2}; and for
 *   Group Rooms, we use a {@link DataTransport}.
 * @emits NetworkQualitySignaling#updated
 */ var NetworkQualitySignaling = function(_super) {
    __extends(NetworkQualitySignaling, _super);
    /**
     * Construct a {@link NetworkQualitySignaling}.
     * @param {Promise<DataTrackReceiver>} getReceiver
     * @param {NetworkQualityConfigurationImpl} networkQualityConfiguration
     */ function NetworkQualitySignaling(getReceiver, networkQualityConfiguration, options) {
        var _this = _super.call(this, getReceiver, 'network_quality', options) || this;
        Object.defineProperties(_this, {
            _level: {
                value: null,
                writable: true
            },
            _levels: {
                value: null,
                writable: true
            },
            _remoteLevels: {
                value: new Map(),
                writable: true
            },
            _networkQualityInputs: {
                value: new AsyncVar()
            },
            _resendTimer: {
                value: new Timeout(function() {
                    // and schedule next timer at x1.5 the delay..
                    _this._resendTimer.setDelay(_this._resendTimer.delay * 1.5);
                    _this._sendNetworkQualityInputs();
                }, NETWORK_QUALITY_RESPONSE_TIME_MS, false)
            },
            _networkQualityReportLevels: {
                get: function() {
                    return {
                        reportLevel: networkQualityConfiguration.local,
                        remoteReportLevel: networkQualityConfiguration.remote
                    };
                }
            }
        });
        _this.on('ready', function(transport) {
            transport.on('message', function(message) {
                _this._log.debug('Incoming: ', message);
                switch(message.type){
                    case 'network_quality':
                        _this._handleNetworkQualityMessage(message);
                        break;
                    default:
                        break;
                }
            });
        });
        _this._sendNetworkQualityInputs();
        return _this;
    }
    Object.defineProperty(NetworkQualitySignaling.prototype, "level", {
        /**
         * Get the current {@link NetworkQualityLevel}, if any.
         * @returns {?NetworkQualityLevel} level - initially null
         */ get: function() {
            return this._level;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NetworkQualitySignaling.prototype, "levels", {
        /**
         * Get the current {@link NetworkQualityLevels}, if any.
         * @returns {?NetworkQualityLevels} levels - initially null
         */ get: function() {
            return this._levels;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NetworkQualitySignaling.prototype, "remoteLevels", {
        /**
         * Get the current {@link NetworkQualityLevels} of remote participants, if any.
         * @returns {Map<String, NetworkQualityLevels>} remoteLevels
         */ get: function() {
            return this._remoteLevels;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Check to see if the {@link NetworkQualityLevel} is new, and raise an
     * event if necessary.
     * @private
     * @param {object} message
     * @returns {void}
     */ NetworkQualitySignaling.prototype._handleNetworkQualityMessage = function(message) {
        var _this = this;
        var updated = false;
        var level = null;
        var local = message ? message.local : null;
        if (typeof local === 'number') {
            // NOTE(mroberts): In prod, we plan to only send the level.
            level = local;
            this._levels = null;
        } else if (typeof local === 'object' && local) {
            // NOTE(mroberts): In dev, we plan to send the decomposed levels. An early
            // VMS version does not compute `level` for us, so we fallback to taking
            // the minimum ourselves.
            this._levels = local;
            level = typeof local.level === 'number' ? local.level : Math.min(local.audio.send, local.audio.recv, local.video.send, local.video.recv);
        }
        if (level !== null && this.level !== level) {
            this._level = level;
            updated = true;
        }
        this._remoteLevels = message && message.remotes ? message.remotes.reduce(function(levels, obj) {
            var oldObj = _this._remoteLevels.get(obj.sid) || {};
            if (oldObj.level !== obj.level) {
                updated = true;
            }
            return levels.set(obj.sid, obj);
        }, new Map()) : this._remoteLevels;
        if (updated) {
            this.emit('updated');
        }
        // score is received. so reset the timer to default timeout.
        this._resendTimer.setDelay(NETWORK_QUALITY_RESPONSE_TIME_MS);
        // timer is cleared only while we are sending inputs.
        // if we are already sending inputs do not send them again.
        if (this._resendTimer.isSet) {
            setTimeout(function() {
                return _this._sendNetworkQualityInputs();
            }, 1000);
        }
    };
    /**
     * Start sending {@link NetworkQualityInputs}.
     * @private
     * @returns {Promise<void>}
     */ NetworkQualitySignaling.prototype._sendNetworkQualityInputs = function() {
        var _this = this;
        this._resendTimer.clear();
        return this._networkQualityInputs.take().then(function(networkQualityInputs) {
            if (_this._transport) {
                _this._transport.publish(createNetworkQualityInputsMessage(networkQualityInputs, _this._networkQualityReportLevels));
            }
        }).finally(function() {
            _this._resendTimer.start();
        });
    };
    /**
     * Put {@link NetworkQualityInputs} to be used for computing
     * {@link NetworkQualityLevel}.
     * @param {NetworkQualityInputs} networkQualityInputs
     * @returns {void}
     */ NetworkQualitySignaling.prototype.put = function(networkQualityInputs) {
        this._networkQualityInputs.put(networkQualityInputs);
    };
    return NetworkQualitySignaling;
}(MediaSignaling);
/**
 * The {@link NetworkQualityLevel} changed.
 * @event NetworkQualitySignaling#updated
 */ /**
 * @typedef {object} NetworkQualityReportLevels
 * @param {number} reportLevel
 * @param {number} remoteReportLevel
 */ /**
 * @param {NetworkQualityInputs} networkQualityInputs
 * @param {NetworkQualityReportLevels} networkQualityReportLevels
 * @returns {object} message
 */ function createNetworkQualityInputsMessage(networkQualityInputs, networkQualityReportLevels) {
    return Object.assign({
        type: 'network_quality'
    }, networkQualityInputs, networkQualityReportLevels);
}
module.exports = NetworkQualitySignaling; //# sourceMappingURL=networkqualitysignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/recording.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
/**
 * Represents recording state
 * @extends EventEmitter
 * @property {?boolean} isEnabled
 */ var RecordingSignaling = function(_super) {
    __extends(RecordingSignaling, _super);
    /**
     * Construct a {@link RecordingSignaling}.
     */ function RecordingSignaling() {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _isEnabled: {
                value: null,
                writable: true
            },
            isEnabled: {
                enumerable: true,
                get: function() {
                    return this._isEnabled;
                }
            }
        });
        return _this;
    }
    /**
     * Disable the {@link RecordingSignaling} if it is not already disabled.
     * @return {this}
     */ RecordingSignaling.prototype.disable = function() {
        return this.enable(false);
    };
    /**
     * Enable (or disable) the {@link RecordingSignaling} if it is not already enabled
     * (or disabled).
     * @param {boolean} [enabled=true]
     * @return {this}
     */ RecordingSignaling.prototype.enable = function(enabled) {
        enabled = typeof enabled === 'boolean' ? enabled : true;
        if (this.isEnabled !== enabled) {
            this._isEnabled = enabled;
            this.emit('updated');
        }
        return this;
    };
    return RecordingSignaling;
}(EventEmitter);
/**
 * Emitted whenever the {@link RecordingSignaling} is updated
 * @event RecordingSignaling#updated
 */ module.exports = RecordingSignaling; //# sourceMappingURL=recording.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/recording.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RecordingSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/recording.js [app-client] (ecmascript)");
/**
 * @extends RecordingSignaling
 */ var RecordingV2 = function(_super) {
    __extends(RecordingV2, _super);
    /**
     * Construct a {@link RecordingV2}.
     */ function RecordingV2() {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _revision: {
                value: 1,
                writable: true
            }
        });
        return _this;
    }
    /**
     * Compare the {@link RecordingV2} to a {@link RecordingV2#Representation}
     * of itself and perform any updates necessary.
     * @param {RecordingV2#Representation} recording
     * @returns {this}
     * @fires RecordingSignaling#updated
     */ RecordingV2.prototype.update = function(recording) {
        if (recording.revision < this._revision) {
            return this;
        }
        this._revision = recording.revision;
        return this.enable(recording.is_recording);
    };
    return RecordingV2;
}(RecordingSignaling);
/**
 * The Room Signaling Protocol (RSP) representation of a {@link RecordingV2}
 * @typedef {object} RecordingV2#Representation
 * @property {boolean} enabled
 * @property {number} revision
 */ module.exports = RecordingV2; //# sourceMappingURL=recording.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/room.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var DefaultRecordingSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/recording.js [app-client] (ecmascript)");
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
var DefaultTimeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var buildLogLevels = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").buildLogLevels;
var DEFAULT_LOG_LEVEL = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_LOG_LEVEL;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), MediaConnectionError = _a.MediaConnectionError, MediaDTLSTransportFailedError = _a.MediaDTLSTransportFailedError, SignalingConnectionDisconnectedError = _a.SignalingConnectionDisconnectedError;
var nInstances = 0;
/*
RoomSignaling States
-----------------------

    +-----------+     +--------------+
    |           |     |              |
    | connected |---->| disconnected |
    |           |     |              |
    +-----------+     +--------------+
          |  ^               ^
          |  |               |
          |  |   +--------------+
          |  +---|              |
          |      | reconnecting |
          +----->|              |
                 +--------------+

*/ var states = {
    connected: [
        'reconnecting',
        'disconnected'
    ],
    reconnecting: [
        'connected',
        'disconnected'
    ],
    disconnected: []
};
/**
 * A {@link Room} implementation
 * @extends StateMachine
 * @property {RTCPeerConnectionState} connectionState
 * @property {?Participant.SID} dominantSpeakerSid
 * @property {ParticipantSignaling} localParticipant
 * @property {RTCIceConnectionState} iceConnectionState
 * @property {string} name
 * @property {Map<string, RemoteParticipantSignaling>} participants
 * @property {RecordingSignaling} recording
 * @property {Room.SID} sid
 * @property {string} state - "connected", "reconnecting", or "disconnected"
 * @property {string} signalingConnectionState - "connected",
 *   "reconnecting", or "disconnected"
 * @emits RoomSignaling#connectionStateChanged
 * @emits RoomSignaling#dominantSpeakerChanged
 * @emits RoomSignaling#iceConnectionStateChanged
 * @emits RoomSignaling#signalingConnectionStateChanged
 */ var RoomSignaling = function(_super) {
    __extends(RoomSignaling, _super);
    /**
     * Construct a {@link RoomSignaling}.
     * @param {ParticipantSignaling} localParticipant
     * @param {Room.SID} sid
     * @param {string} name
     * @param {object} options
     */ function RoomSignaling(localParticipant, sid, name, options) {
        var _this = this;
        options = Object.assign({
            logLevel: DEFAULT_LOG_LEVEL,
            RecordingSignaling: DefaultRecordingSignaling,
            Timeout: DefaultTimeout
        }, options);
        var logLevels = buildLogLevels(options.logLevel);
        _this = _super.call(this, 'connected', states) || this;
        var RecordingSignaling = options.RecordingSignaling;
        var sessionTimeout = new options.Timeout(function() {
            _this._disconnect(_this._reconnectingError);
        }, options.sessionTimeout, false);
        Object.defineProperties(_this, {
            _instanceId: {
                value: nInstances++
            },
            _log: {
                value: options.log ? options.log.createLog('default', _this) : new Log('default', _this, logLevels, options.loggerName)
            },
            _mediaConnectionIsReconnecting: {
                writable: true,
                value: false
            },
            _options: {
                value: options
            },
            _reconnectingError: {
                value: null,
                writable: true
            },
            _sessionTimeout: {
                value: sessionTimeout
            },
            dominantSpeakerSid: {
                enumerable: true,
                value: null,
                writable: true
            },
            localParticipant: {
                enumerable: true,
                value: localParticipant
            },
            name: {
                enumerable: true,
                value: name
            },
            participants: {
                enumerable: true,
                value: new Map()
            },
            recording: {
                enumerable: true,
                value: new RecordingSignaling()
            },
            sid: {
                enumerable: true,
                value: sid
            }
        });
        _this.on('connectionStateChanged', function() {
            if (_this.connectionState === 'failed' && ![
                'disconnected',
                'failed'
            ].includes(_this.iceConnectionState)) {
                _this._disconnect(new MediaDTLSTransportFailedError());
            }
        });
        _this.on('iceConnectionStateChanged', function() {
            return maybeUpdateState(_this);
        });
        _this.on('signalingConnectionStateChanged', function() {
            return maybeUpdateState(_this);
        });
        // NOTE(mmalavalli): In case "iceConnectionState" is already failed, update
        // the RoomSignaling state. setTimeout() ensures that the state is updated
        // after RoomV2's constructor is fully executed, thereby making "signalingConnectionState"
        // available here.
        setTimeout(function() {
            return maybeUpdateState(_this);
        });
        return _this;
    }
    /**
     * Disconnect, possibly with an Error.
     * @private
     * @param {Error} [error]
     * @returns {boolean}
     */ RoomSignaling.prototype._disconnect = function(error) {
        if (this.state !== 'disconnected') {
            this.preempt('disconnected', null, [
                error
            ]);
            return true;
        }
        return false;
    };
    RoomSignaling.prototype.toString = function() {
        return "[RoomSignaling #" + this._instanceId + ": " + (this.localParticipant ? this.localParticipant.sid : 'null') + "]";
    };
    /**
     * Connect {@link RemoteParticipantSignaling} to the {@link RoomSignaling}.
     * @param {RemoteParticipantSignaling} participant
     * @returns {boolean}
     */ RoomSignaling.prototype.connectParticipant = function(participant) {
        var self = this;
        if (participant.state === 'disconnected') {
            return false;
        }
        if (this.participants.has(participant.sid)) {
            return false;
        }
        this.participants.set(participant.sid, participant);
        participant.on('stateChanged', function stateChanged(state) {
            if (state === 'disconnected') {
                participant.removeListener('stateChanged', stateChanged);
                self.participants.delete(participant.sid);
                self.emit('participantDisconnected', participant);
            }
        });
        this.emit('participantConnected', participant);
        return true;
    };
    /**
     * Disconnect.
     * @returns {boolean}
     */ RoomSignaling.prototype.disconnect = function() {
        return this._disconnect();
    };
    /**
     * Set (or unset) the Dominant Speaker.
     * @param {?Participant.SID} dominantSpeakerSid
     * @returns {void}
     */ RoomSignaling.prototype.setDominantSpeaker = function(dominantSpeakerSid) {
        this.dominantSpeakerSid = dominantSpeakerSid;
        this.emit('dominantSpeakerChanged');
    };
    return RoomSignaling;
}(StateMachine);
/**
 * @event RoomSignaling#event:connectionStateChanged
 */ /**
 * @event RoomSignaling#event:dominantSpeakerChanged
 */ /**
 * {@link RemoteParticipantSignaling} connected to the {@link RoomSignaling}.
 * @event RoomSignaling#event:participantConnected
 * @param {RemoteParticipantSignaling} participantSignaling
 */ /**
 * {@link RemoteParticipantSignaling} disconnected from the {@link RoomSignaling}.
 * @event RoomSignaling#event:participantDisconnected
 * @param {RemoteParticipantSignaling} participantSignaling
 */ /**
 * @event RoomSignaling#event:iceConnectionStateChanged
 */ /**
 * @event RoomSignaling#event:signalingConnectionStateChanged
 */ /**
 * Maybe update the {@link RoomSignaling} state.
 * @param {RoomSignaling} roomSignaling
 */ function maybeUpdateState(roomSignaling) {
    if (roomSignaling.state === 'disconnected' || roomSignaling.signalingConnectionState === 'disconnected') {
        roomSignaling._sessionTimeout.clear();
        return;
    }
    var newState;
    if (roomSignaling.signalingConnectionState === 'reconnecting') {
        newState = roomSignaling.signalingConnectionState;
    } else if (roomSignaling.iceConnectionState === 'failed') {
        roomSignaling._mediaConnectionIsReconnecting = true;
        newState = 'reconnecting';
    } else if (roomSignaling.iceConnectionState === 'new' || roomSignaling.iceConnectionState === 'checking') {
        newState = roomSignaling._mediaConnectionIsReconnecting ? 'reconnecting' : 'connected';
    } else {
        roomSignaling._mediaConnectionIsReconnecting = false;
        roomSignaling._reconnectingError = null;
        roomSignaling._sessionTimeout.clear();
        newState = 'connected';
    }
    if (newState === roomSignaling.state) {
        return;
    }
    if (newState === 'reconnecting') {
        roomSignaling._reconnectingError = roomSignaling.signalingConnectionState === 'reconnecting' ? new SignalingConnectionDisconnectedError() : new MediaConnectionError();
        roomSignaling._sessionTimeout.start();
        roomSignaling.preempt(newState, null, [
            roomSignaling._reconnectingError
        ]);
    } else {
        roomSignaling.preempt(newState);
    }
}
module.exports = RoomSignaling; //# sourceMappingURL=room.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/participant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
var NetworkQualityStats = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/networkqualitystats.js [app-client] (ecmascript)");
/*
ParticipantSignaling States
----------------------

    +------------+     +-----------+     +--------------+
    |            |     |           |     |              |
    | connecting |---->| connected |---->| disconnected |
    |            |     |           |     |              |
    +------------+     +-----------+     +--------------+
                           | ^                    ^
                           | |  +--------------+  |
                           | |--|              |  |
                           |--->| reconnecting |--|
                                |              |
                                +--------------+
*/ var states = {
    connecting: [
        'connected'
    ],
    connected: [
        'disconnected',
        'reconnecting'
    ],
    reconnecting: [
        'connected',
        'disconnected'
    ],
    disconnected: []
};
/**
 * A {@link Participant} implementation
 * @extends StateMachine
 * @property {?string} identity
 * @property {?Participant.SID} sid
 * @property {string} state - "connecting", "connected", or "disconnected"
 * @property {Map<Track.ID | Track.SID, TrackSignaling>} tracks
 * @emits ParticipantSignaling#networkQualityLevelChanged
 * @emits ParticipantSignaling#trackAdded
 * @emits ParticipantSignaling#trackRemoved
 */ var ParticipantSignaling = function(_super) {
    __extends(ParticipantSignaling, _super);
    /**
     * Construct a {@link ParticipantSignaling}.
     */ function ParticipantSignaling() {
        var _this = _super.call(this, 'connecting', states) || this;
        Object.defineProperties(_this, {
            _identity: {
                writable: true,
                value: null
            },
            _networkQualityLevel: {
                value: null,
                writable: true
            },
            _networkQualityStats: {
                value: null,
                writable: true
            },
            _sid: {
                writable: true,
                value: null
            },
            identity: {
                enumerable: true,
                get: function() {
                    return this._identity;
                }
            },
            sid: {
                enumerable: true,
                get: function() {
                    return this._sid;
                }
            },
            tracks: {
                enumerable: true,
                value: new Map()
            }
        });
        return _this;
    }
    Object.defineProperty(ParticipantSignaling.prototype, "networkQualityLevel", {
        /**
         * Get the current {@link NetworkQualityLevel}, if any.
         * @returns {?NetworkQualityLevel} networkQualityLevel - initially null
         */ get: function() {
            return this._networkQualityLevel;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ParticipantSignaling.prototype, "networkQualityStats", {
        /**
         * Get the current {@link NetworkQualityStats}
         * @returns {?NetworkQualityStats} networkQualityStats - initially null
         */ get: function() {
            return this._networkQualityStats;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Add the {@link TrackSignaling}, MediaStreamTrack, or
     * {@link DataTrackSender} to the {@link ParticipantSignaling}.
     * @param {TrackSignaling|DataTrackSender|MediaTrackSender} track
     * @returns {this}
     * @fires ParticipantSignaling#trackAdded
     */ ParticipantSignaling.prototype.addTrack = function(track) {
        this.tracks.set(track.id || track.sid, track);
        this.emit('trackAdded', track);
        return this;
    };
    /**
     * Disconnect the {@link ParticipantSignaling}.
     * @returns {boolean}
     */ ParticipantSignaling.prototype.disconnect = function() {
        if (this.state !== 'disconnected') {
            this.preempt('disconnected');
            return true;
        }
        return false;
    };
    /**
     * Remove the {@link TrackSignaling}, MediaStreamTrack, or
     * {@link DataTrackSender} from the {@link ParticipantSignaling}.
     * @param {TrackSignaling|DataTrackSender|MediaTrackSender} track
     * @returns {?TrackSignaling}
     * @fires ParticipantSignaling#trackRemoved
     */ ParticipantSignaling.prototype.removeTrack = function(track) {
        var signaling = this.tracks.get(track.id || track.sid);
        this.tracks.delete(track.id || track.sid);
        if (signaling) {
            this.emit('trackRemoved', track);
        }
        return signaling || null;
    };
    /**
     * @param {NetworkQualityLevel} networkQualityLevel
     * @param {?NetworkQualityLevels} [networkQualityLevels=null]
     * @returns {void}
     */ ParticipantSignaling.prototype.setNetworkQualityLevel = function(networkQualityLevel, networkQualityLevels) {
        if (this._networkQualityLevel !== networkQualityLevel) {
            this._networkQualityLevel = networkQualityLevel;
            this._networkQualityStats = networkQualityLevels && (networkQualityLevels.audio || networkQualityLevels.video) ? new NetworkQualityStats(networkQualityLevels) : null;
            this.emit('networkQualityLevelChanged');
        }
    };
    /**
     * Connect the {@link ParticipantSignaling}.
     * @param {Participant.SID} sid
     * @param {string} identity
     * @returns {boolean}
     */ ParticipantSignaling.prototype.connect = function(sid, identity) {
        if (this.state === 'connecting' || this.state === 'reconnecting') {
            if (!this._sid) {
                this._sid = sid;
            }
            if (!this._identity) {
                this._identity = identity;
            }
            this.preempt('connected');
            return true;
        }
        return false;
    };
    /**
     * Transition to "reconnecting" state.
     * @returns {boolean}
     */ ParticipantSignaling.prototype.reconnecting = function() {
        if (this.state === 'connecting' || this.state === 'connected') {
            this.preempt('reconnecting');
            return true;
        }
        return false;
    };
    return ParticipantSignaling;
}(StateMachine);
/**
 * @event ParticipantSignaling#event:networkQualityLevelChanged
 */ /**
 * {@link TrackSignaling} was added to the {@link ParticipantSignaling}.
 * @event ParticipantSignaling#trackAdded
 * @param {TrackSignaling} track
 */ /**
 * {@link TrackSignaling} was removed from the {@link ParticipantSignaling}.
 * @event ParticipantSignaling#trackRemoved
 * @param {TrackSignaling} track
 */ module.exports = ParticipantSignaling; //# sourceMappingURL=participant.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/remoteparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var ParticipantSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/participant.js [app-client] (ecmascript)");
/**
 * A {@link Participant} implementation
 * @extends ParticipantSignaling
 * @property {string} identity
 * @property {Participant.SID} sid
 */ var RemoteParticipantSignaling = function(_super) {
    __extends(RemoteParticipantSignaling, _super);
    /**
     * Construct a {@link RemoteParticipantSignaling}.
     * @param {Participant.SID} sid
     * @param {string} identity
     */ function RemoteParticipantSignaling(sid, identity) {
        var _this = _super.call(this) || this;
        _this.connect(sid, identity);
        return _this;
    }
    return RemoteParticipantSignaling;
}(ParticipantSignaling);
module.exports = RemoteParticipantSignaling; //# sourceMappingURL=remoteparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/track.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
/**
 * A {@link Track} implementation
 * @extends EventEmitter
 * @property {Track.Kind} kind
 * @property {string} name
 */ var TrackSignaling = function(_super) {
    __extends(TrackSignaling, _super);
    /**
     * Construct a {@link TrackSignaling}.
     * @param {string} name
     * @param {Track.Kind} kind
     * @param {boolean} isEnabled
     * @param {Track.Priority} priority
     */ function TrackSignaling(name, kind, isEnabled, priority) {
        var _this = _super.call(this) || this;
        var sid = null;
        Object.defineProperties(_this, {
            _error: {
                value: null,
                writable: true
            },
            _isEnabled: {
                value: isEnabled,
                writable: true
            },
            _priority: {
                value: priority,
                writable: true
            },
            _trackTransceiver: {
                value: null,
                writable: true
            },
            _sid: {
                get: function() {
                    return sid;
                },
                set: function(_sid) {
                    if (sid === null) {
                        sid = _sid;
                    }
                }
            },
            kind: {
                enumerable: true,
                value: kind
            },
            name: {
                enumerable: true,
                value: name
            }
        });
        return _this;
    }
    Object.defineProperty(TrackSignaling.prototype, "error", {
        /**
         * Non-null if publication or subscription failed.
         * @property {?Error} error
         */ get: function() {
            return this._error;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TrackSignaling.prototype, "isEnabled", {
        /**
         * Whether the {@link TrackSignaling} is enabled.
         * @property {boolean}
         */ get: function() {
            return this._isEnabled;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TrackSignaling.prototype, "priority", {
        /**
         * The {@link TrackSignaling}'s priority.
         * @property {Track.Priority}
         */ get: function() {
            return this._priority;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TrackSignaling.prototype, "sid", {
        /**
         * The {@link TrackSignaling}'s {@link Track.SID}.
         * @property {Track.SID}
         */ get: function() {
            return this._sid;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TrackSignaling.prototype, "trackTransceiver", {
        /**
         * The {@link TrackSignaling}'s {@link TrackTransceiver}.
         * @property {TrackTransceiver}
         */ get: function() {
            return this._trackTransceiver;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Disable the {@link TrackSignaling} if it is not already disabled.
     * @return {this}
     */ TrackSignaling.prototype.disable = function() {
        return this.enable(false);
    };
    /**
     * Enable (or disable) the {@link TrackSignaling} if it is not already enabled
     * (or disabled).
     * @param {boolean} [enabled=true]
     * @return {this}
     */ TrackSignaling.prototype.enable = function(enabled) {
        enabled = typeof enabled === 'boolean' ? enabled : true;
        if (this.isEnabled !== enabled) {
            this._isEnabled = enabled;
            this.emit('updated');
        }
        return this;
    };
    /**
     * Set the {@link TrackTransceiver} on the {@link TrackSignaling}.
     * @param {TrackTransceiver} trackTransceiver
     * @returns {this}
     */ TrackSignaling.prototype.setTrackTransceiver = function(trackTransceiver) {
        trackTransceiver = trackTransceiver || null;
        if (this.trackTransceiver !== trackTransceiver) {
            this._trackTransceiver = trackTransceiver;
            this.emit('updated');
        }
        return this;
    };
    /**
     * Set the SID on the {@link TrackSignaling} once.
     * @param {string} sid
     * @returns {this}
     */ TrackSignaling.prototype.setSid = function(sid) {
        if (this.sid === null) {
            this._sid = sid;
            this.emit('updated');
        }
        return this;
    };
    return TrackSignaling;
}(EventEmitter);
/**
 * Emitted whenever the {@link TrackSignaling} is updated
 * @event TrackSignaling#updated
 */ module.exports = TrackSignaling; //# sourceMappingURL=track.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/remotetrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/track.js [app-client] (ecmascript)");
/**
 * A {@link RemoteTrackPublication} implementation
 * @extends TrackSignaling
 */ var RemoteTrackPublicationSignaling = function(_super) {
    __extends(RemoteTrackPublicationSignaling, _super);
    /**
     * Construct a {@link RemoteTrackPublicationSignaling}.
     * @param {Track.SID} sid
     * @param {string} name
     * @param {Track.Kind} kind
     * @param {boolean} isEnabled
     * @param {Track.Priority} priority
     * @param {boolean} isSwitchedOff
     */ function RemoteTrackPublicationSignaling(sid, name, kind, isEnabled, priority, isSwitchedOff) {
        var _this = _super.call(this, name, kind, isEnabled, priority) || this;
        Object.defineProperties(_this, {
            _isSwitchedOff: {
                value: isSwitchedOff,
                writable: true
            }
        });
        _this.setSid(sid);
        return _this;
    }
    Object.defineProperty(RemoteTrackPublicationSignaling.prototype, "isSubscribed", {
        /**
         * Whether the {@link RemoteTrackPublicationSignaling} is subscribed to.
         * @property {boolean}
         */ get: function() {
            return !!this.trackTransceiver;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RemoteTrackPublicationSignaling.prototype, "isSwitchedOff", {
        /**
         * Whether the {@link RemoteTrackPublicationSignaling} is switched off.
         * @property {boolean}
         */ get: function() {
            return this._isSwitchedOff;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * @param {Error} error
     * @returns {this}
     */ RemoteTrackPublicationSignaling.prototype.subscribeFailed = function(error) {
        if (!this.error) {
            this._error = error;
            this.emit('updated');
        }
        return this;
    };
    /**
     * Update the publish {@link Track.Priority}.
     * @param {Track.Priority} priority
     * @returns {this}
     */ RemoteTrackPublicationSignaling.prototype.setPriority = function(priority) {
        if (this._priority !== priority) {
            this._priority = priority;
            this.emit('updated');
        }
        return this;
    };
    /**
     * Updates track switch on/off state.
     * @param {boolean} isSwitchedOff
     * @returns {this}
     */ RemoteTrackPublicationSignaling.prototype.setSwitchedOff = function(isSwitchedOff) {
        if (this._isSwitchedOff !== isSwitchedOff) {
            this._isSwitchedOff = isSwitchedOff;
            this.emit('updated');
        }
        return this;
    };
    return RemoteTrackPublicationSignaling;
}(TrackSignaling);
module.exports = RemoteTrackPublicationSignaling; //# sourceMappingURL=remotetrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/remotetrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteTrackPublicationSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/remotetrackpublication.js [app-client] (ecmascript)");
/**
 * @extends RemoteTrackPublicationSignaling
 */ var RemoteTrackPublicationV2 = function(_super) {
    __extends(RemoteTrackPublicationV2, _super);
    /**
     * Construct a {@link RemoteTrackPublicationV2}.
     * @param {RemoteTrackPublicationV2#Representation} track
     * @param {boolean} isSwitchedOff
     *
     */ function RemoteTrackPublicationV2(track, isSwitchedOff) {
        return _super.call(this, track.sid, track.name, track.kind, track.enabled, track.priority, isSwitchedOff) || this;
    }
    /**
     * Compare the {@link RemoteTrackPublicationV2} to a
     * {@link RemoteTrackPublicationV2#Representation} of itself and perform any
     * updates necessary.
     * @param {RemoteTrackPublicationV2#Representation} track
     * @returns {this}
     * @fires TrackSignaling#updated
     */ RemoteTrackPublicationV2.prototype.update = function(track) {
        this.enable(track.enabled);
        this.setPriority(track.priority);
        return this;
    };
    return RemoteTrackPublicationV2;
}(RemoteTrackPublicationSignaling);
/**
 * The Room Signaling Protocol (RSP) representation of a {@link RemoteTrackPublicationV2}.
 * @typedef {LocalTrackPublicationV2#Representation} RemoteTrackPublicationV2#Representation
 * @property {boolean} subscribed
 */ module.exports = RemoteTrackPublicationV2; //# sourceMappingURL=remotetrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/remoteparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var RemoteParticipantSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/remoteparticipant.js [app-client] (ecmascript)");
var RemoteTrackPublicationV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/remotetrackpublication.js [app-client] (ecmascript)");
/**
 * @extends RemoteParticipantSignaling
 * @property {?number} revision
 */ var RemoteParticipantV2 = function(_super) {
    __extends(RemoteParticipantV2, _super);
    /**
     * Construct a {@link RemoteParticipantV2}.
     * @param {object} participantState
     * @param {function(Track.SID): boolean} getInitialTrackSwitchOffState
     * @param {function(Track.SID, Track.Priority): boolean} setPriority
     * @param {function(Track.SID, ClientRenderHint): Promise<void>} setRenderHint
     * @param {function(Track.SID): void} clearTrackHint
     * @param {object} [options]
     */ function RemoteParticipantV2(participantState, getInitialTrackSwitchOffState, setPriority, setRenderHint, clearTrackHint, options) {
        var _this = _super.call(this, participantState.sid, participantState.identity) || this;
        options = Object.assign({
            RemoteTrackPublicationV2: RemoteTrackPublicationV2
        }, options);
        Object.defineProperties(_this, {
            _revision: {
                writable: true,
                value: null
            },
            _RemoteTrackPublicationV2: {
                value: options.RemoteTrackPublicationV2
            },
            _getInitialTrackSwitchOffState: {
                value: getInitialTrackSwitchOffState
            },
            updateSubscriberTrackPriority: {
                value: function(trackSid, priority) {
                    return setPriority(trackSid, priority);
                }
            },
            updateTrackRenderHint: {
                value: function(trackSid, renderHint) {
                    return setRenderHint(trackSid, renderHint);
                }
            },
            clearTrackHint: {
                value: function(trackSid) {
                    return clearTrackHint(trackSid);
                }
            },
            revision: {
                enumerable: true,
                get: function() {
                    return this._revision;
                }
            }
        });
        return _this.update(participantState);
    }
    /**
     * @private
     */ RemoteParticipantV2.prototype._getOrCreateTrack = function(trackState) {
        var RemoteTrackPublicationV2 = this._RemoteTrackPublicationV2;
        var track = this.tracks.get(trackState.sid);
        if (!track) {
            var isSwitchedOff = this._getInitialTrackSwitchOffState(trackState.sid);
            track = new RemoteTrackPublicationV2(trackState, isSwitchedOff);
            this.addTrack(track);
        }
        return track;
    };
    /**
     * Update the {@link RemoteParticipantV2} with the new state.
     * @param {object} participantState
     * @returns {this}
     */ RemoteParticipantV2.prototype.update = function(participantState) {
        var _this = this;
        if (this.revision !== null && participantState.revision <= this.revision) {
            return this;
        }
        this._revision = participantState.revision;
        var tracksToKeep = new Set();
        participantState.tracks.forEach(function(trackState) {
            var track = _this._getOrCreateTrack(trackState);
            track.update(trackState);
            tracksToKeep.add(track);
        });
        this.tracks.forEach(function(track) {
            if (!tracksToKeep.has(track)) {
                _this.removeTrack(track);
            }
        });
        switch(participantState.state){
            case 'disconnected':
                this.disconnect();
                break;
            case 'reconnecting':
                this.reconnecting();
                break;
            case 'connected':
                this.connect(this.sid, this.identity);
                break;
        }
        return this;
    };
    return RemoteParticipantV2;
}(RemoteParticipantSignaling);
module.exports = RemoteParticipantV2; //# sourceMappingURL=remoteparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/trackprioritysignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
var TrackPrioritySignaling = function(_super) {
    __extends(TrackPrioritySignaling, _super);
    /**
     * Construct a {@link TrackPrioritySignaling}.
     * @param {Promise<DataTrackReceiver>} getReceiver
     */ function TrackPrioritySignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'track_priority', options) || this;
        Object.defineProperties(_this, {
            _enqueuedPriorityUpdates: {
                value: new Map()
            }
        });
        _this.on('ready', function(transport) {
            Array.from(_this._enqueuedPriorityUpdates.keys()).forEach(function(trackSid) {
                transport.publish({
                    type: 'track_priority',
                    track: trackSid,
                    subscribe: _this._enqueuedPriorityUpdates.get(trackSid)
                });
            // NOTE(mpatwardhan)- we do not clear _enqueuedPriorityUpdates intentionally,
            // this cache will is used to re-send the priorities in case of VMS-FailOver.
            });
        });
        return _this;
    }
    /**
     * @param {Track.SID} trackSid
     * @param {'publish'|'subscribe'} publishOrSubscribe
     * @param {Track.Priority} priority
     */ TrackPrioritySignaling.prototype.sendTrackPriorityUpdate = function(trackSid, publishOrSubscribe, priority) {
        if (publishOrSubscribe !== 'subscribe') {
            throw new Error('only subscribe priorities are supported, found: ' + publishOrSubscribe);
        }
        this._enqueuedPriorityUpdates.set(trackSid, priority);
        if (this._transport) {
            this._transport.publish({
                type: 'track_priority',
                track: trackSid,
                subscribe: priority
            });
        }
    };
    return TrackPrioritySignaling;
}(MediaSignaling);
module.exports = TrackPrioritySignaling; //# sourceMappingURL=trackprioritysignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/trackswitchoffsignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
/**
 * @emits TrackSwitchOffSignalinging#updated
 */ var TrackSwitchOffSignaling = function(_super) {
    __extends(TrackSwitchOffSignaling, _super);
    /**
     * Construct a {@link TrackSwitchOffSignaling}.
     * @param {Promise<DataTrackReceiver>} getReceiver
     */ function TrackSwitchOffSignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'track_switch_off', options) || this;
        _this.on('ready', function(transport) {
            transport.on('message', function(message) {
                switch(message.type){
                    case 'track_switch_off':
                        _this._setTrackSwitchOffUpdates(message.off || [], message.on || []);
                        break;
                    default:
                        break;
                }
            });
        });
        return _this;
    }
    /**
     * @private
     * @param {[Track.SID]} tracksSwitchedOff
     * @param {[Track.SID]} tracksSwitchedOn
     * @returns {void}
     */ TrackSwitchOffSignaling.prototype._setTrackSwitchOffUpdates = function(tracksSwitchedOff, tracksSwitchedOn) {
        this.emit('updated', tracksSwitchedOff, tracksSwitchedOn);
    };
    return TrackSwitchOffSignaling;
}(MediaSignaling);
/**
 * @event TrackSwitchOffSignaling#updated
 */ module.exports = TrackSwitchOffSignaling; //# sourceMappingURL=trackswitchoffsignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/renderhintssignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint callback-return:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
var Timeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var isDeepEqual = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").isDeepEqual;
var RENDER_HINT_RESPONSE_TIME_MS = 2000; // time to wait for server response (before resending all hints.)
var messageId = 1;
var RenderHintsSignaling = function(_super) {
    __extends(RenderHintsSignaling, _super);
    /**
     * Construct a {@link RenderHintsSignaling}.
     */ function RenderHintsSignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'render_hints', options) || this;
        Object.defineProperties(_this, {
            _trackSidsToRenderHints: {
                value: new Map()
            },
            _responseTimer: {
                value: new Timeout(function() {
                    _this._sendAllHints();
                    // once timer fires, for next round double the delay.
                    _this._responseTimer.setDelay(_this._responseTimer.delay * 2);
                }, RENDER_HINT_RESPONSE_TIME_MS, false)
            }
        });
        _this.on('ready', function(transport) {
            transport.on('message', function(message) {
                _this._log.debug('Incoming: ', message);
                switch(message.type){
                    case 'render_hints':
                        _this._processHintResults(message && message.subscriber && message.subscriber.hints || []);
                        break;
                    default:
                        _this._log.warn('Unknown message type: ', message.type);
                        break;
                }
            });
            // NOTE(mpatwardhan): When transport is set (either 1st time of after vms failover)
            // resend all track states.
            _this._sendAllHints();
        });
        return _this;
    }
    RenderHintsSignaling.prototype._sendAllHints = function() {
        var _this = this;
        // to force sending all hints simply mark all tracks as dirty.
        Array.from(this._trackSidsToRenderHints.keys()).forEach(function(trackSid) {
            var trackState = _this._trackSidsToRenderHints.get(trackSid);
            if (trackState.renderDimensions) {
                trackState.isDimensionDirty = true;
            }
            if ('enabled' in trackState) {
                trackState.isEnabledDirty = true;
            }
        });
        this._sendHints();
    };
    RenderHintsSignaling.prototype._processHintResults = function(hintResults) {
        var _this = this;
        this._responseTimer.clear();
        this._responseTimer.setDelay(RENDER_HINT_RESPONSE_TIME_MS);
        hintResults.forEach(function(hintResult) {
            if (hintResult.result !== 'OK') {
                _this._log.debug('Server error processing hint:', hintResult);
            }
        });
        this._sendHints();
    };
    RenderHintsSignaling.prototype._sendHints = function() {
        var _this = this;
        if (!this._transport || this._responseTimer.isSet) {
            return;
        }
        var hints = [];
        Array.from(this._trackSidsToRenderHints.keys()).forEach(function(trackSid) {
            var trackState = _this._trackSidsToRenderHints.get(trackSid);
            if (trackState.isEnabledDirty || trackState.isDimensionDirty) {
                var mspHint = {
                    'track': trackSid
                };
                if (trackState.isEnabledDirty) {
                    mspHint.enabled = trackState.enabled;
                    trackState.isEnabledDirty = false;
                }
                if (trackState.isDimensionDirty) {
                    // eslint-disable-next-line camelcase
                    mspHint.render_dimensions = trackState.renderDimensions;
                    trackState.isDimensionDirty = false;
                }
                hints.push(mspHint);
            }
        });
        if (hints.length > 0) {
            var payLoad = {
                type: 'render_hints',
                subscriber: {
                    id: messageId++,
                    hints: hints
                }
            };
            this._log.debug('Outgoing: ', payLoad);
            this._transport.publish(payLoad);
            this._responseTimer.start();
        }
    };
    /**
     * @param {Track.SID} trackSid
     * @param {ClientRenderHint} renderHint
     */ RenderHintsSignaling.prototype.setTrackHint = function(trackSid, renderHint) {
        var trackState = this._trackSidsToRenderHints.get(trackSid) || {
            isEnabledDirty: false,
            isDimensionDirty: false
        };
        if ('enabled' in renderHint && trackState.enabled !== renderHint.enabled) {
            trackState.enabled = !!renderHint.enabled;
            trackState.isEnabledDirty = true;
        }
        if (renderHint.renderDimensions && !isDeepEqual(renderHint.renderDimensions, trackState.renderDimensions)) {
            // eslint-disable-next-line camelcase
            trackState.renderDimensions = renderHint.renderDimensions;
            trackState.isDimensionDirty = true;
        }
        this._trackSidsToRenderHints.set(trackSid, trackState);
        this._sendHints();
    };
    /**
     * must be called when track is unsubscribed.
     * @param {Track.SID} trackSid
     */ RenderHintsSignaling.prototype.clearTrackHint = function(trackSid) {
        this._trackSidsToRenderHints.delete(trackSid);
    };
    return RenderHintsSignaling;
}(MediaSignaling);
module.exports = RenderHintsSignaling; //# sourceMappingURL=renderhintssignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/publisherhintsignaling.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint callback-return:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var MediaSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/mediasignaling.js [app-client] (ecmascript)");
var messageId = 1;
var PublisherHintsSignaling = function(_super) {
    __extends(PublisherHintsSignaling, _super);
    /**
     * Construct a {@link RenderHintsSignaling}.
     */ function PublisherHintsSignaling(getReceiver, options) {
        var _this = _super.call(this, getReceiver, 'publisher_hints', options) || this;
        _this.on('ready', function(transport) {
            _this._log.debug('publisher_hints transport ready:', transport);
            transport.on('message', function(message) {
                _this._log.debug('Incoming: ', message);
                switch(message.type){
                    case 'publisher_hints':
                        if (message.publisher && message.publisher.hints && message.publisher.id) {
                            _this._processPublisherHints(message.publisher.hints, message.publisher.id);
                        }
                        break;
                    default:
                        _this._log.warn('Unknown message type: ', message.type);
                        break;
                }
            });
        });
        return _this;
    }
    PublisherHintsSignaling.prototype.sendTrackReplaced = function(_a) {
        var trackSid = _a.trackSid;
        if (!this._transport) {
            return;
        }
        var payLoad = {
            type: 'client_reset',
            track: trackSid,
            id: messageId++
        };
        this._log.debug('Outgoing: ', payLoad);
        this._transport.publish(payLoad);
    };
    PublisherHintsSignaling.prototype.sendHintResponse = function(_a) {
        var id = _a.id, hints = _a.hints;
        if (!this._transport) {
            return;
        }
        var payLoad = {
            type: 'publisher_hints',
            id: id,
            hints: hints
        };
        this._log.debug('Outgoing: ', payLoad);
        this._transport.publish(payLoad);
    };
    /**
     * @private
     */ PublisherHintsSignaling.prototype._processPublisherHints = function(hints, id) {
        try {
            this.emit('updated', hints, id);
        } catch (ex) {
            this._log.error('error processing hints:', ex);
        }
    };
    return PublisherHintsSignaling;
}(MediaSignaling);
module.exports = PublisherHintsSignaling; //# sourceMappingURL=publisherhintsignaling.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/room.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable no-console */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var DominantSpeakerSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/dominantspeakersignaling.js [app-client] (ecmascript)");
var TranscriptionSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/transcriptionsignaling.js [app-client] (ecmascript)");
var NetworkQualityMonitor = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/networkqualitymonitor.js [app-client] (ecmascript)");
var NetworkQualitySignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/networkqualitysignaling.js [app-client] (ecmascript)");
var RecordingV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/recording.js [app-client] (ecmascript)");
var RoomSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/room.js [app-client] (ecmascript)");
var RemoteParticipantV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/remoteparticipant.js [app-client] (ecmascript)");
var StatsReport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/stats/statsreport.js [app-client] (ecmascript)");
var TrackPrioritySignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/trackprioritysignaling.js [app-client] (ecmascript)");
var TrackSwitchOffSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/trackswitchoffsignaling.js [app-client] (ecmascript)");
var RenderHintsSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/renderhintssignaling.js [app-client] (ecmascript)");
var PublisherHintsSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/publisherhintsignaling.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), DEFAULT_SESSION_TIMEOUT_SEC = _a.constants.DEFAULT_SESSION_TIMEOUT_SEC, createBandwidthProfilePayload = _a.createBandwidthProfilePayload, defer = _a.defer, difference = _a.difference, filterObject = _a.filterObject, flatMap = _a.flatMap, oncePerTick = _a.oncePerTick;
var MovingAverageDelta = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/movingaveragedelta.js [app-client] (ecmascript)");
var createTwilioError = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)").createTwilioError;
var STATS_PUBLISH_INTERVAL_MS = 10000;
/**
 * @extends RoomSignaling
 */ var RoomV2 = function(_super) {
    __extends(RoomV2, _super);
    function RoomV2(localParticipant, initialState, transport, peerConnectionManager, options) {
        var _this = this;
        initialState.options = Object.assign({
            session_timeout: DEFAULT_SESSION_TIMEOUT_SEC
        }, initialState.options);
        options = Object.assign({
            DominantSpeakerSignaling: DominantSpeakerSignaling,
            TranscriptionSignaling: TranscriptionSignaling,
            NetworkQualityMonitor: NetworkQualityMonitor,
            NetworkQualitySignaling: NetworkQualitySignaling,
            RecordingSignaling: RecordingV2,
            RemoteParticipantV2: RemoteParticipantV2,
            TrackPrioritySignaling: TrackPrioritySignaling,
            TrackSwitchOffSignaling: TrackSwitchOffSignaling,
            bandwidthProfile: null,
            sessionTimeout: initialState.options.session_timeout * 1000,
            statsPublishIntervalMs: STATS_PUBLISH_INTERVAL_MS
        }, options);
        localParticipant.setBandwidthProfile(options.bandwidthProfile);
        var _a = initialState.options, signalingRegion = _a.signaling_region, _b = _a.audio_processors, audioProcessors = _b === void 0 ? [] : _b;
        localParticipant.setSignalingRegion(signalingRegion);
        if (audioProcessors.includes('krisp')) {
            // Note(mpatwardhan): we add rnnoise as allowed_processor to enable testing our pipeline e2e.
            audioProcessors.push('rnnoise');
        }
        localParticipant.setAudioProcessors(audioProcessors);
        peerConnectionManager.setIceReconnectTimeout(options.sessionTimeout);
        _this = _super.call(this, localParticipant, initialState.sid, initialState.name, options) || this;
        var getTrackReceiver = function(id) {
            return _this._getTrackReceiver(id);
        };
        var log = _this._log;
        Object.defineProperties(_this, {
            _disconnectedParticipantRevisions: {
                value: new Map()
            },
            _NetworkQualityMonitor: {
                value: options.NetworkQualityMonitor
            },
            _lastBandwidthProfileRevision: {
                value: localParticipant.bandwidthProfileRevision,
                writable: true
            },
            _mediaStatesWarningsRevision: {
                value: 0,
                writable: true
            },
            _networkQualityMonitor: {
                value: null,
                writable: true
            },
            _networkQualityConfiguration: {
                value: localParticipant.networkQualityConfiguration
            },
            _peerConnectionManager: {
                value: peerConnectionManager
            },
            _published: {
                value: new Map()
            },
            _publishedRevision: {
                value: 0,
                writable: true
            },
            _RemoteParticipantV2: {
                value: options.RemoteParticipantV2
            },
            _subscribed: {
                value: new Map()
            },
            _subscribedRevision: {
                value: 0,
                writable: true
            },
            _subscriptionFailures: {
                value: new Map()
            },
            _dominantSpeakerSignaling: {
                value: new options.DominantSpeakerSignaling(getTrackReceiver, {
                    log: log
                })
            },
            _transcriptionSignaling: {
                value: new options.TranscriptionSignaling(getTrackReceiver, {
                    log: log
                })
            },
            _networkQualitySignaling: {
                value: new options.NetworkQualitySignaling(getTrackReceiver, localParticipant.networkQualityConfiguration, {
                    log: log
                })
            },
            _renderHintsSignaling: {
                value: new RenderHintsSignaling(getTrackReceiver, {
                    log: log
                })
            },
            _publisherHintsSignaling: {
                value: new PublisherHintsSignaling(getTrackReceiver, {
                    log: log
                })
            },
            _trackPrioritySignaling: {
                value: new options.TrackPrioritySignaling(getTrackReceiver, {
                    log: log
                })
            },
            _trackSwitchOffSignaling: {
                value: new options.TrackSwitchOffSignaling(getTrackReceiver, {
                    log: log
                })
            },
            _pendingSwitchOffStates: {
                value: new Map()
            },
            _transport: {
                value: transport
            },
            _trackReceiverDeferreds: {
                value: new Map()
            },
            mediaRegion: {
                enumerable: true,
                value: initialState.options.media_region || null
            }
        });
        _this._initTrackSwitchOffSignaling();
        _this._initDominantSpeakerSignaling();
        _this._initTranscriptionSignaling();
        _this._initNetworkQualityMonitorSignaling();
        _this._initPublisherHintSignaling();
        handleLocalParticipantEvents(_this, localParticipant);
        handlePeerConnectionEvents(_this, peerConnectionManager);
        handleTransportEvents(_this, transport);
        periodicallyPublishStats(_this, transport, options.statsPublishIntervalMs);
        _this._update(initialState);
        // NOTE(mpatwardhan) after initial state we know if publisher_hints are enabled or not
        // if they are not enabled. we need to undo simulcast that was enabled with initial offer.
        _this._peerConnectionManager.setEffectiveAdaptiveSimulcast(_this._publisherHintsSignaling.isSetup);
        return _this;
    }
    Object.defineProperty(RoomV2.prototype, "connectionState", {
        /**
         * The PeerConnection state.
         * @property {RTCPeerConnectionState}
         */ get: function() {
            return this._peerConnectionManager.connectionState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RoomV2.prototype, "signalingConnectionState", {
        /**
         * The Signaling Connection State.
         * @property {string} - "connected", "reconnecting", "disconnected"
         */ get: function() {
            return this._transport.state === 'syncing' ? 'reconnecting' : this._transport.state;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RoomV2.prototype, "iceConnectionState", {
        /**
         * The Ice Connection State.
         * @property {RTCIceConnectionState}
         */ get: function() {
            return this._peerConnectionManager.iceConnectionState;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * @private
     */ RoomV2.prototype._deleteTrackReceiverDeferred = function(id) {
        return this._trackReceiverDeferreds.delete(id);
    };
    /**
     * @private
     */ RoomV2.prototype._getOrCreateTrackReceiverDeferred = function(id) {
        var deferred = this._trackReceiverDeferreds.get(id) || defer();
        var trackReceivers = this._peerConnectionManager.getTrackReceivers();
        // NOTE(mmalavalli): In Firefox, there can be instances where a MediaStreamTrack
        // for the given Track ID already exists, for example, when a Track is removed
        // and added back. If that is the case, then we should resolve 'deferred'.
        var trackReceiver = trackReceivers.find(function(trackReceiver) {
            return trackReceiver.id === id && trackReceiver.readyState !== 'ended';
        });
        if (trackReceiver) {
            deferred.resolve(trackReceiver);
        } else {
            // NOTE(mmalavalli): Only add the 'deferred' to the map if it's not
            // resolved. This will prevent old copies of the MediaStreamTrack from
            // being used when the remote peer removes and re-adds a MediaStreamTrack.
            this._trackReceiverDeferreds.set(id, deferred);
        }
        return deferred;
    };
    /**
     * @private
     */ RoomV2.prototype._addTrackReceiver = function(trackReceiver) {
        var deferred = this._getOrCreateTrackReceiverDeferred(trackReceiver.id);
        deferred.resolve(trackReceiver);
        return this;
    };
    /**
     * @private
     */ RoomV2.prototype._disconnect = function(error) {
        var didDisconnect = _super.prototype._disconnect.call(this, error);
        if (didDisconnect) {
            this._teardownNetworkQualityMonitor();
            this._transport.disconnect();
            this._peerConnectionManager.close();
        }
        this.localParticipant.tracks.forEach(function(track) {
            track.publishFailed(error || new Error('LocalParticipant disconnected'));
        });
        return didDisconnect;
    };
    /**
     * @private
     */ RoomV2.prototype._getTrackReceiver = function(id) {
        var _this = this;
        return this._getOrCreateTrackReceiverDeferred(id).promise.then(function(trackReceiver) {
            _this._deleteTrackReceiverDeferred(id);
            return trackReceiver;
        });
    };
    /**
     * @private
     */ RoomV2.prototype._getInitialTrackSwitchOffState = function(trackSid) {
        var initiallySwitchedOff = this._pendingSwitchOffStates.get(trackSid) || false;
        this._pendingSwitchOffStates.delete(trackSid);
        if (initiallySwitchedOff) {
            this._log.warn("[" + trackSid + "] was initially switched off! ");
        }
        return initiallySwitchedOff;
    };
    /**
     * @private
     */ RoomV2.prototype._getTrackSidsToTrackSignalings = function() {
        var trackSidsToTrackSignalings = flatMap(this.participants, function(participant) {
            return Array.from(participant.tracks);
        });
        return new Map(trackSidsToTrackSignalings);
    };
    /**
     * @private
     */ RoomV2.prototype._getOrCreateRemoteParticipant = function(participantState) {
        var _this = this;
        var RemoteParticipantV2 = this._RemoteParticipantV2;
        var participant = this.participants.get(participantState.sid);
        var self = this;
        if (!participant) {
            participant = new RemoteParticipantV2(participantState, function(trackSid) {
                return _this._getInitialTrackSwitchOffState(trackSid);
            }, function(trackSid, priority) {
                return _this._trackPrioritySignaling.sendTrackPriorityUpdate(trackSid, 'subscribe', priority);
            }, function(trackSid, hint) {
                return _this._renderHintsSignaling.setTrackHint(trackSid, hint);
            }, function(trackSid) {
                return _this._renderHintsSignaling.clearTrackHint(trackSid);
            });
            participant.on('stateChanged', function stateChanged(state) {
                if (state === 'disconnected') {
                    participant.removeListener('stateChanged', stateChanged);
                    self.participants.delete(participant.sid);
                    self._disconnectedParticipantRevisions.set(participant.sid, participant.revision);
                }
            });
            this.connectParticipant(participant);
        }
        return participant;
    };
    /**
     * @private
     */ RoomV2.prototype._getState = function() {
        return {
            participant: this.localParticipant.getState()
        };
    };
    /**
     * @private
     */ RoomV2.prototype._maybeAddBandwidthProfile = function(update) {
        var _a = this.localParticipant, bandwidthProfile = _a.bandwidthProfile, bandwidthProfileRevision = _a.bandwidthProfileRevision;
        if (bandwidthProfile && this._lastBandwidthProfileRevision < bandwidthProfileRevision) {
            this._lastBandwidthProfileRevision = bandwidthProfileRevision;
            return Object.assign({
                bandwidth_profile: createBandwidthProfilePayload(bandwidthProfile)
            }, update);
        }
        return update;
    };
    /**
     * @private
     */ RoomV2.prototype._publishNewLocalParticipantState = function() {
        this._transport.publish(this._maybeAddBandwidthProfile(this._getState()));
    };
    /**
     * @private
     */ RoomV2.prototype._publishPeerConnectionState = function(peerConnectionState) {
        /* eslint camelcase:0 */ this._transport.publish(Object.assign({
            peer_connections: [
                peerConnectionState
            ]
        }, this._getState()));
    };
    /**
     * @private
     */ RoomV2.prototype._update = function(roomState) {
        var _this = this;
        if (roomState.subscribed && roomState.subscribed.revision > this._subscribedRevision) {
            this._subscribedRevision = roomState.subscribed.revision;
            roomState.subscribed.tracks.forEach(function(trackState) {
                if (trackState.id) {
                    _this._subscriptionFailures.delete(trackState.sid);
                    _this._subscribed.set(trackState.sid, trackState.id);
                } else if (trackState.error && !_this._subscriptionFailures.has(trackState.sid)) {
                    _this._subscriptionFailures.set(trackState.sid, trackState.error);
                }
            });
            var subscribedTrackSids_1 = new Set(roomState.subscribed.tracks.filter(function(trackState) {
                return !!trackState.id;
            }).map(function(trackState) {
                return trackState.sid;
            }));
            this._subscribed.forEach(function(trackId, trackSid) {
                if (!subscribedTrackSids_1.has(trackSid)) {
                    _this._subscribed.delete(trackSid);
                }
            });
        }
        var participantsToKeep = new Set();
        // eslint-disable-next-line no-warning-comments
        // TODO(mroberts): Remove me once the Server is fixed.
        (roomState.participants || []).forEach(function(participantState) {
            if (participantState.sid === _this.localParticipant.sid) {
                return;
            }
            // NOTE(mmalavalli): If the incoming revision for a disconnected Participant is less than or
            // equal to the revision when it was disconnected, then the state is old and can be ignored.
            // Otherwise, the Participant was most likely disconnected in a Large Group Room when it
            // stopped publishing media, and hence needs to be re-added.
            var disconnectedParticipantRevision = _this._disconnectedParticipantRevisions.get(participantState.sid);
            if (disconnectedParticipantRevision && participantState.revision <= disconnectedParticipantRevision) {
                return;
            }
            if (disconnectedParticipantRevision) {
                _this._disconnectedParticipantRevisions.delete(participantState.sid);
            }
            var participant = _this._getOrCreateRemoteParticipant(participantState);
            participant.update(participantState);
            participantsToKeep.add(participant);
        });
        if (roomState.type === 'synced') {
            this.participants.forEach(function(participant) {
                if (!participantsToKeep.has(participant)) {
                    participant.disconnect();
                }
            });
        }
        handleSubscriptions(this);
        // eslint-disable-next-line no-warning-comments
        // TODO(mroberts): Remove me once the Server is fixed.
        /* eslint camelcase:0 */ if (roomState.peer_connections) {
            this._peerConnectionManager.update(roomState.peer_connections, roomState.type === 'synced');
        }
        if (roomState.recording) {
            this.recording.update(roomState.recording);
        }
        if (roomState.published && roomState.published.revision > this._publishedRevision) {
            this._publishedRevision = roomState.published.revision;
            roomState.published.tracks.forEach(function(track) {
                if (track.sid) {
                    _this._published.set(track.id, track.sid);
                }
            });
            this.localParticipant.update(roomState.published);
        }
        if (roomState.participant) {
            this.localParticipant.connect(roomState.participant.sid, roomState.participant.identity);
        }
        [
            this._dominantSpeakerSignaling,
            this._transcriptionSignaling,
            this._networkQualitySignaling,
            this._trackPrioritySignaling,
            this._trackSwitchOffSignaling,
            this._renderHintsSignaling,
            this._publisherHintsSignaling
        ].forEach(function(mediaSignaling) {
            var channel = mediaSignaling.channel;
            if (!mediaSignaling.isSetup && roomState.media_signaling && roomState.media_signaling[channel] && roomState.media_signaling[channel].transport && roomState.media_signaling[channel].transport.type === 'data-channel') {
                mediaSignaling.setup(roomState.media_signaling[channel].transport.label);
            }
        });
        if (roomState.type === 'warning' && roomState.states && roomState.states.revision > this._mediaStatesWarningsRevision) {
            this._mediaStatesWarningsRevision = roomState.states.revision;
            this.localParticipant.updateMediaStates(roomState.states);
        }
        return this;
    };
    RoomV2.prototype._initPublisherHintSignaling = function() {
        var _this = this;
        this._publisherHintsSignaling.on('updated', function(hints, id) {
            Promise.all(hints.map(function(hint) {
                return _this.localParticipant.setPublisherHint(hint.track, hint.encodings).then(function(result) {
                    return {
                        track: hint.track,
                        result: result
                    };
                });
            })).then(function(hintResponses) {
                _this._publisherHintsSignaling.sendHintResponse({
                    id: id,
                    hints: hintResponses
                });
            });
        });
        var handleReplaced = function(track) {
            if (track.kind === 'video') {
                track.trackTransceiver.on('replaced', function() {
                    _this._publisherHintsSignaling.sendTrackReplaced({
                        trackSid: track.sid
                    });
                });
            }
        };
        // hook up for any existing and new tracks getting replaced.
        Array.from(this.localParticipant.tracks.values()).forEach(function(track) {
            return handleReplaced(track);
        });
        this.localParticipant.on('trackAdded', function(track) {
            return handleReplaced(track);
        });
    };
    RoomV2.prototype._initTrackSwitchOffSignaling = function() {
        var _this = this;
        this._trackSwitchOffSignaling.on('updated', function(tracksOff, tracksOn) {
            try {
                _this._log.debug('received trackSwitch: ', {
                    tracksOn: tracksOn,
                    tracksOff: tracksOff
                });
                var trackUpdates_1 = new Map();
                tracksOn.forEach(function(trackSid) {
                    return trackUpdates_1.set(trackSid, true);
                });
                tracksOff.forEach(function(trackSid) {
                    if (trackUpdates_1.get(trackSid)) {
                        // NOTE(mpatwardhan): This means that VIDEO-3762 has been reproduced.
                        _this._log.warn(trackSid + " is DUPLICATED in both tracksOff and tracksOn list");
                    }
                    trackUpdates_1.set(trackSid, false);
                });
                _this.participants.forEach(function(participant) {
                    participant.tracks.forEach(function(track) {
                        var isOn = trackUpdates_1.get(track.sid);
                        if (typeof isOn !== 'undefined') {
                            track.setSwitchedOff(!isOn);
                            trackUpdates_1.delete(track.sid);
                        }
                    });
                });
                // NOTE(mpatwardhan): Cache any notification about the tracks that we do not yet know about.
                trackUpdates_1.forEach(function(isOn, trackSid) {
                    return _this._pendingSwitchOffStates.set(trackSid, !isOn);
                });
            } catch (ex) {
                _this._log.error('error processing track switch off:', ex);
            }
        });
    };
    RoomV2.prototype._initDominantSpeakerSignaling = function() {
        var _this = this;
        this._dominantSpeakerSignaling.on('updated', function() {
            return _this.setDominantSpeaker(_this._dominantSpeakerSignaling.loudestParticipantSid);
        });
    };
    RoomV2.prototype._initTranscriptionSignaling = function() {
        var _this = this;
        this._transcriptionSignaling.on('transcription', function(data) {
            _this.emit('transcription', data);
        });
    };
    RoomV2.prototype._initNetworkQualityMonitorSignaling = function() {
        var _this = this;
        this._networkQualitySignaling.on('ready', function() {
            var networkQualityMonitor = new _this._NetworkQualityMonitor(_this._peerConnectionManager, _this._networkQualitySignaling);
            _this._networkQualityMonitor = networkQualityMonitor;
            networkQualityMonitor.on('updated', function() {
                if (_this.iceConnectionState === 'failed') {
                    return;
                }
                _this.localParticipant.setNetworkQualityLevel(networkQualityMonitor.level, networkQualityMonitor.levels);
                _this.participants.forEach(function(participant) {
                    var levels = networkQualityMonitor.remoteLevels.get(participant.sid);
                    if (levels) {
                        participant.setNetworkQualityLevel(levels.level, levels);
                    }
                });
            });
            networkQualityMonitor.start();
        });
        this._networkQualitySignaling.on('teardown', function() {
            return _this._teardownNetworkQualityMonitor();
        });
    };
    RoomV2.prototype._teardownNetworkQualityMonitor = function() {
        if (this._networkQualityMonitor) {
            this._networkQualityMonitor.stop();
            this._networkQualityMonitor = null;
        }
    };
    /**
     * Get the {@link RoomV2}'s media statistics.
     * @returns {Promise.<Map<PeerConnectionV2#id, StandardizedStatsResponse>>}
     */ RoomV2.prototype.getStats = function() {
        var _this = this;
        return this._peerConnectionManager.getStats().then(function(responses) {
            return new Map(Array.from(responses).map(function(_a) {
                var _b = __read(_a, 2), id = _b[0], response = _b[1];
                return [
                    id,
                    Object.assign({}, response, {
                        localAudioTrackStats: filterAndAddLocalTrackSids(_this, response.localAudioTrackStats),
                        localVideoTrackStats: filterAndAddLocalTrackSids(_this, response.localVideoTrackStats),
                        remoteAudioTrackStats: filterAndAddRemoteTrackSids(_this, response.remoteAudioTrackStats),
                        remoteVideoTrackStats: filterAndAddRemoteTrackSids(_this, response.remoteVideoTrackStats)
                    })
                ];
            }));
        });
    };
    return RoomV2;
}(RoomSignaling);
/**
 * Filter out {@link TrackStats} that aren't in the collection while also
 * stamping their Track SIDs.
 * @param {Map<ID, SID>} idToSid
 * @param {Array<TrackStats>} trackStats
 * @returns {Array<TrackStats>}
 */ function filterAndAddTrackSids(idToSid, trackStats) {
    return trackStats.reduce(function(trackStats, trackStat) {
        var trackSid = idToSid.get(trackStat.trackId);
        return trackSid ? [
            Object.assign({}, trackStat, {
                trackSid: trackSid
            })
        ].concat(trackStats) : trackStats;
    }, []);
}
/**
 * Filter out {@link LocalTrackStats} that aren't currently published while also
 * stamping their Track SIDs.
 * @param {RoomV2} roomV2
 * @param {Array<LocalTrackStats>} localTrackStats
 * @returns {Array<LocalTrackStats>}
 */ function filterAndAddLocalTrackSids(roomV2, localTrackStats) {
    return filterAndAddTrackSids(roomV2._published, localTrackStats);
}
/**
 * Filter out {@link RemoteTrackStats} that aren't currently subscribed while
 * also stamping their Track SIDs.
 * @param {RoomV2} roomV2
 * @param {Array<RemoteTrackStats>} remoteTrackStats
 * @returns {Array<RemoteTrackStats>}
 */ function filterAndAddRemoteTrackSids(roomV2, remoteTrackStats) {
    var idToSid = new Map(Array.from(roomV2._subscribed.entries()).map(function(_a) {
        var _b = __read(_a, 2), sid = _b[0], id = _b[1];
        return [
            id,
            sid
        ];
    }));
    return filterAndAddTrackSids(idToSid, remoteTrackStats);
}
/**
 * @typedef {object} RoomV2#Representation
 * @property {string} name
 * @property {LocalParticipantV2#Representation} participant
 * @property {?Array<RemoteParticipantV2#Representation>} participants
 * @property {?Array<PeerConnectionV2#Representation>} peer_connections
 * @property {?RecordingV2#Representation} recording
 * @property {string} sid
 */ function handleLocalParticipantEvents(roomV2, localParticipant) {
    var localParticipantUpdated = oncePerTick(function() {
        roomV2._publishNewLocalParticipantState();
    });
    var renegotiate = oncePerTick(function() {
        var trackSenders = flatMap(localParticipant.tracks, function(trackV2) {
            return trackV2.trackTransceiver;
        });
        roomV2._peerConnectionManager.setTrackSenders(trackSenders);
    });
    localParticipant.on('trackAdded', renegotiate);
    localParticipant.on('trackRemoved', renegotiate);
    localParticipant.on('updated', localParticipantUpdated);
    roomV2.on('stateChanged', function stateChanged(state) {
        if (state === 'disconnected') {
            localParticipant.removeListener('trackAdded', renegotiate);
            localParticipant.removeListener('trackRemoved', renegotiate);
            localParticipant.removeListener('updated', localParticipantUpdated);
            roomV2.removeListener('stateChanged', stateChanged);
            localParticipant.disconnect();
        }
    });
    roomV2.on('signalingConnectionStateChanged', function() {
        var localParticipant = roomV2.localParticipant, signalingConnectionState = roomV2.signalingConnectionState;
        var identity = localParticipant.identity, sid = localParticipant.sid;
        switch(signalingConnectionState){
            case 'connected':
                localParticipant.connect(sid, identity);
                break;
            case 'reconnecting':
                localParticipant.reconnecting();
                break;
        }
    });
}
function handlePeerConnectionEvents(roomV2, peerConnectionManager) {
    peerConnectionManager.on('description', function onDescription(description) {
        roomV2._publishPeerConnectionState(description);
    });
    peerConnectionManager.dequeue('description');
    peerConnectionManager.on('candidates', function onCandidates(candidates) {
        roomV2._publishPeerConnectionState(candidates);
    });
    peerConnectionManager.dequeue('candidates');
    peerConnectionManager.on('trackAdded', roomV2._addTrackReceiver.bind(roomV2));
    peerConnectionManager.dequeue('trackAdded');
    peerConnectionManager.getTrackReceivers().forEach(roomV2._addTrackReceiver, roomV2);
    peerConnectionManager.on('connectionStateChanged', function() {
        roomV2.emit('connectionStateChanged');
    });
    peerConnectionManager.on('iceConnectionStateChanged', function() {
        roomV2.emit('iceConnectionStateChanged');
        if (roomV2.iceConnectionState === 'failed') {
            if (roomV2.localParticipant.networkQualityLevel !== null) {
                roomV2.localParticipant.setNetworkQualityLevel(0);
            }
            roomV2.participants.forEach(function(participant) {
                if (participant.networkQualityLevel !== null) {
                    participant.setNetworkQualityLevel(0);
                }
            });
        }
    });
}
function handleTransportEvents(roomV2, transport) {
    transport.on('message', roomV2._update.bind(roomV2));
    transport.on('stateChanged', function stateChanged(state, error) {
        if (state === 'disconnected') {
            if (roomV2.state !== 'disconnected') {
                roomV2._disconnect(error);
            }
            transport.removeListener('stateChanged', stateChanged);
        }
        roomV2.emit('signalingConnectionStateChanged');
    });
}
/**
 * Periodically publish {@link StatsReport}s.
 * @private
 * @param {RoomV2} roomV2
 * @param {Transport} transport
 * @param {Number} intervalMs
 */ function periodicallyPublishStats(roomV2, transport, intervalMs) {
    var movingAverageDeltas = new Map();
    var oddPublishCount = false;
    var interval = setInterval(function() {
        roomV2.getStats().then(function(stats) {
            oddPublishCount = !oddPublishCount;
            stats.forEach(function(response, id) {
                // NOTE(mmalavalli): A StatsReport is used to publish a "stats-report"
                // event instead of using StandardizedStatsResponse directly because
                // StatsReport will add zeros to properties that do not exist.
                var report = new StatsReport(id, response, true);
                // NOTE(mmalavalli): Since A/V sync metrics are not part of the StatsReport class,
                // we add them to the insights payload here.
                transport.publishEvent('quality', 'stats-report', 'info', {
                    audioTrackStats: report.remoteAudioTrackStats.map(function(trackStat, i) {
                        return addAVSyncMetricsToRemoteTrackStats(trackStat, response.remoteAudioTrackStats[i], movingAverageDeltas);
                    }),
                    localAudioTrackStats: report.localAudioTrackStats.map(function(trackStat, i) {
                        return addAVSyncMetricsToLocalTrackStats(trackStat, response.localAudioTrackStats[i], movingAverageDeltas);
                    }),
                    localVideoTrackStats: report.localVideoTrackStats.map(function(trackStat, i) {
                        return addAVSyncMetricsToLocalTrackStats(trackStat, response.localVideoTrackStats[i], movingAverageDeltas);
                    }),
                    peerConnectionId: report.peerConnectionId,
                    videoTrackStats: report.remoteVideoTrackStats.map(function(trackStat, i) {
                        return addAVSyncMetricsToRemoteTrackStats(trackStat, response.remoteVideoTrackStats[i], movingAverageDeltas);
                    })
                });
                // NOTE(mmalavalli): Clean up entries for Tracks that are no longer published or subscribed to.
                var keys = flatMap([
                    'localAudioTrackStats',
                    'localVideoTrackStats',
                    'remoteAudioTrackStats',
                    'remoteVideoTrackStats'
                ], function(prop) {
                    return report[prop].map(function(_a) {
                        var ssrc = _a.ssrc, trackSid = _a.trackSid;
                        return trackSid + "+" + ssrc;
                    });
                });
                var movingAverageDeltaKeysToBeRemoved = difference(Array.from(movingAverageDeltas.keys()), keys);
                movingAverageDeltaKeysToBeRemoved.forEach(function(key) {
                    return movingAverageDeltas.delete(key);
                });
                if (oddPublishCount) {
                    // NOTE(mmalavalli): null properties of the "active-ice-candidate-pair"
                    // payload are assigned default values until the Insights gateway
                    // accepts null values.
                    var activeIceCandidatePair = replaceNullsWithDefaults(response.activeIceCandidatePair, report.peerConnectionId);
                    transport.publishEvent('quality', 'active-ice-candidate-pair', 'info', activeIceCandidatePair);
                }
            });
        }, function() {
        // Do nothing.
        });
    }, intervalMs);
    roomV2.on('stateChanged', function onStateChanged(state) {
        if (state === 'disconnected') {
            clearInterval(interval);
            roomV2.removeListener('stateChanged', onStateChanged);
        }
    });
}
function handleSubscriptions(room) {
    var trackSidsToTrackSignalings = room._getTrackSidsToTrackSignalings();
    room._subscriptionFailures.forEach(function(error, trackSid) {
        var trackSignaling = trackSidsToTrackSignalings.get(trackSid);
        if (trackSignaling) {
            room._subscriptionFailures.delete(trackSid);
            trackSignaling.subscribeFailed(createTwilioError(error.code, error.message));
        }
    });
    trackSidsToTrackSignalings.forEach(function(trackSignaling) {
        var trackId = room._subscribed.get(trackSignaling.sid);
        if (!trackId || trackSignaling.isSubscribed && trackSignaling.trackTransceiver.id !== trackId) {
            trackSignaling.setTrackTransceiver(null);
        }
        if (trackId) {
            room._getTrackReceiver(trackId).then(function(trackReceiver) {
                return trackSignaling.setTrackTransceiver(trackReceiver);
            });
        }
    });
}
/**
 * NOTE(mmalavalli): Since A/V sync metrics are not part of the public StatsReport class, we add them
 * only for reporting purposes.
 * @private
 */ function addAVSyncMetricsToLocalTrackStats(trackStats, trackResponse, movingAverageDeltas) {
    var framesEncoded = trackResponse.framesEncoded, packetsSent = trackResponse.packetsSent, totalEncodeTime = trackResponse.totalEncodeTime, totalPacketSendDelay = trackResponse.totalPacketSendDelay;
    var augmentedTrackStats = Object.assign({}, trackStats);
    var key = trackStats.trackSid + "+" + trackStats.ssrc;
    var trackMovingAverageDeltas = movingAverageDeltas.get(key) || new Map();
    if (typeof totalEncodeTime === 'number' && typeof framesEncoded === 'number') {
        var trackAvgEncodeDelayMovingAverageDelta = trackMovingAverageDeltas.get('avgEncodeDelay') || new MovingAverageDelta();
        trackAvgEncodeDelayMovingAverageDelta.putSample(totalEncodeTime * 1000, framesEncoded);
        augmentedTrackStats.avgEncodeDelay = Math.round(trackAvgEncodeDelayMovingAverageDelta.get());
        trackMovingAverageDeltas.set('avgEncodeDelay', trackAvgEncodeDelayMovingAverageDelta);
    }
    if (typeof totalPacketSendDelay === 'number' && typeof packetsSent === 'number') {
        var trackAvgPacketSendDelayMovingAverageDelta = trackMovingAverageDeltas.get('avgPacketSendDelay') || new MovingAverageDelta();
        trackAvgPacketSendDelayMovingAverageDelta.putSample(totalPacketSendDelay * 1000, packetsSent);
        augmentedTrackStats.avgPacketSendDelay = Math.round(trackAvgPacketSendDelayMovingAverageDelta.get());
        trackMovingAverageDeltas.set('avgPacketSendDelay', trackAvgPacketSendDelayMovingAverageDelta);
    }
    movingAverageDeltas.set(key, trackMovingAverageDeltas);
    return augmentedTrackStats;
}
/**
 * NOTE(mmalavalli): Since A/V sync metrics are not part of the public StatsReport class, we add them
 * only for reporting purposes.
 * @private
 */ function addAVSyncMetricsToRemoteTrackStats(trackStats, trackResponse, movingAverageDeltas) {
    var estimatedPlayoutTimestamp = trackResponse.estimatedPlayoutTimestamp, framesDecoded = trackResponse.framesDecoded, jitterBufferDelay = trackResponse.jitterBufferDelay, jitterBufferEmittedCount = trackResponse.jitterBufferEmittedCount, totalDecodeTime = trackResponse.totalDecodeTime;
    var augmentedTrackStats = Object.assign({}, trackStats);
    var key = trackStats.trackSid + "+" + trackStats.ssrc;
    var trackMovingAverageDeltas = movingAverageDeltas.get(key) || new Map();
    if (typeof estimatedPlayoutTimestamp === 'number') {
        augmentedTrackStats.estimatedPlayoutTimestamp = estimatedPlayoutTimestamp;
    }
    if (typeof framesDecoded === 'number' && typeof totalDecodeTime === 'number') {
        var trackAvgDecodeDelayMovingAverageDelta = trackMovingAverageDeltas.get('avgDecodeDelay') || new MovingAverageDelta();
        trackAvgDecodeDelayMovingAverageDelta.putSample(totalDecodeTime * 1000, framesDecoded);
        augmentedTrackStats.avgDecodeDelay = Math.round(trackAvgDecodeDelayMovingAverageDelta.get());
        trackMovingAverageDeltas.set('avgDecodeDelay', trackAvgDecodeDelayMovingAverageDelta);
    }
    if (typeof jitterBufferDelay === 'number' && typeof jitterBufferEmittedCount === 'number') {
        var trackAvgJitterBufferDelayMovingAverageDelta = trackMovingAverageDeltas.get('avgJitterBufferDelay') || new MovingAverageDelta();
        trackAvgJitterBufferDelayMovingAverageDelta.putSample(jitterBufferDelay * 1000, jitterBufferEmittedCount);
        augmentedTrackStats.avgJitterBufferDelay = Math.round(trackAvgJitterBufferDelayMovingAverageDelta.get());
        trackMovingAverageDeltas.set('avgJitterBufferDelay', trackAvgJitterBufferDelayMovingAverageDelta);
    }
    movingAverageDeltas.set(key, trackMovingAverageDeltas);
    return augmentedTrackStats;
}
function replaceNullsWithDefaults(activeIceCandidatePair, peerConnectionId) {
    activeIceCandidatePair = Object.assign({
        availableIncomingBitrate: 0,
        availableOutgoingBitrate: 0,
        bytesReceived: 0,
        bytesSent: 0,
        consentRequestsSent: 0,
        currentRoundTripTime: 0,
        lastPacketReceivedTimestamp: 0,
        lastPacketSentTimestamp: 0,
        nominated: false,
        peerConnectionId: peerConnectionId,
        priority: 0,
        readable: false,
        requestsReceived: 0,
        requestsSent: 0,
        responsesReceived: 0,
        responsesSent: 0,
        retransmissionsReceived: 0,
        retransmissionsSent: 0,
        state: 'failed',
        totalRoundTripTime: 0,
        transportId: '',
        writable: false
    }, filterObject(activeIceCandidatePair || {}, null));
    activeIceCandidatePair.localCandidate = Object.assign({
        candidateType: 'host',
        deleted: false,
        ip: '',
        port: 0,
        priority: 0,
        protocol: 'udp',
        url: ''
    }, filterObject(activeIceCandidatePair.localCandidate || {}, null));
    activeIceCandidatePair.remoteCandidate = Object.assign({
        candidateType: 'host',
        ip: '',
        port: 0,
        priority: 0,
        protocol: 'udp',
        url: ''
    }, filterObject(activeIceCandidatePair.remoteCandidate || {}, null));
    return activeIceCandidatePair;
}
module.exports = RoomV2; //# sourceMappingURL=room.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/twilioconnectiontransport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
var TwilioConnection = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/twilioconnection.js [app-client] (ecmascript)");
var DefaultBackoff = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/backoff.js [app-client] (ecmascript)");
var reconnectBackoffConfig = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").reconnectBackoffConfig;
var Timeout = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), SDK_NAME = _a.SDK_NAME, SDK_VERSION = _a.SDK_VERSION, SDP_FORMAT = _a.SDP_FORMAT;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), createBandwidthProfilePayload = _b.createBandwidthProfilePayload, createMediaSignalingPayload = _b.createMediaSignalingPayload, createMediaWarningsPayload = _b.createMediaWarningsPayload, createSubscribePayload = _b.createSubscribePayload, getUserAgent = _b.getUserAgent, isNonArrayObject = _b.isNonArrayObject;
var _c = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), createTwilioError = _c.createTwilioError, RoomCompletedError = _c.RoomCompletedError, SignalingConnectionError = _c.SignalingConnectionError, SignalingServerBusyError = _c.SignalingServerBusyError;
var ICE_VERSION = 1;
var RSP_VERSION = 2;
/*
TwilioConnectionTransport States
----------------

                      +-----------+
                      |           |
                      |  syncing  |---------+
                      |           |         |
                      +-----------+         |
                         ^     |            |
                         |     |            |
                         |     v            v
    +------------+    +-----------+    +--------------+
    |            |    |           |    |              |
    | connecting |--->| connected |--->| disconnected |
    |            |    |           |    |              |
    +------------+    +-----------+    +--------------+
             |                              ^
             |                              |
             |                              |
             +------------------------------+

*/ var states = {
    connecting: [
        'connected',
        'disconnected'
    ],
    connected: [
        'disconnected',
        'syncing'
    ],
    syncing: [
        'connected',
        'disconnected'
    ],
    disconnected: []
};
/**
 * A {@link TwilioConnectionTransport} supports sending and receiving Room Signaling Protocol
 * (RSP) messages. It also supports RSP requests, such as Sync and Disconnect.
 * @extends StateMachine
 * @emits TwilioConnectionTransport#connected
 * @emits TwilioConnectionTransport#message
 */ var TwilioConnectionTransport = function(_super) {
    __extends(TwilioConnectionTransport, _super);
    /**
     * Construct a {@link TwilioConnectionTransport}.
     * @param {?string} name
     * @param {string} accessToken
     * @param {ParticipantSignaling} localParticipant
     * @param {PeerConnectionManager} peerConnectionManager
     * @param {string} wsServer
     * @param {object} [options]
     */ function TwilioConnectionTransport(name, accessToken, localParticipant, peerConnectionManager, wsServer, options) {
        var _this = this;
        options = Object.assign({
            Backoff: DefaultBackoff,
            TwilioConnection: TwilioConnection,
            iceServers: null,
            trackPriority: true,
            trackSwitchOff: true,
            renderHints: true,
            userAgent: getUserAgent()
        }, options);
        _this = _super.call(this, 'connecting', states) || this;
        Object.defineProperties(_this, {
            _accessToken: {
                value: accessToken
            },
            _adaptiveSimulcast: {
                value: options.adaptiveSimulcast
            },
            _automaticSubscription: {
                value: options.automaticSubscription
            },
            _bandwidthProfile: {
                value: options.bandwidthProfile
            },
            _dominantSpeaker: {
                value: options.dominantSpeaker
            },
            _receiveTranscriptions: {
                value: options.receiveTranscriptions
            },
            _eventObserver: {
                value: options.eventObserver,
                writable: false
            },
            _renderHints: {
                value: options.renderHints
            },
            _iceServersStatus: {
                value: Array.isArray(options.iceServers) ? 'overrode' : 'acquire'
            },
            _localParticipant: {
                value: localParticipant
            },
            _name: {
                value: name
            },
            _networkQuality: {
                value: isNonArrayObject(options.networkQuality) || options.networkQuality
            },
            _notifyWarnings: {
                value: options.notifyWarnings
            },
            _options: {
                value: options
            },
            _peerConnectionManager: {
                value: peerConnectionManager
            },
            _sessionTimer: {
                value: null,
                writable: true
            },
            _sessionTimeoutMS: {
                value: 0,
                writable: true
            },
            _reconnectBackoff: {
                value: new options.Backoff(reconnectBackoffConfig)
            },
            _session: {
                value: null,
                writable: true
            },
            _trackPriority: {
                value: options.trackPriority
            },
            _trackSwitchOff: {
                value: options.trackSwitchOff
            },
            _twilioConnection: {
                value: null,
                writable: true
            },
            _updatesReceived: {
                value: []
            },
            _updatesToSend: {
                value: []
            },
            _userAgent: {
                value: options.userAgent
            },
            _wsServer: {
                value: wsServer
            }
        });
        setupTransport(_this);
        return _this;
    }
    /**
     * Create a Connect, Sync or Disconnect RSP message.
     * @private
     * @returns {?object}
     */ TwilioConnectionTransport.prototype._createConnectOrSyncOrDisconnectMessage = function() {
        if (this.state === 'connected') {
            return null;
        }
        if (this.state === 'disconnected') {
            return {
                session: this._session,
                type: 'disconnect',
                version: RSP_VERSION
            };
        }
        var type = {
            connecting: 'connect',
            syncing: 'sync'
        }[this.state];
        var message = {
            name: this._name,
            participant: this._localParticipant.getState(),
            peer_connections: this._peerConnectionManager.getStates(),
            type: type,
            version: RSP_VERSION
        };
        if (message.type === 'connect') {
            message.ice_servers = this._iceServersStatus;
            message.publisher = {
                name: SDK_NAME,
                sdk_version: SDK_VERSION,
                user_agent: this._userAgent
            };
            if (this._bandwidthProfile) {
                message.bandwidth_profile = createBandwidthProfilePayload(this._bandwidthProfile);
            }
            if (this._notifyWarnings) {
                message.participant.media_warnings = createMediaWarningsPayload(this._notifyWarnings);
            }
            message.media_signaling = createMediaSignalingPayload(this._dominantSpeaker, this._networkQuality, this._trackPriority, this._trackSwitchOff, this._adaptiveSimulcast, this._renderHints, this._receiveTranscriptions);
            message.subscribe = createSubscribePayload(this._automaticSubscription);
            message.format = SDP_FORMAT;
            message.token = this._accessToken;
        } else if (message.type === 'sync') {
            message.session = this._session;
            message.token = this._accessToken;
        } else if (message.type === 'update') {
            message.session = this._session;
        }
        return message;
    };
    /**
     * Create an "ice" message.
     * @private
     */ TwilioConnectionTransport.prototype._createIceMessage = function() {
        return {
            edge: 'roaming',
            token: this._accessToken,
            type: 'ice',
            version: ICE_VERSION
        };
    };
    /**
     * Send a Connect, Sync or Disconnect RSP message.
     * @private
     */ TwilioConnectionTransport.prototype._sendConnectOrSyncOrDisconnectMessage = function() {
        var message = this._createConnectOrSyncOrDisconnectMessage();
        if (message) {
            this._twilioConnection.sendMessage(message);
        }
    };
    /**
     * Disconnect the {@link TwilioConnectionTransport}. Returns true if calling the method resulted
     * in disconnection.
     * @param {TwilioError} [error]
     * @returns {boolean}
     */ TwilioConnectionTransport.prototype.disconnect = function(error) {
        if (this.state !== 'disconnected') {
            this.preempt('disconnected', null, [
                error
            ]);
            this._sendConnectOrSyncOrDisconnectMessage();
            this._twilioConnection.close();
            return true;
        }
        return false;
    };
    /**
     * Publish an RSP Update. Returns true if calling the method resulted in
     * publishing (or eventually publishing) the update.
     * @param {object} update
     * @returns {boolean}
     */ TwilioConnectionTransport.prototype.publish = function(update) {
        switch(this.state){
            case 'connected':
                this._twilioConnection.sendMessage(Object.assign({
                    session: this._session,
                    type: 'update',
                    version: RSP_VERSION
                }, update));
                return true;
            case 'connecting':
            case 'syncing':
                this._updatesToSend.push(update);
                return true;
            case 'disconnected':
            default:
                return false;
        }
    };
    /**
     * Publish (or queue) an event to the Insights gateway.
     * @param {string} group - Event group name
     * @param {string} name - Event name
     * @param {string} level - Event level
     * @param {object} payload - Event payload
     * @returns {void}
     */ TwilioConnectionTransport.prototype.publishEvent = function(group, name, level, payload) {
        this._eventObserver.emit('event', {
            group: group,
            name: name,
            level: level,
            payload: payload
        });
    };
    /**
     * Sync the {@link TwilioConnectionTransport}. Returns true if calling the method resulted in
     * syncing.
     * @returns {boolean}
     */ TwilioConnectionTransport.prototype.sync = function() {
        if (this.state === 'connected') {
            this.preempt('syncing');
            this._sendConnectOrSyncOrDisconnectMessage();
            return true;
        }
        return false;
    };
    /**
     * @private
     * @returns {void}
     */ TwilioConnectionTransport.prototype._setSession = function(session, sessionTimeout) {
        this._session = session;
        this._sessionTimeoutMS = sessionTimeout * 1000;
    };
    /**
     * Determines if we should attempt reconnect.
     * returns a Promise to wait on before attempting to
     * reconnect. returns null if its not okay to reconnect.
     * @private
     * @returns {Promise<void>}
     */ TwilioConnectionTransport.prototype._getReconnectTimer = function() {
        var _this = this;
        if (this._sessionTimeoutMS === 0) {
            // this means either we have never connected.
            // or we timed out while trying to reconnect
            // In either case we do not want to reconnect.
            return null;
        }
        // start session timer
        if (!this._sessionTimer) {
            this._sessionTimer = new Timeout(function() {
                // ensure that _clearReconnectTimer wasn't
                // called while we were waiting.
                if (_this._sessionTimer) {
                    // do not allow any more reconnect attempts.
                    _this._sessionTimeoutMS = 0;
                }
            }, this._sessionTimeoutMS);
        }
        // return promise that waits with exponential backoff.
        return new Promise(function(resolve) {
            _this._reconnectBackoff.backoff(resolve);
        });
    };
    /**
     * clears the session reconnect timer.
     *
     * @private
     * @returns {void}
     */ TwilioConnectionTransport.prototype._clearReconnectTimer = function() {
        this._reconnectBackoff.reset();
        if (this._sessionTimer) {
            this._sessionTimer.clear();
            this._sessionTimer = null;
        }
    };
    return TwilioConnectionTransport;
}(StateMachine);
/**
 * @event TwilioConnectionTransport#connected
 * @param {object} initialState
 */ /**
 * @event TwilioConnectionTransport#message
 * @param {object} peerConnections
 */ function reducePeerConnections(peerConnections) {
    return Array.from(peerConnections.reduce(function(peerConnectionsById, update) {
        var reduced = peerConnectionsById.get(update.id) || update;
        // First, reduce the top-level `description` property.
        if (!reduced.description && update.description) {
            reduced.description = update.description;
        } else if (reduced.description && update.description) {
            if (update.description.revision > reduced.description.revision) {
                reduced.description = update.description;
            }
        }
        // Then, reduce the top-level `ice` property.
        if (!reduced.ice && update.ice) {
            reduced.ice = update.ice;
        } else if (reduced.ice && update.ice) {
            if (update.ice.revision > reduced.ice.revision) {
                reduced.ice = update.ice;
            }
        }
        // Finally, update the map.
        peerConnectionsById.set(reduced.id, reduced);
        return peerConnectionsById;
    }, new Map()).values());
}
function reduceUpdates(updates) {
    return updates.reduce(function(reduced, update) {
        // First, reduce the top-level `participant` property.
        if (!reduced.participant && update.participant) {
            reduced.participant = update.participant;
        } else if (reduced.participant && update.participant) {
            if (update.participant.revision > reduced.participant.revision) {
                reduced.participant = update.participant;
            }
        }
        // Then, reduce the top-level `peer_connections` property.
        /* eslint camelcase:0 */ if (!reduced.peer_connections && update.peer_connections) {
            reduced.peer_connections = reducePeerConnections(update.peer_connections);
        } else if (reduced.peer_connections && update.peer_connections) {
            reduced.peer_connections = reducePeerConnections(reduced.peer_connections.concat(update.peer_connections));
        }
        return reduced;
    }, {});
}
function setupTransport(transport) {
    function createOrResetTwilioConnection() {
        if (transport.state === 'disconnected') {
            return;
        }
        if (transport._twilioConnection) {
            transport._twilioConnection.removeListener('message', handleMessage);
        }
        var _iceServersStatus = transport._iceServersStatus, _options = transport._options, _wsServer = transport._wsServer, state = transport.state;
        var TwilioConnection = _options.TwilioConnection;
        var twilioConnection = new TwilioConnection(_wsServer, Object.assign({
            helloBody: state === 'connecting' && _iceServersStatus === 'acquire' ? transport._createIceMessage() : transport._createConnectOrSyncOrDisconnectMessage()
        }, _options));
        twilioConnection.once('close', function(reason) {
            if (reason === TwilioConnection.CloseReason.LOCAL) {
                disconnect();
            } else {
                disconnect(new Error(reason));
            }
        });
        twilioConnection.on('message', handleMessage);
        transport._twilioConnection = twilioConnection;
    }
    function disconnect(error) {
        if (transport.state === 'disconnected') {
            return;
        }
        if (!error) {
            transport.disconnect();
            return;
        }
        var reconnectTimer = transport._getReconnectTimer();
        if (!reconnectTimer) {
            var twilioError = error.message === TwilioConnection.CloseReason.BUSY ? new SignalingServerBusyError() : new SignalingConnectionError();
            transport.disconnect(twilioError);
            return;
        }
        if (transport.state === 'connected') {
            transport.preempt('syncing');
        }
        reconnectTimer.then(createOrResetTwilioConnection);
    }
    function handleMessage(message) {
        if (transport.state === 'disconnected') {
            return;
        }
        if (message.type === 'error') {
            transport.disconnect(createTwilioError(message.code, message.message));
            return;
        }
        switch(transport.state){
            case 'connected':
                switch(message.type){
                    case 'connected':
                    case 'synced':
                    case 'update':
                    case 'warning':
                        transport.emit('message', message);
                        return;
                    case 'disconnected':
                        transport.disconnect(message.status === 'completed' ? new RoomCompletedError() : null);
                        return;
                    default:
                        // Do nothing.
                        return;
                }
            case 'connecting':
                switch(message.type){
                    case 'iced':
                        transport._options.onIced(message.ice_servers).then(function() {
                            transport._sendConnectOrSyncOrDisconnectMessage();
                        });
                        return;
                    case 'connected':
                        transport._setSession(message.session, message.options.session_timeout);
                        transport.emit('connected', message);
                        transport.preempt('connected');
                        return;
                    case 'synced':
                    case 'update':
                        transport._updatesReceived.push(message);
                        return;
                    case 'disconnected':
                        transport.disconnect(message.status === 'completed' ? new RoomCompletedError() : null);
                        return;
                    default:
                        // Do nothing.
                        return;
                }
            case 'syncing':
                switch(message.type){
                    case 'connected':
                    case 'update':
                        transport._updatesReceived.push(message);
                        return;
                    case 'synced':
                        transport._clearReconnectTimer();
                        transport.emit('message', message);
                        transport.preempt('connected');
                        return;
                    case 'disconnected':
                        transport.disconnect(message.status === 'completed' ? new RoomCompletedError() : null);
                        return;
                    default:
                        // Do nothing.
                        return;
                }
            default:
                // Impossible
                return;
        }
    }
    transport.on('stateChanged', function stateChanged(state) {
        switch(state){
            case 'connected':
                {
                    var updates = transport._updatesToSend.splice(0);
                    if (updates.length) {
                        transport.publish(reduceUpdates(updates));
                    }
                    transport._updatesReceived.splice(0).forEach(function(update) {
                        return transport.emit('message', update);
                    });
                    return;
                }
            case 'disconnected':
                transport._twilioConnection.removeListener('message', handleMessage);
                transport.removeListener('stateChanged', stateChanged);
                return;
            case 'syncing':
                // Do nothing.
                return;
            default:
                // Impossible
                return;
        }
    });
    var _options = transport._options, _iceServersStatus = transport._iceServersStatus;
    var iceServers = _options.iceServers, onIced = _options.onIced;
    if (_iceServersStatus === 'overrode') {
        onIced(iceServers).then(createOrResetTwilioConnection);
    } else {
        createOrResetTwilioConnection();
    }
}
module.exports = TwilioConnectionTransport; //# sourceMappingURL=twilioconnectiontransport.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/cancelableroomsignalingpromise.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var CancelablePromise = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/cancelablepromise.js [app-client] (ecmascript)");
var DefaultPeerConnectionManager = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/peerconnectionmanager.js [app-client] (ecmascript)");
var DefaultRoomV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/room.js [app-client] (ecmascript)");
var DefaultTransport = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/twilioconnectiontransport.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)"), SignalingConnectionDisconnectedError = _a.SignalingConnectionDisconnectedError, SignalingIncomingMessageInvalidError = _a.SignalingIncomingMessageInvalidError;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), flatMap = _b.flatMap, createRoomConnectEventPayload = _b.createRoomConnectEventPayload;
function createCancelableRoomSignalingPromise(token, wsServer, localParticipant, encodingParameters, preferredCodecs, options) {
    options = Object.assign({
        PeerConnectionManager: DefaultPeerConnectionManager,
        RoomV2: DefaultRoomV2,
        Transport: DefaultTransport
    }, options);
    var adaptiveSimulcast = preferredCodecs.video[0] && preferredCodecs.video[0].adaptiveSimulcast === true;
    var PeerConnectionManager = options.PeerConnectionManager, RoomV2 = options.RoomV2, Transport = options.Transport, iceServers = options.iceServers, log = options.log;
    var peerConnectionManager = new PeerConnectionManager(encodingParameters, preferredCodecs, options);
    var trackSenders = flatMap(localParticipant.tracks, function(trackV2) {
        return [
            trackV2.trackTransceiver
        ];
    });
    peerConnectionManager.setTrackSenders(trackSenders);
    var cancellationError = new Error('Canceled');
    var transport;
    var cancelablePromise = new CancelablePromise(function(resolve, reject, isCanceled) {
        var onIced = function(iceServers) {
            if (isCanceled()) {
                reject(cancellationError);
                return Promise.reject(cancellationError);
            }
            log.debug('Got ICE servers:', iceServers);
            options.iceServers = iceServers;
            peerConnectionManager.setConfiguration(options);
            return peerConnectionManager.createAndOffer().then(function() {
                if (isCanceled()) {
                    reject(cancellationError);
                    throw cancellationError;
                }
                log.debug('createAndOffer() succeeded.');
                // NOTE(mmalavalli): PeerConnectionManager#createAndOffer() queues the
                // initial offer in the event queue for the 'description' event. So,
                // we are dequeueing to prevent the spurious 'update' message sent by
                // the client after connecting to a room.
                peerConnectionManager.dequeue('description');
            }).catch(function(error) {
                log.error('createAndOffer() failed:', error);
                reject(error);
                throw error;
            });
        };
        var automaticSubscription = options.automaticSubscription, bandwidthProfile = options.bandwidthProfile, dominantSpeaker = options.dominantSpeaker, receiveTranscriptions = options.receiveTranscriptions, environment = options.environment, eventObserver = options.eventObserver, loggerName = options.loggerName, logLevel = options.logLevel, name = options.name, networkMonitor = options.networkMonitor, networkQuality = options.networkQuality, notifyWarnings = options.notifyWarnings, realm = options.realm, sdpSemantics = options.sdpSemantics;
        // decide which msp channels to request
        // dominantSpeaker, networkQuality
        var trackPriority = !!bandwidthProfile;
        var trackSwitchOff = !!bandwidthProfile;
        var renderHints = !!bandwidthProfile && (options.clientTrackSwitchOffControl !== 'disabled' || options.contentPreferencesMode !== 'disabled');
        var transportOptions = Object.assign({
            adaptiveSimulcast: adaptiveSimulcast,
            automaticSubscription: automaticSubscription,
            dominantSpeaker: dominantSpeaker,
            receiveTranscriptions: receiveTranscriptions,
            environment: environment,
            eventObserver: eventObserver,
            loggerName: loggerName,
            logLevel: logLevel,
            networkMonitor: networkMonitor,
            networkQuality: networkQuality,
            notifyWarnings: notifyWarnings,
            iceServers: iceServers,
            onIced: onIced,
            realm: realm,
            renderHints: renderHints,
            sdpSemantics: sdpSemantics,
            trackPriority: trackPriority,
            trackSwitchOff: trackSwitchOff
        }, bandwidthProfile ? {
            bandwidthProfile: bandwidthProfile
        } : {});
        transport = new Transport(name, token, localParticipant, peerConnectionManager, wsServer, transportOptions);
        var connectEventPayload = createRoomConnectEventPayload(options);
        eventObserver.emit('event', connectEventPayload);
        transport.once('connected', function(initialState) {
            log.debug('Transport connected:', initialState);
            if (isCanceled()) {
                reject(cancellationError);
                return;
            }
            var localParticipantState = initialState.participant;
            if (!localParticipantState) {
                reject(new SignalingIncomingMessageInvalidError());
                return;
            }
            resolve(new RoomV2(localParticipant, initialState, transport, peerConnectionManager, options));
        });
        transport.once('stateChanged', function(state, error) {
            if (state === 'disconnected') {
                transport = null;
                reject(error || new SignalingConnectionDisconnectedError());
            } else {
                log.debug('Transport state changed:', state);
            }
        });
    }, function() {
        if (transport) {
            transport.disconnect();
            transport = null;
        }
    });
    cancelablePromise.catch(function() {
        if (transport) {
            transport.disconnect();
            transport = null;
        }
        peerConnectionManager.close();
    });
    return cancelablePromise;
}
module.exports = createCancelableRoomSignalingPromise; //# sourceMappingURL=cancelableroomsignalingpromise.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/localparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var ParticipantSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/participant.js [app-client] (ecmascript)");
var LocalParticipantSignaling = function(_super) {
    __extends(LocalParticipantSignaling, _super);
    function LocalParticipantSignaling() {
        var _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _publicationsToTrackSenders: {
                value: new Map()
            },
            _trackSendersToPublications: {
                value: new Map()
            }
        });
        return _this;
    }
    /**
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @param {string} name
     * @param {Track.Priority} priority
     * @param {?NoiseCancellationVendor} noiseCancellationVendor
     * @returns {LocalTrackPublicationSignaling} publication
     */ LocalParticipantSignaling.prototype.addTrack = function(trackSender, name, priority, noiseCancellationVendor) {
        if (noiseCancellationVendor === void 0) {
            noiseCancellationVendor = null;
        }
        var publication = this._createLocalTrackPublicationSignaling(trackSender, name, priority, noiseCancellationVendor);
        this._trackSendersToPublications.set(trackSender, publication);
        this._publicationsToTrackSenders.set(publication, trackSender);
        _super.prototype.addTrack.call(this, publication);
        return this;
    };
    /**
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @returns {?LocalTrackPublicationSignaling}
     */ LocalParticipantSignaling.prototype.getPublication = function(trackSender) {
        return this._trackSendersToPublications.get(trackSender) || null;
    };
    /**
     * @param {LocalTrackPublicationSignaling} trackPublication
     * @returns {?DataTrackSender|MediaTrackSender}
     */ LocalParticipantSignaling.prototype.getSender = function(trackPublication) {
        return this._publicationsToTrackSenders.get(trackPublication) || null;
    };
    /**
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @returns {?LocalTrackPublicationSignaling}
     */ LocalParticipantSignaling.prototype.removeTrack = function(trackSender) {
        var publication = this._trackSendersToPublications.get(trackSender);
        if (!publication) {
            return null;
        }
        this._trackSendersToPublications.delete(trackSender);
        this._publicationsToTrackSenders.delete(publication);
        var didDelete = _super.prototype.removeTrack.call(this, publication);
        if (didDelete) {
            publication.stop();
        }
        return publication;
    };
    return LocalParticipantSignaling;
}(ParticipantSignaling);
module.exports = LocalParticipantSignaling; //# sourceMappingURL=localparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/localtrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TrackSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/track.js [app-client] (ecmascript)");
/**
 * A {@link LocalTrackPublication} implementation
 * @extends TrackSignaling
 * @property {Track.ID} id
 */ var LocalTrackPublicationSignaling = function(_super) {
    __extends(LocalTrackPublicationSignaling, _super);
    /**
     * Construct a {@link LocalTrackPublicationSignaling}. {@link TrackSenders}
     * are always cloned.
     * @param {DataTrackSender|MediaTrackSender} trackSender - the {@link TrackSender}
     *   of the {@link LocalTrack} to be published
     * @param {string} name - the name of the {@link LocalTrack} to be published
     * @param {Track.Priority} priority - initial {@link Track.Priority}
     */ function LocalTrackPublicationSignaling(trackSender, name, priority) {
        var _this = this;
        // NOTE(lrivas): Safely clone a media stream track while preserving the original
        // enabled state. This is needed because Safari 18 incorrectly enables tracks
        // during cloning. Bug report: https://bugs.webkit.org/show_bug.cgi?id=281758
        var clonedTrackSender = trackSender.clone();
        if (trackSender.kind !== 'data') {
            clonedTrackSender.track.enabled = trackSender.track.enabled;
        }
        trackSender = clonedTrackSender;
        var enabled = trackSender.kind === 'data' ? true : trackSender.track.enabled;
        _this = _super.call(this, name, trackSender.kind, enabled, priority) || this;
        _this.setTrackTransceiver(trackSender);
        Object.defineProperties(_this, {
            _updatedPriority: {
                value: priority,
                writable: true
            },
            id: {
                enumerable: true,
                value: trackSender.id
            }
        });
        return _this;
    }
    Object.defineProperty(LocalTrackPublicationSignaling.prototype, "updatedPriority", {
        /**
         * The updated {@link Track.Priority} of the {@link LocalTrack}.
         * @property {Track.priority}
         */ get: function() {
            return this._updatedPriority;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Enable (or disable) the {@link LocalTrackPublicationSignaling} if it is not
     * already enabled (or disabled). This also updates the cloned
     * {@link MediaTrackSender}'s MediaStreamTracks `enabled` state.
     * @param {boolean} [enabled=true]
     * @return {this}
     */ LocalTrackPublicationSignaling.prototype.enable = function(enabled) {
        enabled = typeof enabled === 'boolean' ? enabled : true;
        this.trackTransceiver.track.enabled = enabled;
        return _super.prototype.enable.call(this, enabled);
    };
    /**
     * Rejects the SID's deferred promise with the given Error.
     * @param {Error} error
     * @returns {this}
     */ LocalTrackPublicationSignaling.prototype.publishFailed = function(error) {
        if (setError(this, error)) {
            this.emit('updated');
        }
        return this;
    };
    /**
     * Update the {@link Track.Priority} of the published {@link LocalTrack}.
     * @param {Track.priority} priority
     * @returns {this}
     */ LocalTrackPublicationSignaling.prototype.setPriority = function(priority) {
        if (this._updatedPriority !== priority) {
            this._updatedPriority = priority;
            this.emit('updated');
        }
        return this;
    };
    /**
     * Set the published {@link LocalTrack}'s {@link Track.SID}.
     * @param {Track.SID} sid
     * @returns {this}
     */ LocalTrackPublicationSignaling.prototype.setSid = function(sid) {
        if (this._error) {
            return this;
        }
        return _super.prototype.setSid.call(this, sid);
    };
    /**
     * Stop the cloned {@link TrackSender}.
     * @returns {void}
     */ LocalTrackPublicationSignaling.prototype.stop = function() {
        this.trackTransceiver.stop();
    };
    return LocalTrackPublicationSignaling;
}(TrackSignaling);
/**
 * @param {LocalTrackPublication} publication
 * @param {Error} error
 * @returns {boolean} updated
 */ function setError(publication, error) {
    if (publication._sid !== null || publication._error) {
        return false;
    }
    publication._error = error;
    return true;
}
module.exports = LocalTrackPublicationSignaling; //# sourceMappingURL=localtrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/localtrackpublication.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalTrackPublicationSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/localtrackpublication.js [app-client] (ecmascript)");
var TwilioWarning = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twiliowarning.js [app-client] (ecmascript)");
var createTwilioError = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)").createTwilioError;
/**
 * @extends LocalTrackPublicationSignaling
 */ var LocalTrackPublicationV2 = function(_super) {
    __extends(LocalTrackPublicationV2, _super);
    /**
     * Construct a {@link LocalTrackPublicationV2}.
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @param {string} name
     * @param {Track.Priority} priority
     * @param {?NoiseCancellationVendor} noiseCancellationVendor
     * @param {object} [options]
     */ function LocalTrackPublicationV2(trackSender, name, priority, noiseCancellationVendor, options) {
        var _this = _super.call(this, trackSender, name, priority) || this;
        Object.defineProperties(_this, {
            _log: {
                value: options.log.createLog('default', _this)
            },
            _mediaStates: {
                value: {
                    recordings: null
                },
                writable: true
            },
            _noiseCancellationVendor: {
                value: noiseCancellationVendor
            }
        });
        return _this;
    }
    /**
     * Get the {@link LocalTrackPublicationV2#Representation} of a given {@link TrackSignaling}.
     * @returns {LocalTrackPublicationV2#Representation} - without the SID
     */ LocalTrackPublicationV2.prototype.getState = function() {
        var state = {
            enabled: this.isEnabled,
            id: this.id,
            kind: this.kind,
            name: this.name,
            priority: this.updatedPriority
        };
        if (this._noiseCancellationVendor) {
            // eslint-disable-next-line camelcase
            state.audio_processor = this._noiseCancellationVendor;
        }
        return state;
    };
    LocalTrackPublicationV2.prototype.toString = function() {
        return "[LocalTrackPublicationV2: " + this.sid + "]";
    };
    /**
     * Compare the {@link LocalTrackPublicationV2} to a {@link LocalTrackPublicationV2#Representation} of itself
     * and perform any updates necessary.
     * @param {PublishedTrack} track
     * @returns {this}
     * @fires TrackSignaling#updated
     */ LocalTrackPublicationV2.prototype.update = function(track) {
        switch(track.state){
            case 'ready':
                this.setSid(track.sid);
                break;
            case 'failed':
                {
                    var error = track.error;
                    this.publishFailed(createTwilioError(error.code, error.message));
                    break;
                }
            default:
                break;
        }
        return this;
    };
    LocalTrackPublicationV2.prototype.updateMediaStates = function(mediaStates) {
        if (!mediaStates || !mediaStates.recordings || this._mediaStates.recordings === mediaStates.recordings) {
            return this;
        }
        this._mediaStates.recordings = mediaStates.recordings;
        switch(this._mediaStates.recordings){
            case 'OK':
                this._log.info('Warnings have cleared.');
                this.emit('warningsCleared');
                break;
            case 'NO_MEDIA':
                this._log.warn('Recording media lost.');
                this.emit('warning', TwilioWarning.recordingMediaLost);
                break;
            default:
                this._log.warn("Unknown media state detected: " + this._mediaStates.recordings);
                break;
        }
        return this;
    };
    return LocalTrackPublicationV2;
}(LocalTrackPublicationSignaling);
/**
 * The Room Signaling Protocol (RSP) representation of a {@link LocalTrackPublicationV2}.
 * @typedef {object} LocalTrackPublicationV2#Representation
 * @property {boolean} enabled
 * @property {Track.ID} id
 * @property {Track.Kind} kind
 * @property {string} name
 * @priority {Track.Priority} priority
 * @property {Track.SID} sid
 */ module.exports = LocalTrackPublicationV2; //# sourceMappingURL=localtrackpublication.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/localparticipant.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var LocalParticipantSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/localparticipant.js [app-client] (ecmascript)");
var LocalTrackPublicationV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/localtrackpublication.js [app-client] (ecmascript)");
var DEFAULT_LOG_LEVEL = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").DEFAULT_LOG_LEVEL;
var Log = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), buildLogLevels = _a.buildLogLevels, isDeepEqual = _a.isDeepEqual;
/**
 * @extends ParticipantSignaling
 * @property {BandwidthProfileOptions} bandwidthProfile
 * @property {NetworkQualityConfigurationImpl} networkQualityConfiguration
 * @property {number} revision
 * @emits LocalParticipantV2#updated
 */ var LocalParticipantV2 = function(_super) {
    __extends(LocalParticipantV2, _super);
    /**
     * Construct a {@link LocalParticipantV2}.
     * @param {EncodingParametersImpl} encodingParameters
     * @param {NetworkQualityConfigurationImpl} networkQualityConfiguration
     * @param {object} [options]
     */ function LocalParticipantV2(encodingParameters, networkQualityConfiguration, options) {
        var _this = this;
        options = Object.assign({
            logLevel: DEFAULT_LOG_LEVEL,
            LocalTrackPublicationV2: LocalTrackPublicationV2
        }, options);
        _this = _super.call(this) || this;
        var logLevels = buildLogLevels(options.logLevel);
        Object.defineProperties(_this, {
            _bandwidthProfile: {
                value: null,
                writable: true
            },
            _bandwidthProfileRevision: {
                value: 0,
                writable: true
            },
            _encodingParameters: {
                value: encodingParameters
            },
            _removeListeners: {
                value: new Map()
            },
            _LocalTrackPublicationV2: {
                value: options.LocalTrackPublicationV2
            },
            _log: {
                value: options.log ? options.log.createLog('default', _this) : new Log('default', _this, logLevels, options.loggerName)
            },
            _publishedRevision: {
                writable: true,
                value: 0
            },
            _revision: {
                writable: true,
                value: 1
            },
            _signalingRegion: {
                value: null,
                writable: true
            },
            audioProcessors: {
                value: [],
                writable: true
            },
            bandwidthProfile: {
                enumerable: true,
                get: function() {
                    return this._bandwidthProfile;
                }
            },
            bandwidthProfileRevision: {
                enumerable: true,
                get: function() {
                    return this._bandwidthProfileRevision;
                }
            },
            networkQualityConfiguration: {
                enumerable: true,
                value: networkQualityConfiguration
            },
            revision: {
                enumerable: true,
                get: function() {
                    return this._revision;
                }
            },
            signalingRegion: {
                enumerable: true,
                get: function() {
                    return this._signalingRegion;
                }
            }
        });
        return _this;
    }
    LocalParticipantV2.prototype.toString = function() {
        return "[LocalParticipantSignaling: " + this.sid + "]";
    };
    /**
     * Set the signalingRegion.
     * @param {string} signalingRegion.
     */ LocalParticipantV2.prototype.setSignalingRegion = function(signalingRegion) {
        if (!this._signalingRegion) {
            this._signalingRegion = signalingRegion;
        }
    };
    /**
     * Update the {@link BandwidthProfileOptions}.
     * @param {BandwidthProfileOptions} bandwidthProfile
     */ LocalParticipantV2.prototype.setBandwidthProfile = function(bandwidthProfile) {
        if (!isDeepEqual(this._bandwidthProfile, bandwidthProfile)) {
            // NOTE(mmalavalli): Object.assign() copies the values of only
            // the top level properties. In order to deep copy the object, we
            // stringify and parse the object.
            this._bandwidthProfile = JSON.parse(JSON.stringify(bandwidthProfile));
            this._bandwidthProfileRevision++;
            this.didUpdate();
        }
    };
    /**
     * Sets the AudioProcessors enabled for this room.
     * @param {string[]} audioProcessors
     */ LocalParticipantV2.prototype.setAudioProcessors = function(audioProcessors) {
        this.audioProcessors = audioProcessors;
    };
    /**
     * returns current {@link EncodingParametersImpl}.
     * @returns {EncodingParametersImpl}
     */ LocalParticipantV2.prototype.getParameters = function() {
        return this._encodingParameters;
    };
    /**
     * Set the {@link EncodingParameters}.
     * @param {?EncodingParameters} encodingParameters
     * @returns {this}
     */ LocalParticipantV2.prototype.setParameters = function(encodingParameters) {
        this._encodingParameters.update(encodingParameters);
        return this;
    };
    /**
     * Update the {@link LocalParticipantV2} with the new state.
     * @param {Published} published
     * @returns {this}
     */ LocalParticipantV2.prototype.update = function(published) {
        if (this._publishedRevision >= published.revision) {
            return this;
        }
        this._publishedRevision = published.revision;
        published.tracks.forEach(function(publicationState) {
            var localTrackPublicationV2 = this.tracks.get(publicationState.id);
            if (localTrackPublicationV2) {
                localTrackPublicationV2.update(publicationState);
            }
        }, this);
        return this;
    };
    LocalParticipantV2.prototype.updateMediaStates = function(mediaStates) {
        if (!mediaStates || !mediaStates.tracks) {
            return this;
        }
        Array.from(this.tracks.values()).forEach(function(publication) {
            var states = mediaStates.tracks[publication.sid];
            if (states) {
                publication.updateMediaStates(states);
            }
        });
        return this;
    };
    /**
     * @protected
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @param {string} name
     * @param {Track.Priority} priority
     * @param {?NoiseCancellationVendor} noiseCancellationVendor
     * @returns {LocalTrackPublicationV2}
     */ LocalParticipantV2.prototype._createLocalTrackPublicationSignaling = function(trackSender, name, priority, noiseCancellationVendor) {
        return new this._LocalTrackPublicationV2(trackSender, name, priority, noiseCancellationVendor, {
            log: this._log
        });
    };
    /**
     * Add a {@link LocalTrackPublicationV2} for the given {@link DataTrackSender}
     * or {@link MediaTrackSender} to the {@link LocalParticipantV2}.
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @param {string} name
     * @param {Track.Priority} priority
     * @returns {this}
     */ LocalParticipantV2.prototype.addTrack = function(trackSender, name, priority, noiseCancellationVendor) {
        var _this = this;
        _super.prototype.addTrack.call(this, trackSender, name, priority, noiseCancellationVendor);
        var publication = this.getPublication(trackSender);
        var isEnabled = publication.isEnabled, updatedPriority = publication.updatedPriority;
        var updated = function() {
            // NOTE(mmalavalli): The LocalParticipantV2's state is only published if
            // the "updated" event is emitted due to LocalTrackPublicationV2's
            // .isEnabled or .updatedPriority being changed. We do not publish if it is fired due to the
            // LocalTrackPublicationV2's .sid being set.
            if (isEnabled !== publication.isEnabled || updatedPriority !== publication.updatedPriority) {
                _this.didUpdate();
                isEnabled = publication.isEnabled;
                updatedPriority = publication.updatedPriority;
            }
        };
        publication.on('updated', updated);
        this._removeListener(publication);
        this._removeListeners.set(publication, function() {
            return publication.removeListener('updated', updated);
        });
        this.didUpdate();
        return this;
    };
    /**
     * @private
     * @param {LocalTrackPublicationV2} publication
     * @returns {void}
     */ LocalParticipantV2.prototype._removeListener = function(publication) {
        var removeListener = this._removeListeners.get(publication);
        if (removeListener) {
            removeListener();
        }
    };
    /**
     * Get the current state of the {@link LocalParticipantV2}.
     * @returns {object}
     */ LocalParticipantV2.prototype.getState = function() {
        return {
            revision: this.revision,
            tracks: Array.from(this.tracks.values()).map(function(track) {
                return track.getState();
            })
        };
    };
    /**
     * Increment the revision for the {@link LocalParticipantV2}.
     * @private
     * @returns {void}
     */ LocalParticipantV2.prototype.didUpdate = function() {
        this._revision++;
        this.emit('updated');
    };
    /**
     * Remove the {@link LocalTrackPublicationV2} for the given {@link DataTrackSender}
     * or {@link MediaTrackSender} from the {@link LocalParticipantV2}.
     * @param {DataTrackSender|MediaTrackSender} trackSender
     * @returns {?LocalTrackPublicationV2}
     */ LocalParticipantV2.prototype.removeTrack = function(trackSender) {
        var publication = _super.prototype.removeTrack.call(this, trackSender);
        if (publication) {
            trackSender.removeClone(publication.trackTransceiver);
            this._removeListener(publication);
            this.didUpdate();
        }
        return publication;
    };
    /**
     * Updates the verbosity of network quality information.
     * @param {NetworkQualityConfiguration} networkQualityConfiguration
     * @returns {void}
     */ LocalParticipantV2.prototype.setNetworkQualityConfiguration = function(networkQualityConfiguration) {
        this.networkQualityConfiguration.update(networkQualityConfiguration);
    };
    /**
     * updates encodings for simulcast layers.
     * @param {Track.SID} trackSid
     * @param {Array<{enabled: boolean, layer_index: number}>} encodings
     * @returns {Promise<string>} string indicating result of the operation. can be one of
     *  "OK", "INVALID_HINT", "COULD_NOT_APPLY_HINT", "UNKNOWN_TRACK"
     */ LocalParticipantV2.prototype.setPublisherHint = function(trackSid, encodings) {
        var trackSignaling = Array.from(this.tracks.values()).find(function(trackPub) {
            return trackPub.sid === trackSid;
        });
        if (!trackSignaling) {
            this._log.warn("track:" + trackSid + " not found");
            return Promise.resolve('UNKNOWN_TRACK');
        }
        return trackSignaling.trackTransceiver.setPublisherHint(encodings);
    };
    return LocalParticipantV2;
}(LocalParticipantSignaling);
/**
 * @interface Published
 * @property {number} revision
 * @property {Array<PublishedTrack>} tracks
 */ /**
 * @typedef {CreatedTrack|ReadyTrack|FailedTrack} PublishedTrack
 */ /**
 * @interface CreatedTrack
 * @property {Track.ID} id
 * @property {string} state - "created"
 */ /**
 * @interface ReadyTrack
 * @property {Track.ID} id
 * @property {Track.SID} sid
 * @property {string} state - "ready"
 */ /**
 * @interface FailedTrack
 * @property {Track.ID} id
 * @property {TrackError} error
 * @property {string} state - "failed"
 */ /**
 * @interface TrackError
 * @property {number} code
 * @property {string} message
 */ /**
 * @event LocalParticipantV2#updated
 */ module.exports = LocalParticipantV2; //# sourceMappingURL=localparticipant.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint consistent-return:0 */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var ParticipantSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/participant.js [app-client] (ecmascript)");
var RoomSignaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/room.js [app-client] (ecmascript)");
var StateMachine = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/statemachine.js [app-client] (ecmascript)");
/*
Signaling States
----------------

              +---------+
              |         |
              | opening |
         +--->|         |
         |    +---------+
    +--------+   |   |   +------+
    |        |<--+   +-->|      |
    | closed |<----------| open |
    |        |<--+   +-->|      |
    +--------+   |   |   +------+
              +---------+   |
              |         |<--+
              | closing |
              |         |
              +---------+

*/ var states = {
    closed: [
        'opening'
    ],
    opening: [
        'closed',
        'open'
    ],
    open: [
        'closed',
        'closing'
    ],
    closing: [
        'closed',
        'open'
    ]
};
/**
 * @extends StateMachine
 * @property {string} state - one of "closed", "opening", "open", or "closing"
 */ var Signaling = function(_super) {
    __extends(Signaling, _super);
    /**
     * Construct {@link Signaling}.
     */ function Signaling() {
        return _super.call(this, 'closed', states) || this;
    }
    /**
     * @private
     */ // NOTE(mroberts): This is a dummy implementation suitable for testing.
    Signaling.prototype._close = function(key) {
        this.transition('closing', key);
        this.transition('closed', key);
        return Promise.resolve(this);
    };
    /**
     * @private
     */ // NOTE(mroberts): This is a dummy implementation suitable for testing.
    Signaling.prototype._connect = function(localParticipant, token, encodingParameters, preferredCodecs, options) {
        localParticipant.connect('PA00000000000000000000000000000000', 'test');
        var sid = 'RM00000000000000000000000000000000';
        var promise = Promise.resolve(new RoomSignaling(localParticipant, sid, options));
        promise.cancel = function cancel() {};
        return promise;
    };
    /**
     * @private
     */ // NOTE(mroberts): This is a dummy implementation suitable for testing.
    Signaling.prototype._open = function(key) {
        this.transition('opening', key);
        this.transition('open', key);
        return Promise.resolve(this);
    };
    /**
     * Close the {@link Signaling}.
     * @returns {Promise<this>}
     */ Signaling.prototype.close = function() {
        var _this = this;
        return this.bracket('close', function(key) {
            switch(_this.state){
                case 'closed':
                    return _this;
                case 'open':
                    return _this._close(key);
                default:
                    throw new Error("Unexpected Signaling state \"" + _this.state + "\"");
            }
        });
    };
    /**
     * Connect to a {@link RoomSignaling}.
     * @param {ParticipantSignaling} localParticipant
     * @param {string} token
     * @param {EncodingParametersImpl} encodingParameters
     * @param {PreferredCodecs} preferredCodecs
     * @param {object} options
     * @returns {Promise<function(): CancelablePromise<RoomSignaling>>}
     */ Signaling.prototype.connect = function(localParticipant, token, encodingParameters, preferredCodecs, options) {
        var self = this;
        return this.bracket('connect', function transition(key) {
            switch(self.state){
                case 'closed':
                    return self._open(key).then(transition.bind(null, key));
                case 'open':
                    // NOTE(mroberts): We don't need to hold the lock in _connect. Instead,
                    // we just need to ensure the Signaling remains open.
                    self.releaseLockCompletely(key);
                    return self._connect(localParticipant, token, encodingParameters, preferredCodecs, options);
                default:
                    throw new Error("Unexpected Signaling state \"" + self.state + "\"");
            }
        });
    };
    /**
     * Create a local {@link ParticipantSignaling}.
     * @returns {ParticipantSignaling}
     */ Signaling.prototype.createLocalParticipantSignaling = function() {
        return new ParticipantSignaling();
    };
    /**
     * Open the {@link Signaling}.
     * @returns {Promise<this>}
     */ Signaling.prototype.open = function() {
        var _this = this;
        return this.bracket('open', function(key) {
            switch(_this.state){
                case 'closed':
                    return _this._open(key);
                case 'open':
                    return _this;
                default:
                    throw new Error("Unexpected Signaling state \"" + _this.state + "\"");
            }
        });
    };
    return Signaling;
}(StateMachine);
module.exports = Signaling; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/signaling/v2/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var defaultCreateCancelableRoomSignalingPromise = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/cancelableroomsignalingpromise.js [app-client] (ecmascript)");
var LocalParticipantV2 = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/v2/localparticipant.js [app-client] (ecmascript)");
var Signaling = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/signaling/index.js [app-client] (ecmascript)");
/**
 * {@link SignalingV2} implements version 2 of our signaling protocol.
 * @extends Signaling
 */ var SignalingV2 = function(_super) {
    __extends(SignalingV2, _super);
    /**
     * Construct {@link SignalingV2}.
     * @param {string} wsServer
     * @param {?object} [options={}]
     */ function SignalingV2(wsServer, options) {
        var _this = this;
        /* eslint new-cap:0 */ options = Object.assign({
            createCancelableRoomSignalingPromise: defaultCreateCancelableRoomSignalingPromise
        }, options);
        _this = _super.call(this) || this;
        Object.defineProperties(_this, {
            _createCancelableRoomSignalingPromise: {
                value: options.createCancelableRoomSignalingPromise
            },
            _options: {
                value: options
            },
            _wsServer: {
                value: wsServer
            }
        });
        return _this;
    }
    /**
     * @private
     */ SignalingV2.prototype._connect = function(localParticipant, token, encodingParameters, preferredCodecs, options) {
        options = Object.assign({}, this._options, options);
        return this._createCancelableRoomSignalingPromise.bind(null, token, this._wsServer, localParticipant, encodingParameters, preferredCodecs, options);
    };
    SignalingV2.prototype.createLocalParticipantSignaling = function(encodingParameters, networkQualityConfiguration) {
        return new LocalParticipantV2(encodingParameters, networkQualityConfiguration);
    };
    return SignalingV2;
}(Signaling);
module.exports = SignalingV2; //# sourceMappingURL=index.js.map
}}),
}]);

//# sourceMappingURL=node_modules_twilio-video_es5_signaling_e7baa574._.js.map