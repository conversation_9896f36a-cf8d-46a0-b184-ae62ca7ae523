(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/twilio-video/es5/util/dynamicimport.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = function(scope) {
    var location = scope.location, URL = scope.URL;
    if ([
        location,
        URL
    ].some(function(api) {
        return !api;
    })) {
        return function dynamicImportNotSupported(module1) {
            return Promise.reject(new Error("Failed to import: " + module1 + ": dynamicImport is not supported"));
        };
    }
    scope.__twilioVideoImportedModules = {
    };
    return function dynamicImport(module1) {
        if (module1 in scope.__twilioVideoImportedModules) {
            return Promise.resolve(scope.__twilioVideoImportedModules[module1]);
        }
        // NOTE(mmalavalli): Calling import() directly can cause build issues in TypeScript and Webpack
        // (and probably other frameworks). So, we create a Function that calls import() in its body.
        // eslint-disable-next-line no-new-func
        return new Function('scope', "return import('" + new URL(module1, location) + "').then(m => scope.__twilioVideoImportedModules['" + module1 + "'] = m);")(scope);
    };
}(globalThis); //# sourceMappingURL=dynamicimport.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/* eslint-disable camelcase */ var packageInfo = __turbopack_context__.r("[project]/node_modules/twilio-video/package.json (json)");
module.exports.SDK_NAME = packageInfo.name + ".js";
module.exports.SDK_VERSION = packageInfo.version;
module.exports.SDP_FORMAT = 'unified';
module.exports.hardwareDevicePublisheriPad = {
    hwDeviceManufacturer: 'Apple',
    hwDeviceModel: 'iPad',
    hwDeviceType: 'tablet',
    platformName: 'iOS'
};
module.exports.hardwareDevicePublisheriPhone = {
    hwDeviceManufacturer: 'Apple',
    hwDeviceModel: 'iPhone',
    hwDeviceType: 'mobile',
    platformName: 'iOS'
};
module.exports.DEFAULT_ENVIRONMENT = 'prod';
module.exports.DEFAULT_REALM = 'us1';
module.exports.DEFAULT_REGION = 'gll';
module.exports.DEFAULT_LOG_LEVEL = 'warn';
module.exports.DEFAULT_LOGGER_NAME = 'twilio-video';
module.exports.WS_SERVER = function(environment, region) {
    region = region === 'gll' ? 'global' : encodeURIComponent(region);
    return environment === 'prod' ? "wss://" + region + ".vss.twilio.com/signaling" : "wss://" + region + ".vss." + environment + ".twilio.com/signaling";
};
module.exports.PUBLISH_MAX_ATTEMPTS = 5;
module.exports.PUBLISH_BACKOFF_JITTER = 10;
module.exports.PUBLISH_BACKOFF_MS = 20;
/**
 * Returns the appropriate indefinite article ("a" | "an").
 * @param {string} word - The word which determines whether "a" | "an" is returned
 * @returns {string} "a" if word's first letter is a vowel, "an" otherwise
 */ function article(word) {
    // NOTE(mmalavalli): This will not be accurate for words like "hour",
    // which have consonants as their first character, but are pronounced like
    // vowels. We can address this issue if the need arises.
    return [
        'a',
        'e',
        'i',
        'o',
        'u'
    ].includes(word.toLowerCase()[0]) ? 'an' : 'a';
}
module.exports.typeErrors = {
    ILLEGAL_INVOKE: function(name, context) {
        return new TypeError("Illegal call to " + name + ": " + context);
    },
    INVALID_TYPE: function(name, type) {
        return new TypeError(name + " must be " + article(type) + " " + type);
    },
    INVALID_VALUE: function(name, values) {
        return new RangeError(name + " must be one of " + values.join(', '));
    },
    REQUIRED_ARGUMENT: function(name) {
        return new TypeError(name + " must be specified");
    }
};
module.exports.DEFAULT_FRAME_RATE = 24;
module.exports.DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS = 10000;
module.exports.DEFAULT_ICE_GATHERING_TIMEOUT_MS = 15000;
module.exports.DEFAULT_SESSION_TIMEOUT_SEC = 30;
module.exports.DEFAULT_NQ_LEVEL_LOCAL = 1;
module.exports.DEFAULT_NQ_LEVEL_REMOTE = 0;
module.exports.MAX_NQ_LEVEL = 3;
module.exports.ICE_ACTIVITY_CHECK_PERIOD_MS = 1000;
module.exports.ICE_INACTIVITY_THRESHOLD_MS = 3000;
module.exports.iceRestartBackoffConfig = {
    factor: 1.1,
    min: 1,
    max: module.exports.DEFAULT_SESSION_TIMEOUT_SEC * 1000,
    jitter: 1
};
module.exports.reconnectBackoffConfig = {
    factor: 1.5,
    min: 80,
    jitter: 1
};
module.exports.subscriptionMode = {
    MODE_COLLABORATION: 'collaboration',
    MODE_GRID: 'grid',
    MODE_PRESENTATION: 'presentation'
};
module.exports.trackSwitchOffMode = {
    MODE_DISABLED: 'disabled',
    MODE_DETECTED: 'detected',
    MODE_PREDICTED: 'predicted'
};
module.exports.trackPriority = {
    PRIORITY_HIGH: 'high',
    PRIORITY_LOW: 'low',
    PRIORITY_STANDARD: 'standard'
};
module.exports.clientTrackSwitchOffControl = {
    MODE_AUTO: 'auto',
    MODE_MANUAL: 'manual'
};
module.exports.videoContentPreferencesMode = {
    MODE_AUTO: 'auto',
    MODE_MANUAL: 'manual'
}; //# sourceMappingURL=constants.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/log.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint new-cap:0 */ 'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var defaultGetLogger = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/vendor/loglevel.js [app-client] (ecmascript)").getLogger;
var constants = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)");
var DEFAULT_LOG_LEVEL = constants.DEFAULT_LOG_LEVEL, DEFAULT_LOGGER_NAME = constants.DEFAULT_LOGGER_NAME;
var E = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)").typeErrors;
var deprecationWarningsByComponentConstructor;
function getDeprecationWarnings(componentConstructor) {
    deprecationWarningsByComponentConstructor = deprecationWarningsByComponentConstructor || new Map();
    if (deprecationWarningsByComponentConstructor.has(componentConstructor)) {
        return deprecationWarningsByComponentConstructor.get(componentConstructor);
    }
    var deprecationWarnings = new Set();
    deprecationWarningsByComponentConstructor.set(componentConstructor, deprecationWarnings);
    return deprecationWarnings;
}
/**
 * Selectively outputs messages to console based on specified minimum module
 * specific log levels.
 *
 * NOTE: The values in the logLevels object passed to the constructor is changed
 *       by subsequent calls to {@link Log#setLevels}.
 */ var Log = function() {
    /**
     * Construct a new {@link Log} object.
     * @param {String} moduleName - Name of the logging module (webrtc/media/signaling)
     * @param {object} component - Component owning this instance of {@link Log}
     * @param {LogLevels} logLevels - Logging levels. See {@link LogLevels}
     * @param {String} loggerName - Name of the logger instance. Used when calling getLogger from loglevel module
     * @param {Function} [getLogger] - optional method used internally.
     */ function Log(moduleName, component, logLevels, loggerName, getLogger) {
        if (typeof moduleName !== 'string') {
            throw E.INVALID_TYPE('moduleName', 'string');
        }
        if (!component) {
            throw E.REQUIRED_ARGUMENT('component');
        }
        if (typeof logLevels !== 'object') {
            logLevels = {};
        }
        getLogger = getLogger || defaultGetLogger;
        validateLogLevels(logLevels);
        /* istanbul ignore next */ Object.defineProperties(this, {
            _component: {
                value: component
            },
            _logLevels: {
                value: logLevels
            },
            _warnings: {
                value: new Set()
            },
            _loggerName: {
                get: function get() {
                    var name = loggerName && typeof loggerName === 'string' ? loggerName : DEFAULT_LOGGER_NAME;
                    if (!this._logLevelsEqual) {
                        name = name + "-" + moduleName;
                    }
                    return name;
                }
            },
            _logger: {
                get: function get() {
                    var logger = getLogger(this._loggerName);
                    var level = this._logLevels[moduleName] || DEFAULT_LOG_LEVEL;
                    // There is no 'off' in the logger module. It uses 'silent' instead
                    level = level === 'off' ? 'silent' : level;
                    logger.setDefaultLevel(level);
                    return logger;
                }
            },
            _logLevelsEqual: {
                get: function get() {
                    // True if all levels are the same
                    return new Set(Object.values(this._logLevels)).size === 1;
                }
            },
            logLevel: {
                get: function get() {
                    return Log.getLevelByName(logLevels[moduleName] || DEFAULT_LOG_LEVEL);
                }
            },
            name: {
                get: component.toString.bind(component)
            }
        });
    }
    /**
     * Get the log level (number) by its name (string)
     * @param {String} name - Name of the log level
     * @returns {Number} Requested log level
     * @throws {TwilioError} INVALID_LOG_LEVEL (32056)
     * @public
     */ Log.getLevelByName = function(name) {
        if (!isNaN(name)) {
            return parseInt(name, 10);
        }
        name = name.toUpperCase();
        validateLogLevel(name);
        return Log[name];
    };
    /**
     * Create a child {@link Log} instance with this._logLevels
     * @param moduleName - Name of the logging module
     * @param component - Component owning this instance of {@link Log}
     * @returns {Log} this
     */ Log.prototype.createLog = function(moduleName, component) {
        var name = this._loggerName;
        // Grab the original logger name
        if (!this._logLevelsEqual) {
            name = name.substring(0, name.lastIndexOf('-'));
        }
        return new Log(moduleName, component, this._logLevels, name);
    };
    /**
     * Set new log levels.
     * This changes the levels for all its ancestors,
     * siblings, and children and descendants instances of {@link Log}.
     * @param {LogLevels} levels - New log levels
     * @throws {TwilioError} INVALID_ARGUMENT
     * @returns {Log} this
     */ Log.prototype.setLevels = function(levels) {
        validateLogLevels(levels);
        Object.assign(this._logLevels, levels);
        return this;
    };
    /**
     * Log a message using the logger method appropriate for the specified logLevel
     * @param {Number} logLevel - Log level of the message being logged
     * @param {Array} messages - Message(s) to log
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.log = function(logLevel, messages) {
        var name = Log._levels[logLevel];
        // eslint-disable-next-line no-use-before-define
        if (!name) {
            throw E.INVALID_VALUE('logLevel', LOG_LEVEL_VALUES);
        }
        name = name.toLowerCase();
        var prefix = [
            new Date().toISOString(),
            name,
            this.name
        ];
        (this._logger[name] || function noop() {}).apply(void 0, __spreadArray([], __read(prefix.concat(messages))));
        return this;
    };
    /**
     * Log a debug message
     * @param {...String} messages - Message(s) to pass to the logger
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.debug = function() {
        return this.log(Log.DEBUG, [].slice.call(arguments));
    };
    /**
     * Log a deprecation warning. Deprecation warnings are logged as warnings and
     * they are only ever logged once.
     * @param {String} deprecationWarning - The deprecation warning
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.deprecated = function(deprecationWarning) {
        var deprecationWarnings = getDeprecationWarnings(this._component.constructor);
        if (deprecationWarnings.has(deprecationWarning)) {
            return this;
        }
        deprecationWarnings.add(deprecationWarning);
        return this.warn(deprecationWarning);
    };
    /**
     * Log an info message
     * @param {...String} messages - Message(s) to pass to the logger
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.info = function() {
        return this.log(Log.INFO, [].slice.call(arguments));
    };
    /**
     * Log a warn message
     * @param {...String} messages - Message(s) to pass to the logger
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.warn = function() {
        return this.log(Log.WARN, [].slice.call(arguments));
    };
    /**
     * Log a warning once.
     * @param {String} warning
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.warnOnce = function(warning) {
        if (this._warnings.has(warning)) {
            return this;
        }
        this._warnings.add(warning);
        return this.warn(warning);
    };
    /**
     * Log an error message
     * @param {...String} messages - Message(s) to pass to the logger
     * @returns {Log} This instance of {@link Log}
     * @public
     */ Log.prototype.error = function() {
        return this.log(Log.ERROR, [].slice.call(arguments));
    };
    /**
     * Log an error message and throw an exception
     * @param {TwilioError} error - Error to throw
     * @param {String} customMessage - Custom message for the error
     * @public
     */ Log.prototype.throw = function(error, customMessage) {
        if (error.clone) {
            error = error.clone(customMessage);
        }
        this.log(Log.ERROR, error);
        throw error;
    };
    return Log;
}();
// Singleton Constants
/* eslint key-spacing:0 */ /* istanbul ignore next */ Object.defineProperties(Log, {
    DEBUG: {
        value: 0
    },
    INFO: {
        value: 1
    },
    WARN: {
        value: 2
    },
    ERROR: {
        value: 3
    },
    OFF: {
        value: 4
    },
    _levels: {
        value: [
            'DEBUG',
            'INFO',
            'WARN',
            'ERROR',
            'OFF'
        ]
    }
});
var LOG_LEVELS_SET = {};
var LOG_LEVEL_VALUES = [];
var LOG_LEVEL_NAMES = Log._levels.map(function(level, i) {
    LOG_LEVELS_SET[level] = true;
    LOG_LEVEL_VALUES.push(i);
    return level;
});
function validateLogLevel(level) {
    if (!(level in LOG_LEVELS_SET)) {
        throw E.INVALID_VALUE('level', LOG_LEVEL_NAMES);
    }
}
function validateLogLevels(levels) {
    Object.keys(levels).forEach(function(moduleName) {
        validateLogLevel(levels[moduleName].toUpperCase());
    });
}
module.exports = Log; //# sourceMappingURL=log.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/sid.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var SID_CHARS = '1234567890abcdef';
var SID_CHAR_LENGTH = 32;
// copied from: https://code.hq.twilio.com/flex/monkey/blob/0fdce2b6c52d6be0b17a5cdb92f0c54f119b8ea8/src/client/lib/sid.ts#L39
/**
 * Generates a random sid using given prefix.
 * @param {string} prefix
 * @returns string
 */ function createSID(prefix) {
    var result = '';
    for(var i = 0; i < SID_CHAR_LENGTH; i++){
        result += SID_CHARS.charAt(Math.floor(Math.random() * SID_CHARS.length));
    }
    return "" + prefix + result;
}
exports.sessionSID = createSID('SS');
exports.createSID = createSID; //# sourceMappingURL=sid.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/twiliowarning.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * @private
 * Represents a warning encountered when
 * interacting with one of Twilio's services.
 */ // eslint-disable-next-line
var TwilioWarning = {
    recordingMediaLost: 'recording-media-lost'
};
module.exports = TwilioWarning; //# sourceMappingURL=twiliowarning.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var constants = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)");
var E = constants.typeErrors, trackPriority = constants.trackPriority;
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)");
var sessionSID = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sid.js [app-client] (ecmascript)").sessionSID;
var TwilioWarning = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twiliowarning.js [app-client] (ecmascript)");
/**
 * Return the given {@link LocalTrack} or a new {@link LocalTrack} for the
 * given MediaStreamTrack.
 * @param {LocalTrack|MediaStreamTrack} track
 * @param {object} options
 * @returns {LocalTrack}
 * @throws {TypeError}
 */ function asLocalTrack(track, options) {
    if (track instanceof options.LocalAudioTrack || track instanceof options.LocalVideoTrack || track instanceof options.LocalDataTrack) {
        return track;
    }
    if (track instanceof options.MediaStreamTrack) {
        return track.kind === 'audio' ? new options.LocalAudioTrack(track, options) : new options.LocalVideoTrack(track, options);
    }
    /* eslint new-cap:0 */ throw E.INVALID_TYPE('track', 'LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack');
}
/**
 * Create a new {@link LocalTrackPublication} for the given {@link LocalTrack}.
 * @param {LocalTrack} track
 * @param {LocalTrackPublicationSignaling} signaling
 * @param {function(track: LocalTrackPublication): void} unpublish
 * @param {object} options
 */ function asLocalTrackPublication(track, signaling, unpublish, options) {
    var LocalTrackPublication = {
        audio: options.LocalAudioTrackPublication,
        video: options.LocalVideoTrackPublication,
        data: options.LocalDataTrackPublication
    }[track.kind];
    return new LocalTrackPublication(signaling, track, unpublish, options);
}
/**
 * Capitalize a word.
 * @param {string} word
 * @returns {string} capitalized
 */ function capitalize(word) {
    return word[0].toUpperCase() + word.slice(1);
}
/**
 * Log deprecation warnings for the given events of an EventEmitter.
 * @param {string} name
 * @param {EventEmitter} emitter
 * @param {Map<string, string>} events
 * @param {Log} log
 */ function deprecateEvents(name, emitter, events, log) {
    var warningsShown = new Set();
    emitter.on('newListener', function newListener(event) {
        if (events.has(event) && !warningsShown.has(event)) {
            log.deprecated(name + "#" + event + " has been deprecated and scheduled for removal in twilio-video.js@2.0.0." + (events.get(event) ? " Use " + name + "#" + events.get(event) + " instead." : ''));
            warningsShown.add(event);
        }
        if (warningsShown.size >= events.size) {
            emitter.removeListener('newListener', newListener);
        }
    });
}
/**
 * Finds the items in list1 that are not in list2.
 * @param {Array<*>|Map<*>|Set<*>} list1
 * @param {Array<*>|Map<*>|Set<*>} list2
 * @returns {Set}
 */ function difference(list1, list2) {
    list1 = Array.isArray(list1) ? new Set(list1) : new Set(list1.values());
    list2 = Array.isArray(list2) ? new Set(list2) : new Set(list2.values());
    var difference = new Set();
    list1.forEach(function(item) {
        if (!list2.has(item)) {
            difference.add(item);
        }
    });
    return difference;
}
/**
 * Filter out the keys in an object with a given value.
 * @param {object} object - Object to be filtered
 * @param {*} [filterValue] - Value to be filtered out; If not specified, then
 *   filters out all keys which have an explicit value of "undefined"
 * @returns {object} - Filtered object
 */ function filterObject(object, filterValue) {
    return Object.keys(object).reduce(function(filtered, key) {
        if (object[key] !== filterValue) {
            filtered[key] = object[key];
        }
        return filtered;
    }, {});
}
/**
 * Map a list to an array of arrays, and return the flattened result.
 * @param {Array<*>|Set<*>|Map<*>} list
 * @param {function(*): Array<*>} [mapFn]
 * @returns Array<*>
 */ function flatMap(list, mapFn) {
    var listArray = list instanceof Map || list instanceof Set ? Array.from(list.values()) : list;
    mapFn = mapFn || function mapFn(item) {
        return item;
    };
    return listArray.reduce(function(flattened, item) {
        var mapped = mapFn(item);
        return flattened.concat(mapped);
    }, []);
}
/**
 * Get the user agent string, or return "Unknown".
 * @returns {string}
 */ function getUserAgent() {
    return typeof navigator !== 'undefined' && navigator.userAgent ? navigator.userAgent : 'Unknown';
}
/**
 * Get the platform component of the user agent string.
 * Example:
 *   Input - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36
 *   Output - macintosh
 * @returns {string}
 */ function getPlatform() {
    var userAgent = getUserAgent();
    var _a = __read(userAgent.match(/\(([^)]+)\)/) || [], 2), _b = _a[1], match = _b === void 0 ? 'unknown' : _b;
    var _c = __read(match.split(';').map(function(entry) {
        return entry.trim();
    }), 1), platform = _c[0];
    return platform.toLowerCase();
}
/**
 * Create a unique identifier.
 * @returns {string}
 */ function makeUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0;
        var v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
    });
}
/**
 * Ensure that the given function is called once per tick.
 * @param {function} fn - Function to be executed
 * @returns {function} - Schedules the given function to be called on the next tick
 */ function oncePerTick(fn) {
    var timeout = null;
    function nextTick() {
        timeout = null;
        fn();
    }
    return function scheduleNextTick() {
        if (timeout) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(nextTick);
    };
}
function promiseFromEvents(operation, eventEmitter, successEvent, failureEvent) {
    return new Promise(function(resolve, reject) {
        function onSuccess() {
            var args = [].slice.call(arguments);
            if (failureEvent) {
                eventEmitter.removeListener(failureEvent, onFailure);
            }
            resolve.apply(void 0, __spreadArray([], __read(args)));
        }
        function onFailure() {
            var args = [].slice.call(arguments);
            eventEmitter.removeListener(successEvent, onSuccess);
            reject.apply(void 0, __spreadArray([], __read(args)));
        }
        eventEmitter.once(successEvent, onSuccess);
        if (failureEvent) {
            eventEmitter.once(failureEvent, onFailure);
        }
        operation();
    });
}
/**
 * Traverse down multiple nodes on an object and return null if
 * any link in the path is unavailable.
 * @param {Object} obj - Object to traverse
 * @param {String} path - Path to traverse. Period-separated.
 * @returns {Any|null}
 */ function getOrNull(obj, path) {
    return path.split('.').reduce(function(output, step) {
        if (!output) {
            return null;
        }
        return output[step];
    }, obj);
}
/**
 * @typedef {object} Deferred
 * @property {Promise} promise
 * @property {function} reject
 * @property {function} resolve
 */ /**
 * Create a {@link Deferred}.
 * @returns {Deferred}
 */ function defer() {
    var deferred = {};
    deferred.promise = new Promise(function(resolve, reject) {
        deferred.resolve = resolve;
        deferred.reject = reject;
    });
    return deferred;
}
/**
 * Copy a method from a `source` prototype onto a `wrapper` prototype. Invoking
 * the method on the `wrapper` prototype will invoke the corresponding method
 * on an instance accessed by `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @param {string} methodName
 * @returns {undefined}
 */ function delegateMethod(source, wrapper, target, methodName) {
    if (methodName in wrapper) {
        // Skip any methods already set.
        return;
    } else if (methodName.match(/^on[a-z]+$/)) {
        // Skip EventHandlers (these are handled in the constructor).
        return;
    }
    var type;
    try {
        type = typeof source[methodName];
    } catch (error) {
    // NOTE(mroberts): Attempting to check the type of non-function members
    // on the prototype throws an error for some types.
    }
    if (type !== 'function') {
        // Skip non-function members.
        return;
    }
    /* eslint no-loop-func:0 */ wrapper[methodName] = function() {
        var _a;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        return (_a = this[target])[methodName].apply(_a, __spreadArray([], __read(args)));
    };
}
/**
 * Copy methods from a `source` prototype onto a `wrapper` prototype. Invoking
 * the methods on the `wrapper` prototype will invoke the corresponding method
 * on an instance accessed by `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @returns {undefined}
 */ function delegateMethods(source, wrapper, target) {
    for(var methodName in source){
        delegateMethod(source, wrapper, target, methodName);
    }
}
/**
 * Determine whether two values are deeply equal.
 * @param {*} val1
 * @param {*} val2
 * @returns {boolean}
 */ function isDeepEqual(val1, val2) {
    if (val1 === val2) {
        return true;
    }
    if (typeof val1 !== typeof val2) {
        return false;
    }
    if (val1 === null) {
        return val2 === null;
    }
    if (val2 === null) {
        return false;
    }
    if (Array.isArray(val1)) {
        return Array.isArray(val2) && val1.length === val2.length && val1.every(function(val, i) {
            return isDeepEqual(val, val2[i]);
        });
    }
    if (typeof val1 === 'object') {
        var val1Keys = Object.keys(val1).sort();
        var val2Keys = Object.keys(val2).sort();
        return !Array.isArray(val2) && isDeepEqual(val1Keys, val2Keys) && val1Keys.every(function(key) {
            return isDeepEqual(val1[key], val2[key]);
        });
    }
    return false;
}
/**
 * Whether the given argument is a non-array object.
 * @param {*} object
 * @return {boolean}
 */ function isNonArrayObject(object) {
    return typeof object === 'object' && !Array.isArray(object);
}
/**
 * For each property name on the `source` prototype, add getters and/or setters
 * to `wrapper` that proxy to `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @returns {undefined}
 */ function proxyProperties(source, wrapper, target) {
    Object.getOwnPropertyNames(source).forEach(function(propertyName) {
        proxyProperty(source, wrapper, target, propertyName);
    });
}
/**
 * For the property name on the `source` prototype, add a getter and/or setter
 * to `wrapper` that proxies to `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @param {string} propertyName
 * @returns {undefined}
 */ function proxyProperty(source, wrapper, target, propertyName) {
    if (propertyName in wrapper) {
        // Skip any properties already set.
        return;
    } else if (propertyName.match(/^on[a-z]+$/)) {
        Object.defineProperty(wrapper, propertyName, {
            value: null,
            writable: true
        });
        target.addEventListener(propertyName.slice(2), function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            wrapper.dispatchEvent.apply(wrapper, __spreadArray([], __read(args)));
        });
        return;
    }
    Object.defineProperty(wrapper, propertyName, {
        enumerable: true,
        get: function() {
            return target[propertyName];
        }
    });
}
/**
 * This is a function for turning a Promise into the kind referenced in the
 * Legacy Interface Extensions section of the WebRTC spec.
 * @param {Promise<*>} promise
 * @param {function<*>} onSuccess
 * @param {function<Error>} onFailure
 * @returns {Promise<undefined>}
 */ function legacyPromise(promise, onSuccess, onFailure) {
    if (onSuccess) {
        return promise.then(function(result) {
            onSuccess(result);
        }, function(error) {
            onFailure(error);
        });
    }
    return promise;
}
/**
 * Build the {@link LogLevels} object.
 * @param {String|LogLevel} logLevel - Log level name or object
 * @returns {LogLevels}
 */ function buildLogLevels(logLevel) {
    if (typeof logLevel === 'string') {
        return {
            default: logLevel,
            media: logLevel,
            signaling: logLevel,
            webrtc: logLevel
        };
    }
    return logLevel;
}
/**
 * Get the {@link Track}'s derived class name
 * @param {Track} track
 * @param {?boolean} [local=undefined]
 * @returns {string}
 */ function trackClass(track, local) {
    local = local ? 'Local' : '';
    return local + (track.kind || '').replace(/\w{1}/, function(m) {
        return m.toUpperCase();
    }) + "Track";
}
/**
 * Get the {@link TrackPublication}'s derived class name
 * @param {TrackPublication} publication
 * @param {?boolean} [local=undefined]
 * @returns {string}
 */ function trackPublicationClass(publication, local) {
    local = local ? 'Local' : '';
    return local + (publication.kind || '').replace(/\w{1}/, function(m) {
        return m.toUpperCase();
    }) + "TrackPublication";
}
/**
 * Sets all underscore-prefixed properties on `object` non-enumerable.
 * @param {Object} object
 * @returns {void}
 */ function hidePrivateProperties(object) {
    Object.getOwnPropertyNames(object).forEach(function(name) {
        if (name.startsWith('_')) {
            hideProperty(object, name);
        }
    });
}
/**
 * Creates a new subclass which, in the constructor, sets all underscore-prefixed
 * properties and the given public properties non-enumerable. This is useful for
 * patching up classes like EventEmitter which may set properties like `_events`
 * and `domain`.
 * @param {Function} klass
 * @param {Array<string>} props
 * @returns {Function} subclass
 */ function hidePrivateAndCertainPublicPropertiesInClass(klass, props) {
    // NOTE(mroberts): We do this to avoid giving the class a name.
    return function(_super) {
        __extends(class_1, _super);
        function class_1() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            var _this = _super.apply(this, __spreadArray([], __read(args))) || this;
            hidePrivateProperties(_this);
            hidePublicProperties(_this, props);
            return _this;
        }
        return class_1;
    }(klass);
}
/**
 * Hide a property of an object.
 * @param {object} object
 * @param {string} name
 */ function hideProperty(object, name) {
    var descriptor = Object.getOwnPropertyDescriptor(object, name);
    descriptor.enumerable = false;
    Object.defineProperty(object, name, descriptor);
}
/**
 * Hide the given public properties of an object.
 * @param {object} object
 * @param {Array<string>} [props=[]]
 */ function hidePublicProperties(object, props) {
    if (props === void 0) {
        props = [];
    }
    props.forEach(function(name) {
        // eslint-disable-next-line no-prototype-builtins
        if (object.hasOwnProperty(name)) {
            hideProperty(object, name);
        }
    });
}
/**
 * Convert an Array of values to an Array of JSON values by calling
 * `valueToJSON` on each value.
 * @param {Array<*>} array
 * @returns {Array<*>}
 */ function arrayToJSON(array) {
    return array.map(valueToJSON);
}
/**
 * Convert a Set of values to an Array of JSON values by calling `valueToJSON`
 * on each value.
 * @param {Set<*>} set
 * @returns {Array<*>}
 */ function setToJSON(set) {
    return arrayToJSON(__spreadArray([], __read(set)));
}
/**
 * Convert a Map from strings to values to an object of JSON values by calling
 * `valueToJSON` on each value.
 * @param {Map<string, *>} map
 * @returns {object}
 */ function mapToJSON(map) {
    return __spreadArray([], __read(map.entries())).reduce(function(json, _a) {
        var _b;
        var _c = __read(_a, 2), key = _c[0], value = _c[1];
        return Object.assign((_b = {}, _b[key] = valueToJSON(value), _b), json);
    }, {});
}
/**
 * Convert an object to a JSON value by calling `valueToJSON` on its enumerable
 * keys.
 * @param {object} object
 * @returns {object}
 */ function objectToJSON(object) {
    return Object.entries(object).reduce(function(json, _a) {
        var _b;
        var _c = __read(_a, 2), key = _c[0], value = _c[1];
        return Object.assign((_b = {}, _b[key] = valueToJSON(value), _b), json);
    }, {});
}
/**
 * Convert a value into a JSON value.
 * @param {*} value
 * @returns {*}
 */ function valueToJSON(value) {
    if (Array.isArray(value)) {
        return arrayToJSON(value);
    } else if (value instanceof Set) {
        return setToJSON(value);
    } else if (value instanceof Map) {
        return mapToJSON(value);
    } else if (value && typeof value === 'object') {
        return objectToJSON(value);
    }
    return value;
}
function createRoomConnectEventPayload(connectOptions) {
    function boolToString(val) {
        return val ? 'true' : 'false';
    }
    var payload = {
        sessionSID: sessionSID,
        // arrays props converted to lengths.
        iceServers: (connectOptions.iceServers || []).length,
        audioTracks: (connectOptions.tracks || []).filter(function(track) {
            return track.kind === 'audio';
        }).length,
        videoTracks: (connectOptions.tracks || []).filter(function(track) {
            return track.kind === 'video';
        }).length,
        dataTracks: (connectOptions.tracks || []).filter(function(track) {
            return track.kind === 'data';
        }).length
    };
    // boolean properties.
    [
        [
            'audio'
        ],
        [
            'automaticSubscription'
        ],
        [
            'enableDscp'
        ],
        [
            'eventListener'
        ],
        [
            'preflight'
        ],
        [
            'video'
        ],
        [
            'dominantSpeaker',
            'enableDominantSpeaker'
        ]
    ].forEach(function(_a) {
        var _b = __read(_a, 2), prop = _b[0], eventProp = _b[1];
        eventProp = eventProp || prop;
        payload[eventProp] = boolToString(!!connectOptions[prop]);
    });
    // numbers properties.
    [
        [
            'maxVideoBitrate'
        ],
        [
            'maxAudioBitrate'
        ]
    ].forEach(function(_a) {
        var _b = __read(_a, 2), prop = _b[0], eventProp = _b[1];
        eventProp = eventProp || prop;
        if (typeof connectOptions[prop] === 'number') {
            payload[eventProp] = connectOptions[prop];
        } else if (!isNaN(Number(connectOptions[prop]))) {
            payload[eventProp] = Number(connectOptions[prop]);
        }
    });
    // string properties.
    [
        [
            'iceTransportPolicy'
        ],
        [
            'region'
        ],
        [
            'name',
            'roomName'
        ]
    ].forEach(function(_a) {
        var _b = __read(_a, 2), prop = _b[0], eventProp = _b[1];
        eventProp = eventProp || prop;
        if (typeof connectOptions[prop] === 'string') {
            payload[eventProp] = connectOptions[prop];
        } else if (typeof connectOptions[prop] === 'number' && prop === 'name') {
            payload[eventProp] = connectOptions[prop].toString();
        }
    });
    // array props stringified.
    [
        'preferredAudioCodecs',
        'preferredVideoCodecs'
    ].forEach(function(prop) {
        if (prop in connectOptions) {
            payload[prop] = JSON.stringify(connectOptions[prop]);
        }
    });
    if ('networkQuality' in connectOptions) {
        payload.networkQualityConfiguration = {};
        if (isNonArrayObject(connectOptions.networkQuality)) {
            [
                'local',
                'remote'
            ].forEach(function(prop) {
                if (typeof connectOptions.networkQuality[prop] === 'number') {
                    payload.networkQualityConfiguration[prop] = connectOptions.networkQuality[prop];
                }
            });
        } else {
            payload.networkQualityConfiguration.remote = 0;
            payload.networkQualityConfiguration.local = connectOptions.networkQuality ? 1 : 0;
        }
    }
    if (connectOptions.bandwidthProfile && connectOptions.bandwidthProfile.video) {
        var videoBPOptions_1 = connectOptions.bandwidthProfile.video || {};
        payload.bandwidthProfileOptions = {};
        [
            'mode',
            'maxTracks',
            'trackSwitchOffMode',
            'dominantSpeakerPriority',
            'maxSubscriptionBitrate',
            'renderDimensions',
            'contentPreferencesMode',
            'clientTrackSwitchOffControl'
        ].forEach(function(prop) {
            if (typeof videoBPOptions_1[prop] === 'number' || typeof videoBPOptions_1[prop] === 'string') {
                payload.bandwidthProfileOptions[prop] = videoBPOptions_1[prop];
            } else if (typeof videoBPOptions_1[prop] === 'boolean') {
                payload.bandwidthProfileOptions[prop] = boolToString(videoBPOptions_1[prop]);
            } else if (typeof videoBPOptions_1[prop] === 'object') {
                payload.bandwidthProfileOptions[prop] = JSON.stringify(videoBPOptions_1[prop]);
            }
        });
    }
    // WebRTC Redirections
    var webRTCRedirectionsInsightsMap = {
        'RTCPeerConnection': 'customRTCPeerConnectionImpl',
        'MediaStream': 'customMediaStreamImpl',
        'getUserMedia': 'customGetUserMediaImpl',
        'enumerateDevices': 'customEnumerateDevicesImpl'
    };
    Object.entries(webRTCRedirectionsInsightsMap).forEach(function(_a) {
        var _b = __read(_a, 2), optionName = _b[0], insightName = _b[1];
        payload[insightName] = boolToString(typeof connectOptions[optionName] !== 'undefined');
    });
    return {
        group: 'room',
        name: 'connect',
        level: 'info',
        payload: payload
    };
}
/**
 * Create the bandwidth profile payload included in an RSP connect message.
 * @param {BandwidthProfileOptions} bandwidthProfile
 * @returns {object}
 */ function createBandwidthProfilePayload(bandwidthProfile) {
    return createRSPPayload(bandwidthProfile, [
        {
            prop: 'video',
            type: 'object',
            transform: createBandwidthProfileVideoPayload
        }
    ]);
}
/**
 * Create the bandwidth profile video payload included in an RSP connect message.
 * @param {VideoBandwidthProfileOptions} bandwidthProfileVideo
 * @returns {object}
 */ function createBandwidthProfileVideoPayload(bandwidthProfileVideo) {
    return createRSPPayload(bandwidthProfileVideo, [
        {
            prop: 'dominantSpeakerPriority',
            type: 'string',
            payloadProp: 'active_speaker_priority'
        },
        {
            prop: 'maxSubscriptionBitrate',
            type: 'number',
            payloadProp: 'max_subscription_bandwidth'
        },
        {
            prop: 'maxTracks',
            type: 'number',
            payloadProp: 'max_tracks'
        },
        {
            prop: 'mode',
            type: 'string'
        },
        {
            prop: 'renderDimensions',
            type: 'object',
            payloadProp: 'render_dimensions',
            transform: createRenderDimensionsPayload
        },
        {
            prop: 'trackSwitchOffMode',
            type: 'string',
            payloadProp: 'track_switch_off'
        }
    ]);
}
/**
 * Create the Media Signaling payload included in an RSP connect message.
 * @param {boolean} dominantSpeaker - whether to enable the Dominant Speaker
 *   protocol or not
 * @param {boolean} networkQuality - whether to enable the Network Quality
 *   protocol or not
 * @param {boolean} trackPriority - whether to enable the Track Priority
 *   protocol or not
 * @param {boolean} trackSwitchOff - whether to enable the Track Switch-Off
 *   protocol or not.
 * @param {boolean} renderHints - whether to enable the renderHints
 *   protocol or not.
 * @returns {object}
 */ function createMediaSignalingPayload(dominantSpeaker, networkQuality, trackPriority, trackSwitchOff, adaptiveSimulcast, renderHints, extensionTranscriptions) {
    var transports = {
        transports: [
            {
                type: 'data-channel'
            }
        ]
    };
    return Object.assign(dominantSpeaker ? {
        active_speaker: transports
    } : {}, networkQuality ? {
        network_quality: transports
    } : {}, renderHints ? {
        render_hints: transports
    } : {}, adaptiveSimulcast ? {
        publisher_hints: transports
    } : {}, trackPriority ? {
        track_priority: transports
    } : {}, trackSwitchOff ? {
        track_switch_off: transports
    } : {}, extensionTranscriptions ? {
        extension_transcriptions: transports
    } : {});
}
/**
 * Create {@link VideoTrack.Dimensions} RSP payload.
 * @param {VideoTrack.Dimensions} [dimensions]
 * @returns {object}
 */ function createDimensionsPayload(dimensions) {
    return createRSPPayload(dimensions, [
        {
            prop: 'height',
            type: 'number'
        },
        {
            prop: 'width',
            type: 'number'
        }
    ]);
}
/**
 * Create {@link VideoRenderDimensions} RSP payload.
 * @param renderDimensions
 * @returns {object}
 */ function createRenderDimensionsPayload(renderDimensions) {
    var PRIORITY_HIGH = trackPriority.PRIORITY_HIGH, PRIORITY_LOW = trackPriority.PRIORITY_LOW, PRIORITY_STANDARD = trackPriority.PRIORITY_STANDARD;
    return createRSPPayload(renderDimensions, [
        {
            prop: PRIORITY_HIGH,
            type: 'object',
            transform: createDimensionsPayload
        },
        {
            prop: PRIORITY_LOW,
            type: 'object',
            transform: createDimensionsPayload
        },
        {
            prop: PRIORITY_STANDARD,
            type: 'object',
            transform: createDimensionsPayload
        }
    ]);
}
/**
 * Create an RSP payload for the given object.
 * @param {object} object - object for which RSP payload is to be generated
 * @param {Array<object>} propConversions - conversion rules for object properties;
 *   they specify how object properties should be converted to their corresponding
 *   RSP payload properties
 * @returns {object}
 */ function createRSPPayload(object, propConversions) {
    return propConversions.reduce(function(payload, _a) {
        var _b;
        var prop = _a.prop, type = _a.type, _c = _a.payloadProp, payloadProp = _c === void 0 ? prop : _c, _d = _a.transform, transform = _d === void 0 ? function(x) {
            return x;
        } : _d;
        return typeof object[prop] === type ? Object.assign((_b = {}, _b[payloadProp] = transform(object[prop]), _b), payload) : payload;
    }, {});
}
/**
 * Create the subscribe payload included in an RSP connect/update message.
 * @param {boolean} automaticSubscription - whether to subscribe to all RemoteTracks
 * @returns {object}
 */ function createSubscribePayload(automaticSubscription) {
    return {
        rules: [
            {
                type: automaticSubscription ? 'include' : 'exclude',
                all: true
            }
        ],
        revision: 1
    };
}
function createMediaWarningsPayload(notifyWarnings) {
    var _a;
    var mediaWarnings = (_a = {}, _a[TwilioWarning.recordingMediaLost] = 'recordings', _a);
    return notifyWarnings.map(function(twilioWarningName) {
        return mediaWarnings[twilioWarningName];
    }).filter(function(name) {
        return !!name;
    });
}
/**
 * Add random jitter to a given value in the range [-jitter, jitter].
 * @private
 * @param {number} value
 * @param {number} jitter
 * @returns {number} value + random(-jitter, +jitter)
 */ function withJitter(value, jitter) {
    var rand = Math.random();
    return value - jitter + Math.floor(2 * jitter * rand + 0.5);
}
/**
 * Checks if the a number is in the range [min, max].
 * @private
 * @param {num} num
 * @param {number} min
 * @param {number} max
 * @return {boolean}
 */ function inRange(num, min, max) {
    return min <= num && num <= max;
}
/**
 * returns true if given MediaStreamTrack is a screen share track
 * @private
 * @param {MediaStreamTrack} track
 * @returns {boolean}
 */ function isChromeScreenShareTrack(track) {
    // NOTE(mpatwardhan): Chrome creates screen share tracks with label like: "screen:69734272*"
    // we will check for label that starts with "screen:D" where D being a digit.
    return util.guessBrowser() === 'chrome' && track.kind === 'video' && 'displaySurface' in track.getSettings();
}
/**
 * Returns a promise that resolve after timeoutMS have passed.
 * @param {number} timeoutMS - time to wait in milliseconds.
 * @returns {Promise<void>}
 */ function waitForSometime(timeoutMS) {
    if (timeoutMS === void 0) {
        timeoutMS = 10;
    }
    return new Promise(function(resolve) {
        return setTimeout(resolve, timeoutMS);
    });
}
/**
 * Returns a promise that resolve after event is received
 * @returns {Promise<void>}
 */ function waitForEvent(eventTarget, event) {
    return new Promise(function(resolve) {
        eventTarget.addEventListener(event, function onevent(e) {
            eventTarget.removeEventListener(event, onevent);
            resolve(e);
        });
    });
}
exports.constants = constants;
exports.createBandwidthProfilePayload = createBandwidthProfilePayload;
exports.createMediaSignalingPayload = createMediaSignalingPayload;
exports.createMediaWarningsPayload = createMediaWarningsPayload;
exports.createRoomConnectEventPayload = createRoomConnectEventPayload;
exports.createSubscribePayload = createSubscribePayload;
exports.asLocalTrack = asLocalTrack;
exports.asLocalTrackPublication = asLocalTrackPublication;
exports.capitalize = capitalize;
exports.deprecateEvents = deprecateEvents;
exports.difference = difference;
exports.filterObject = filterObject;
exports.flatMap = flatMap;
exports.getPlatform = getPlatform;
exports.getUserAgent = getUserAgent;
exports.hidePrivateProperties = hidePrivateProperties;
exports.hidePrivateAndCertainPublicPropertiesInClass = hidePrivateAndCertainPublicPropertiesInClass;
exports.isDeepEqual = isDeepEqual;
exports.isNonArrayObject = isNonArrayObject;
exports.inRange = inRange;
exports.makeUUID = makeUUID;
exports.oncePerTick = oncePerTick;
exports.promiseFromEvents = promiseFromEvents;
exports.getOrNull = getOrNull;
exports.defer = defer;
exports.delegateMethods = delegateMethods;
exports.proxyProperties = proxyProperties;
exports.legacyPromise = legacyPromise;
exports.buildLogLevels = buildLogLevels;
exports.trackClass = trackClass;
exports.trackPublicationClass = trackPublicationClass;
exports.valueToJSON = valueToJSON;
exports.withJitter = withJitter;
exports.isChromeScreenShareTrack = isChromeScreenShareTrack;
exports.waitForSometime = waitForSometime;
exports.waitForEvent = waitForEvent; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals chrome, navigator */ 'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
/**
 * Check whether the current browser is an Android device.
 * @returns {boolean}
 */ function isAndroid() {
    return /Android/.test(navigator.userAgent);
}
/**
 * Detects whether or not a device is an Apple touch screen device.
 * @returns {boolean}
 */ function hasTouchScreen() {
    return !!(navigator && navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
}
/**
 * Detects whether or not a device is an iPad.
 * @returns {boolean}
 */ function isIpad() {
    return hasTouchScreen() && window.screen.width >= 744 && (/Macintosh/i.test(navigator.userAgent) || /iPad/.test(navigator.userAgent) || /iPad/.test(navigator.platform));
}
/**
 * Detects whether or not a device is an iPhone.
 * @returns {boolean}
 */ function isIphone() {
    return hasTouchScreen() && window.screen.width <= 476 && (/Macintosh/i.test(navigator.userAgent) || /iPhone/.test(navigator.userAgent) || /iPhone/.test(navigator.platform));
}
/**
 * Check whether the current device is an iOS device.
 * @returns {boolean}
 */ function isIOS() {
    return isIpad() || isIphone();
}
/**
 * Check whether the current browser is a mobile browser
 * @returns {boolean}
 */ function isMobile() {
    return /Mobi/.test(navigator.userAgent);
}
/**
 * Check whether the current browser is non-Chromium Edge.
 * @param {string} browser
 * @returns {boolean}
 */ function isNonChromiumEdge(browser) {
    return browser === 'chrome' && /Edge/.test(navigator.userAgent) && (typeof chrome === 'undefined' || typeof chrome.runtime === 'undefined');
}
/**
 * Get the name of the rebranded Chromium browser, if any. Re-branded Chrome's user
 * agent has the following format:
 * <source>/<version> (<os>) <engine>/<version> (<engine_name>) Chrome/<version> [Mobile] Safari/<version>
 * @param browser
 * @returns {?string} Name of the rebranded Chrome browser, or null if the browser
 *   is either not Chrome or vanilla Chrome.
 */ function rebrandedChromeBrowser(browser) {
    // If the browser is not Chrome based, then it is not a rebranded Chrome browser.
    if (browser !== 'chrome') {
        return null;
    }
    // Latest desktop Brave browser has a "brave" property in navigator.
    if ('brave' in navigator) {
        return 'brave';
    }
    // Remove the "(.+)" entries from the user agent thereby retaining only the
    // <name>[/<version>] entries.
    var parenthesizedSubstrings = getParenthesizedSubstrings(navigator.userAgent);
    var nameAndVersions = parenthesizedSubstrings.reduce(function(userAgent, substring) {
        return userAgent.replace(substring, '');
    }, navigator.userAgent);
    // Extract the potential browser <name>s by ignoring the first two names, which
    // point to <source> and <engine>.
    var matches = nameAndVersions.match(/[^\s]+/g) || [];
    var _a = __read(matches.map(function(nameAndVersion) {
        return nameAndVersion.split('/')[0].toLowerCase();
    })), browserNames = _a.slice(2);
    // Extract the <name> that is not expected to be present in the vanilla Chrome
    // browser, which indicates the rebranded name (ex: "edg[e]", "electron"). If null,
    // then this is a vanilla Chrome browser.
    return browserNames.find(function(name) {
        return ![
            'chrome',
            'mobile',
            'safari'
        ].includes(name);
    }) || null;
}
/**
 * Get the name of the mobile webkit based browser, if any.
 * @param browser
 * @returns {?string} Name of the mobile webkit based browser, or null if the browser
 *   is either not webkit based or mobile safari.
 */ function mobileWebKitBrowser(browser) {
    if (browser !== 'safari') {
        return null;
    }
    if ('brave' in navigator) {
        return 'brave';
    }
    return [
        'edge',
        'edg'
    ].find(function(name) {
        return navigator.userAgent.toLowerCase().includes(name);
    }) || null;
}
/**
 * Get the top level parenthesized substrings within a given string. Unmatched
 * parentheses are ignored.
 * Ex: "abc) (def) gh(ij) (kl (mn)o) (pqr" => ["(def)", "(ij)", "(kl (mn)o)"]
 * @param {string} string
 * @returns {string[]}
 */ function getParenthesizedSubstrings(string) {
    var openParenthesisPositions = [];
    var substrings = [];
    for(var i = 0; i < string.length; i++){
        if (string[i] === '(') {
            openParenthesisPositions.push(i);
        } else if (string[i] === ')' && openParenthesisPositions.length > 0) {
            var openParenthesisPosition = openParenthesisPositions.pop();
            if (openParenthesisPositions.length === 0) {
                substrings.push(string.substring(openParenthesisPosition, i + 1));
            }
        }
    }
    return substrings;
}
module.exports = {
    isAndroid: isAndroid,
    isIOS: isIOS,
    isIpad: isIpad,
    isIphone: isIphone,
    isMobile: isMobile,
    isNonChromiumEdge: isNonChromiumEdge,
    mobileWebKitBrowser: mobileWebKitBrowser,
    rebrandedChromeBrowser: rebrandedChromeBrowser
}; //# sourceMappingURL=browserdetection.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/detectsilentaudio.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var detectSilence = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/detectsilence.js [app-client] (ecmascript)");
var N_ATTEMPTS = 3;
var ATTEMPT_DURATION_MS = 250;
/**
 * Detect whether the audio stream rendered by the given HTMLVideoElement is silent.
 * @param {HTMLAudioElement} el
 * @returns {Promise<boolean>} true if silent, false if not.
 */ function detectSilentAudio(el) {
    // NOTE(mmalavalli): We have to delay require-ing AudioContextFactory, because
    // it exports a default instance whose constructor calls Object.assign.
    var AudioContextFactory = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webaudio/audiocontext.js [app-client] (ecmascript)");
    var holder = {};
    var audioContext = AudioContextFactory.getOrCreate(holder);
    var attemptsLeft = N_ATTEMPTS;
    function doCheckSilence() {
        attemptsLeft--;
        return detectSilence(audioContext, el.srcObject, ATTEMPT_DURATION_MS).then(function(isSilent) {
            if (!isSilent) {
                return false;
            }
            if (attemptsLeft > 0) {
                return doCheckSilence();
            }
            return true;
        }).catch(function() {
            // NOTE(mmalavalli): If an error is thrown while detect silence, the audio
            // stream is assumed to be silent.
            return true;
        });
    }
    // Resolve the returned Promise with true if 3 consecutive attempts
    // to detect silent audio are successful.
    return doCheckSilence().finally(function() {
        AudioContextFactory.release(holder);
    });
}
module.exports = detectSilentAudio; //# sourceMappingURL=detectsilentaudio.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/localmediarestartdeferreds.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var defer = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").defer;
/**
 * This is a pair of Deferreds that are set whenever local media is muted and
 * resolved whenever local media is unmuted/ended and restarted if necessary.
 */ var LocalMediaRestartDeferreds = function() {
    /**
     * Constructor.
     */ function LocalMediaRestartDeferreds() {
        Object.defineProperties(this, {
            _audio: {
                value: defer(),
                writable: true
            },
            _video: {
                value: defer(),
                writable: true
            }
        });
        // Initially, resolve both the Deferreds.
        this._audio.resolve();
        this._video.resolve();
    }
    /**
     * Resolve the Deferred for audio or video.
     * @param {'audio'|'video'} kind
     */ LocalMediaRestartDeferreds.prototype.resolveDeferred = function(kind) {
        if (kind === 'audio') {
            this._audio.resolve();
        } else {
            this._video.resolve();
        }
    };
    /**
     * Start the Deferred for audio or video.
     * @param {'audio' | 'video'} kind
     */ LocalMediaRestartDeferreds.prototype.startDeferred = function(kind) {
        if (kind === 'audio') {
            this._audio = defer();
        } else {
            this._video = defer();
        }
    };
    /**
     * Wait until the Deferred for audio or video is resolved.
     * @param {'audio'|'video'} kind
     * @returns {Promise<void>}
     */ LocalMediaRestartDeferreds.prototype.whenResolved = function(kind) {
        return kind === 'audio' ? this._audio.promise : this._video.promise;
    };
    return LocalMediaRestartDeferreds;
}();
module.exports = new LocalMediaRestartDeferreds(); //# sourceMappingURL=localmediarestartdeferreds.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/detectsilentvideo.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
// Cached copy of the <canvas> used to check silent video frames.
var canvas = null;
var N_SAMPLES = 3;
var SAMPLE_HEIGHT = 50;
var SAMPLE_INTERVAL_MS = 250;
var SAMPLE_WIDTH = 50;
/**
 * Check whether the current video frame is silent by selecting a 50x50
 * sample and calculating the max value of the pixel data. If it is 0, then
 * the frame is considered to be silent.
 * @private
 * @param {HTMLVideoElement} el
 * @returns {boolean} true if silent, false if not
 */ function checkSilence(el) {
    try {
        var context = canvas.getContext('2d');
        context.drawImage(el, 0, 0, SAMPLE_WIDTH, SAMPLE_HEIGHT);
        var frame = context.getImageData(0, 0, SAMPLE_WIDTH, SAMPLE_HEIGHT);
        var frameDataWithoutAlpha = frame.data.filter(function(item, i) {
            return (i + 1) % 4;
        });
        var max = Math.max.apply(Math, frameDataWithoutAlpha);
        return max === 0;
    } catch (ex) {
        // eslint-disable-next-line no-console
        console.log('Error checking silence: ', ex);
        return false;
    }
}
/**
 * Detect whether the video stream rendered by the given HTMLVideoElement is silent.
 * @param {HTMLVideoElement} el
 * @returns {Promise<boolean>} true if silent, false if not.
 */ function detectSilentVideo(el) {
    // Create the canvas when detectSilentVideo() is called for the
    // first time.
    canvas = canvas || document.createElement('canvas');
    // Resolve the returned Promise with true if 3 consecutive sample
    // frames from the video being played by the HTMLVideoElement are
    // silent.
    return new Promise(function(resolve) {
        var samplesLeft = N_SAMPLES;
        setTimeout(function doCheckSilence() {
            samplesLeft--;
            if (!checkSilence(el)) {
                return resolve(false);
            }
            if (samplesLeft > 0) {
                return setTimeout(doCheckSilence, SAMPLE_INTERVAL_MS);
            }
            return resolve(true);
        }, SAMPLE_INTERVAL_MS);
    });
}
module.exports = detectSilentVideo; //# sourceMappingURL=detectsilentvideo.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/documentvisibilitymonitor.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * The {@link DocumentVisibilityMonitor} monitors the visibility state of the DOM
 * and executes the attached listeners in phase order when the DOM is visible.
 */ var DocumentVisibilityMonitor = function() {
    /**
     * Constructor.
     * @param {number} [nPhases=1] - the number of phases
     */ function DocumentVisibilityMonitor(nPhases) {
        var _this = this;
        if (nPhases === void 0) {
            nPhases = 1;
        }
        Object.defineProperties(this, {
            _listeners: {
                value: []
            },
            _onVisibilityChange: {
                value: function() {
                    _this._emitVisible(document.visibilityState === 'visible');
                }
            }
        });
        for(var i = 0; i < nPhases; i++){
            this._listeners.push([]);
        }
    }
    /**
     * clears the state.
     */ DocumentVisibilityMonitor.prototype.clear = function() {
        var nPhases = this._listeners.length;
        for(var i = 0; i < nPhases; i++){
            this._listeners[i] = [];
        }
    };
    DocumentVisibilityMonitor.prototype._listenerCount = function() {
        return this._listeners.reduce(function(count, phaseListeners) {
            return count + phaseListeners.length;
        }, 0);
    };
    /**
     * Call all the listeners. Makes sure that all listeners for a given phase
     * are executed before calling the listeners of the next phase.
     * @private
     */ DocumentVisibilityMonitor.prototype._emitVisible = function(isVisible) {
        var _this = this;
        var promise = Promise.resolve();
        var _loop_1 = function(phase) {
            promise = promise.then(function() {
                return _this._emitVisiblePhase(phase, isVisible);
            });
        };
        for(var phase = 1; phase <= this._listeners.length; phase++){
            _loop_1(phase);
        }
        return promise;
    };
    /**
     * Call all the listeners for a given phase.
     * @private
     */ DocumentVisibilityMonitor.prototype._emitVisiblePhase = function(phase, isVisible) {
        var phaseListeners = this._listeners[phase - 1];
        return Promise.all(phaseListeners.map(function(listener) {
            var ret = listener(isVisible);
            return ret instanceof Promise ? ret : Promise.resolve(ret);
        }));
    };
    /**
     * Start listening to the DOM visibility state change.
     * @private
     */ DocumentVisibilityMonitor.prototype._start = function() {
        document.addEventListener('visibilitychange', this._onVisibilityChange);
    };
    /**
     * Stop listening to the DOM visibility state change.
     * @private
     */ DocumentVisibilityMonitor.prototype._stop = function() {
        document.removeEventListener('visibilitychange', this._onVisibilityChange);
    };
    /**
     * Listen for the DOM visibility changes at the given phase.
     * @param {number} phase
     * @param {function} listener
     * @returns {this}
     */ DocumentVisibilityMonitor.prototype.onVisibilityChange = function(phase, listener) {
        if (typeof phase !== 'number' || phase <= 0 || phase > this._listeners.length) {
            throw new Error('invalid phase: ', phase);
        }
        var phaseListeners = this._listeners[phase - 1];
        phaseListeners.push(listener);
        if (this._listenerCount() === 1) {
            this._start();
        }
        return this;
    };
    /**
     * Stop listening for the DOM visibility change at the given phase.
     * @param {number} phase
     * @param {function} listener
     * @returns {this}
     */ DocumentVisibilityMonitor.prototype.offVisibilityChange = function(phase, listener) {
        if (typeof phase !== 'number' || phase <= 0 || phase > this._listeners.length) {
            throw new Error('invalid phase: ', phase);
        }
        var phaseListeners = this._listeners[phase - 1];
        var index = phaseListeners.indexOf(listener);
        if (index !== -1) {
            phaseListeners.splice(index, 1);
            if (this._listenerCount() === 0) {
                this._stop();
            }
        }
        return this;
    };
    return DocumentVisibilityMonitor;
}();
module.exports = new DocumentVisibilityMonitor(2); //# sourceMappingURL=documentvisibilitymonitor.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/networkmonitor.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Monitor the network connection status to detect interruptions and handoffs.
 */ var NetworkMonitor = function() {
    /**
     * Construct a {@link NetworkMonitor}.
     * @param {function} onNetworkChanged
     * @param {*} [options]
     */ function NetworkMonitor(onNetworkChanged, options) {
        var _this = this;
        options = Object.assign({
            navigator: navigator,
            window: window
        }, options);
        var nav = options.navigator;
        var connection = nav.connection || {
            type: null
        };
        var type = connection.type;
        var _a = connection.type ? {
            _events: {
                value: [
                    'change',
                    'typechange'
                ]
            },
            _listener: {
                value: function() {
                    var networkChanged = type !== _this.type && _this.isOnline;
                    type = _this.type;
                    if (networkChanged) {
                        onNetworkChanged();
                    }
                }
            },
            _target: {
                value: connection
            }
        } : {
            _events: {
                value: [
                    'online'
                ]
            },
            _listener: {
                value: onNetworkChanged
            },
            _target: {
                value: options.window
            }
        }, _events = _a._events, _listener = _a._listener, _target = _a._target;
        Object.defineProperties(this, {
            isOnline: {
                enumerable: true,
                get: function() {
                    return typeof nav.onLine === 'boolean' ? nav.onLine : true;
                }
            },
            type: {
                enumerable: true,
                get: function() {
                    return connection.type || null;
                }
            },
            _listener: _listener,
            _events: _events,
            _target: _target
        });
    }
    /**
     * Start the {@link NetworkMonitor}.
     */ NetworkMonitor.prototype.start = function() {
        var _this = this;
        this._events.forEach(function(event) {
            _this._target.addEventListener(event, _this._listener);
        });
    };
    /**
     * Stop the {@link NetworkMonitor}.
     */ NetworkMonitor.prototype.stop = function() {
        var _this = this;
        this._events.forEach(function(event) {
            _this._target.removeEventListener(event, _this._listener);
        });
    };
    return NetworkMonitor;
}();
module.exports = NetworkMonitor; //# sourceMappingURL=networkmonitor.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/timeout.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * A {@link Timeout} represents a resettable and clearable timeout.
 */ var Timeout = function() {
    /**
     * Construct a {@link Timeout}.
     * @param {function} fn - Function to call
     * @param {number} delay - Delay in milliseconds
     * @param {boolean} [autoStart=true] - If true, then start the {@link Timeout}.
     */ function Timeout(fn, delay, autoStart) {
        if (autoStart === void 0) {
            autoStart = true;
        }
        Object.defineProperties(this, {
            _delay: {
                value: delay,
                writable: true
            },
            _fn: {
                value: fn
            },
            _timeout: {
                value: null,
                writable: true
            }
        });
        if (autoStart) {
            this.start();
        }
    }
    Object.defineProperty(Timeout.prototype, "delay", {
        /**
         * The {@link Timeout} delay in milliseconds.
         * @property {number}
         */ get: function() {
            return this._delay;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeout.prototype, "isSet", {
        /**
         * Whether the {@link Timeout} is set.
         * @property {boolean}
         */ get: function() {
            return !!this._timeout;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Update the {@link Timeout} delay.
     * @param {number} delay
     * @returns {void}
     */ Timeout.prototype.setDelay = function(delay) {
        this._delay = delay;
    };
    /**
     * Start the {@link Timeout}, if not already started.
     * @returns {void}
     */ Timeout.prototype.start = function() {
        var _this = this;
        if (!this.isSet) {
            this._timeout = setTimeout(function() {
                var fn = _this._fn;
                _this.clear();
                fn();
            }, this._delay);
        }
    };
    /**
     * Clear the {@link Timeout}.
     * @returns {void}
     */ Timeout.prototype.clear = function() {
        clearTimeout(this._timeout);
        this._timeout = null;
    };
    /**
     * Reset the {@link Timeout}.
     * @returns {void}
     */ Timeout.prototype.reset = function() {
        this.clear();
        this.start();
    };
    return Timeout;
}();
module.exports = Timeout; //# sourceMappingURL=timeout.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/twilioerror.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
/**
 * @extends Error
 * @property {number} code - Error code
 */ var TwilioError = function(_super) {
    __extends(TwilioError, _super);
    /**
     * Creates a new {@link TwilioError}
     * @param {number} code - Error code
     * @param {string} [message] - Error message
     * @param {string} [fileName] - Name of the script file where error was generated
     * @param {number} [lineNumber] - Line number of the script file where error was generated
     */ function TwilioError(code) {
        var _this = this;
        var args = [].slice.call(arguments, 1);
        _this = _super.apply(this, __spreadArray([], __read(args))) || this;
        Object.setPrototypeOf(_this, TwilioError.prototype);
        var error = Error.apply(_this, args);
        error.name = 'TwilioError';
        Object.defineProperty(_this, 'code', {
            value: code,
            enumerable: true
        });
        Object.getOwnPropertyNames(error).forEach(function(prop) {
            Object.defineProperty(this, prop, {
                value: error[prop],
                enumerable: true
            });
        }, _this);
        return _this;
    }
    /**
     * Returns human readable string describing the error.
     * @returns {string}
     */ TwilioError.prototype.toString = function() {
        var message = this.message ? ": " + this.message : '';
        return this.name + " " + this.code + message;
    };
    return TwilioError;
}(Error);
module.exports = TwilioError; //# sourceMappingURL=twilioerror.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/twilio-video-errors.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// NOTE: Do not edit this file. This code is auto-generated. Contact the
// Twilio SDK Team for more information.
'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var TwilioError = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/twilioerror.js [app-client] (ecmascript)");
var TwilioErrorByCode = {};
/**
 * Create a {@link TwilioError} for a given code and message.
 * @private
 * @param {number} [code] - Error code
 * @param {string} [message] - Error message
 * @returns {TwilioError}
 */ exports.createTwilioError = function createTwilioError(code, message) {
    code = typeof code === 'number' ? code : 0;
    message = typeof message === 'string' && message ? message : 'Unknown error';
    return TwilioErrorByCode[code] ? new TwilioErrorByCode[code]() : new TwilioError(code, message);
};
/**
 * @class AccessTokenInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room is invalid.
 * @extends TwilioError
 * @property {number} code - 20101
 * @property {string} message - 'Invalid Access Token'
 */ var AccessTokenInvalidError = function(_super) {
    __extends(AccessTokenInvalidError, _super);
    function AccessTokenInvalidError() {
        var _this = _super.call(this, 20101, 'Invalid Access Token') || this;
        Object.setPrototypeOf(_this, AccessTokenInvalidError.prototype);
        return _this;
    }
    return AccessTokenInvalidError;
}(TwilioError);
exports.AccessTokenInvalidError = AccessTokenInvalidError;
Object.defineProperty(TwilioErrorByCode, 20101, {
    value: AccessTokenInvalidError
});
/**
 * @class AccessTokenHeaderInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has an invalid header.
 * @extends TwilioError
 * @property {number} code - 20102
 * @property {string} message - 'Invalid Access Token header'
 */ var AccessTokenHeaderInvalidError = function(_super) {
    __extends(AccessTokenHeaderInvalidError, _super);
    function AccessTokenHeaderInvalidError() {
        var _this = _super.call(this, 20102, 'Invalid Access Token header') || this;
        Object.setPrototypeOf(_this, AccessTokenHeaderInvalidError.prototype);
        return _this;
    }
    return AccessTokenHeaderInvalidError;
}(TwilioError);
exports.AccessTokenHeaderInvalidError = AccessTokenHeaderInvalidError;
Object.defineProperty(TwilioErrorByCode, 20102, {
    value: AccessTokenHeaderInvalidError
});
/**
 * @class AccessTokenIssuerInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room contains an invalid issuer or subject.
 * @extends TwilioError
 * @property {number} code - 20103
 * @property {string} message - 'Invalid Access Token issuer/subject'
 */ var AccessTokenIssuerInvalidError = function(_super) {
    __extends(AccessTokenIssuerInvalidError, _super);
    function AccessTokenIssuerInvalidError() {
        var _this = _super.call(this, 20103, 'Invalid Access Token issuer/subject') || this;
        Object.setPrototypeOf(_this, AccessTokenIssuerInvalidError.prototype);
        return _this;
    }
    return AccessTokenIssuerInvalidError;
}(TwilioError);
exports.AccessTokenIssuerInvalidError = AccessTokenIssuerInvalidError;
Object.defineProperty(TwilioErrorByCode, 20103, {
    value: AccessTokenIssuerInvalidError
});
/**
 * @class AccessTokenExpiredError
 * @classdesc Raised whenever the AccessToken used for connecting, or reconnecting to a Room has expired.
 * @extends TwilioError
 * @property {number} code - 20104
 * @property {string} message - 'Access Token expired or expiration date invalid'
 */ var AccessTokenExpiredError = function(_super) {
    __extends(AccessTokenExpiredError, _super);
    function AccessTokenExpiredError() {
        var _this = _super.call(this, 20104, 'Access Token expired or expiration date invalid') || this;
        Object.setPrototypeOf(_this, AccessTokenExpiredError.prototype);
        return _this;
    }
    return AccessTokenExpiredError;
}(TwilioError);
exports.AccessTokenExpiredError = AccessTokenExpiredError;
Object.defineProperty(TwilioErrorByCode, 20104, {
    value: AccessTokenExpiredError
});
/**
 * @class AccessTokenNotYetValidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room is not yet valid.
 * @extends TwilioError
 * @property {number} code - 20105
 * @property {string} message - 'Access Token not yet valid'
 */ var AccessTokenNotYetValidError = function(_super) {
    __extends(AccessTokenNotYetValidError, _super);
    function AccessTokenNotYetValidError() {
        var _this = _super.call(this, 20105, 'Access Token not yet valid') || this;
        Object.setPrototypeOf(_this, AccessTokenNotYetValidError.prototype);
        return _this;
    }
    return AccessTokenNotYetValidError;
}(TwilioError);
exports.AccessTokenNotYetValidError = AccessTokenNotYetValidError;
Object.defineProperty(TwilioErrorByCode, 20105, {
    value: AccessTokenNotYetValidError
});
/**
 * @class AccessTokenGrantsInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has invalid grants.
 * @extends TwilioError
 * @property {number} code - 20106
 * @property {string} message - 'Invalid Access Token grants'
 */ var AccessTokenGrantsInvalidError = function(_super) {
    __extends(AccessTokenGrantsInvalidError, _super);
    function AccessTokenGrantsInvalidError() {
        var _this = _super.call(this, 20106, 'Invalid Access Token grants') || this;
        Object.setPrototypeOf(_this, AccessTokenGrantsInvalidError.prototype);
        return _this;
    }
    return AccessTokenGrantsInvalidError;
}(TwilioError);
exports.AccessTokenGrantsInvalidError = AccessTokenGrantsInvalidError;
Object.defineProperty(TwilioErrorByCode, 20106, {
    value: AccessTokenGrantsInvalidError
});
/**
 * @class AccessTokenSignatureInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has an invalid signature.
 * @extends TwilioError
 * @property {number} code - 20107
 * @property {string} message - 'Invalid Access Token signature'
 */ var AccessTokenSignatureInvalidError = function(_super) {
    __extends(AccessTokenSignatureInvalidError, _super);
    function AccessTokenSignatureInvalidError() {
        var _this = _super.call(this, 20107, 'Invalid Access Token signature') || this;
        Object.setPrototypeOf(_this, AccessTokenSignatureInvalidError.prototype);
        return _this;
    }
    return AccessTokenSignatureInvalidError;
}(TwilioError);
exports.AccessTokenSignatureInvalidError = AccessTokenSignatureInvalidError;
Object.defineProperty(TwilioErrorByCode, 20107, {
    value: AccessTokenSignatureInvalidError
});
/**
 * @class SignalingConnectionError
 * @classdesc Raised whenever a signaling connection error occurs that is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53000
 * @property {string} message - 'Signaling connection error'
 */ var SignalingConnectionError = function(_super) {
    __extends(SignalingConnectionError, _super);
    function SignalingConnectionError() {
        var _this = _super.call(this, 53000, 'Signaling connection error') || this;
        Object.setPrototypeOf(_this, SignalingConnectionError.prototype);
        return _this;
    }
    return SignalingConnectionError;
}(TwilioError);
exports.SignalingConnectionError = SignalingConnectionError;
Object.defineProperty(TwilioErrorByCode, 53000, {
    value: SignalingConnectionError
});
/**
 * @class SignalingConnectionDisconnectedError
 * @classdesc Raised whenever the signaling connection is unexpectedly disconnected.
 * @extends TwilioError
 * @property {number} code - 53001
 * @property {string} message - 'Signaling connection disconnected'
 */ var SignalingConnectionDisconnectedError = function(_super) {
    __extends(SignalingConnectionDisconnectedError, _super);
    function SignalingConnectionDisconnectedError() {
        var _this = _super.call(this, 53001, 'Signaling connection disconnected') || this;
        Object.setPrototypeOf(_this, SignalingConnectionDisconnectedError.prototype);
        return _this;
    }
    return SignalingConnectionDisconnectedError;
}(TwilioError);
exports.SignalingConnectionDisconnectedError = SignalingConnectionDisconnectedError;
Object.defineProperty(TwilioErrorByCode, 53001, {
    value: SignalingConnectionDisconnectedError
});
/**
 * @class SignalingConnectionTimeoutError
 * @classdesc Raised when connection liveliness checks fail, or when the signaling session expires.
 * @extends TwilioError
 * @property {number} code - 53002
 * @property {string} message - 'Signaling connection timed out'
 */ var SignalingConnectionTimeoutError = function(_super) {
    __extends(SignalingConnectionTimeoutError, _super);
    function SignalingConnectionTimeoutError() {
        var _this = _super.call(this, 53002, 'Signaling connection timed out') || this;
        Object.setPrototypeOf(_this, SignalingConnectionTimeoutError.prototype);
        return _this;
    }
    return SignalingConnectionTimeoutError;
}(TwilioError);
exports.SignalingConnectionTimeoutError = SignalingConnectionTimeoutError;
Object.defineProperty(TwilioErrorByCode, 53002, {
    value: SignalingConnectionTimeoutError
});
/**
 * @class SignalingIncomingMessageInvalidError
 * @classdesc Raised whenever the Client receives a message from the Server that the Client cannot handle.
 * @extends TwilioError
 * @property {number} code - 53003
 * @property {string} message - 'Client received an invalid signaling message'
 */ var SignalingIncomingMessageInvalidError = function(_super) {
    __extends(SignalingIncomingMessageInvalidError, _super);
    function SignalingIncomingMessageInvalidError() {
        var _this = _super.call(this, 53003, 'Client received an invalid signaling message') || this;
        Object.setPrototypeOf(_this, SignalingIncomingMessageInvalidError.prototype);
        return _this;
    }
    return SignalingIncomingMessageInvalidError;
}(TwilioError);
exports.SignalingIncomingMessageInvalidError = SignalingIncomingMessageInvalidError;
Object.defineProperty(TwilioErrorByCode, 53003, {
    value: SignalingIncomingMessageInvalidError
});
/**
 * @class SignalingOutgoingMessageInvalidError
 * @classdesc Raised whenever the Client sends a message to the Server that the Server cannot handle.
 * @extends TwilioError
 * @property {number} code - 53004
 * @property {string} message - 'Client sent an invalid signaling message'
 */ var SignalingOutgoingMessageInvalidError = function(_super) {
    __extends(SignalingOutgoingMessageInvalidError, _super);
    function SignalingOutgoingMessageInvalidError() {
        var _this = _super.call(this, 53004, 'Client sent an invalid signaling message') || this;
        Object.setPrototypeOf(_this, SignalingOutgoingMessageInvalidError.prototype);
        return _this;
    }
    return SignalingOutgoingMessageInvalidError;
}(TwilioError);
exports.SignalingOutgoingMessageInvalidError = SignalingOutgoingMessageInvalidError;
Object.defineProperty(TwilioErrorByCode, 53004, {
    value: SignalingOutgoingMessageInvalidError
});
/**
 * @class SignalingServerBusyError
 * @classdesc Raised when the server is too busy to accept new clients.
 * @extends TwilioError
 * @property {number} code - 53006
 * @property {string} message - 'Video server is busy'
 */ var SignalingServerBusyError = function(_super) {
    __extends(SignalingServerBusyError, _super);
    function SignalingServerBusyError() {
        var _this = _super.call(this, 53006, 'Video server is busy') || this;
        Object.setPrototypeOf(_this, SignalingServerBusyError.prototype);
        return _this;
    }
    return SignalingServerBusyError;
}(TwilioError);
exports.SignalingServerBusyError = SignalingServerBusyError;
Object.defineProperty(TwilioErrorByCode, 53006, {
    value: SignalingServerBusyError
});
/**
 * @class RoomNameInvalidError
 * @classdesc Raised whenever a Room name is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53100
 * @property {string} message - 'Room name is invalid'
 */ var RoomNameInvalidError = function(_super) {
    __extends(RoomNameInvalidError, _super);
    function RoomNameInvalidError() {
        var _this = _super.call(this, 53100, 'Room name is invalid') || this;
        Object.setPrototypeOf(_this, RoomNameInvalidError.prototype);
        return _this;
    }
    return RoomNameInvalidError;
}(TwilioError);
exports.RoomNameInvalidError = RoomNameInvalidError;
Object.defineProperty(TwilioErrorByCode, 53100, {
    value: RoomNameInvalidError
});
/**
 * @class RoomNameTooLongError
 * @classdesc Raised whenever a Room name is too long.
 * @extends TwilioError
 * @property {number} code - 53101
 * @property {string} message - 'Room name is too long'
 */ var RoomNameTooLongError = function(_super) {
    __extends(RoomNameTooLongError, _super);
    function RoomNameTooLongError() {
        var _this = _super.call(this, 53101, 'Room name is too long') || this;
        Object.setPrototypeOf(_this, RoomNameTooLongError.prototype);
        return _this;
    }
    return RoomNameTooLongError;
}(TwilioError);
exports.RoomNameTooLongError = RoomNameTooLongError;
Object.defineProperty(TwilioErrorByCode, 53101, {
    value: RoomNameTooLongError
});
/**
 * @class RoomNameCharsInvalidError
 * @classdesc Raised whenever a Room name contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53102
 * @property {string} message - 'Room name contains invalid characters'
 */ var RoomNameCharsInvalidError = function(_super) {
    __extends(RoomNameCharsInvalidError, _super);
    function RoomNameCharsInvalidError() {
        var _this = _super.call(this, 53102, 'Room name contains invalid characters') || this;
        Object.setPrototypeOf(_this, RoomNameCharsInvalidError.prototype);
        return _this;
    }
    return RoomNameCharsInvalidError;
}(TwilioError);
exports.RoomNameCharsInvalidError = RoomNameCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53102, {
    value: RoomNameCharsInvalidError
});
/**
 * @class RoomCreateFailedError
 * @classdesc Raised whenever the Server is unable to create a Room.
 * @extends TwilioError
 * @property {number} code - 53103
 * @property {string} message - 'Unable to create Room'
 */ var RoomCreateFailedError = function(_super) {
    __extends(RoomCreateFailedError, _super);
    function RoomCreateFailedError() {
        var _this = _super.call(this, 53103, 'Unable to create Room') || this;
        Object.setPrototypeOf(_this, RoomCreateFailedError.prototype);
        return _this;
    }
    return RoomCreateFailedError;
}(TwilioError);
exports.RoomCreateFailedError = RoomCreateFailedError;
Object.defineProperty(TwilioErrorByCode, 53103, {
    value: RoomCreateFailedError
});
/**
 * @class RoomConnectFailedError
 * @classdesc Raised whenever a Client is unable to connect to a Room, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53104
 * @property {string} message - 'Unable to connect to Room'
 */ var RoomConnectFailedError = function(_super) {
    __extends(RoomConnectFailedError, _super);
    function RoomConnectFailedError() {
        var _this = _super.call(this, 53104, 'Unable to connect to Room') || this;
        Object.setPrototypeOf(_this, RoomConnectFailedError.prototype);
        return _this;
    }
    return RoomConnectFailedError;
}(TwilioError);
exports.RoomConnectFailedError = RoomConnectFailedError;
Object.defineProperty(TwilioErrorByCode, 53104, {
    value: RoomConnectFailedError
});
/**
 * @class RoomMaxParticipantsExceededError
 * @classdesc Raised whenever a Client is unable to connect to a Room because the Room contains too many Participants.
 * @extends TwilioError
 * @property {number} code - 53105
 * @property {string} message - 'Room contains too many Participants'
 */ var RoomMaxParticipantsExceededError = function(_super) {
    __extends(RoomMaxParticipantsExceededError, _super);
    function RoomMaxParticipantsExceededError() {
        var _this = _super.call(this, 53105, 'Room contains too many Participants') || this;
        Object.setPrototypeOf(_this, RoomMaxParticipantsExceededError.prototype);
        return _this;
    }
    return RoomMaxParticipantsExceededError;
}(TwilioError);
exports.RoomMaxParticipantsExceededError = RoomMaxParticipantsExceededError;
Object.defineProperty(TwilioErrorByCode, 53105, {
    value: RoomMaxParticipantsExceededError
});
/**
 * @class RoomNotFoundError
 * @classdesc Raised whenever attempting operation on a non-existent Room.
 * @extends TwilioError
 * @property {number} code - 53106
 * @property {string} message - 'Room not found'
 */ var RoomNotFoundError = function(_super) {
    __extends(RoomNotFoundError, _super);
    function RoomNotFoundError() {
        var _this = _super.call(this, 53106, 'Room not found') || this;
        Object.setPrototypeOf(_this, RoomNotFoundError.prototype);
        return _this;
    }
    return RoomNotFoundError;
}(TwilioError);
exports.RoomNotFoundError = RoomNotFoundError;
Object.defineProperty(TwilioErrorByCode, 53106, {
    value: RoomNotFoundError
});
/**
 * @class RoomMaxParticipantsOutOfRangeError
 * @classdesc Raised in the REST API when MaxParticipants is set out of range.
 * @extends TwilioError
 * @property {number} code - 53107
 * @property {string} message - 'MaxParticipants is out of range'
 */ var RoomMaxParticipantsOutOfRangeError = function(_super) {
    __extends(RoomMaxParticipantsOutOfRangeError, _super);
    function RoomMaxParticipantsOutOfRangeError() {
        var _this = _super.call(this, 53107, 'MaxParticipants is out of range') || this;
        Object.setPrototypeOf(_this, RoomMaxParticipantsOutOfRangeError.prototype);
        return _this;
    }
    return RoomMaxParticipantsOutOfRangeError;
}(TwilioError);
exports.RoomMaxParticipantsOutOfRangeError = RoomMaxParticipantsOutOfRangeError;
Object.defineProperty(TwilioErrorByCode, 53107, {
    value: RoomMaxParticipantsOutOfRangeError
});
/**
 * @class RoomTypeInvalidError
 * @classdesc Raised in the REST API when the user attempts to create a Room with an invalid RoomType
 * @extends TwilioError
 * @property {number} code - 53108
 * @property {string} message - 'RoomType is not valid'
 */ var RoomTypeInvalidError = function(_super) {
    __extends(RoomTypeInvalidError, _super);
    function RoomTypeInvalidError() {
        var _this = _super.call(this, 53108, 'RoomType is not valid') || this;
        Object.setPrototypeOf(_this, RoomTypeInvalidError.prototype);
        return _this;
    }
    return RoomTypeInvalidError;
}(TwilioError);
exports.RoomTypeInvalidError = RoomTypeInvalidError;
Object.defineProperty(TwilioErrorByCode, 53108, {
    value: RoomTypeInvalidError
});
/**
 * @class RoomTimeoutOutOfRangeError
 * @classdesc Raised in the REST API when Timeout is set out of range.
 * @extends TwilioError
 * @property {number} code - 53109
 * @property {string} message - 'Timeout is out of range'
 */ var RoomTimeoutOutOfRangeError = function(_super) {
    __extends(RoomTimeoutOutOfRangeError, _super);
    function RoomTimeoutOutOfRangeError() {
        var _this = _super.call(this, 53109, 'Timeout is out of range') || this;
        Object.setPrototypeOf(_this, RoomTimeoutOutOfRangeError.prototype);
        return _this;
    }
    return RoomTimeoutOutOfRangeError;
}(TwilioError);
exports.RoomTimeoutOutOfRangeError = RoomTimeoutOutOfRangeError;
Object.defineProperty(TwilioErrorByCode, 53109, {
    value: RoomTimeoutOutOfRangeError
});
/**
 * @class RoomStatusCallbackMethodInvalidError
 * @classdesc Raised in the REST API when StatusCallbackMethod is set to an invalid value.
 * @extends TwilioError
 * @property {number} code - 53110
 * @property {string} message - 'StatusCallbackMethod is invalid'
 */ var RoomStatusCallbackMethodInvalidError = function(_super) {
    __extends(RoomStatusCallbackMethodInvalidError, _super);
    function RoomStatusCallbackMethodInvalidError() {
        var _this = _super.call(this, 53110, 'StatusCallbackMethod is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusCallbackMethodInvalidError.prototype);
        return _this;
    }
    return RoomStatusCallbackMethodInvalidError;
}(TwilioError);
exports.RoomStatusCallbackMethodInvalidError = RoomStatusCallbackMethodInvalidError;
Object.defineProperty(TwilioErrorByCode, 53110, {
    value: RoomStatusCallbackMethodInvalidError
});
/**
 * @class RoomStatusCallbackInvalidError
 * @classdesc Raised in the REST API when StatusCallback is not a valid URL or the url is too long.
 * @extends TwilioError
 * @property {number} code - 53111
 * @property {string} message - 'StatusCallback is invalid'
 */ var RoomStatusCallbackInvalidError = function(_super) {
    __extends(RoomStatusCallbackInvalidError, _super);
    function RoomStatusCallbackInvalidError() {
        var _this = _super.call(this, 53111, 'StatusCallback is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusCallbackInvalidError.prototype);
        return _this;
    }
    return RoomStatusCallbackInvalidError;
}(TwilioError);
exports.RoomStatusCallbackInvalidError = RoomStatusCallbackInvalidError;
Object.defineProperty(TwilioErrorByCode, 53111, {
    value: RoomStatusCallbackInvalidError
});
/**
 * @class RoomStatusInvalidError
 * @classdesc Raised in the REST API when Status is not valid or the Room is not in-progress.
 * @extends TwilioError
 * @property {number} code - 53112
 * @property {string} message - 'Status is invalid'
 */ var RoomStatusInvalidError = function(_super) {
    __extends(RoomStatusInvalidError, _super);
    function RoomStatusInvalidError() {
        var _this = _super.call(this, 53112, 'Status is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusInvalidError.prototype);
        return _this;
    }
    return RoomStatusInvalidError;
}(TwilioError);
exports.RoomStatusInvalidError = RoomStatusInvalidError;
Object.defineProperty(TwilioErrorByCode, 53112, {
    value: RoomStatusInvalidError
});
/**
 * @class RoomRoomExistsError
 * @classdesc Raised in the REST API when the Room creation fails because a Room exists with the same name.
 * @extends TwilioError
 * @property {number} code - 53113
 * @property {string} message - 'Room exists'
 */ var RoomRoomExistsError = function(_super) {
    __extends(RoomRoomExistsError, _super);
    function RoomRoomExistsError() {
        var _this = _super.call(this, 53113, 'Room exists') || this;
        Object.setPrototypeOf(_this, RoomRoomExistsError.prototype);
        return _this;
    }
    return RoomRoomExistsError;
}(TwilioError);
exports.RoomRoomExistsError = RoomRoomExistsError;
Object.defineProperty(TwilioErrorByCode, 53113, {
    value: RoomRoomExistsError
});
/**
 * @class RoomInvalidParametersError
 * @classdesc Raised in the REST API when one or more Room creation parameter is incompatible with the Room type.
 * @extends TwilioError
 * @property {number} code - 53114
 * @property {string} message - 'Room creation parameter(s) incompatible with the Room type'
 */ var RoomInvalidParametersError = function(_super) {
    __extends(RoomInvalidParametersError, _super);
    function RoomInvalidParametersError() {
        var _this = _super.call(this, 53114, 'Room creation parameter(s) incompatible with the Room type') || this;
        Object.setPrototypeOf(_this, RoomInvalidParametersError.prototype);
        return _this;
    }
    return RoomInvalidParametersError;
}(TwilioError);
exports.RoomInvalidParametersError = RoomInvalidParametersError;
Object.defineProperty(TwilioErrorByCode, 53114, {
    value: RoomInvalidParametersError
});
/**
 * @class RoomMediaRegionInvalidError
 * @classdesc Raised in the REST API when MediaRegion is set to an invalid value.
 * @extends TwilioError
 * @property {number} code - 53115
 * @property {string} message - 'MediaRegion is invalid'
 */ var RoomMediaRegionInvalidError = function(_super) {
    __extends(RoomMediaRegionInvalidError, _super);
    function RoomMediaRegionInvalidError() {
        var _this = _super.call(this, 53115, 'MediaRegion is invalid') || this;
        Object.setPrototypeOf(_this, RoomMediaRegionInvalidError.prototype);
        return _this;
    }
    return RoomMediaRegionInvalidError;
}(TwilioError);
exports.RoomMediaRegionInvalidError = RoomMediaRegionInvalidError;
Object.defineProperty(TwilioErrorByCode, 53115, {
    value: RoomMediaRegionInvalidError
});
/**
 * @class RoomMediaRegionUnavailableError
 * @classdesc Raised in the REST API when MediaRegion is set to a valid value but no media servers are available.
 * @extends TwilioError
 * @property {number} code - 53116
 * @property {string} message - 'There are no media servers available in the MediaRegion'
 */ var RoomMediaRegionUnavailableError = function(_super) {
    __extends(RoomMediaRegionUnavailableError, _super);
    function RoomMediaRegionUnavailableError() {
        var _this = _super.call(this, 53116, 'There are no media servers available in the MediaRegion') || this;
        Object.setPrototypeOf(_this, RoomMediaRegionUnavailableError.prototype);
        return _this;
    }
    return RoomMediaRegionUnavailableError;
}(TwilioError);
exports.RoomMediaRegionUnavailableError = RoomMediaRegionUnavailableError;
Object.defineProperty(TwilioErrorByCode, 53116, {
    value: RoomMediaRegionUnavailableError
});
/**
 * @class RoomSubscriptionOperationNotSupportedError
 * @classdesc Raised whenever the subscription operation requested is not supported for the Room type.
 * @extends TwilioError
 * @property {number} code - 53117
 * @property {string} message - 'The subscription operation requested is not supported for the Room type'
 */ var RoomSubscriptionOperationNotSupportedError = function(_super) {
    __extends(RoomSubscriptionOperationNotSupportedError, _super);
    function RoomSubscriptionOperationNotSupportedError() {
        var _this = _super.call(this, 53117, 'The subscription operation requested is not supported for the Room type') || this;
        Object.setPrototypeOf(_this, RoomSubscriptionOperationNotSupportedError.prototype);
        return _this;
    }
    return RoomSubscriptionOperationNotSupportedError;
}(TwilioError);
exports.RoomSubscriptionOperationNotSupportedError = RoomSubscriptionOperationNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53117, {
    value: RoomSubscriptionOperationNotSupportedError
});
/**
 * @class RoomCompletedError
 * @classdesc Raised whenever a Room is completed via the REST API.
 * @extends TwilioError
 * @property {number} code - 53118
 * @property {string} message - 'Room completed'
 */ var RoomCompletedError = function(_super) {
    __extends(RoomCompletedError, _super);
    function RoomCompletedError() {
        var _this = _super.call(this, 53118, 'Room completed') || this;
        Object.setPrototypeOf(_this, RoomCompletedError.prototype);
        return _this;
    }
    return RoomCompletedError;
}(TwilioError);
exports.RoomCompletedError = RoomCompletedError;
Object.defineProperty(TwilioErrorByCode, 53118, {
    value: RoomCompletedError
});
/**
 * @class RoomAudioOnlyFlagNotSupportedError
 * @classdesc Raised whenever a participant tries to set the AudioOnly flag for a Room type other than Group Rooms.
 * @extends TwilioError
 * @property {number} code - 53124
 * @property {string} message - 'The AudioOnly flag is not supported for the Room type'
 */ var RoomAudioOnlyFlagNotSupportedError = function(_super) {
    __extends(RoomAudioOnlyFlagNotSupportedError, _super);
    function RoomAudioOnlyFlagNotSupportedError() {
        var _this = _super.call(this, 53124, 'The AudioOnly flag is not supported for the Room type') || this;
        Object.setPrototypeOf(_this, RoomAudioOnlyFlagNotSupportedError.prototype);
        return _this;
    }
    return RoomAudioOnlyFlagNotSupportedError;
}(TwilioError);
exports.RoomAudioOnlyFlagNotSupportedError = RoomAudioOnlyFlagNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53124, {
    value: RoomAudioOnlyFlagNotSupportedError
});
/**
 * @class RoomTrackKindNotSupportedError
 * @classdesc Raised whenever a participant tries to publish a track or connects with a track that is not supported by the group room.
 * @extends TwilioError
 * @property {number} code - 53125
 * @property {string} message - 'The track kind is not supported by the Room'
 */ var RoomTrackKindNotSupportedError = function(_super) {
    __extends(RoomTrackKindNotSupportedError, _super);
    function RoomTrackKindNotSupportedError() {
        var _this = _super.call(this, 53125, 'The track kind is not supported by the Room') || this;
        Object.setPrototypeOf(_this, RoomTrackKindNotSupportedError.prototype);
        return _this;
    }
    return RoomTrackKindNotSupportedError;
}(TwilioError);
exports.RoomTrackKindNotSupportedError = RoomTrackKindNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53125, {
    value: RoomTrackKindNotSupportedError
});
/**
 * @class ParticipantIdentityInvalidError
 * @classdesc Raised whenever a Participant identity is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53200
 * @property {string} message - 'Participant identity is invalid'
 */ var ParticipantIdentityInvalidError = function(_super) {
    __extends(ParticipantIdentityInvalidError, _super);
    function ParticipantIdentityInvalidError() {
        var _this = _super.call(this, 53200, 'Participant identity is invalid') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityInvalidError.prototype);
        return _this;
    }
    return ParticipantIdentityInvalidError;
}(TwilioError);
exports.ParticipantIdentityInvalidError = ParticipantIdentityInvalidError;
Object.defineProperty(TwilioErrorByCode, 53200, {
    value: ParticipantIdentityInvalidError
});
/**
 * @class ParticipantIdentityTooLongError
 * @classdesc Raised whenever a Participant identity is too long.
 * @extends TwilioError
 * @property {number} code - 53201
 * @property {string} message - 'Participant identity is too long'
 */ var ParticipantIdentityTooLongError = function(_super) {
    __extends(ParticipantIdentityTooLongError, _super);
    function ParticipantIdentityTooLongError() {
        var _this = _super.call(this, 53201, 'Participant identity is too long') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityTooLongError.prototype);
        return _this;
    }
    return ParticipantIdentityTooLongError;
}(TwilioError);
exports.ParticipantIdentityTooLongError = ParticipantIdentityTooLongError;
Object.defineProperty(TwilioErrorByCode, 53201, {
    value: ParticipantIdentityTooLongError
});
/**
 * @class ParticipantIdentityCharsInvalidError
 * @classdesc Raised whenever a Participant identity contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53202
 * @property {string} message - 'Participant identity contains invalid characters'
 */ var ParticipantIdentityCharsInvalidError = function(_super) {
    __extends(ParticipantIdentityCharsInvalidError, _super);
    function ParticipantIdentityCharsInvalidError() {
        var _this = _super.call(this, 53202, 'Participant identity contains invalid characters') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityCharsInvalidError.prototype);
        return _this;
    }
    return ParticipantIdentityCharsInvalidError;
}(TwilioError);
exports.ParticipantIdentityCharsInvalidError = ParticipantIdentityCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53202, {
    value: ParticipantIdentityCharsInvalidError
});
/**
 * @class ParticipantMaxTracksExceededError
 * @classdesc Raised whenever a Participant tries to publish a Track and the maximum number of published tracks allowed in the Room at the same time has been reached
 * @extends TwilioError
 * @property {number} code - 53203
 * @property {string} message - 'The maximum number of published tracks allowed in the Room at the same time has been reached'
 */ var ParticipantMaxTracksExceededError = function(_super) {
    __extends(ParticipantMaxTracksExceededError, _super);
    function ParticipantMaxTracksExceededError() {
        var _this = _super.call(this, 53203, 'The maximum number of published tracks allowed in the Room at the same time has been reached') || this;
        Object.setPrototypeOf(_this, ParticipantMaxTracksExceededError.prototype);
        return _this;
    }
    return ParticipantMaxTracksExceededError;
}(TwilioError);
exports.ParticipantMaxTracksExceededError = ParticipantMaxTracksExceededError;
Object.defineProperty(TwilioErrorByCode, 53203, {
    value: ParticipantMaxTracksExceededError
});
/**
 * @class ParticipantNotFoundError
 * @classdesc Raised whenever attempting an operation on a non-existent Participant.
 * @extends TwilioError
 * @property {number} code - 53204
 * @property {string} message - 'Participant not found'
 */ var ParticipantNotFoundError = function(_super) {
    __extends(ParticipantNotFoundError, _super);
    function ParticipantNotFoundError() {
        var _this = _super.call(this, 53204, 'Participant not found') || this;
        Object.setPrototypeOf(_this, ParticipantNotFoundError.prototype);
        return _this;
    }
    return ParticipantNotFoundError;
}(TwilioError);
exports.ParticipantNotFoundError = ParticipantNotFoundError;
Object.defineProperty(TwilioErrorByCode, 53204, {
    value: ParticipantNotFoundError
});
/**
 * @class ParticipantDuplicateIdentityError
 * @classdesc Raised by the server to the existing Participant when a new Participant joins a Room with the same identity as the existing Participant.
 * @extends TwilioError
 * @property {number} code - 53205
 * @property {string} message - 'Participant disconnected because of duplicate identity'
 */ var ParticipantDuplicateIdentityError = function(_super) {
    __extends(ParticipantDuplicateIdentityError, _super);
    function ParticipantDuplicateIdentityError() {
        var _this = _super.call(this, 53205, 'Participant disconnected because of duplicate identity') || this;
        Object.setPrototypeOf(_this, ParticipantDuplicateIdentityError.prototype);
        return _this;
    }
    return ParticipantDuplicateIdentityError;
}(TwilioError);
exports.ParticipantDuplicateIdentityError = ParticipantDuplicateIdentityError;
Object.defineProperty(TwilioErrorByCode, 53205, {
    value: ParticipantDuplicateIdentityError
});
/**
 * @class TrackInvalidError
 * @classdesc Raised whenever a Track is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53300
 * @property {string} message - 'Track is invalid'
 */ var TrackInvalidError = function(_super) {
    __extends(TrackInvalidError, _super);
    function TrackInvalidError() {
        var _this = _super.call(this, 53300, 'Track is invalid') || this;
        Object.setPrototypeOf(_this, TrackInvalidError.prototype);
        return _this;
    }
    return TrackInvalidError;
}(TwilioError);
exports.TrackInvalidError = TrackInvalidError;
Object.defineProperty(TwilioErrorByCode, 53300, {
    value: TrackInvalidError
});
/**
 * @class TrackNameInvalidError
 * @classdesc Raised whenever a Track name is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53301
 * @property {string} message - 'Track name is invalid'
 */ var TrackNameInvalidError = function(_super) {
    __extends(TrackNameInvalidError, _super);
    function TrackNameInvalidError() {
        var _this = _super.call(this, 53301, 'Track name is invalid') || this;
        Object.setPrototypeOf(_this, TrackNameInvalidError.prototype);
        return _this;
    }
    return TrackNameInvalidError;
}(TwilioError);
exports.TrackNameInvalidError = TrackNameInvalidError;
Object.defineProperty(TwilioErrorByCode, 53301, {
    value: TrackNameInvalidError
});
/**
 * @class TrackNameTooLongError
 * @classdesc Raised whenever a Track name is too long.
 * @extends TwilioError
 * @property {number} code - 53302
 * @property {string} message - 'Track name is too long'
 */ var TrackNameTooLongError = function(_super) {
    __extends(TrackNameTooLongError, _super);
    function TrackNameTooLongError() {
        var _this = _super.call(this, 53302, 'Track name is too long') || this;
        Object.setPrototypeOf(_this, TrackNameTooLongError.prototype);
        return _this;
    }
    return TrackNameTooLongError;
}(TwilioError);
exports.TrackNameTooLongError = TrackNameTooLongError;
Object.defineProperty(TwilioErrorByCode, 53302, {
    value: TrackNameTooLongError
});
/**
 * @class TrackNameCharsInvalidError
 * @classdesc Raised whenever a Track name contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53303
 * @property {string} message - 'Track name contains invalid characters'
 */ var TrackNameCharsInvalidError = function(_super) {
    __extends(TrackNameCharsInvalidError, _super);
    function TrackNameCharsInvalidError() {
        var _this = _super.call(this, 53303, 'Track name contains invalid characters') || this;
        Object.setPrototypeOf(_this, TrackNameCharsInvalidError.prototype);
        return _this;
    }
    return TrackNameCharsInvalidError;
}(TwilioError);
exports.TrackNameCharsInvalidError = TrackNameCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53303, {
    value: TrackNameCharsInvalidError
});
/**
 * @class TrackNameIsDuplicatedError
 * @classdesc Raised whenever a Participant is currently publishing a Track with the same name.
 * @extends TwilioError
 * @property {number} code - 53304
 * @property {string} message - 'Track name is duplicated'
 */ var TrackNameIsDuplicatedError = function(_super) {
    __extends(TrackNameIsDuplicatedError, _super);
    function TrackNameIsDuplicatedError() {
        var _this = _super.call(this, 53304, 'Track name is duplicated') || this;
        Object.setPrototypeOf(_this, TrackNameIsDuplicatedError.prototype);
        return _this;
    }
    return TrackNameIsDuplicatedError;
}(TwilioError);
exports.TrackNameIsDuplicatedError = TrackNameIsDuplicatedError;
Object.defineProperty(TwilioErrorByCode, 53304, {
    value: TrackNameIsDuplicatedError
});
/**
 * @class TrackServerTrackCapacityReachedError
 * @classdesc The server does not have enough resources available to create a new Track.
 * @extends TwilioError
 * @property {number} code - 53305
 * @property {string} message - 'The server has reached capacity and cannot fulfill this request'
 */ var TrackServerTrackCapacityReachedError = function(_super) {
    __extends(TrackServerTrackCapacityReachedError, _super);
    function TrackServerTrackCapacityReachedError() {
        var _this = _super.call(this, 53305, 'The server has reached capacity and cannot fulfill this request') || this;
        Object.setPrototypeOf(_this, TrackServerTrackCapacityReachedError.prototype);
        return _this;
    }
    return TrackServerTrackCapacityReachedError;
}(TwilioError);
exports.TrackServerTrackCapacityReachedError = TrackServerTrackCapacityReachedError;
Object.defineProperty(TwilioErrorByCode, 53305, {
    value: TrackServerTrackCapacityReachedError
});
/**
 * @class MediaClientLocalDescFailedError
 * @classdesc Raised whenever a Client is unable to create or apply a local media description.
 * @extends TwilioError
 * @property {number} code - 53400
 * @property {string} message - 'Client is unable to create or apply a local media description'
 */ var MediaClientLocalDescFailedError = function(_super) {
    __extends(MediaClientLocalDescFailedError, _super);
    function MediaClientLocalDescFailedError() {
        var _this = _super.call(this, 53400, 'Client is unable to create or apply a local media description') || this;
        Object.setPrototypeOf(_this, MediaClientLocalDescFailedError.prototype);
        return _this;
    }
    return MediaClientLocalDescFailedError;
}(TwilioError);
exports.MediaClientLocalDescFailedError = MediaClientLocalDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53400, {
    value: MediaClientLocalDescFailedError
});
/**
 * @class MediaServerLocalDescFailedError
 * @classdesc Raised whenever the Server is unable to create or apply a local media description.
 * @extends TwilioError
 * @property {number} code - 53401
 * @property {string} message - 'Server is unable to create or apply a local media description'
 */ var MediaServerLocalDescFailedError = function(_super) {
    __extends(MediaServerLocalDescFailedError, _super);
    function MediaServerLocalDescFailedError() {
        var _this = _super.call(this, 53401, 'Server is unable to create or apply a local media description') || this;
        Object.setPrototypeOf(_this, MediaServerLocalDescFailedError.prototype);
        return _this;
    }
    return MediaServerLocalDescFailedError;
}(TwilioError);
exports.MediaServerLocalDescFailedError = MediaServerLocalDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53401, {
    value: MediaServerLocalDescFailedError
});
/**
 * @class MediaClientRemoteDescFailedError
 * @classdesc Raised whenever the Client receives a remote media description but is unable to apply it.
 * @extends TwilioError
 * @property {number} code - 53402
 * @property {string} message - 'Client is unable to apply a remote media description'
 */ var MediaClientRemoteDescFailedError = function(_super) {
    __extends(MediaClientRemoteDescFailedError, _super);
    function MediaClientRemoteDescFailedError() {
        var _this = _super.call(this, 53402, 'Client is unable to apply a remote media description') || this;
        Object.setPrototypeOf(_this, MediaClientRemoteDescFailedError.prototype);
        return _this;
    }
    return MediaClientRemoteDescFailedError;
}(TwilioError);
exports.MediaClientRemoteDescFailedError = MediaClientRemoteDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53402, {
    value: MediaClientRemoteDescFailedError
});
/**
 * @class MediaServerRemoteDescFailedError
 * @classdesc Raised whenever the Server receives a remote media description but is unable to apply it.
 * @extends TwilioError
 * @property {number} code - 53403
 * @property {string} message - 'Server is unable to apply a remote media description'
 */ var MediaServerRemoteDescFailedError = function(_super) {
    __extends(MediaServerRemoteDescFailedError, _super);
    function MediaServerRemoteDescFailedError() {
        var _this = _super.call(this, 53403, 'Server is unable to apply a remote media description') || this;
        Object.setPrototypeOf(_this, MediaServerRemoteDescFailedError.prototype);
        return _this;
    }
    return MediaServerRemoteDescFailedError;
}(TwilioError);
exports.MediaServerRemoteDescFailedError = MediaServerRemoteDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53403, {
    value: MediaServerRemoteDescFailedError
});
/**
 * @class MediaNoSupportedCodecError
 * @classdesc Raised whenever the intersection of codecs supported by the Client and the Server (or, in peer-to-peer, the Client and another Participant) is empty.
 * @extends TwilioError
 * @property {number} code - 53404
 * @property {string} message - 'No supported codec'
 */ var MediaNoSupportedCodecError = function(_super) {
    __extends(MediaNoSupportedCodecError, _super);
    function MediaNoSupportedCodecError() {
        var _this = _super.call(this, 53404, 'No supported codec') || this;
        Object.setPrototypeOf(_this, MediaNoSupportedCodecError.prototype);
        return _this;
    }
    return MediaNoSupportedCodecError;
}(TwilioError);
exports.MediaNoSupportedCodecError = MediaNoSupportedCodecError;
Object.defineProperty(TwilioErrorByCode, 53404, {
    value: MediaNoSupportedCodecError
});
/**
 * @class MediaConnectionError
 * @classdesc Raised by the Client or Server whenever a media connection fails or raised by the Client whenever it detects that media has stopped flowing.
 * @extends TwilioError
 * @property {number} code - 53405
 * @property {string} message - 'Media connection failed or Media activity ceased'
 */ var MediaConnectionError = function(_super) {
    __extends(MediaConnectionError, _super);
    function MediaConnectionError() {
        var _this = _super.call(this, 53405, 'Media connection failed or Media activity ceased') || this;
        Object.setPrototypeOf(_this, MediaConnectionError.prototype);
        return _this;
    }
    return MediaConnectionError;
}(TwilioError);
exports.MediaConnectionError = MediaConnectionError;
Object.defineProperty(TwilioErrorByCode, 53405, {
    value: MediaConnectionError
});
/**
 * @class MediaDTLSTransportFailedError
 * @classdesc There was a problem while negotiating with the remote DTLS peer. Therefore the Participant will not be able to publish or subscribe to Tracks.
 * @extends TwilioError
 * @property {number} code - 53407
 * @property {string} message - 'Media connection failed due to DTLS handshake failure'
 */ var MediaDTLSTransportFailedError = function(_super) {
    __extends(MediaDTLSTransportFailedError, _super);
    function MediaDTLSTransportFailedError() {
        var _this = _super.call(this, 53407, 'Media connection failed due to DTLS handshake failure') || this;
        Object.setPrototypeOf(_this, MediaDTLSTransportFailedError.prototype);
        return _this;
    }
    return MediaDTLSTransportFailedError;
}(TwilioError);
exports.MediaDTLSTransportFailedError = MediaDTLSTransportFailedError;
Object.defineProperty(TwilioErrorByCode, 53407, {
    value: MediaDTLSTransportFailedError
});
/**
 * @class ConfigurationAcquireFailedError
 * @classdesc Raised whenever the Client is unable to acquire configuration information from the Server.
 * @extends TwilioError
 * @property {number} code - 53500
 * @property {string} message - 'Unable to acquire configuration'
 */ var ConfigurationAcquireFailedError = function(_super) {
    __extends(ConfigurationAcquireFailedError, _super);
    function ConfigurationAcquireFailedError() {
        var _this = _super.call(this, 53500, 'Unable to acquire configuration') || this;
        Object.setPrototypeOf(_this, ConfigurationAcquireFailedError.prototype);
        return _this;
    }
    return ConfigurationAcquireFailedError;
}(TwilioError);
exports.ConfigurationAcquireFailedError = ConfigurationAcquireFailedError;
Object.defineProperty(TwilioErrorByCode, 53500, {
    value: ConfigurationAcquireFailedError
});
/**
 * @class ConfigurationAcquireTurnFailedError
 * @classdesc Raised whenever the Server is unable to return TURN credentials to the Client
 * @extends TwilioError
 * @property {number} code - 53501
 * @property {string} message - 'Unable to acquire TURN credentials'
 */ var ConfigurationAcquireTurnFailedError = function(_super) {
    __extends(ConfigurationAcquireTurnFailedError, _super);
    function ConfigurationAcquireTurnFailedError() {
        var _this = _super.call(this, 53501, 'Unable to acquire TURN credentials') || this;
        Object.setPrototypeOf(_this, ConfigurationAcquireTurnFailedError.prototype);
        return _this;
    }
    return ConfigurationAcquireTurnFailedError;
}(TwilioError);
exports.ConfigurationAcquireTurnFailedError = ConfigurationAcquireTurnFailedError;
Object.defineProperty(TwilioErrorByCode, 53501, {
    value: ConfigurationAcquireTurnFailedError
}); //# sourceMappingURL=twilio-video-errors.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/movingaveragedelta.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * Calculates the moving average delta for the given pair ofsamples. A sample (S)
 * consists of a numerator (Sn) and a denominator (Sd).The moving average delta is
 * calculated as follows:
 *
 * MovingAvgDelta = (Sn[1] - Sn[0]) / (Sd[1] - Sd[0])
 */ var MovingAverageDelta = function() {
    /**
     * Constructor.
     */ function MovingAverageDelta() {
        Object.defineProperties(this, {
            _samples: {
                value: [
                    {
                        denominator: 0,
                        numerator: 0
                    },
                    {
                        denominator: 0,
                        numerator: 0
                    }
                ]
            }
        });
    }
    /**
     * Get the moving average delta.
     * @returns {number}
     */ MovingAverageDelta.prototype.get = function() {
        var samples = this._samples;
        var denominatorDelta = samples[1].denominator - samples[0].denominator || Infinity;
        var numeratorDelta = samples[1].numerator - samples[0].numerator;
        return numeratorDelta / denominatorDelta;
    };
    /**
     * Put a sample and get rid of the older sample to maintain sample size of 2.
     * @param numerator
     * @param denominator
     */ MovingAverageDelta.prototype.putSample = function(numerator, denominator) {
        var samples = this._samples;
        samples.shift();
        samples.push({
            denominator: denominator,
            numerator: numerator
        });
    };
    return MovingAverageDelta;
}();
module.exports = MovingAverageDelta; //# sourceMappingURL=movingaveragedelta.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/eventobserver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable no-console */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var VALID_GROUPS = [
    'signaling',
    'room',
    'media',
    'quality',
    'video-processor',
    'preflight'
];
var VALID_LEVELS = [
    'debug',
    'error',
    'info',
    'warning'
];
/**
 * EventObserver listens to SDK events and re-emits them on the
 * @link EventListener} with some additional information.
 * @extends EventEmitter
 * @emits EventObserver#event
 */ var EventObserver = function(_super) {
    __extends(EventObserver, _super);
    /**
     * Constructor.
     * @param {InsightsPublisher} publisher
     * @param {number} connectTimestamp
     * @param {Log} log
     * @param {EventListener} [eventListener]
     */ function EventObserver(publisher, connectTimestamp, log, eventListener) {
        if (eventListener === void 0) {
            eventListener = null;
        }
        var _this = _super.call(this) || this;
        _this.on('event', function(_a) {
            var name = _a.name, group = _a.group, level = _a.level, payload = _a.payload;
            if (typeof name !== 'string') {
                log.error('Unexpected name: ', name);
                throw new Error('Unexpected name: ', name);
            }
            if (!VALID_GROUPS.includes(group)) {
                log.error('Unexpected group: ', group);
                throw new Error('Unexpected group: ', group);
            }
            if (!VALID_LEVELS.includes(level)) {
                log.error('Unexpected level: ', level);
                throw new Error('Unexpected level: ', level);
            }
            var timestamp = Date.now();
            var elapsedTime = timestamp - connectTimestamp;
            var publisherPayload = Object.assign({
                elapsedTime: elapsedTime,
                level: level
            }, payload ? payload : {});
            publisher.publish(group, name, publisherPayload);
            var event = Object.assign({
                elapsedTime: elapsedTime,
                group: group,
                level: level,
                name: name,
                timestamp: timestamp
            }, payload ? {
                payload: payload
            } : {});
            var logLevel = {
                debug: 'debug',
                error: 'error',
                info: 'info',
                warning: 'warn'
            }[level];
            log[logLevel]('event', event);
            if (eventListener && group === 'signaling') {
                eventListener.emit('event', event);
            }
        });
        return _this;
    }
    return EventObserver;
}(EventEmitter);
/**
 * An SDK event.
 * @event EventObserver#event
 * @param {{name: string, payload: *}} event
 */ module.exports = EventObserver; //# sourceMappingURL=eventobserver.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/insightspublisher/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable camelcase */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var EventEmitter = __turbopack_context__.r("[project]/node_modules/events/events.js [app-client] (ecmascript)").EventEmitter;
var getUserAgent = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").getUserAgent;
var MAX_RECONNECT_ATTEMPTS = 5;
var RECONNECT_INTERVAL_MS = 50;
var WS_CLOSE_NORMAL = 1000;
var toplevel = globalThis;
var WebSocket = toplevel.WebSocket ? toplevel.WebSocket : __turbopack_context__.r("[project]/node_modules/twilio-video/src/ws.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), hardwareDevicePublisheriPad = _a.hardwareDevicePublisheriPad, hardwareDevicePublisheriPhone = _a.hardwareDevicePublisheriPhone;
var util = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)");
var browserdetection = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)");
/**
 * Publish events to the Insights gateway.
 * @extends EventEmitter
 * @emits InsightsPublisher#connected
 * @emits InsightsPublisher#disconnected
 * @emits InsightsPublisher#reconnecting
 */ var InsightsPublisher = function(_super) {
    __extends(InsightsPublisher, _super);
    /**
     * @param {string} token - Insights gateway token
     * @param {string} sdkName - Name of the SDK using the {@link InsightsPublisher}
     * @param {string} sdkVersion - Version of the SDK using the {@link InsightsPublisher}
     * @param {string} environment - One of 'dev', 'stage' or 'prod'
     * @param {string} realm - Region identifier
     * @param {InsightsPublisherOptions} options - Override default behavior
     */ function InsightsPublisher(token, sdkName, sdkVersion, environment, realm, options) {
        var _this = _super.call(this) || this;
        options = Object.assign({
            gateway: createGateway(environment, realm) + "/v1/VideoEvents",
            maxReconnectAttempts: MAX_RECONNECT_ATTEMPTS,
            reconnectIntervalMs: RECONNECT_INTERVAL_MS,
            userAgent: getUserAgent(),
            WebSocket: WebSocket
        }, options);
        Object.defineProperties(_this, {
            _connectTimestamp: {
                value: 0,
                writable: true
            },
            _eventQueue: {
                value: []
            },
            _readyToConnect: {
                value: util.defer()
            },
            _reconnectAttemptsLeft: {
                value: options.maxReconnectAttempts,
                writable: true
            },
            _ws: {
                value: null,
                writable: true
            },
            _WebSocket: {
                value: options.WebSocket
            }
        });
        _this._readyToConnect.promise.then(function(_a) {
            var roomSid = _a.roomSid, participantSid = _a.participantSid;
            var self = _this;
            _this.on('disconnected', function maybeReconnect(error) {
                self._session = null;
                if (error && self._reconnectAttemptsLeft > 0) {
                    self.emit('reconnecting');
                    reconnect(self, token, sdkName, sdkVersion, roomSid, participantSid, options);
                    return;
                }
                self.removeListener('disconnected', maybeReconnect);
            });
            connect(_this, token, sdkName, sdkVersion, roomSid, participantSid, options);
        }).catch(function() {
        // ignore failures to connect
        });
        return _this;
    }
    /**
     * Start connecting to the Insights gateway.
     * @param {string} roomSid
     * @param {string} participantSid
     * @returns {void}
     */ InsightsPublisher.prototype.connect = function(roomSid, participantSid) {
        this._readyToConnect.resolve({
            roomSid: roomSid,
            participantSid: participantSid
        });
    };
    /**
     * Publish an event to the Insights gateway.
     * @private
     * @param {*} event
     */ InsightsPublisher.prototype._publish = function(event) {
        event.session = this._session;
        this._ws.send(JSON.stringify(event));
    };
    /**
     * Disconnect from the Insights gateway.
     * @returns {boolean} true if called when connecting/open, false if not
     */ InsightsPublisher.prototype.disconnect = function() {
        if (this._ws === null || this._ws.readyState === this._WebSocket.CLOSING || this._ws.readyState === this._WebSocket.CLOSED) {
            return false;
        }
        try {
            this._ws.close();
        } catch (error) {
        // Do nothing.
        }
        this.emit('disconnected');
        return true;
    };
    /**
     * Publish (or queue, if not connected) an event to the Insights gateway.
     * @param {string} groupName - Event group name
     * @param {string} eventName - Event name
     * @param {object} payload - Event payload
     * @returns {boolean} true if queued or published, false if disconnect() called
     */ InsightsPublisher.prototype.publish = function(groupName, eventName, payload) {
        if (this._ws !== null && (this._ws.readyState === this._WebSocket.CLOSING || this._ws.readyState === this._WebSocket.CLOSED)) {
            return false;
        }
        var publishOrEnqueue = typeof this._session === 'string' ? this._publish.bind(this) : this._eventQueue.push.bind(this._eventQueue);
        publishOrEnqueue({
            group: groupName,
            name: eventName,
            payload: payload,
            timestamp: Date.now(),
            type: 'event',
            version: 1
        });
        return true;
    };
    return InsightsPublisher;
}(EventEmitter);
/**
 * Start connecting to the Insights gateway.
 * @private
 * @param {InsightsPublisher} publisher
 * @param {string} name
 * @param {string} token
 * @param {string} sdkName
 * @param {string} sdkVersion
 * @param {string} roomSid
 * @param {string} participantSid
 * @param {InsightsPublisherOptions} options
 */ function connect(publisher, token, sdkName, sdkVersion, roomSid, participantSid, options) {
    publisher._connectTimestamp = Date.now();
    publisher._reconnectAttemptsLeft--;
    publisher._ws = new options.WebSocket(options.gateway);
    var ws = publisher._ws;
    ws.addEventListener('close', function(event) {
        if (event.code === WS_CLOSE_NORMAL) {
            publisher.emit('disconnected');
            return;
        }
        publisher.emit('disconnected', new Error("WebSocket Error " + event.code + ": " + event.reason));
    });
    ws.addEventListener('message', function(message) {
        handleConnectResponse(publisher, JSON.parse(message.data), options);
    });
    ws.addEventListener('open', function() {
        var connectRequest = {
            type: 'connect',
            token: token,
            version: 1
        };
        connectRequest.publisher = {
            name: sdkName,
            sdkVersion: sdkVersion,
            userAgent: options.userAgent,
            participantSid: participantSid,
            roomSid: roomSid
        };
        if (browserdetection.isIpad()) {
            connectRequest.publisher = __assign(__assign({}, connectRequest.publisher), hardwareDevicePublisheriPad);
        } else if (browserdetection.isIphone()) {
            connectRequest.publisher = __assign(__assign({}, connectRequest.publisher), hardwareDevicePublisheriPhone);
        }
        ws.send(JSON.stringify(connectRequest));
    });
}
/**
 * Create the Insights Websocket gateway URL.
 * @param {string} environment
 * @param {string} realm
 * @returns {string}
 */ function createGateway(environment, realm) {
    return environment === 'prod' ? "wss://sdkgw." + realm + ".twilio.com" : "wss://sdkgw." + environment + "-" + realm + ".twilio.com";
}
/**
 * Handle connect response from the Insights gateway.
 * @param {InsightsPublisher} publisher
 * @param {*} response
 * @param {InsightsPublisherOptions} options
 */ function handleConnectResponse(publisher, response, options) {
    switch(response.type){
        case 'connected':
            publisher._session = response.session;
            publisher._reconnectAttemptsLeft = options.maxReconnectAttempts;
            publisher._eventQueue.splice(0).forEach(publisher._publish, publisher);
            publisher.emit('connected');
            break;
        case 'error':
            publisher._ws.close();
            publisher.emit('disconnected', new Error(response.message));
            break;
    }
}
/**
 * Start re-connecting to the Insights gateway with an appropriate delay based
 * on InsightsPublisherOptions#reconnectIntervalMs.
 * @private
 * @param {InsightsPublisher} publisher
 * @param {string} token
 * @param {string} sdkName
 * @param {string} sdkVersion
 * @param {string} roomSid
 * @param {string} participantSid
 * @param {InsightsPublisherOptions} options
 */ function reconnect(publisher, token, sdkName, sdkVersion, roomSid, participantSid, options) {
    var connectInterval = Date.now() - publisher._connectTimestamp;
    var timeToWait = options.reconnectIntervalMs - connectInterval;
    if (timeToWait > 0) {
        setTimeout(function() {
            connect(publisher, token, sdkName, sdkVersion, roomSid, participantSid, options);
        }, timeToWait);
        return;
    }
    connect(publisher, token, sdkName, sdkVersion, roomSid, participantSid, options);
}
/**
 * The {@link InsightsPublisher} is connected to the gateway.
 * @event InsightsPublisher#connected
 */ /**
 * The {@link InsightsPublisher} is disconnected from the gateway.
 * @event InsightsPublisher#disconnected
 * @param {Error} [error] - Optional error if disconnected unintentionally
 */ /**
 * The {@link InsightsPublisher} is re-connecting to the gateway.
 * @event InsightsPublisher#reconnecting
 */ /**
 * {@link InsightsPublisher} options.
 * @typedef {object} InsightsPublisherOptions
 * @property {string} [gateway=sdkgw.{environment}-{realm}.twilio.com] - Insights WebSocket gateway url
 * @property {number} [maxReconnectAttempts=5] - Max re-connect attempts
 * @property {number} [reconnectIntervalMs=50] - Re-connect interval in ms
 */ module.exports = InsightsPublisher; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/cancelablepromise.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
/**
 * A Promise that can be canceled with {@link CancelablePromise#cancel}.
 * @extends Promise
*/ var CancelablePromise = function() {
    /**
     * Construct a new {@link CancelablePromise}.
     * @param {CancelablePromise.OnCreate} onCreate
     * @param {CancelablePromise.OnCancel} onCancel
    */ /**
     * A function to be called on {@link CancelablePromise} creation
     * @typedef {function} CancelablePromise.OnCreate
     * @param {function(*)} resolve
     * @param {function(*)} reject
     * @param {function(): boolean} isCanceled
    */ /**
     * A function to be called when {@link CancelablePromise#cancel} is called
     * @typedef {function} CancelablePromise.OnCancel
     */ function CancelablePromise(onCreate, onCancel) {
        var _this = this;
        /* istanbul ignore next */ Object.defineProperties(this, {
            _isCancelable: {
                writable: true,
                value: true
            },
            _isCanceled: {
                writable: true,
                value: false
            },
            _onCancel: {
                value: onCancel
            }
        });
        Object.defineProperty(this, '_promise', {
            value: new Promise(function(resolve, reject) {
                onCreate(function(value) {
                    _this._isCancelable = false;
                    resolve(value);
                }, function(reason) {
                    _this._isCancelable = false;
                    reject(reason);
                }, function() {
                    return _this._isCanceled;
                });
            })
        });
    }
    /**
     * Create a synchronously-rejected {@link CancelablePromise}.
     * @param {*} reason
     * @returns {Promise<*>}
     */ CancelablePromise.reject = function(reason) {
        return new CancelablePromise(function rejected(resolve, reject) {
            reject(reason);
        }, function onCancel() {
        // Do nothing.
        });
    };
    /**
     * Create a synchronously-resolved {@link CancelablePromise}.
     * @param {*|Promise<*>|Thenable<*>} result
     * @returns {CancelablePromise<*>}
     */ CancelablePromise.resolve = function(result) {
        return new CancelablePromise(function resolved(resolve) {
            resolve(result);
        }, function onCancel() {
        // Do nothing.
        });
    };
    /**
     * Attempt to cancel the {@link CancelablePromise}.
     * @returns {this}
     */ CancelablePromise.prototype.cancel = function() {
        if (this._isCancelable) {
            this._isCanceled = true;
            this._onCancel();
        }
        return this;
    };
    /**
     * @param {function} onRejected
     * @returns {CancelablePromise}
     */ CancelablePromise.prototype.catch = function() {
        var args = [].slice.call(arguments);
        var promise = this._promise;
        return new CancelablePromise(function onCreate(resolve, reject) {
            promise.catch.apply(promise, __spreadArray([], __read(args))).then(resolve, reject);
        }, this._onCancel);
    };
    /**
     * @param {?function} onResolved
     * @param {function} [onRejected]
     * @returns {CancelablePromise}
     */ CancelablePromise.prototype.then = function() {
        var args = [].slice.call(arguments);
        var promise = this._promise;
        return new CancelablePromise(function onCreate(resolve, reject) {
            promise.then.apply(promise, __spreadArray([], __read(args))).then(resolve, reject);
        }, this._onCancel);
    };
    /**
   * @param {?function} onFinally
   * @returns {CancelablePromise}
   */ CancelablePromise.prototype.finally = function() {
        var args = [].slice.call(arguments);
        var promise = this._promise;
        return new CancelablePromise(function onCreate(resolve, reject) {
            promise.finally.apply(promise, __spreadArray([], __read(args))).then(resolve, reject);
        }, this._onCancel);
    };
    return CancelablePromise;
}();
module.exports = CancelablePromise; //# sourceMappingURL=cancelablepromise.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/validate.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var isNonArrayObject = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").isNonArrayObject;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/constants.js [app-client] (ecmascript)"), E = _a.typeErrors, clientTrackSwitchOffControl = _a.clientTrackSwitchOffControl, videoContentPreferencesMode = _a.videoContentPreferencesMode, subscriptionMode = _a.subscriptionMode, trackPriority = _a.trackPriority, trackSwitchOffMode = _a.trackSwitchOffMode;
/**
 * Validate the {@link BandwidthProfileOptions} object.
 * @param {BandwidthProfileOptions} bandwidthProfile
 * @returns {?Error} - null if valid, Error if not.
 */ function validateBandwidthProfile(bandwidthProfile) {
    var error = validateObject(bandwidthProfile, 'options.bandwidthProfile');
    if (!bandwidthProfile || error) {
        return error;
    }
    error = validateObject(bandwidthProfile.video, 'options.bandwidthProfile.video', [
        {
            prop: 'contentPreferencesMode',
            values: Object.values(videoContentPreferencesMode)
        },
        {
            prop: 'dominantSpeakerPriority',
            values: Object.values(trackPriority)
        },
        {
            prop: 'maxSubscriptionBitrate',
            type: 'number'
        },
        {
            prop: 'maxTracks',
            type: 'number'
        },
        {
            prop: 'mode',
            values: Object.values(subscriptionMode)
        },
        {
            prop: 'clientTrackSwitchOffControl',
            values: Object.values(clientTrackSwitchOffControl)
        },
        {
            prop: 'trackSwitchOffMode',
            values: Object.values(trackSwitchOffMode)
        }
    ]);
    if (error) {
        return error;
    }
    if (bandwidthProfile.video) {
        // maxTracks is replaced by clientTrackSwitchOffControl.
        // throw an error if both are specified.
        if ('maxTracks' in bandwidthProfile.video && 'clientTrackSwitchOffControl' in bandwidthProfile.video) {
            return new TypeError('options.bandwidthProfile.video.maxTracks is deprecated. Use options.bandwidthProfile.video.clientTrackSwitchOffControl instead.');
        }
        // renderDimensions is replaced by contentPreferencesMode.
        // throw an error if both are specified.
        if ('renderDimensions' in bandwidthProfile.video && 'contentPreferencesMode' in bandwidthProfile.video) {
            return new TypeError('options.bandwidthProfile.video.renderDimensions is deprecated. Use options.bandwidthProfile.video.contentPreferencesMode instead.');
        }
        return validateRenderDimensions(bandwidthProfile.video.renderDimensions);
    }
    return null;
}
/**
 * Throw if the given track is not a {@link LocalAudioTrack}, a
 * {@link LocalVideoTrack} or a MediaStreamTrack.
 * @param {*} track
 * @param {object} options
 */ function validateLocalTrack(track, options) {
    if (!(track instanceof options.LocalAudioTrack || track instanceof options.LocalDataTrack || track instanceof options.LocalVideoTrack || track instanceof options.MediaStreamTrack)) {
        /* eslint new-cap:0 */ throw E.INVALID_TYPE('track', 'LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack');
    }
}
/**
 * Validate an object. An object is valid if it is undefined or a non-null, non-array
 * object whose properties satisfy the specified data-type or value-range requirements.
 * @param {object} object - the object to be validated
 * @param {string} name - the object name to be used to build the error message, if invalid
 * @param {Array<object>} [propChecks] - optional data-type or value-range requirements
 *   for the object's properties
 * @returns {?Error} - null if object is valid, Error if not
 */ function validateObject(object, name, propChecks) {
    if (propChecks === void 0) {
        propChecks = [];
    }
    // NOTE(mmalavalli): We determine that an undefined object is valid because this
    // means the parent object does not contain this object as a property, which is
    // a valid scenario.
    if (typeof object === 'undefined') {
        return null;
    }
    // NOTE(mmalavalli): We determine that if the object is null, or an Array, or
    // any other non-object type, then it is invalid.
    if (object === null || !isNonArrayObject(object)) {
        return E.INVALID_TYPE(name, 'object');
    }
    // NOTE(mmalavalli): We determine that the object is invalid if at least one of
    // its properties does not satisfy its data-type or value-range requirement.
    return propChecks.reduce(function(error, _a) {
        var prop = _a.prop, type = _a.type, values = _a.values;
        if (error || !(prop in object)) {
            return error;
        }
        var value = object[prop];
        if (type && typeof value !== type) {
            return E.INVALID_TYPE(name + "." + prop, type);
        }
        if (type === 'number' && isNaN(value)) {
            return E.INVALID_TYPE(name + "." + prop, type);
        }
        if (Array.isArray(values) && !values.includes(value)) {
            return E.INVALID_VALUE(name + "." + prop, values);
        }
        return error;
    }, null);
}
/**
 * Validates the renderDimensions field to be "auto" or {@link VideoRenderDimensions} object.
 * @param {string|VideoRenderDimensions} renderDimensions
 * @returns {?Error} - null if valid, Error if not.
 */ function validateRenderDimensions(renderDimensions) {
    var name = 'options.bandwidthProfile.video.renderDimensions';
    var error = validateObject(renderDimensions, name);
    return renderDimensions ? error || Object.values(trackPriority).reduce(function(error, prop) {
        return error || validateObject(renderDimensions[prop], name + "." + prop, [
            {
                prop: 'height',
                type: 'number'
            },
            {
                prop: 'width',
                type: 'number'
            }
        ]);
    }, null) : error;
}
exports.validateBandwidthProfile = validateBandwidthProfile;
exports.validateLocalTrack = validateLocalTrack;
exports.validateObject = validateObject; //# sourceMappingURL=validate.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/nullobserver.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* eslint-disable no-console */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var NullObserver = function() {
    function NullObserver(callback) {
        Object.defineProperties(this, {
            _callback: {
                value: callback
            }
        });
    }
    NullObserver.prototype.observe = function() {};
    NullObserver.prototype.unobserve = function() {};
    NullObserver.prototype.makeVisible = function(videoEl) {
        var visibleEntry = this._makeFakeEntry(videoEl, true);
        this._callback([
            visibleEntry
        ]);
    };
    NullObserver.prototype.makeInvisible = function(videoEl) {
        var invisibleEntry = this._makeFakeEntry(videoEl, false);
        this._callback([
            invisibleEntry
        ]);
    };
    NullObserver.prototype._makeFakeEntry = function(videoElement, isIntersecting) {
        return {
            target: videoElement,
            isIntersecting: isIntersecting
        };
    };
    return NullObserver;
}();
var NullIntersectionObserver = function(_super) {
    __extends(NullIntersectionObserver, _super);
    function NullIntersectionObserver() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return NullIntersectionObserver;
}(NullObserver);
var NullResizeObserver = function(_super) {
    __extends(NullResizeObserver, _super);
    function NullResizeObserver() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NullResizeObserver.prototype.resize = function(videoEl) {
        var entry = this._makeFakeEntry(videoEl, true);
        this._callback([
            entry
        ]);
    };
    return NullResizeObserver;
}(NullObserver);
module.exports = {
    NullIntersectionObserver: NullIntersectionObserver,
    NullResizeObserver: NullResizeObserver,
    NullObserver: NullObserver
}; //# sourceMappingURL=nullobserver.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/insightspublisher/null.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// eslint-disable-next-line no-warning-comments
// TODO(mroberts): This should be described as implementing some
// InsightsPublisher interface.
'use strict';
/**
 * Null Insights publisher.
 */ var InsightsPublisher = function() {
    function InsightsPublisher() {
        Object.defineProperties(this, {
            _connected: {
                writable: true,
                value: true
            }
        });
    }
    /**
     * Connect
     * @returns {void}
     */ InsightsPublisher.prototype.connect = function() {};
    /**
     * Disconnect.
     * @returns {boolean}
     */ InsightsPublisher.prototype.disconnect = function() {
        if (this._connected) {
            this._connected = false;
            return true;
        }
        return false;
    };
    /**
     * Publish.
     * @returns {boolean}
     */ InsightsPublisher.prototype.publish = function() {
        return this._connected;
    };
    return InsightsPublisher;
}();
module.exports = InsightsPublisher; //# sourceMappingURL=null.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/backoff.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Expose `Backoff`.
 */ var Backoff = function() {
    /**
     * Construct a {@link Backoff}.
     * @param {object} options
     * @property {number} min - Initial timeout in milliseconds [100]
     * @property {number} max - Max timeout [10000]
     * @property {boolean} jitter - Apply jitter [0]
     * @property {number} factor - Multiplication factor for Backoff operation [2]
     */ function Backoff(options) {
        Object.defineProperties(this, {
            _min: {
                value: options.min || 100
            },
            _max: {
                value: options.max || 10000
            },
            _jitter: {
                value: options.jitter > 0 && options.jitter <= 1 ? options.jitter : 0
            },
            _factor: {
                value: options.factor || 2
            },
            _attempts: {
                value: 0,
                writable: true
            },
            _duration: {
                enumerable: false,
                get: function() {
                    var ms = this._min * Math.pow(this._factor, this._attempts);
                    if (this._jitter) {
                        var rand = Math.random();
                        var deviation = Math.floor(rand * this._jitter * ms);
                        ms = (Math.floor(rand * 10) & 1) === 0 ? ms - deviation : ms + deviation;
                    }
                    return Math.min(ms, this._max) | 0;
                }
            },
            _timeoutID: {
                value: null,
                writable: true
            }
        });
    }
    /**
    * Start the backoff operation.
    * @param {function} fn - Function to call
    * @return {void}
    * @api public
    */ Backoff.prototype.backoff = function(fn) {
        var _this = this;
        var duration = this._duration;
        if (this._timeoutID) {
            clearTimeout(this._timeoutID);
            this._timeoutID = null;
        }
        this._timeoutID = setTimeout(function() {
            _this._attempts++;
            fn();
        }, duration);
    };
    /**
    * Reset the number of attempts and clear the timer.
    *
    * @return {void}
    * @api public
    */ Backoff.prototype.reset = function() {
        this._attempts = 0;
        if (this._timeoutID) {
            clearTimeout(this._timeoutID);
            this._timeoutID = null;
        }
    };
    return Backoff;
}();
module.exports = Backoff; //# sourceMappingURL=backoff.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/sdp/simulcast.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), difference = _a.difference, flatMap = _a.flatMap;
/**
 * Create a random {@link SSRC}.
 * @returns {SSRC}
 */ function createSSRC() {
    var ssrcMax = 0xffffffff;
    return String(Math.floor(Math.random() * ssrcMax));
}
/**
 * @property {string} cName
 * @property {boolean} isSimulcastEnabled
 * @property {Map<RtxSSRC, PrimarySSRC>} rtxPairs
 * @property {Set<PrimarySSRC>} primarySSRCs
 * @property {string} streamId
 * @property {Track.ID} trackId
 */ var TrackAttributes = function() {
    /**
     * Construct a {@link MediaStreamTrack} attribute store.
     * @param {Track.ID} trackId - The MediaStreamTrack ID
     * @param {MediaStreamID} streamId - The MediaStream ID
     * @param {string} cName - The MediaStream cname
     */ function TrackAttributes(trackId, streamId, cName) {
        Object.defineProperties(this, {
            cName: {
                enumerable: true,
                value: cName
            },
            isSimulcastEnabled: {
                enumerable: true,
                value: false,
                writable: true
            },
            primarySSRCs: {
                enumerable: true,
                value: new Set()
            },
            rtxPairs: {
                enumerable: true,
                value: new Map()
            },
            streamId: {
                enumerable: true,
                value: streamId
            },
            trackId: {
                enumerable: true,
                value: trackId
            }
        });
    }
    /**
     * Add {@link SimSSRC}s to the {@link TrackAttributes}.
     * @returns {void}
     */ TrackAttributes.prototype.addSimulcastSSRCs = function() {
        if (this.isSimulcastEnabled) {
            return;
        }
        var simulcastSSRCs = [
            createSSRC(),
            createSSRC()
        ];
        simulcastSSRCs.forEach(function(ssrc) {
            this.primarySSRCs.add(ssrc);
        }, this);
        if (this.rtxPairs.size) {
            simulcastSSRCs.forEach(function(ssrc) {
                this.rtxPairs.set(createSSRC(), ssrc);
            }, this);
        }
    };
    /**
     * Add the given {@link PrimarySSRC} or {@link RtxSSRC} to the {@link TrackAttributes}
     * and update the "isSimulcastEnabled" flag if it is also a {@link SimSSRC}.
     * @param {SSRC} ssrc - The {@link SSRC} to be added
     * @param {?PrimarySSRC} primarySSRC - The {@link PrimarySSRC}; if the given
     *   {@link SSRC} itself is the {@link PrimarySSRC}, then this is set to null
     * @param {boolean} isSimSSRC - true if the given {@link SSRC} is a
     *   {@link SimSSRC}; false otherwise
     * @returns {void}
     */ TrackAttributes.prototype.addSSRC = function(ssrc, primarySSRC, isSimSSRC) {
        if (primarySSRC) {
            this.rtxPairs.set(ssrc, primarySSRC);
        } else {
            this.primarySSRCs.add(ssrc);
        }
        this.isSimulcastEnabled = this.isSimulcastEnabled || isSimSSRC;
    };
    /**
     * Construct the SDP lines for the {@link TrackAttributes}.
     * @param {boolean} [excludeRtx=false]
     * @returns {Array<string>} Array of SDP lines
     */ TrackAttributes.prototype.toSdpLines = function(excludeRtx) {
        var _this = this;
        var rtxPairs = excludeRtx ? [] : Array.from(this.rtxPairs.entries()).map(function(rtxPair) {
            return rtxPair.reverse();
        });
        var simSSRCs = Array.from(this.primarySSRCs.values());
        var ssrcs = rtxPairs.length ? flatMap(rtxPairs) : simSSRCs;
        var attrLines = flatMap(ssrcs, function(ssrc) {
            return [
                "a=ssrc:" + ssrc + " cname:" + _this.cName,
                "a=ssrc:" + ssrc + " msid:" + _this.streamId + " " + _this.trackId
            ];
        });
        var rtxPairLines = rtxPairs.map(function(rtxPair) {
            return "a=ssrc-group:FID " + rtxPair.join(' ');
        });
        var simGroupLines = [
            "a=ssrc-group:SIM " + simSSRCs.join(' ')
        ];
        return rtxPairLines.concat(attrLines).concat(simGroupLines);
    };
    return TrackAttributes;
}();
/**
 * Get the matches for a given RegEx pattern.
 * @param {string} section - SDP media section
 * @param {string} pattern - RegEx pattern
 * @returns {Array<Array<string>>} - Array of pattern matches
 */ function getMatches(section, pattern) {
    var matches = section.match(new RegExp(pattern, 'gm')) || [];
    return matches.map(function(match) {
        var matches = match.match(new RegExp(pattern)) || [];
        return matches.slice(1);
    });
}
/**
 * Get the {@link SimSSRC}s that belong to a simulcast group.
 * @param {string} section - SDP media section
 * @returns {Set<SimSSRC>} Set of simulcast {@link SSRC}s
 */ function getSimulcastSSRCs(section) {
    var simGroupPattern = '^a=ssrc-group:SIM ([0-9]+) ([0-9]+) ([0-9]+)$';
    return new Set(flatMap(getMatches(section, simGroupPattern)));
}
/**
 * Get the value of the given attribute for an SSRC.
 * @param {string} section - SDP media section
 * @param {SSRC} ssrc - {@link SSRC} whose attribute's value is to be determinded
 * @param {string} attribute - {@link SSRC} attribute name
 * @param {string} - {@link SSRC} attribute value
 */ function getSSRCAttribute(section, ssrc, attribute) {
    var pattern = "a=ssrc:" + ssrc + " " + attribute + ":(.+)";
    return section.match(new RegExp(pattern))[1];
}
/**
 * Create a Map of {@link PrimarySSRC}s and their {@link RtxSSRC}s.
 * @param {string} section - SDP media section
 * @returns {Map<RtxSSRC, PrimarySSRC>} - Map of {@link RtxSSRC}s and their
 *   corresponding {@link PrimarySSRC}s
 */ function getSSRCRtxPairs(section) {
    var rtxPairPattern = '^a=ssrc-group:FID ([0-9]+) ([0-9]+)$';
    return new Map(getMatches(section, rtxPairPattern).map(function(pair) {
        return pair.reverse();
    }));
}
/**
 * Create SSRC attribute tuples.
 * @param {string} section
 * @returns {Array<[SSRC, MediaStreamID, Track.ID]>}
 */ function createSSRCAttributeTuples(section) {
    var _a = __read(flatMap(getMatches(section, '^a=msid:(.+) (.+)$')), 2), streamId = _a[0], trackId = _a[1];
    var ssrcs = flatMap(getMatches(section, '^a=ssrc:(.+) cname:.+$'));
    return ssrcs.map(function(ssrc) {
        return [
            ssrc,
            streamId,
            trackId
        ];
    });
}
/**
 * Create a Map of MediaStreamTrack IDs and their {@link TrackAttributes}.
 * @param {string} section - SDP media section
 * @returns {Map<Track.ID, TrackAttributes>}
 */ function createTrackIdsToAttributes(section) {
    var simSSRCs = getSimulcastSSRCs(section);
    var rtxPairs = getSSRCRtxPairs(section);
    var ssrcAttrTuples = createSSRCAttributeTuples(section);
    return ssrcAttrTuples.reduce(function(trackIdsToSSRCs, tuple) {
        var ssrc = tuple[0];
        var streamId = tuple[1];
        var trackId = tuple[2];
        var trackAttributes = trackIdsToSSRCs.get(trackId) || new TrackAttributes(trackId, streamId, getSSRCAttribute(section, ssrc, 'cname'));
        var primarySSRC = rtxPairs.get(ssrc) || null;
        trackAttributes.addSSRC(ssrc, primarySSRC, simSSRCs.has(ssrc));
        return trackIdsToSSRCs.set(trackId, trackAttributes);
    }, new Map());
}
/**
 * Apply simulcast settings to the given SDP media section.
 * @param {string} section - SDP media section
 * @param {Map<Track.ID, TrackAttributes>} trackIdsToAttributes - Existing
 *   map which will be updated for new MediaStreamTrack IDs
 * @returns {string} - The transformed SDP media section
 */ function setSimulcastInMediaSection(section, trackIdsToAttributes) {
    var newTrackIdsToAttributes = createTrackIdsToAttributes(section);
    var newTrackIds = Array.from(newTrackIdsToAttributes.keys());
    var trackIds = Array.from(trackIdsToAttributes.keys());
    var trackIdsToAdd = difference(newTrackIds, trackIds);
    var trackIdsToIgnore = difference(trackIds, newTrackIds);
    // Update "trackIdsToAttributes" with TrackAttributes for new
    // MediaStreamTrack IDs.
    var trackAttributesToAdd = flatMap(trackIdsToAdd, function(trackId) {
        return newTrackIdsToAttributes.get(trackId);
    });
    trackAttributesToAdd.forEach(function(trackAttributes) {
        trackAttributes.addSimulcastSSRCs();
        trackIdsToAttributes.set(trackAttributes.trackId, trackAttributes);
    });
    // Get the SDP lines of the relevant MediaStreamTrack IDs from
    // "trackIdsToAttributes".
    trackIds = Array.from(trackIdsToAttributes.keys());
    var relevantTrackIds = difference(trackIds, trackIdsToIgnore);
    var relevantTrackAttributes = flatMap(relevantTrackIds, function(trackId) {
        return trackIdsToAttributes.get(trackId);
    });
    var excludeRtx = !section.match(/a=rtpmap:[0-9]+ rtx/);
    var relevantSdpLines = flatMap(relevantTrackAttributes, function(trackAttributes) {
        return trackAttributes.toSdpLines(excludeRtx);
    });
    // Add the simulcast SSRC SDP lines to the media section. The Set ensures
    // that the duplicates of the SSRC SDP lines that are in both "section" and
    // "relevantSdpLines" are removed.
    var sectionLines = flatMap(new Set(section.split('\r\n').concat(relevantSdpLines)));
    var xGoogleFlagConference = 'a=x-google-flag:conference';
    if (!section.match(xGoogleFlagConference)) {
        sectionLines.push(xGoogleFlagConference);
    }
    return sectionLines.join('\r\n');
}
/**
 * String representing a MediaStream ID.
 * @typedef {string} MediaStreamID
 */ /**
 * String representing the SSRC of a MediaStreamTrack.
 * @typedef {string} SSRC
 */ /**
 * Primary SSRC.
 * @typedef {SSRC} PrimarySSRC
 */ /**
 * Retransmission SSRC.
 * @typedef {SSRC} RtxSSRC
 */ /**
 * Simulcast SSRC.
 * @typedef {SSRC} SimSSRC
 */ module.exports = setSimulcastInMediaSection; //# sourceMappingURL=simulcast.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/sdp/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)"), difference = _a.difference, flatMap = _a.flatMap;
var setSimulcastInMediaSection = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/simulcast.js [app-client] (ecmascript)");
var ptToFixedBitrateAudioCodecName = {
    0: 'PCMU',
    8: 'PCMA'
};
/**
 * A payload type
 * @typedef {number} PT
 */ /**
 * An {@link AudioCodec} or {@link VideoCodec}
 * @typedef {AudioCodec|VideoCodec} Codec
 */ /**
 * Create a Codec Map for the given m= section.
 * @param {string} section - The given m= section
 * @returns {Map<Codec, Array<PT>>}
 */ function createCodecMapForMediaSection(section) {
    return Array.from(createPtToCodecName(section)).reduce(function(codecMap, pair) {
        var pt = pair[0];
        var codecName = pair[1];
        var pts = codecMap.get(codecName) || [];
        return codecMap.set(codecName, pts.concat(pt));
    }, new Map());
}
/**
 * Create a Map of MIDs to m= sections for the given SDP.
 * @param {string} sdp
 * @returns {Map<string, string>}
 */ function createMidToMediaSectionMap(sdp) {
    return getMediaSections(sdp).reduce(function(midsToMediaSections, mediaSection) {
        var mid = getMidForMediaSection(mediaSection);
        return mid ? midsToMediaSections.set(mid, mediaSection) : midsToMediaSections;
    }, new Map());
}
/**
 * Create a Map from PTs to codec names for the given m= section.
 * @param {string} mediaSection - The given m= section.
 * @returns {Map<PT, Codec>} ptToCodecName
 */ function createPtToCodecName(mediaSection) {
    return getPayloadTypesInMediaSection(mediaSection).reduce(function(ptToCodecName, pt) {
        var rtpmapPattern = new RegExp("a=rtpmap:" + pt + " ([^/]+)");
        var matches = mediaSection.match(rtpmapPattern);
        var codecName = matches ? matches[1].toLowerCase() : ptToFixedBitrateAudioCodecName[pt] ? ptToFixedBitrateAudioCodecName[pt].toLowerCase() : '';
        return ptToCodecName.set(pt, codecName);
    }, new Map());
}
/**
 * Get the associated fmtp attributes for the given Payload Type in an m= section.
 * @param {PT} pt
 * @param {string} mediaSection
 * @returns {?object}
 */ function getFmtpAttributesForPt(pt, mediaSection) {
    // In "a=fmtp:<pt> <name>=<value>[;<name>=<value>]*", the regex matches the codec
    // profile parameters expressed as name/value pairs separated by ";".
    var fmtpRegex = new RegExp("^a=fmtp:" + pt + " (.+)$", 'm');
    var matches = mediaSection.match(fmtpRegex);
    return matches && matches[1].split(';').reduce(function(attrs, nvPair) {
        var _a = __read(nvPair.split('='), 2), name = _a[0], value = _a[1];
        attrs[name] = isNaN(value) ? value : parseInt(value, 10);
        return attrs;
    }, {});
}
/**
 * Get the MID for the given m= section.
 * @param {string} mediaSection
 * @return {?string}
 */ function getMidForMediaSection(mediaSection) {
    // In "a=mid:<mid>", the regex matches <mid>.
    var midMatches = mediaSection.match(/^a=mid:(.+)$/m);
    return midMatches && midMatches[1];
}
/**
 * Get the m= sections of a particular kind and direction from an sdp.
 * @param {string} sdp - SDP string
 * @param {string} [kind] - Pattern for matching kind
 * @param {string} [direction] - Pattern for matching direction
 * @returns {Array<string>} mediaSections
 */ function getMediaSections(sdp, kind, direction) {
    return sdp.replace(/\r\n\r\n$/, '\r\n').split('\r\nm=').slice(1).map(function(mediaSection) {
        return "m=" + mediaSection;
    }).filter(function(mediaSection) {
        var kindPattern = new RegExp("m=" + (kind || '.*'), 'gm');
        var directionPattern = new RegExp("a=" + (direction || '.*'), 'gm');
        return kindPattern.test(mediaSection) && directionPattern.test(mediaSection);
    });
}
/**
 * Get the Codec Payload Types present in the first line of the given m= section
 * @param {string} section - The m= section
 * @returns {Array<PT>} Payload Types
 */ function getPayloadTypesInMediaSection(section) {
    var mLine = section.split('\r\n')[0];
    // In "m=<kind> <port> <proto> <payload_type_1> <payload_type_2> ... <payload_type_n>",
    // the regex matches <port> and the Payload Types.
    var matches = mLine.match(/([0-9]+)/g);
    // This should not happen, but in case there are no Payload Types in
    // the m= line, return an empty array.
    if (!matches) {
        return [];
    }
    // Since only the Payload Types are needed, we discard the <port>.
    return matches.slice(1).map(function(match) {
        return parseInt(match, 10);
    });
}
/**
 * Create the reordered Codec Payload Types based on the preferred Codec Names.
 * @param {Map<Codec, Array<PT>>} codecMap - Codec Map
 * @param {Array<AudioCodecSettings|VideoCodecSettings>} preferredCodecs - Preferred Codecs
 * @returns {Array<PT>} Reordered Payload Types
 */ function getReorderedPayloadTypes(codecMap, preferredCodecs) {
    preferredCodecs = preferredCodecs.map(function(_a) {
        var codec = _a.codec;
        return codec.toLowerCase();
    });
    var preferredPayloadTypes = flatMap(preferredCodecs, function(codecName) {
        return codecMap.get(codecName) || [];
    });
    var remainingCodecs = difference(Array.from(codecMap.keys()), preferredCodecs);
    var remainingPayloadTypes = flatMap(remainingCodecs, function(codecName) {
        return codecMap.get(codecName);
    });
    return preferredPayloadTypes.concat(remainingPayloadTypes);
}
/**
 * Set the given Codec Payload Types in the first line of the given m= section.
 * @param {Array<PT>} payloadTypes - Payload Types
 * @param {string} section - Given m= section
 * @returns {string} - Updated m= section
 */ function setPayloadTypesInMediaSection(payloadTypes, section) {
    var lines = section.split('\r\n');
    var mLine = lines[0];
    var otherLines = lines.slice(1);
    mLine = mLine.replace(/([0-9]+\s?)+$/, payloadTypes.join(' '));
    return [
        mLine
    ].concat(otherLines).join('\r\n');
}
/**
 * Return a new SDP string with the re-ordered codec preferences.
 * @param {string} sdp
 * @param {Array<AudioCodec>} preferredAudioCodecs - If empty, the existing order
 *   of audio codecs is preserved
 * @param {Array<VideoCodecSettings>} preferredVideoCodecs - If empty, the
 *   existing order of video codecs is preserved
 * @returns {string} Updated SDP string
 */ function setCodecPreferences(sdp, preferredAudioCodecs, preferredVideoCodecs) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(function(section) {
        // Codec preferences should not be applied to m=application sections.
        if (!/^m=(audio|video)/.test(section)) {
            return section;
        }
        var kind = section.match(/^m=(audio|video)/)[1];
        var codecMap = createCodecMapForMediaSection(section);
        var preferredCodecs = kind === 'audio' ? preferredAudioCodecs : preferredVideoCodecs;
        var payloadTypes = getReorderedPayloadTypes(codecMap, preferredCodecs);
        var newSection = setPayloadTypesInMediaSection(payloadTypes, section);
        var pcmaPayloadTypes = codecMap.get('pcma') || [];
        var pcmuPayloadTypes = codecMap.get('pcmu') || [];
        var fixedBitratePayloadTypes = kind === 'audio' ? new Set(pcmaPayloadTypes.concat(pcmuPayloadTypes)) : new Set();
        return fixedBitratePayloadTypes.has(payloadTypes[0]) ? newSection.replace(/\r\nb=(AS|TIAS):([0-9]+)/g, '') : newSection;
    })).join('\r\n');
}
/**
 * Return a new SDP string with simulcast settings.
 * @param {string} sdp
 * @param {Map<Track.ID, TrackAttributes>} trackIdsToAttributes
 * @returns {string} Updated SDP string
 */ function setSimulcast(sdp, trackIdsToAttributes) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(function(section) {
        section = section.replace(/\r\n$/, '');
        if (!/^m=video/.test(section)) {
            return section;
        }
        var codecMap = createCodecMapForMediaSection(section);
        var payloadTypes = getPayloadTypesInMediaSection(section);
        var vp8PayloadTypes = new Set(codecMap.get('vp8') || []);
        var hasVP8PayloadType = payloadTypes.some(function(payloadType) {
            return vp8PayloadTypes.has(payloadType);
        });
        return hasVP8PayloadType ? setSimulcastInMediaSection(section, trackIdsToAttributes) : section;
    })).concat('').join('\r\n');
}
/**
 * Get the matching Payload Types in an m= section for a particular peer codec.
 * @param {Codec} peerCodec
 * @param {PT} peerPt
 * @param {Map<Codec, PT>} codecsToPts
 * @param {string} section
 * @param {string} peerSection
 * @returns {Array<PT>}
 */ function getMatchingPayloadTypes(peerCodec, peerPt, codecsToPts, section, peerSection) {
    // If there is at most one local Payload Type that matches the remote codec, retain it.
    var matchingPts = codecsToPts.get(peerCodec) || [];
    if (matchingPts.length <= 1) {
        return matchingPts;
    }
    // If there are no fmtp attributes for the codec in the peer m= section, then we
    // cannot get a match in the  m= section. In that case, retain all matching Payload
    // Types.
    var peerFmtpAttrs = getFmtpAttributesForPt(peerPt, peerSection);
    if (!peerFmtpAttrs) {
        return matchingPts;
    }
    // Among the matched local Payload Types, find the one that matches the remote
    // fmtp attributes.
    var matchingPt = matchingPts.find(function(pt) {
        var fmtpAttrs = getFmtpAttributesForPt(pt, section);
        return fmtpAttrs && Object.keys(peerFmtpAttrs).every(function(attr) {
            return peerFmtpAttrs[attr] === fmtpAttrs[attr];
        });
    });
    // If none of the matched Payload Types also have matching fmtp attributes,
    // then retain all of them, otherwise retain only the Payload Type that
    // matches the peer fmtp attributes.
    return typeof matchingPt === 'number' ? [
        matchingPt
    ] : matchingPts;
}
/**
 * Filter codecs in an m= section based on its peer m= section from the other peer.
 * @param {string} section
 * @param {Map<string, string>} peerMidsToMediaSections
 * @param {Array<string>} codecsToRemove
 * @returns {string}
 */ function filterCodecsInMediaSection(section, peerMidsToMediaSections, codecsToRemove) {
    // Do nothing if the m= section represents neither audio nor video.
    if (!/^m=(audio|video)/.test(section)) {
        return section;
    }
    // Do nothing if the m= section does not have an equivalent remote m= section.
    var mid = getMidForMediaSection(section);
    var peerSection = mid && peerMidsToMediaSections.get(mid);
    if (!peerSection) {
        return section;
    }
    // Construct a Map of the peer Payload Types to their codec names.
    var peerPtToCodecs = createPtToCodecName(peerSection);
    // Construct a Map of the codec names to their Payload Types.
    var codecsToPts = createCodecMapForMediaSection(section);
    // Maintain a list of non-rtx Payload Types to retain.
    var pts = flatMap(Array.from(peerPtToCodecs), function(_a) {
        var _b = __read(_a, 2), peerPt = _b[0], peerCodec = _b[1];
        return peerCodec !== 'rtx' && !codecsToRemove.includes(peerCodec) ? getMatchingPayloadTypes(peerCodec, peerPt, codecsToPts, section, peerSection) : [];
    });
    // For each Payload Type that will be retained, retain their corresponding rtx
    // Payload Type if present.
    var rtxPts = codecsToPts.get('rtx') || [];
    // In "a=fmtp:<rtxPt> apt=<apt>", extract the codec PT <apt> associated with rtxPt.
    pts = pts.concat(rtxPts.filter(function(rtxPt) {
        var fmtpAttrs = getFmtpAttributesForPt(rtxPt, section);
        return fmtpAttrs && pts.includes(fmtpAttrs.apt);
    }));
    // Filter out the below mentioned attribute lines in the m= section that do not
    // belong to one of the Payload Types that are to be retained.
    // 1. "a=rtpmap:<pt> <codec>"
    // 2. "a=rtcp-fb:<pt> <attr>[ <attr>]*"
    // 3. "a=fmtp:<pt> <name>=<value>[;<name>=<value>]*"
    var lines = section.split('\r\n').filter(function(line) {
        var ptMatches = line.match(/^a=(rtpmap|fmtp|rtcp-fb):(.+) .+$/);
        var pt = ptMatches && ptMatches[2];
        return !ptMatches || pt && pts.includes(parseInt(pt, 10));
    });
    // Filter the list of Payload Types in the first line of the m= section.
    var orderedPts = getPayloadTypesInMediaSection(section).filter(function(pt) {
        return pts.includes(pt);
    });
    return setPayloadTypesInMediaSection(orderedPts, lines.join('\r\n'));
}
/**
 * Filter local codecs based on the remote SDP.
 * @param {string} localSdp
 * @param {string} remoteSdp
 * @returns {string} - Updated local SDP
 */ function filterLocalCodecs(localSdp, remoteSdp) {
    var localMediaSections = getMediaSections(localSdp);
    var localSession = localSdp.split('\r\nm=')[0];
    var remoteMidsToMediaSections = createMidToMediaSectionMap(remoteSdp);
    return [
        localSession
    ].concat(localMediaSections.map(function(localSection) {
        return filterCodecsInMediaSection(localSection, remoteMidsToMediaSections, []);
    })).join('\r\n');
}
/**
 * Return a new SDP string after reverting simulcast for non vp8 sections in remote sdp.
 * @param localSdp - simulcast enabled local sdp
 * @param localSdpWithoutSimulcast - local sdp before simulcast was set
 * @param remoteSdp - remote sdp
 * @param revertForAll - when true simulcast will be reverted for all codecs. when false it will be reverted
 *  only for non-vp8 codecs.
 * @return {string} Updated SDP string
 */ function revertSimulcast(localSdp, localSdpWithoutSimulcast, remoteSdp, revertForAll) {
    if (revertForAll === void 0) {
        revertForAll = false;
    }
    var remoteMidToMediaSections = createMidToMediaSectionMap(remoteSdp);
    var localMidToMediaSectionsWithoutSimulcast = createMidToMediaSectionMap(localSdpWithoutSimulcast);
    var mediaSections = getMediaSections(localSdp);
    var session = localSdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(function(section) {
        section = section.replace(/\r\n$/, '');
        if (!/^m=video/.test(section)) {
            return section;
        }
        var midMatches = section.match(/^a=mid:(.+)$/m);
        var mid = midMatches && midMatches[1];
        if (!mid) {
            return section;
        }
        var remoteSection = remoteMidToMediaSections.get(mid);
        var remotePtToCodecs = createPtToCodecName(remoteSection);
        var remotePayloadTypes = getPayloadTypesInMediaSection(remoteSection);
        var isVP8ThePreferredCodec = remotePayloadTypes.length && remotePtToCodecs.get(remotePayloadTypes[0]) === 'vp8';
        var shouldRevertSimulcast = revertForAll || !isVP8ThePreferredCodec;
        return shouldRevertSimulcast ? localMidToMediaSectionsWithoutSimulcast.get(mid).replace(/\r\n$/, '') : section;
    })).concat('').join('\r\n');
}
/**
 * Add or rewrite MSIDs for new m= sections in the given SDP with their corresponding
 * local MediaStreamTrack IDs. These can be different when previously removed MediaStreamTracks
 * are added back (or Track IDs may not be present in the SDPs at all once browsers implement
 * the latest WebRTC spec).
 * @param {string} sdp
 * @param {Map<string, Track.ID>} activeMidsToTrackIds
 * @param {Map<Track.Kind, Array<Track.ID>>} trackIdsByKind
 * @returns {string}
 */ function addOrRewriteNewTrackIds(sdp, activeMidsToTrackIds, trackIdsByKind) {
    // NOTE(mmalavalli): The m= sections for the new MediaStreamTracks are usually
    // present after the m= sections for the existing MediaStreamTracks, in order
    // of addition.
    var newMidsToTrackIds = Array.from(trackIdsByKind).reduce(function(midsToTrackIds, _a) {
        var _b = __read(_a, 2), kind = _b[0], trackIds = _b[1];
        var mediaSections = getMediaSections(sdp, kind, 'send(only|recv)');
        var newMids = mediaSections.map(getMidForMediaSection).filter(function(mid) {
            return !activeMidsToTrackIds.has(mid);
        });
        newMids.forEach(function(mid, i) {
            return midsToTrackIds.set(mid, trackIds[i]);
        });
        return midsToTrackIds;
    }, new Map());
    return addOrRewriteTrackIds(sdp, newMidsToTrackIds);
}
/**
 * Add or rewrite MSIDs in the given SDP with their corresponding local MediaStreamTrack IDs.
 * These IDs need not be the same (or Track IDs may not be present in the SDPs at all once
 * browsers implement the latest WebRTC spec).
 * @param {string} sdp
 * @param {Map<string, Track.ID>} midsToTrackIds
 * @returns {string}
 */ function addOrRewriteTrackIds(sdp, midsToTrackIds) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(function(mediaSection) {
        // Do nothing if the m= section represents neither audio nor video.
        if (!/^m=(audio|video)/.test(mediaSection)) {
            return mediaSection;
        }
        // This shouldn't happen, but in case there is no MID for the m= section, do nothing.
        var mid = getMidForMediaSection(mediaSection);
        if (!mid) {
            return mediaSection;
        }
        // In case there is no Track ID for the given MID in the map, do nothing.
        var trackId = midsToTrackIds.get(mid);
        if (!trackId) {
            return mediaSection;
        }
        // This shouldn't happen, but in case there is no a=msid: line, do nothing.
        var attributes = (mediaSection.match(/^a=msid:(.+)$/m) || [])[1];
        if (!attributes) {
            return mediaSection;
        }
        // If the a=msid: line contains the "appdata" field, then replace it with the Track ID,
        // otherwise append the Track ID.
        var _a = __read(attributes.split(' '), 2), msid = _a[0], trackIdToRewrite = _a[1];
        var msidRegex = new RegExp("msid:" + msid + (trackIdToRewrite ? " " + trackIdToRewrite : '') + "$", 'gm');
        return mediaSection.replace(msidRegex, "msid:" + msid + " " + trackId);
    })).join('\r\n');
}
/**
 * Removes specified ssrc attributes from given sdp.
 * @param {string} sdp
 * @param {Array<string>} ssrcAttributesToRemove
 * @returns {string}
 */ function removeSSRCAttributes(sdp, ssrcAttributesToRemove) {
    return sdp.split('\r\n').filter(function(line) {
        return !ssrcAttributesToRemove.find(function(srcAttribute) {
            return new RegExp('a=ssrc:.*' + srcAttribute + ':', 'g').test(line);
        });
    }).join('\r\n');
}
/**
 * Disable RTX in a given sdp.
 * @param {string} sdp
 * @returns {string} sdp without RTX
 */ function disableRtx(sdp) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(function(mediaSection) {
        // Do nothing if the m= section does not represent a video track.
        if (!/^m=video/.test(mediaSection)) {
            return mediaSection;
        }
        // Create a map of codecs to payload types.
        var codecsToPts = createCodecMapForMediaSection(mediaSection);
        // Get the RTX payload types.
        var rtxPts = codecsToPts.get('rtx');
        // Do nothing if there are no RTX payload types.
        if (!rtxPts) {
            return mediaSection;
        }
        // Remove the RTX payload types.
        var pts = new Set(getPayloadTypesInMediaSection(mediaSection));
        rtxPts.forEach(function(rtxPt) {
            return pts.delete(rtxPt);
        });
        // Get the RTX SSRC.
        var rtxSSRCMatches = mediaSection.match(/a=ssrc-group:FID [0-9]+ ([0-9]+)/);
        var rtxSSRC = rtxSSRCMatches && rtxSSRCMatches[1];
        // Remove the following lines associated with the RTX payload types:
        // 1. "a=fmtp:<rtxPt> apt=<pt>"
        // 2. "a=rtpmap:<rtxPt> rtx/..."
        // 3. "a=ssrc:<rtxSSRC> cname:..."
        // 4. "a=ssrc-group:FID <SSRC> <rtxSSRC>"
        var filterRegexes = [
            /^a=fmtp:.+ apt=.+$/,
            /^a=rtpmap:.+ rtx\/.+$/,
            /^a=ssrc-group:.+$/
        ].concat(rtxSSRC ? [
            new RegExp("^a=ssrc:" + rtxSSRC + " .+$")
        ] : []);
        mediaSection = mediaSection.split('\r\n').filter(function(line) {
            return filterRegexes.every(function(regex) {
                return !regex.test(line);
            });
        }).join('\r\n');
        // Reconstruct the m= section without the RTX payload types.
        return setPayloadTypesInMediaSection(Array.from(pts), mediaSection);
    })).join('\r\n');
}
/**
 * Generate an a=fmtp: line from the given payload type and attributes.
 * @param {PT} pt
 * @param {*} fmtpAttrs
 * @returns {string}
 */ function generateFmtpLineFromPtAndAttributes(pt, fmtpAttrs) {
    var serializedFmtpAttrs = Object.entries(fmtpAttrs).map(function(_a) {
        var _b = __read(_a, 2), name = _b[0], value = _b[1];
        return name + "=" + value;
    }).join(';');
    return "a=fmtp:" + pt + " " + serializedFmtpAttrs;
}
/**
 * Enable DTX for opus in the m= sections for the given MIDs.`
 * @param {string} sdp
 * @param {Array<string>} [mids] - If not specified, enables opus DTX for all
 *   audio m= lines.
 * @returns {string}
 */ function enableDtxForOpus(sdp, mids) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    mids = mids || mediaSections.filter(function(section) {
        return /^m=audio/.test(section);
    }).map(getMidForMediaSection);
    return [
        session
    ].concat(mediaSections.map(function(section) {
        // Do nothing if the m= section is not audio.
        if (!/^m=audio/.test(section)) {
            return section;
        }
        // Build a map codecs to payload types.
        var codecsToPts = createCodecMapForMediaSection(section);
        // Do nothing if a payload type for opus does not exist.
        var opusPt = codecsToPts.get('opus');
        if (!opusPt) {
            return section;
        }
        // If no fmtp attributes are found for opus, do nothing.
        var opusFmtpAttrs = getFmtpAttributesForPt(opusPt, section);
        if (!opusFmtpAttrs) {
            return section;
        }
        // Add usedtx=1 to the a=fmtp: line for opus.
        var origOpusFmtpLine = generateFmtpLineFromPtAndAttributes(opusPt, opusFmtpAttrs);
        var origOpusFmtpRegex = new RegExp(origOpusFmtpLine);
        // If the m= section's MID is in the list of MIDs, then enable dtx. Otherwise disable it.
        var mid = getMidForMediaSection(section);
        if (mids.includes(mid)) {
            opusFmtpAttrs.usedtx = 1;
        } else {
            delete opusFmtpAttrs.usedtx;
        }
        var opusFmtpLineWithDtx = generateFmtpLineFromPtAndAttributes(opusPt, opusFmtpAttrs);
        return section.replace(origOpusFmtpRegex, opusFmtpLineWithDtx);
    })).join('\r\n');
}
exports.addOrRewriteNewTrackIds = addOrRewriteNewTrackIds;
exports.addOrRewriteTrackIds = addOrRewriteTrackIds;
exports.createCodecMapForMediaSection = createCodecMapForMediaSection;
exports.createPtToCodecName = createPtToCodecName;
exports.disableRtx = disableRtx;
exports.enableDtxForOpus = enableDtxForOpus;
exports.filterLocalCodecs = filterLocalCodecs;
exports.getMediaSections = getMediaSections;
exports.removeSSRCAttributes = removeSSRCAttributes;
exports.revertSimulcast = revertSimulcast;
exports.setCodecPreferences = setCodecPreferences;
exports.setSimulcast = setSimulcast; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/filter.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var Filter = function() {
    function Filter(options) {
        options = Object.assign({
            getKey: function defaultGetKey(a) {
                return a;
            },
            getValue: function defaultGetValue(a) {
                return a;
            },
            isLessThanOrEqualTo: function defaultIsLessThanOrEqualTo(a, b) {
                return a <= b;
            }
        }, options);
        Object.defineProperties(this, {
            _getKey: {
                value: options.getKey
            },
            _getValue: {
                value: options.getValue
            },
            _isLessThanOrEqualTo: {
                value: options.isLessThanOrEqualTo
            },
            _map: {
                value: new Map()
            }
        });
    }
    Filter.prototype.toMap = function() {
        return new Map(this._map);
    };
    Filter.prototype.updateAndFilter = function(entries) {
        return entries.filter(this.update, this);
    };
    Filter.prototype.update = function(entry) {
        var key = this._getKey(entry);
        var value = this._getValue(entry);
        if (this._map.has(key) && this._isLessThanOrEqualTo(value, this._map.get(key))) {
            return false;
        }
        this._map.set(key, value);
        return true;
    };
    return Filter;
}();
module.exports = Filter; //# sourceMappingURL=filter.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/sdp/trackmatcher.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var getMediaSections = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/index.js [app-client] (ecmascript)").getMediaSections;
/**
 * An {@link TrackMatcher} matches an RTCTrackEvent with a MediaStreamTrack
 * ID based on the MID of the underlying RTCRtpTransceiver.
 */ var TrackMatcher = function() {
    /**
     * Construct an {@link TrackMatcher}.
     */ function TrackMatcher() {
        Object.defineProperties(this, {
            _midsToTrackIds: {
                value: new Map(),
                writable: true
            }
        });
    }
    /**
     * Match a given MediaStreamTrack with its ID.
     * @param {RTCTrackEvent} event
     * @returns {?Track.ID}
     */ TrackMatcher.prototype.match = function(event) {
        return this._midsToTrackIds.get(event.transceiver.mid) || null;
    };
    /**
     * Update the {@link TrackMatcher} with a new SDP.
     * @param {string} sdp
     */ TrackMatcher.prototype.update = function(sdp) {
        var sections = getMediaSections(sdp, '(audio|video)');
        this._midsToTrackIds = sections.reduce(function(midsToTrackIds, section) {
            var midMatches = section.match(/^a=mid:(.+)$/m) || [];
            var trackIdMatches = section.match(/^a=msid:.+ (.+)$/m) || [];
            var mid = midMatches[1];
            var trackId = trackIdMatches[1];
            return mid && trackId ? midsToTrackIds.set(mid, trackId) : midsToTrackIds;
        }, this._midsToTrackIds);
    };
    return TrackMatcher;
}();
module.exports = TrackMatcher; //# sourceMappingURL=trackmatcher.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/sdp/issue8329.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var RTCSessionDescription = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)").RTCSessionDescription;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/sdp/index.js [app-client] (ecmascript)"), createPtToCodecName = _a.createPtToCodecName, getMediaSections = _a.getMediaSections;
/**
 * An RTX payload type
 * @typedef {PT} RtxPT
 */ /**
 * A non-RTX payload type
 * @typedef {PT} NonRtxPT
 */ /**
 * A Set with at least one element
 * @typedef {Set} NonEmptySet
 */ /**
 * Apply the workaround for Issue 8329 to an RTCSessionDescriptionInit.
 * @param {RTCSessionDescriptionInit} description
 * @returns {RTCSessionDescription} newDescription
 */ function workaround(description) {
    var descriptionInit = {
        type: description.type
    };
    if (description.type !== 'rollback') {
        descriptionInit.sdp = sdpWorkaround(description.sdp);
    }
    return new RTCSessionDescription(descriptionInit);
}
/**
 * @param {string} sdp
 * @returns {string} newSdp
 */ function sdpWorkaround(sdp) {
    var mediaSections = getMediaSections(sdp);
    var session = sdp.split('\r\nm=')[0];
    return [
        session
    ].concat(mediaSections.map(mediaSectionWorkaround)).join('\r\n');
}
/**
 * @param {string} mediaSection
 * @returns {string} newMediaSection
 */ function mediaSectionWorkaround(mediaSection) {
    var ptToCodecName = createPtToCodecName(mediaSection);
    mediaSection = deleteDuplicateRtxPts(mediaSection, ptToCodecName);
    var codecNameToPts = createCodecNameToPts(ptToCodecName);
    var rtxPts = codecNameToPts.get('rtx') || new Set();
    var invalidRtxPts = new Set();
    var rtxPtToAssociatedPt = createRtxPtToAssociatedPt(mediaSection, ptToCodecName, rtxPts, invalidRtxPts);
    var associatedPtToRtxPt = createAssociatedPtToRtxPt(rtxPtToAssociatedPt, invalidRtxPts);
    var unassociatedRtxPts = Array.from(invalidRtxPts);
    // NOTE(mroberts): We normalize to lowercase.
    var knownCodecNames = [
        'h264',
        'vp8',
        'vp9'
    ];
    var unassociatedPts = knownCodecNames.reduce(function(unassociatedPts, codecName) {
        var pts = codecNameToPts.get(codecName) || new Set();
        return Array.from(pts).reduce(function(unassociatedPts, pt) {
            return associatedPtToRtxPt.has(pt) ? unassociatedPts : unassociatedPts.add(pt);
        }, unassociatedPts);
    }, new Set());
    unassociatedPts.forEach(function(pt) {
        if (unassociatedRtxPts.length) {
            var rtxPt = unassociatedRtxPts.shift();
            mediaSection = deleteFmtpAttributesForRtxPt(mediaSection, rtxPt);
            mediaSection = addFmtpAttributeForRtxPt(mediaSection, rtxPt, pt);
        }
    });
    unassociatedRtxPts.forEach(function(rtxPt) {
        mediaSection = deleteFmtpAttributesForRtxPt(mediaSection, rtxPt);
        mediaSection = deleteRtpmapAttributesForRtxPt(mediaSection, rtxPt);
    });
    return mediaSection;
}
/**
 * @param {string} mediaSection
 * @param {Map<PT, Codec>} ptToCodecName
 * @returns {string} newMediaSection
 */ function deleteDuplicateRtxPts(mediaSection, ptToCodecName) {
    // NOTE(syerrapragada): In some cases Chrome produces an offer/answer
    // with duplicate "rtx" payload mapping in media section. When applied,
    // Chrome rejects the SDP. We workaround this by deleting duplicate
    // "rtx" mappings found in SDP.
    return Array.from(ptToCodecName.keys()).reduce(function(section, pt) {
        var rtpmapRegex = new RegExp("^a=rtpmap:" + pt + " rtx.+$", 'gm');
        return (section.match(rtpmapRegex) || []).slice(ptToCodecName.get(pt) === 'rtx' ? 1 : 0).reduce(function(section, rtpmap) {
            var rtpmapRegex = new RegExp("\r\n" + rtpmap);
            var fmtpmapRegex = new RegExp("\r\na=fmtp:" + pt + " apt=[0-9]+");
            return section.replace(rtpmapRegex, '').replace(fmtpmapRegex, '');
        }, section);
    }, mediaSection);
}
/**
 * @param {Map<PT, Codec>} ptToCodecName
 * @returns {Map<string, NonEmptySet<PT>>} codecNameToPts
 */ function createCodecNameToPts(ptToCodecName) {
    var codecNameToPts = new Map();
    ptToCodecName.forEach(function(codecName, pt) {
        var pts = codecNameToPts.get(codecName) || new Set();
        return codecNameToPts.set(codecName, pts.add(pt));
    });
    return codecNameToPts;
}
/**
 * @param {string} mediaSection
 * @param {Map<PT, Codec>} ptToCodecName
 * @param {Set<RtxPT>} rtxPts
 * @param {Set<RtxPT>} invalidRtxPts
 * @returns {Map<RtxPT, NonRtxPT>} rtxPtToAssociatedPt
 */ function createRtxPtToAssociatedPt(mediaSection, ptToCodecName, rtxPts, invalidRtxPts) {
    return Array.from(rtxPts).reduce(function(rtxPtToAssociatedPt, rtxPt) {
        var fmtpPattern = new RegExp("a=fmtp:" + rtxPt + " apt=(\\d+)");
        var matches = mediaSection.match(fmtpPattern);
        if (!matches) {
            invalidRtxPts.add(rtxPt);
            return rtxPtToAssociatedPt;
        }
        var pt = Number.parseInt(matches[1]);
        if (!ptToCodecName.has(pt)) {
            // This is Issue 8329.
            invalidRtxPts.add(rtxPt);
            return rtxPtToAssociatedPt;
        }
        var codecName = ptToCodecName.get(pt);
        if (codecName === 'rtx') {
            // Strange
            invalidRtxPts.add(rtxPt);
            return rtxPtToAssociatedPt;
        }
        return rtxPtToAssociatedPt.set(rtxPt, pt);
    }, new Map());
}
/**
 * @param {string} mediaSection
 * @param {Map<RtxPT, NonRtxPT>} rtxPtToAssociatedPt
 * @param {Set<RtxPT>} invalidRtxPts
 * @returns {Map<NonRtxPT, RtxPT>} associatedPtToRtxPt
 */ function createAssociatedPtToRtxPt(rtxPtToAssociatedPt, invalidRtxPts) {
    // First, we construct a Map<NonRtxPT, NonEmptySet<RtxPT>>.
    var associatedPtToRtxPts = Array.from(rtxPtToAssociatedPt).reduce(function(associatedPtToRtxPts, pair) {
        var rtxPt = pair[0];
        var pt = pair[1];
        var rtxPts = associatedPtToRtxPts.get(pt) || new Set();
        return associatedPtToRtxPts.set(pt, rtxPts.add(rtxPt));
    }, new Map());
    // Then, we filter down to a Map<NonRtxPT, RtxPt>. Any RtxPTs that map to the
    // same NonRtxPT are removed and added to invalidRtxPts.
    return Array.from(associatedPtToRtxPts).reduce(function(associatedPtToRtxPt, pair) {
        var pt = pair[0];
        var rtxPts = Array.from(pair[1]);
        if (rtxPts.length > 1) {
            rtxPts.forEach(function(rtxPt) {
                invalidRtxPts.add(rtxPt);
            });
            return associatedPtToRtxPt;
        }
        return associatedPtToRtxPt.set(pt, rtxPts[0]);
    }, new Map());
}
/**
 * @param {string} mediaSection
 * @param {RtxPT} rtxPt
 * @returns {string} newMediaSection
 */ function deleteFmtpAttributesForRtxPt(mediaSection, rtxPt) {
    var pattern = new RegExp("a=fmtp:" + rtxPt + ".*\r\n", 'gm');
    return mediaSection.replace(pattern, '');
}
/**
 * @param {string} mediaSection
 * @param {RtxPT} rtxPt
 * @returns {string} newMediaSection
 */ function deleteRtpmapAttributesForRtxPt(mediaSection, rtxPt) {
    var pattern = new RegExp("a=rtpmap:" + rtxPt + ".*\r\n", 'gm');
    return mediaSection.replace(pattern, '');
}
/**
 * @param {string} mediaSection
 * @param {RtxPT} rtxPt
 * @param {NonRtxPT} pt
 * @returns {string} newMediaSection
 */ function addFmtpAttributeForRtxPt(mediaSection, rtxPt, pt) {
    return mediaSection.endsWith('\r\n') ? mediaSection + "a=fmtp:" + rtxPt + " apt=" + pt + "\r\n" : mediaSection + "\r\na=fmtp:" + rtxPt + " apt=" + pt;
}
module.exports = workaround; //# sourceMappingURL=issue8329.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/asyncvar.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var defer = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/index.js [app-client] (ecmascript)").defer;
/**
 * An {@link AsyncVar} is an "asynchronous variable" which may or may not
 * contain a value of some type T. You can put a value into the {@link AsyncVar}
 * with {@link AsyncVar#put}. Callers can take a value out of the
 * {@link AsyncVar} by queueing up with {@link AsyncVar#take}. N calls to
 * {@link AsyncVar#take} require N calls to {@link AsyncVar#put} to resolve, and
 * they resolve in order.
 */ var AsyncVar = function() {
    /**
     * Construct an {@link AsyncVar}.
     */ function AsyncVar() {
        Object.defineProperties(this, {
            _deferreds: {
                value: []
            },
            _hasValue: {
                value: false,
                writable: true
            },
            _value: {
                value: null,
                writable: true
            }
        });
    }
    /**
     * Put a value into the {@link AsyncVar}.
     * @param {T} value
     * @returns {this}
     */ AsyncVar.prototype.put = function(value) {
        this._hasValue = true;
        this._value = value;
        var deferred = this._deferreds.shift();
        if (deferred) {
            deferred.resolve(value);
        }
        return this;
    };
    /**
     * Take the value out of the {@link AsyncVar}.
     * @returns {Promise<T>}
     */ AsyncVar.prototype.take = function() {
        var _this = this;
        if (this._hasValue && !this._deferreds.length) {
            this._hasValue = false;
            return Promise.resolve(this._value);
        }
        var deferred = defer();
        this._deferreds.push(deferred);
        return deferred.promise.then(function(value) {
            _this._hasValue = false;
            return value;
        });
    };
    return AsyncVar;
}();
module.exports = AsyncVar; //# sourceMappingURL=asyncvar.js.map
}}),
"[project]/node_modules/twilio-video/es5/util/support.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), guessBrowser = _a.guessBrowser, isWebRTCSupported = _a.support;
var getSdpFormat = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)").getSdpFormat;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/util/browserdetection.js [app-client] (ecmascript)"), isAndroid = _b.isAndroid, isMobile = _b.isMobile, isNonChromiumEdge = _b.isNonChromiumEdge, rebrandedChromeBrowser = _b.rebrandedChromeBrowser, mobileWebKitBrowser = _b.mobileWebKitBrowser;
var SUPPORTED_CHROME_BASED_BROWSERS = [
    'crios',
    'edg',
    'edge',
    'electron',
    'headlesschrome'
];
var SUPPORTED_ANDROID_BROWSERS = [
    'chrome',
    'firefox'
];
var SUPPORTED_IOS_BROWSERS = [
    'chrome',
    'safari'
];
// Currently none. Add 'brave', 'edg', and 'edge' here once we start supporting them
var SUPPORTED_MOBILE_WEBKIT_BASED_BROWSERS = [];
/**
 * Check if the current browser is officially supported by twilio-video.js.
 * @returns {boolean}
 */ function isSupported() {
    var browser = guessBrowser();
    // NOTE (csantos): Return right away if there is no browser detected
    // to prevent unnecessary checks which could lead to errors
    if (!browser) {
        return false;
    }
    var rebrandedChrome = rebrandedChromeBrowser(browser);
    var mobileWebKit = mobileWebKitBrowser(browser);
    var supportedMobileBrowsers = isAndroid() ? SUPPORTED_ANDROID_BROWSERS : SUPPORTED_IOS_BROWSERS;
    return !!browser && isWebRTCSupported() && getSdpFormat() === 'unified' && (!rebrandedChrome || SUPPORTED_CHROME_BASED_BROWSERS.includes(rebrandedChrome)) && !isNonChromiumEdge(browser) && (!mobileWebKit || SUPPORTED_MOBILE_WEBKIT_BASED_BROWSERS.includes(mobileWebKit)) && (!isMobile() || supportedMobileBrowsers.includes(browser));
}
module.exports = isSupported; //# sourceMappingURL=support.js.map
}}),
}]);

//# sourceMappingURL=node_modules_twilio-video_es5_util_4ad12d9f._.js.map