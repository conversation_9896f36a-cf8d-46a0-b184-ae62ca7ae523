(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
/**
 * Create a {@link Deferred}.
 * @returns {Deferred}
 */ function defer() {
    var deferred = {};
    deferred.promise = new Promise(function(resolve, reject) {
        deferred.resolve = resolve;
        deferred.reject = reject;
    });
    return deferred;
}
/**
 * Copy a method from a `source` prototype onto a `wrapper` prototype. Invoking
 * the method on the `wrapper` prototype will invoke the corresponding method
 * on an instance accessed by `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @param {string} methodName
 * @returns {undefined}
 */ function delegateMethod(source, wrapper, target, methodName) {
    if (methodName in wrapper) {
        // Skip any methods already set.
        return;
    } else if (methodName.match(/^on[a-z]+$/)) {
        // Skip EventHandlers (these are handled in the constructor).
        return;
    }
    var isProperty = false;
    try {
        var propDesc = Object.getOwnPropertyDescriptor(source, methodName);
        isProperty = propDesc && !!propDesc.get;
    } catch (error) {
    // its okay to eat failure here.
    }
    // NOTE(mpatwardhan):skip properties. we are only interested in overriding
    // functions. we do not even want to evaluate  `typeof source[methodName]` for properties
    // because getter would get invoked, and they might have side effects.
    // For example RTCPeerConnection.peerIdentity is a property that returns a promise.
    // calling typeof RTCPeerConnection.peerIdentity, would leak a promise, and in case it rejects
    // we see errors.
    if (isProperty) {
        return;
    }
    var type;
    try {
        type = typeof source[methodName];
    } catch (error) {
    // NOTE(mroberts): Attempting to check the type of non-function members
    // on the prototype throws an error for some types.
    }
    if (type !== 'function') {
        // Skip non-function members.
        return;
    }
    /* eslint no-loop-func:0 */ wrapper[methodName] = function() {
        return this[target][methodName].apply(this[target], arguments);
    };
}
/**
 * Copy methods from a `source` prototype onto a `wrapper` prototype. Invoking
 * the methods on the `wrapper` prototype will invoke the corresponding method
 * on an instance accessed by `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @returns {undefined}
 */ function delegateMethods(source, wrapper, target) {
    for(var methodName in source){
        delegateMethod(source, wrapper, target, methodName);
    }
}
/**
 * Finds the items in list1 that are not in list2.
 * @param {Array<*>|Map<*>|Set<*>} list1
 * @param {Array<*>|Map<*>|Set<*>} list2
 * @returns {Set}
 */ function difference(list1, list2) {
    list1 = Array.isArray(list1) ? new Set(list1) : new Set(list1.values());
    list2 = Array.isArray(list2) ? new Set(list2) : new Set(list2.values());
    var difference = new Set();
    list1.forEach(function(item) {
        if (!list2.has(item)) {
            difference.add(item);
        }
    });
    return difference;
}
/**
 * Map a list to an array of arrays, and return the flattened result.
 * @param {Array<*>|Set<*>|Map<*>} list
 * @param {function(*): Array<*>} mapFn
 * @returns Array<*>
 */ function flatMap(list, mapFn) {
    var listArray = list instanceof Map || list instanceof Set ? Array.from(list.values()) : list;
    return listArray.reduce(function(flattened, item) {
        return flattened.concat(mapFn(item));
    }, []);
}
/**
 * Get the browser's user agent, if available.
 * @returns {?string}
 */ function getUserAgent() {
    return typeof navigator !== 'undefined' && typeof navigator.userAgent === 'string' ? navigator.userAgent : null;
}
/**
 * Guess the browser.
 * @param {string} [userAgent=navigator.userAgent]
 * @returns {?string} browser - "chrome", "firefox", "safari", or null
 */ function guessBrowser(userAgent) {
    if (typeof userAgent === 'undefined') {
        userAgent = getUserAgent();
    }
    if (/Chrome|CriOS/.test(userAgent)) {
        return 'chrome';
    }
    if (/Firefox|FxiOS/.test(userAgent)) {
        return 'firefox';
    }
    if (/Safari|iPhone|iPad|iPod/.test(userAgent)) {
        return 'safari';
    }
    return null;
}
/**
 * Guess the browser version.
 * @param {string} [userAgent=navigator.userAgent]
 * @returns {?{major: number, minor: number}}
 */ function guessBrowserVersion(userAgent) {
    if (typeof userAgent === 'undefined') {
        userAgent = getUserAgent();
    }
    var prefix = {
        chrome: 'Chrome|CriOS',
        firefox: 'Firefox|FxiOS',
        safari: 'Version'
    }[guessBrowser(userAgent)];
    if (!prefix) {
        return null;
    }
    var regex = new RegExp("(" + prefix + ")/([^\\s]+)");
    var _a = __read(userAgent.match(regex) || [], 3), match = _a[2];
    if (!match) {
        return null;
    }
    var versions = match.split('.').map(Number);
    return {
        major: isNaN(versions[0]) ? null : versions[0],
        minor: isNaN(versions[1]) ? null : versions[1]
    };
}
/**
 * Check whether the current browser is iOS Chrome.
 * @param {string} [userAgent=navigator.userAgent]
 * @returns {boolean}
 */ function isIOSChrome(userAgent) {
    if (typeof userAgent === 'undefined') {
        userAgent = getUserAgent();
    }
    return /Mobi/.test(userAgent) && guessBrowser() === 'chrome' && /iPad|iPhone|iPod/.test(userAgent);
}
/**
 * Intercept an event that might otherwise be proxied on an EventTarget.
 * @param {EventTarget} target
 * @param {string} type
 * @returns {void}
 */ function interceptEvent(target, type) {
    var currentListener = null;
    Object.defineProperty(target, 'on' + type, {
        get: function() {
            return currentListener;
        },
        set: function(newListener) {
            if (currentListener) {
                this.removeEventListener(type, currentListener);
            }
            if (typeof newListener === 'function') {
                currentListener = newListener;
                this.addEventListener(type, currentListener);
            } else {
                currentListener = null;
            }
        }
    });
}
/**
 * This is a function for turning a Promise into the kind referenced in the
 * Legacy Interface Extensions section of the WebRTC spec.
 * @param {Promise<*>} promise
 * @param {function<*>} onSuccess
 * @param {function<Error>} onFailure
 * @returns {Promise<undefined>}
 */ function legacyPromise(promise, onSuccess, onFailure) {
    return onSuccess ? promise.then(onSuccess, onFailure) : promise;
}
/**
 * Make a unique ID.
 * @return {string}
 */ function makeUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0;
        var v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
    });
}
/**
 * For each property name on the `source` prototype, add getters and/or setters
 * to `wrapper` that proxy to `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @returns {undefined}
 */ function proxyProperties(source, wrapper, target) {
    Object.getOwnPropertyNames(source).forEach(function(propertyName) {
        proxyProperty(source, wrapper, target, propertyName);
    });
}
/**
 * For the property name on the `source` prototype, add a getter and/or setter
 * to `wrapper` that proxies to `target`.
 * @param {object} source
 * @param {object} wrapper
 * @param {string} target
 * @param {string} propertyName
 * @returns {undefined}
 */ function proxyProperty(source, wrapper, target, propertyName) {
    if (propertyName in wrapper) {
        // Skip any properties already set.
        return;
    } else if (propertyName.match(/^on[a-z]+$/)) {
        Object.defineProperty(wrapper, propertyName, {
            value: null,
            writable: true
        });
        target.addEventListener(propertyName.slice(2), function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            return wrapper.dispatchEvent.apply(wrapper, __spreadArray([], __read(args)));
        });
        return;
    }
    Object.defineProperty(wrapper, propertyName, {
        enumerable: true,
        get: function() {
            return target[propertyName];
        }
    });
}
/**
 * Check whether native WebRTC APIs are supported.
 * @returns {boolean}
 */ function support() {
    return typeof navigator === 'object' && typeof navigator.mediaDevices === 'object' && typeof navigator.mediaDevices.getUserMedia === 'function' && typeof RTCPeerConnection === 'function';
}
/**
 * Create a Set of supported codecs for a certain kind of media.
 * @param {'audio'|'video'} kind
 * @returns {Promise<Set<AudioCodec|VideoCodec>>}
 */ function createSupportedCodecsSet(kind) {
    if (typeof RTCRtpSender !== 'undefined' && typeof RTCRtpSender.getCapabilities === 'function') {
        return Promise.resolve(new Set(RTCRtpSender.getCapabilities(kind).codecs.map(function(_a) {
            var mimeType = _a.mimeType;
            return mimeType.split('/')[1].toLowerCase();
        })));
    }
    if (typeof RTCPeerConnection === 'undefined' || typeof RTCPeerConnection.prototype === 'undefined' || typeof RTCPeerConnection.prototype.addTransceiver !== 'function' || typeof RTCPeerConnection.prototype.close !== 'function' || typeof RTCPeerConnection.prototype.createOffer !== 'function') {
        return Promise.resolve(new Set());
    }
    var pc = new RTCPeerConnection();
    pc.addTransceiver(kind);
    return pc.createOffer().then(function(_a) {
        var sdp = _a.sdp;
        pc.close();
        return new Set((sdp.match(/^a=rtpmap:.+$/gm) || []).map(function(line) {
            return line.match(/^a=rtpmap:.+ ([^/]+)/)[1].toLowerCase();
        }));
    }, function() {
        pc.close();
        return new Set();
    });
}
// NOTE(mmalavalli): Cache the supported audio and video codecs here.
var supportedCodecs = new Map();
/**
 * Check whether a given codec for a certain kind of media is supported.
 * @param {AudioCodec|VideoCodec} codec
 * @param {'audio'|'video'} kind
 * @returns {Promise<boolean>}
 */ function isCodecSupported(codec, kind) {
    var codecs = supportedCodecs.get(kind);
    if (codecs) {
        return Promise.resolve(codecs.has(codec.toLowerCase()));
    }
    return createSupportedCodecsSet(kind).then(function(codecs) {
        supportedCodecs.set(kind, codecs);
        return codecs.has(codec.toLowerCase());
    });
}
/**
 * Clear cached supported codecs (unit tests only).
 */ function clearCachedSupportedCodecs() {
    supportedCodecs.clear();
}
/**
 * @typedef {object} Deferred
 * @property {Promise} promise
 * @property {function} reject
 * @property {function} resolve
 */ exports.clearCachedSupportedCodecs = clearCachedSupportedCodecs;
exports.defer = defer;
exports.delegateMethods = delegateMethods;
exports.difference = difference;
exports.flatMap = flatMap;
exports.guessBrowser = guessBrowser;
exports.guessBrowserVersion = guessBrowserVersion;
exports.isCodecSupported = isCodecSupported;
exports.isIOSChrome = isIOSChrome;
exports.interceptEvent = interceptEvent;
exports.legacyPromise = legacyPromise;
exports.makeUUID = makeUUID;
exports.proxyProperties = proxyProperties;
exports.support = support; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCPeerConnection, RTCRtpTransceiver */ 'use strict';
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), flatMap = _a.flatMap, guessBrowser = _a.guessBrowser;
// NOTE(mmalavalli): We cache Chrome's sdpSemantics support in order to prevent
// instantiation of more than one RTCPeerConnection.
var isSdpSemanticsSupported = null;
/**
 * Check if Chrome supports specifying sdpSemantics for an RTCPeerConnection.
 * @return {boolean}
 */ function checkIfSdpSemanticsIsSupported() {
    if (typeof isSdpSemanticsSupported === 'boolean') {
        return isSdpSemanticsSupported;
    }
    if (typeof RTCPeerConnection === 'undefined') {
        isSdpSemanticsSupported = false;
        return isSdpSemanticsSupported;
    }
    try {
        // eslint-disable-next-line no-new
        new RTCPeerConnection({
            sdpSemantics: 'foo'
        });
        isSdpSemanticsSupported = false;
    } catch (e) {
        isSdpSemanticsSupported = true;
    }
    return isSdpSemanticsSupported;
}
// NOTE(mmalavalli): We cache Chrome's SDP format in order to prevent
// instantiation of more than one RTCPeerConnection.
var chromeSdpFormat = null;
/**
 * Clear cached Chrome's SDP format
 */ function clearChromeCachedSdpFormat() {
    chromeSdpFormat = null;
}
/**
 * Get Chrome's default SDP format.
 * @returns {'planb'|'unified'}
 */ function getChromeDefaultSdpFormat() {
    if (!chromeSdpFormat) {
        if (typeof RTCPeerConnection !== 'undefined' && 'addTransceiver' in RTCPeerConnection.prototype) {
            var pc = new RTCPeerConnection();
            try {
                pc.addTransceiver('audio');
                chromeSdpFormat = 'unified';
            } catch (e) {
                chromeSdpFormat = 'planb';
            }
            pc.close();
        } else {
            chromeSdpFormat = 'planb';
        }
    }
    return chromeSdpFormat;
}
/**
 * Get Chrome's SDP format.
 * @param {'plan-b'|'unified-plan'} [sdpSemantics]
 * @returns {'planb'|'unified'}
 */ function getChromeSdpFormat(sdpSemantics) {
    if (!sdpSemantics || !checkIfSdpSemanticsIsSupported()) {
        return getChromeDefaultSdpFormat();
    }
    return ({
        'plan-b': 'planb',
        'unified-plan': 'unified'
    })[sdpSemantics];
}
/**
 * Get Safari's default SDP format.
 * @returns {'planb'|'unified'}
 */ function getSafariSdpFormat() {
    return typeof RTCRtpTransceiver !== 'undefined' && 'currentDirection' in RTCRtpTransceiver.prototype ? 'unified' : 'planb';
}
/**
 * Get the browser's default SDP format.
 * @param {'plan-b'|'unified-plan'} [sdpSemantics]
 * @returns {'planb'|'unified'}
 */ function getSdpFormat(sdpSemantics) {
    return ({
        chrome: getChromeSdpFormat(sdpSemantics),
        firefox: 'unified',
        safari: getSafariSdpFormat()
    })[guessBrowser()] || null;
}
/**
 * Match a pattern across lines, returning the first capture group for any
 * matches.
 * @param {string} pattern
 * @param {string} lines
 * @returns {Set<string>} matches
 */ function getMatches(pattern, lines) {
    var matches = lines.match(new RegExp(pattern, 'gm')) || [];
    return matches.reduce(function(results, line) {
        var match = line.match(new RegExp(pattern));
        return match ? results.add(match[1]) : results;
    }, new Set());
}
/**
 * Get a Set of MediaStreamTrack IDs from an SDP.
 * @param {string} pattern
 * @param {string} sdp
 * @returns {Set<string>}
 */ function getTrackIds(pattern, sdp) {
    return getMatches(pattern, sdp);
}
/**
 * Get a Set of MediaStreamTrack IDs from a Plan B SDP.
 * @param {string} sdp - Plan B SDP
 * @returns {Set<string>} trackIds
 */ function getPlanBTrackIds(sdp) {
    return getTrackIds('^a=ssrc:[0-9]+ +msid:.+ +(.+) *$', sdp);
}
/**
 * Get a Set of MediaStreamTrack IDs from a Unified Plan SDP.
 * @param {string} sdp - Unified Plan SDP
 * @returns {Set<string>} trackIds
 */ function getUnifiedPlanTrackIds(sdp) {
    return getTrackIds('^a=msid:.+ +(.+) *$', sdp);
}
/**
 * Get a Set of SSRCs for a MediaStreamTrack from a Plan B SDP.
 * @param {string} sdp - Plan B SDP
 * @param {string} trackId - MediaStreamTrack ID
 * @returns {Set<string>}
 */ function getPlanBSSRCs(sdp, trackId) {
    var pattern = "^a=ssrc:([0-9]+) +msid:[^ ]+ +" + trackId + " *$";
    return getMatches(pattern, sdp);
}
/**
 * Get the m= sections of a particular kind and direction from an sdp.
 * @param {string} sdp -  sdp string
 * @param {string} [kind] - Pattern for matching kind
 * @param {string} [direction] - Pattern for matching direction
 * @returns {Array<string>} mediaSections
 */ function getMediaSections(sdp, kind, direction) {
    if (kind === void 0) {
        kind = '.*';
    }
    if (direction === void 0) {
        direction = '.*';
    }
    return sdp.split('\r\nm=').slice(1).map(function(mediaSection) {
        return "m=" + mediaSection;
    }).filter(function(mediaSection) {
        var kindPattern = new RegExp("m=" + kind, 'gm');
        var directionPattern = new RegExp("a=" + direction, 'gm');
        return kindPattern.test(mediaSection) && directionPattern.test(mediaSection);
    });
}
/**
 * Get the Set of SSRCs announced in a MediaSection.
 * @param {string} mediaSection
 * @returns {Array<string>} ssrcs
 */ function getMediaSectionSSRCs(mediaSection) {
    return Array.from(getMatches('^a=ssrc:([0-9]+) +.*$', mediaSection));
}
/**
 * Get a Set of SSRCs for a MediaStreamTrack from a Unified Plan SDP.
 * @param {string} sdp - Unified Plan SDP
 * @param {string} trackId - MediaStreamTrack ID
 * @returns {Set<string>}
 */ function getUnifiedPlanSSRCs(sdp, trackId) {
    var mediaSections = getMediaSections(sdp);
    var msidAttrRegExp = new RegExp("^a=msid:[^ ]+ +" + trackId + " *$", 'gm');
    var matchingMediaSections = mediaSections.filter(function(mediaSection) {
        return mediaSection.match(msidAttrRegExp);
    });
    return new Set(flatMap(matchingMediaSections, getMediaSectionSSRCs));
}
/**
 * Get a Map from MediaStreamTrack IDs to SSRCs from an SDP.
 * @param {function(string): Set<string>} getTrackIds
 * @param {function(string, string): Set<string>} getSSRCs
 * @param {string} sdp - SDP
 * @returns {Map<string, Set<string>>} trackIdsToSSRCs
 */ function getTrackIdsToSSRCs(getTrackIds, getSSRCs, sdp) {
    return new Map(Array.from(getTrackIds(sdp)).map(function(trackId) {
        return [
            trackId,
            getSSRCs(sdp, trackId)
        ];
    }));
}
/**
 * Get a Map from MediaStreamTrack IDs to SSRCs from a Plan B SDP.
 * @param {string} sdp - Plan B SDP
 * @returns {Map<string, Set<string>>} trackIdsToSSRCs
 */ function getPlanBTrackIdsToSSRCs(sdp) {
    return getTrackIdsToSSRCs(getPlanBTrackIds, getPlanBSSRCs, sdp);
}
/**
 * Get a Map from MediaStreamTrack IDs to SSRCs from a Plan B SDP.
 * @param {string} sdp - Plan B SDP
 * @returns {Map<string, Set<string>>} trackIdsToSSRCs
 */ function getUnifiedPlanTrackIdsToSSRCs(sdp) {
    return getTrackIdsToSSRCs(getUnifiedPlanTrackIds, getUnifiedPlanSSRCs, sdp);
}
/**
 * Update the mappings from MediaStreamTrack IDs to SSRCs as indicated by both
 * the Map from MediaStreamTrack IDs to SSRCs and the SDP itself. This method
 * ensures that SSRCs never change once announced.
 * @param {function(string): Map<string, Set<string>>} getTrackIdsToSSRCs
 * @param {Map<string, Set<string>>} trackIdsToSSRCs
 * @param {string} sdp - SDP
 * @returns {strinng} updatedSdp - updated SDP
 */ function updateTrackIdsToSSRCs(getTrackIdsToSSRCs, trackIdsToSSRCs, sdp) {
    var newTrackIdsToSSRCs = getTrackIdsToSSRCs(sdp);
    var newSSRCsToOldSSRCs = new Map();
    // NOTE(mroberts): First, update a=ssrc attributes.
    newTrackIdsToSSRCs.forEach(function(ssrcs, trackId) {
        if (!trackIdsToSSRCs.has(trackId)) {
            trackIdsToSSRCs.set(trackId, ssrcs);
            return;
        }
        var oldSSRCs = Array.from(trackIdsToSSRCs.get(trackId));
        var newSSRCs = Array.from(ssrcs);
        oldSSRCs.forEach(function(oldSSRC, i) {
            var newSSRC = newSSRCs[i];
            newSSRCsToOldSSRCs.set(newSSRC, oldSSRC);
            var pattern = "^a=ssrc:" + newSSRC + " (.*)$";
            var replacement = "a=ssrc:" + oldSSRC + " $1";
            sdp = sdp.replace(new RegExp(pattern, 'gm'), replacement);
        });
    });
    // NOTE(mroberts): Then, update a=ssrc-group attributes.
    var pattern = '^(a=ssrc-group:[^ ]+ +)(.*)$';
    var matches = sdp.match(new RegExp(pattern, 'gm')) || [];
    matches.forEach(function(line) {
        var match = line.match(new RegExp(pattern));
        if (!match) {
            return;
        }
        var prefix = match[1];
        var newSSRCs = match[2];
        var oldSSRCs = newSSRCs.split(' ').map(function(newSSRC) {
            var oldSSRC = newSSRCsToOldSSRCs.get(newSSRC);
            return oldSSRC ? oldSSRC : newSSRC;
        }).join(' ');
        sdp = sdp.replace(match[0], prefix + oldSSRCs);
    });
    return sdp;
}
/**
 * Update the mappings from MediaStreamTrack IDs to SSRCs as indicated by both
 * the Map from MediaStreamTrack IDs to SSRCs and the Plan B SDP itself. This
 * method ensures that SSRCs never change once announced.
 * @param {Map<string, Set<string>>} trackIdsToSSRCs
 * @param {string} sdp - Plan B SDP
 * @returns {string} updatedSdp - updated Plan B SDP
 */ function updatePlanBTrackIdsToSSRCs(trackIdsToSSRCs, sdp) {
    return updateTrackIdsToSSRCs(getPlanBTrackIdsToSSRCs, trackIdsToSSRCs, sdp);
}
/**
 * Update the mappings from MediaStreamTrack IDs to SSRCs as indicated by both
 * the Map from MediaStreamTrack IDs to SSRCs and the Plan B SDP itself. This
 * method ensures that SSRCs never change once announced.
 * @param {Map<string, Set<string>>} trackIdsToSSRCs
 * @param {string} sdp - Plan B SDP
 * @returns {string} updatedSdp - updated Plan B SDP
 */ function updateUnifiedPlanTrackIdsToSSRCs(trackIdsToSSRCs, sdp) {
    return updateTrackIdsToSSRCs(getUnifiedPlanTrackIdsToSSRCs, trackIdsToSSRCs, sdp);
}
exports.clearChromeCachedSdpFormat = clearChromeCachedSdpFormat;
exports.getSdpFormat = getSdpFormat;
exports.getMediaSections = getMediaSections;
exports.getPlanBTrackIds = getPlanBTrackIds;
exports.getUnifiedPlanTrackIds = getUnifiedPlanTrackIds;
exports.getPlanBSSRCs = getPlanBSSRCs;
exports.getUnifiedPlanSSRCs = getUnifiedPlanSSRCs;
exports.updatePlanBTrackIdsToSSRCs = updatePlanBTrackIdsToSSRCs;
exports.updateUnifiedPlanTrackIdsToSSRCs = updateUnifiedPlanTrackIdsToSSRCs; //# sourceMappingURL=sdp.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/getstats.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), flatMap = _a.flatMap, guessBrowser = _a.guessBrowser, guessBrowserVersion = _a.guessBrowserVersion;
var getSdpFormat = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)").getSdpFormat;
var guess = guessBrowser();
var guessVersion = guessBrowserVersion();
var isChrome = guess === 'chrome';
var isFirefox = guess === 'firefox';
var isSafari = guess === 'safari';
var chromeMajorVersion = isChrome ? guessVersion.major : null;
var CHROME_LEGACY_MAX_AUDIO_LEVEL = 32767;
/**
 * Helper function to find a specific stat from a report.
 * Browsers provide the stats report as a Map,
 * but Citrix provides stats report as an array.
 * @private
 */ function getStatById(report, id) {
    if (typeof report.get === 'function') {
        return report.get(id);
    }
    return report.find(function(s) {
        return s.id === id;
    });
}
/**
 * Filter the RTCStatsReport to only include stats related to a specific track.
 * This function is designed for use with Citrix, where getStats(track) is not supported.
 * It includes specific logic to filter the statistics report returned by Citrix and should
 * only be used when getStats(track) fails.
 *
 * @param {RTCStatsReport|Array<RTCStats>} arrayOrMap - Full stats report or array of stats
 * @param {MediaStreamTrack} track - The track to filter by
 * @param {boolean} [isRemote=false] - Whether this is a remote track
 * @returns {RTCStatsReport} Filtered stats
 * @private
 */ function filterStatsByTrack(arrayOrMap, track, isRemote) {
    var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h, e_9, _j, e_10, _k, e_11, _l;
    if (isRemote === void 0) {
        isRemote = false;
    }
    // Handle different input types
    var allStats;
    if (Array.isArray(arrayOrMap)) {
        allStats = new Map(arrayOrMap.map(function(stat) {
            return [
                stat.id || String(Math.random()),
                stat
            ];
        }));
    } else if (arrayOrMap instanceof Map) {
        allStats = arrayOrMap;
    } else if (typeof arrayOrMap === 'object' && arrayOrMap !== null) {
        // Handle object-style stats (non-standard)
        var statsMap_1 = new Map();
        Object.keys(arrayOrMap).forEach(function(key) {
            statsMap_1.set(key, arrayOrMap[key]);
        });
        allStats = statsMap_1;
    } else {
        return new Map();
    }
    if (!allStats || !track) {
        return new Map();
    }
    var filteredReport = new Map();
    var trackId = track.id;
    var trackKind = track.kind;
    // Step 1: Find the primary track-specific stats
    var primaryStats = null;
    var primaryStatsId = null;
    var ssrc = null;
    try {
        // Find the primary stat for this track (inbound-rtp for remote, media-source for local)
        for(var allStats_1 = __values(allStats), allStats_1_1 = allStats_1.next(); !allStats_1_1.done; allStats_1_1 = allStats_1.next()){
            var _m = __read(allStats_1_1.value, 2), id = _m[0], stat = _m[1];
            // For remote tracks, find matching inbound-rtp with matching trackIdentifier
            if (isRemote && stat.type === 'inbound-rtp' && stat.trackIdentifier === trackId) {
                primaryStats = stat;
                primaryStatsId = id;
                ssrc = stat.ssrc;
                break;
            } else if (!isRemote && stat.type === 'media-source' && stat.trackIdentifier === trackId) {
                // For local tracks, find matching media-source with matching trackIdentifier
                primaryStats = stat;
                primaryStatsId = id;
                break;
            } else if (stat.type === 'track' && stat.trackIdentifier === trackId) {
                // Also check for track stats with matching trackIdentifier
                if (!primaryStats) {
                    primaryStats = stat;
                    primaryStatsId = id;
                }
            }
        }
    } catch (e_1_1) {
        e_1 = {
            error: e_1_1
        };
    } finally{
        try {
            if (allStats_1_1 && !allStats_1_1.done && (_a = allStats_1.return)) _a.call(allStats_1);
        } finally{
            if (e_1) throw e_1.error;
        }
    }
    // If no primary stat was found using the trackId, try a more lenient approach
    if (!primaryStats) {
        // For remote tracks, try to find an inbound-rtp of the correct kind
        if (isRemote) {
            // Get all inbound-rtp stats of the right kind
            var candidateInbounds = [];
            try {
                for(var allStats_2 = __values(allStats), allStats_2_1 = allStats_2.next(); !allStats_2_1.done; allStats_2_1 = allStats_2.next()){
                    var _o = __read(allStats_2_1.value, 2), id = _o[0], stat = _o[1];
                    if (stat.type === 'inbound-rtp' && (stat.kind === trackKind || stat.mediaType === trackKind)) {
                        candidateInbounds.push({
                            id: id,
                            stat: stat
                        });
                    }
                }
            } catch (e_2_1) {
                e_2 = {
                    error: e_2_1
                };
            } finally{
                try {
                    if (allStats_2_1 && !allStats_2_1.done && (_b = allStats_2.return)) _b.call(allStats_2);
                } finally{
                    if (e_2) throw e_2.error;
                }
            }
            // If there are multiple candidates, we need to be careful
            if (candidateInbounds.length === 1) {
                // Only one candidate, use it
                primaryStats = candidateInbounds[0].stat;
                primaryStatsId = candidateInbounds[0].id;
                ssrc = primaryStats.ssrc;
            } else if (candidateInbounds.length > 1) {
                // Multiple candidates - if we have the trackId, try to match by mid
                // otherwise just take the first one
                primaryStats = candidateInbounds[0].stat;
                primaryStatsId = candidateInbounds[0].id;
                ssrc = primaryStats.ssrc;
            }
        } else {
            try {
                // For local tracks, try to find a media-source of the correct kind
                for(var allStats_3 = __values(allStats), allStats_3_1 = allStats_3.next(); !allStats_3_1.done; allStats_3_1 = allStats_3.next()){
                    var _p = __read(allStats_3_1.value, 2), id = _p[0], stat = _p[1];
                    if (stat.type === 'media-source' && stat.kind === trackKind) {
                        primaryStats = stat;
                        primaryStatsId = id;
                        break;
                    }
                }
            } catch (e_3_1) {
                e_3 = {
                    error: e_3_1
                };
            } finally{
                try {
                    if (allStats_3_1 && !allStats_3_1.done && (_c = allStats_3.return)) _c.call(allStats_3);
                } finally{
                    if (e_3) throw e_3.error;
                }
            }
        }
    }
    // If we still didn't find a primary stat, return an empty report
    if (!primaryStats) {
        return filteredReport;
    }
    // Step 2: Add the primary stat
    filteredReport.set(primaryStatsId, primaryStats);
    // Step 3: Add related stats using direct references
    var directlyRelatedIds = new Set();
    // Track different types of related IDs
    if (isRemote) {
        // For remote tracks (inbound-rtp is primary)
        if (primaryStats.codecId) {
            directlyRelatedIds.add(primaryStats.codecId);
        }
        if (primaryStats.transportId) {
            directlyRelatedIds.add(primaryStats.transportId);
        }
        if (primaryStats.remoteId) {
            directlyRelatedIds.add(primaryStats.remoteId);
        }
        // Find remote-outbound-rtp based on ssrc
        if (ssrc) {
            try {
                for(var allStats_4 = __values(allStats), allStats_4_1 = allStats_4.next(); !allStats_4_1.done; allStats_4_1 = allStats_4.next()){
                    var _q = __read(allStats_4_1.value, 2), id = _q[0], stat = _q[1];
                    if (stat.type === 'remote-outbound-rtp' && stat.ssrc === ssrc) {
                        directlyRelatedIds.add(id);
                    }
                }
            } catch (e_4_1) {
                e_4 = {
                    error: e_4_1
                };
            } finally{
                try {
                    if (allStats_4_1 && !allStats_4_1.done && (_d = allStats_4.return)) _d.call(allStats_4);
                } finally{
                    if (e_4) throw e_4.error;
                }
            }
        }
        try {
            // Add codec, transport, and remote stats
            for(var directlyRelatedIds_1 = __values(directlyRelatedIds), directlyRelatedIds_1_1 = directlyRelatedIds_1.next(); !directlyRelatedIds_1_1.done; directlyRelatedIds_1_1 = directlyRelatedIds_1.next()){
                var relatedId = directlyRelatedIds_1_1.value;
                if (allStats.has(relatedId)) {
                    filteredReport.set(relatedId, allStats.get(relatedId));
                }
            }
        } catch (e_5_1) {
            e_5 = {
                error: e_5_1
            };
        } finally{
            try {
                if (directlyRelatedIds_1_1 && !directlyRelatedIds_1_1.done && (_e = directlyRelatedIds_1.return)) _e.call(directlyRelatedIds_1);
            } finally{
                if (e_5) throw e_5.error;
            }
        }
        try {
            // Add the track stats if it exists
            for(var allStats_5 = __values(allStats), allStats_5_1 = allStats_5.next(); !allStats_5_1.done; allStats_5_1 = allStats_5.next()){
                var _r = __read(allStats_5_1.value, 2), id = _r[0], stat = _r[1];
                if (stat.type === 'track' && stat.trackIdentifier === trackId) {
                    filteredReport.set(id, stat);
                }
            }
        } catch (e_6_1) {
            e_6 = {
                error: e_6_1
            };
        } finally{
            try {
                if (allStats_5_1 && !allStats_5_1.done && (_f = allStats_5.return)) _f.call(allStats_5);
            } finally{
                if (e_6) throw e_6.error;
            }
        }
    } else {
        try {
            // For local tracks (media-source is primary)
            // Find outbound-rtp that references this media source
            for(var allStats_6 = __values(allStats), allStats_6_1 = allStats_6.next(); !allStats_6_1.done; allStats_6_1 = allStats_6.next()){
                var _s = __read(allStats_6_1.value, 2), id = _s[0], stat = _s[1];
                if (stat.type === 'outbound-rtp' && stat.mediaSourceId === primaryStatsId) {
                    filteredReport.set(id, stat);
                    // Add codec and transport
                    if (stat.codecId) {
                        directlyRelatedIds.add(stat.codecId);
                    }
                    if (stat.transportId) {
                        directlyRelatedIds.add(stat.transportId);
                    }
                    // Find remote-inbound-rtp that references this outbound-rtp
                    var outboundId = id;
                    try {
                        for(var allStats_7 = (e_8 = void 0, __values(allStats)), allStats_7_1 = allStats_7.next(); !allStats_7_1.done; allStats_7_1 = allStats_7.next()){
                            var _t = __read(allStats_7_1.value, 2), remoteId = _t[0], remoteStat = _t[1];
                            if (remoteStat.type === 'remote-inbound-rtp' && remoteStat.localId === outboundId) {
                                filteredReport.set(remoteId, remoteStat);
                            }
                        }
                    } catch (e_8_1) {
                        e_8 = {
                            error: e_8_1
                        };
                    } finally{
                        try {
                            if (allStats_7_1 && !allStats_7_1.done && (_h = allStats_7.return)) _h.call(allStats_7);
                        } finally{
                            if (e_8) throw e_8.error;
                        }
                    }
                }
            }
        } catch (e_7_1) {
            e_7 = {
                error: e_7_1
            };
        } finally{
            try {
                if (allStats_6_1 && !allStats_6_1.done && (_g = allStats_6.return)) _g.call(allStats_6);
            } finally{
                if (e_7) throw e_7.error;
            }
        }
        try {
            // Add codec and transport stats
            for(var directlyRelatedIds_2 = __values(directlyRelatedIds), directlyRelatedIds_2_1 = directlyRelatedIds_2.next(); !directlyRelatedIds_2_1.done; directlyRelatedIds_2_1 = directlyRelatedIds_2.next()){
                var relatedId = directlyRelatedIds_2_1.value;
                if (allStats.has(relatedId)) {
                    filteredReport.set(relatedId, allStats.get(relatedId));
                }
            }
        } catch (e_9_1) {
            e_9 = {
                error: e_9_1
            };
        } finally{
            try {
                if (directlyRelatedIds_2_1 && !directlyRelatedIds_2_1.done && (_j = directlyRelatedIds_2.return)) _j.call(directlyRelatedIds_2);
            } finally{
                if (e_9) throw e_9.error;
            }
        }
    }
    // Step 4: Add candidate pair and certificate info for context
    // This is useful information that applies to all tracks
    // but doesn't risk mixing data between tracks
    var selectedPairId = null;
    var transportIds = new Set();
    try {
        // Find all transport IDs referenced in our filtered stats
        for(var _u = __values(filteredReport.values()), _v = _u.next(); !_v.done; _v = _u.next()){
            var stat = _v.value;
            if (stat.transportId) {
                transportIds.add(stat.transportId);
            }
        }
    } catch (e_10_1) {
        e_10 = {
            error: e_10_1
        };
    } finally{
        try {
            if (_v && !_v.done && (_k = _u.return)) _k.call(_u);
        } finally{
            if (e_10) throw e_10.error;
        }
    }
    try {
        // Add the transports
        for(var transportIds_1 = __values(transportIds), transportIds_1_1 = transportIds_1.next(); !transportIds_1_1.done; transportIds_1_1 = transportIds_1.next()){
            var transportId = transportIds_1_1.value;
            if (allStats.has(transportId)) {
                var transport = allStats.get(transportId);
                filteredReport.set(transportId, transport);
                // Track the selected candidate pair
                if (transport.selectedCandidatePairId) {
                    selectedPairId = transport.selectedCandidatePairId;
                }
                // Add certificate info
                if (transport.localCertificateId && allStats.has(transport.localCertificateId)) {
                    filteredReport.set(transport.localCertificateId, allStats.get(transport.localCertificateId));
                }
                if (transport.remoteCertificateId && allStats.has(transport.remoteCertificateId)) {
                    filteredReport.set(transport.remoteCertificateId, allStats.get(transport.remoteCertificateId));
                }
            }
        }
    } catch (e_11_1) {
        e_11 = {
            error: e_11_1
        };
    } finally{
        try {
            if (transportIds_1_1 && !transportIds_1_1.done && (_l = transportIds_1.return)) _l.call(transportIds_1);
        } finally{
            if (e_11) throw e_11.error;
        }
    }
    // Add only the selected candidate pair, not all candidate pairs
    if (selectedPairId && allStats.has(selectedPairId)) {
        var selectedPair = allStats.get(selectedPairId);
        filteredReport.set(selectedPairId, selectedPair);
        // Add the local and remote candidates for the selected pair
        if (selectedPair.localCandidateId && allStats.has(selectedPair.localCandidateId)) {
            filteredReport.set(selectedPair.localCandidateId, allStats.get(selectedPair.localCandidateId));
        }
        if (selectedPair.remoteCandidateId && allStats.has(selectedPair.remoteCandidateId)) {
            filteredReport.set(selectedPair.remoteCandidateId, allStats.get(selectedPair.remoteCandidateId));
        }
    }
    return filteredReport;
}
/**
 * Get the standardized {@link RTCPeerConnection} statistics.
 * @param {RTCPeerConnection} peerConnection
 * @param {object} [options] - Used for testing
 * @returns {Promise.<StandardizedStatsResponse>}
 */ function getStats(peerConnection, options) {
    if (!(peerConnection && typeof peerConnection.getStats === 'function')) {
        return Promise.reject(new Error('Given PeerConnection does not support getStats'));
    }
    return _getStats(peerConnection, options);
}
/**
 * getStats() implementation.
 * @param {RTCPeerConnection} peerConnection
 * @param {object} [options] - Used for testing
 * @returns {Promise.<StandardizedStatsResponse>}
 */ function _getStats(peerConnection, options) {
    var localAudioTracks = getTracks(peerConnection, 'audio', 'local');
    var localVideoTracks = getTracks(peerConnection, 'video', 'local');
    var remoteAudioTracks = getTracks(peerConnection, 'audio');
    var remoteVideoTracks = getTracks(peerConnection, 'video');
    var statsResponse = {
        activeIceCandidatePair: null,
        localAudioTrackStats: [],
        localVideoTrackStats: [],
        remoteAudioTrackStats: [],
        remoteVideoTrackStats: []
    };
    var trackStatsPromises = flatMap([
        [
            localAudioTracks,
            'localAudioTrackStats',
            false
        ],
        [
            localVideoTracks,
            'localVideoTrackStats',
            false
        ],
        [
            remoteAudioTracks,
            'remoteAudioTrackStats',
            true
        ],
        [
            remoteVideoTracks,
            'remoteVideoTrackStats',
            true
        ]
    ], function(_a) {
        var _b = __read(_a, 3), tracks = _b[0], statsArrayName = _b[1], isRemote = _b[2];
        return tracks.map(function(track) {
            return getTrackStats(peerConnection, track, Object.assign({
                isRemote: isRemote
            }, options)).then(function(trackStatsArray) {
                trackStatsArray.forEach(function(trackStats) {
                    trackStats.trackId = track.id;
                    statsResponse[statsArrayName].push(trackStats);
                });
            });
        });
    });
    return Promise.all(trackStatsPromises).then(function() {
        return getActiveIceCandidatePairStats(peerConnection, options);
    }).then(function(activeIceCandidatePairStatsReport) {
        statsResponse.activeIceCandidatePair = activeIceCandidatePairStatsReport;
        return statsResponse;
    });
}
/**
 * Generate the {@link StandardizedActiveIceCandidatePairStatsReport} for the
 * {@link RTCPeerConnection}.
 * @param {RTCPeerConnection} peerConnection
 * @param {object} [options]
 * @returns {Promise<StandardizedActiveIceCandidatePairStatsReport>}
 */ function getActiveIceCandidatePairStats(peerConnection, options) {
    if (options === void 0) {
        options = {};
    }
    if (typeof options.testForChrome !== 'undefined' || isChrome || typeof options.testForSafari !== 'undefined' || isSafari) {
        return peerConnection.getStats().then(standardizeChromeOrSafariActiveIceCandidatePairStats);
    }
    if (typeof options.testForFirefox !== 'undefined' || isFirefox) {
        return peerConnection.getStats().then(standardizeFirefoxActiveIceCandidatePairStats);
    }
    return Promise.reject(new Error('RTCPeerConnection#getStats() not supported'));
}
/**
 * Standardize the active RTCIceCandidate pair's statistics in Chrome or Safari.
 * @param {RTCStatsReport} stats
 * @returns {?StandardizedActiveIceCandidatePairStatsReport}
 */ function standardizeChromeOrSafariActiveIceCandidatePairStats(stats) {
    var activeCandidatePairStats = Array.from(stats.values()).find(function(_a) {
        var nominated = _a.nominated, type = _a.type;
        return type === 'candidate-pair' && nominated;
    });
    if (!activeCandidatePairStats) {
        return null;
    }
    var activeLocalCandidateStats = getStatById(stats, activeCandidatePairStats.localCandidateId);
    var activeRemoteCandidateStats = getStatById(stats, activeCandidatePairStats.remoteCandidateId);
    var standardizedCandidateStatsKeys = [
        {
            key: 'candidateType',
            type: 'string'
        },
        {
            key: 'ip',
            type: 'string'
        },
        {
            key: 'port',
            type: 'number'
        },
        {
            key: 'priority',
            type: 'number'
        },
        {
            key: 'protocol',
            type: 'string'
        },
        {
            key: 'url',
            type: 'string'
        }
    ];
    var standardizedLocalCandidateStatsKeys = standardizedCandidateStatsKeys.concat([
        {
            key: 'deleted',
            type: 'boolean'
        },
        {
            key: 'relayProtocol',
            type: 'string'
        }
    ]);
    var standatdizedLocalCandidateStatsReport = activeLocalCandidateStats ? standardizedLocalCandidateStatsKeys.reduce(function(report, _a) {
        var key = _a.key, type = _a.type;
        report[key] = typeof activeLocalCandidateStats[key] === type ? activeLocalCandidateStats[key] : key === 'deleted' ? false : null;
        return report;
    }, {}) : null;
    var standardizedRemoteCandidateStatsReport = activeRemoteCandidateStats ? standardizedCandidateStatsKeys.reduce(function(report, _a) {
        var key = _a.key, type = _a.type;
        report[key] = typeof activeRemoteCandidateStats[key] === type ? activeRemoteCandidateStats[key] : null;
        return report;
    }, {}) : null;
    return [
        {
            key: 'availableIncomingBitrate',
            type: 'number'
        },
        {
            key: 'availableOutgoingBitrate',
            type: 'number'
        },
        {
            key: 'bytesReceived',
            type: 'number'
        },
        {
            key: 'bytesSent',
            type: 'number'
        },
        {
            key: 'consentRequestsSent',
            type: 'number'
        },
        {
            key: 'currentRoundTripTime',
            type: 'number'
        },
        {
            key: 'lastPacketReceivedTimestamp',
            type: 'number'
        },
        {
            key: 'lastPacketSentTimestamp',
            type: 'number'
        },
        {
            key: 'nominated',
            type: 'boolean'
        },
        {
            key: 'priority',
            type: 'number'
        },
        {
            key: 'readable',
            type: 'boolean'
        },
        {
            key: 'requestsReceived',
            type: 'number'
        },
        {
            key: 'requestsSent',
            type: 'number'
        },
        {
            key: 'responsesReceived',
            type: 'number'
        },
        {
            key: 'responsesSent',
            type: 'number'
        },
        {
            key: 'retransmissionsReceived',
            type: 'number'
        },
        {
            key: 'retransmissionsSent',
            type: 'number'
        },
        {
            key: 'state',
            type: 'string',
            fixup: function(state) {
                return state === 'inprogress' ? 'in-progress' : state;
            }
        },
        {
            key: 'totalRoundTripTime',
            type: 'number'
        },
        {
            key: 'transportId',
            type: 'string'
        },
        {
            key: 'writable',
            type: 'boolean'
        }
    ].reduce(function(report, _a) {
        var key = _a.key, type = _a.type, fixup = _a.fixup;
        report[key] = typeof activeCandidatePairStats[key] === type ? fixup ? fixup(activeCandidatePairStats[key]) : activeCandidatePairStats[key] : null;
        return report;
    }, {
        localCandidate: standatdizedLocalCandidateStatsReport,
        remoteCandidate: standardizedRemoteCandidateStatsReport
    });
}
/**
 * Standardize the active RTCIceCandidate pair's statistics in Firefox.
 * @param {RTCStatsReport} stats
 * @returns {?StandardizedActiveIceCandidatePairStatsReport}
 */ function standardizeFirefoxActiveIceCandidatePairStats(stats) {
    var activeCandidatePairStats = Array.from(stats.values()).find(function(_a) {
        var nominated = _a.nominated, type = _a.type;
        return type === 'candidate-pair' && nominated;
    });
    if (!activeCandidatePairStats) {
        return null;
    }
    var activeLocalCandidateStats = getStatById(stats, activeCandidatePairStats.localCandidateId);
    var activeRemoteCandidateStats = getStatById(stats, activeCandidatePairStats.remoteCandidateId);
    var standardizedCandidateStatsKeys = [
        {
            key: 'candidateType',
            type: 'string'
        },
        {
            key: 'ip',
            ffKeys: [
                'address',
                'ipAddress'
            ],
            type: 'string'
        },
        {
            key: 'port',
            ffKeys: [
                'portNumber'
            ],
            type: 'number'
        },
        {
            key: 'priority',
            type: 'number'
        },
        {
            key: 'protocol',
            ffKeys: [
                'transport'
            ],
            type: 'string'
        },
        {
            key: 'url',
            type: 'string'
        }
    ];
    var standardizedLocalCandidateStatsKeys = standardizedCandidateStatsKeys.concat([
        {
            key: 'deleted',
            type: 'boolean'
        },
        {
            key: 'relayProtocol',
            type: 'string'
        }
    ]);
    var candidateTypes = {
        host: 'host',
        peerreflexive: 'prflx',
        relayed: 'relay',
        serverreflexive: 'srflx'
    };
    var standatdizedLocalCandidateStatsReport = activeLocalCandidateStats ? standardizedLocalCandidateStatsKeys.reduce(function(report, _a) {
        var ffKeys = _a.ffKeys, key = _a.key, type = _a.type;
        var localStatKey = ffKeys && ffKeys.find(function(key) {
            return key in activeLocalCandidateStats;
        }) || key;
        report[key] = typeof activeLocalCandidateStats[localStatKey] === type ? localStatKey === 'candidateType' ? candidateTypes[activeLocalCandidateStats[localStatKey]] || activeLocalCandidateStats[localStatKey] : activeLocalCandidateStats[localStatKey] : localStatKey === 'deleted' ? false : null;
        return report;
    }, {}) : null;
    var standardizedRemoteCandidateStatsReport = activeRemoteCandidateStats ? standardizedCandidateStatsKeys.reduce(function(report, _a) {
        var ffKeys = _a.ffKeys, key = _a.key, type = _a.type;
        var remoteStatKey = ffKeys && ffKeys.find(function(key) {
            return key in activeRemoteCandidateStats;
        }) || key;
        report[key] = typeof activeRemoteCandidateStats[remoteStatKey] === type ? remoteStatKey === 'candidateType' ? candidateTypes[activeRemoteCandidateStats[remoteStatKey]] || activeRemoteCandidateStats[remoteStatKey] : activeRemoteCandidateStats[remoteStatKey] : null;
        return report;
    }, {}) : null;
    return [
        {
            key: 'availableIncomingBitrate',
            type: 'number'
        },
        {
            key: 'availableOutgoingBitrate',
            type: 'number'
        },
        {
            key: 'bytesReceived',
            type: 'number'
        },
        {
            key: 'bytesSent',
            type: 'number'
        },
        {
            key: 'consentRequestsSent',
            type: 'number'
        },
        {
            key: 'currentRoundTripTime',
            type: 'number'
        },
        {
            key: 'lastPacketReceivedTimestamp',
            type: 'number'
        },
        {
            key: 'lastPacketSentTimestamp',
            type: 'number'
        },
        {
            key: 'nominated',
            type: 'boolean'
        },
        {
            key: 'priority',
            type: 'number'
        },
        {
            key: 'readable',
            type: 'boolean'
        },
        {
            key: 'requestsReceived',
            type: 'number'
        },
        {
            key: 'requestsSent',
            type: 'number'
        },
        {
            key: 'responsesReceived',
            type: 'number'
        },
        {
            key: 'responsesSent',
            type: 'number'
        },
        {
            key: 'retransmissionsReceived',
            type: 'number'
        },
        {
            key: 'retransmissionsSent',
            type: 'number'
        },
        {
            key: 'state',
            type: 'string'
        },
        {
            key: 'totalRoundTripTime',
            type: 'number'
        },
        {
            key: 'transportId',
            type: 'string'
        },
        {
            key: 'writable',
            type: 'boolean'
        }
    ].reduce(function(report, _a) {
        var key = _a.key, type = _a.type;
        report[key] = typeof activeCandidatePairStats[key] === type ? activeCandidatePairStats[key] : null;
        return report;
    }, {
        localCandidate: standatdizedLocalCandidateStatsReport,
        remoteCandidate: standardizedRemoteCandidateStatsReport
    });
}
/**
 * Get local/remote audio/video MediaStreamTracks.
 * @param {RTCPeerConnection} peerConnection - The RTCPeerConnection
 * @param {string} kind - 'audio' or 'video'
 * @param {string} [localOrRemote] - 'local' or 'remote'
 * @returns {Array<MediaStreamTrack>}
 */ function getTracks(peerConnection, kind, localOrRemote) {
    var getSendersOrReceivers = localOrRemote === 'local' ? 'getSenders' : 'getReceivers';
    if (peerConnection[getSendersOrReceivers]) {
        return peerConnection[getSendersOrReceivers]().map(function(_a) {
            var track = _a.track;
            return track;
        }).filter(function(track) {
            return track && track.kind === kind;
        });
    }
    var getStreams = localOrRemote === 'local' ? 'getLocalStreams' : 'getRemoteStreams';
    var getTracks = kind === 'audio' ? 'getAudioTracks' : 'getVideoTracks';
    return flatMap(peerConnection[getStreams](), function(stream) {
        return stream[getTracks]();
    });
}
/**
 * Determine if a track is remote by examining the PeerConnection's receivers.
 * This function is designed for use with Citrix, where getStats(track) is not supported.
 * @param {RTCPeerConnection} peerConnection
 * @param {MediaStreamTrack} track
 * @returns {boolean} True if the track is a remote track
 * @private
 */ function isRemoteTrack(peerConnection, track) {
    var e_12, _a, e_13, _b, e_14, _c;
    if (!peerConnection || !track) {
        return false;
    }
    // Check if the track belongs to any receiver (remote)
    if (peerConnection.getReceivers) {
        var receivers = peerConnection.getReceivers();
        try {
            for(var receivers_1 = __values(receivers), receivers_1_1 = receivers_1.next(); !receivers_1_1.done; receivers_1_1 = receivers_1.next()){
                var receiver = receivers_1_1.value;
                if (receiver.track && receiver.track.id === track.id) {
                    return true;
                }
            }
        } catch (e_12_1) {
            e_12 = {
                error: e_12_1
            };
        } finally{
            try {
                if (receivers_1_1 && !receivers_1_1.done && (_a = receivers_1.return)) _a.call(receivers_1);
            } finally{
                if (e_12) throw e_12.error;
            }
        }
    }
    // Check remote streams if getReceivers is not available
    if (peerConnection.getRemoteStreams) {
        var remoteStreams = peerConnection.getRemoteStreams();
        try {
            for(var remoteStreams_1 = __values(remoteStreams), remoteStreams_1_1 = remoteStreams_1.next(); !remoteStreams_1_1.done; remoteStreams_1_1 = remoteStreams_1.next()){
                var stream = remoteStreams_1_1.value;
                var tracks = stream.getTracks();
                try {
                    for(var tracks_1 = (e_14 = void 0, __values(tracks)), tracks_1_1 = tracks_1.next(); !tracks_1_1.done; tracks_1_1 = tracks_1.next()){
                        var remoteTrack = tracks_1_1.value;
                        if (remoteTrack.id === track.id) {
                            return true;
                        }
                    }
                } catch (e_14_1) {
                    e_14 = {
                        error: e_14_1
                    };
                } finally{
                    try {
                        if (tracks_1_1 && !tracks_1_1.done && (_c = tracks_1.return)) _c.call(tracks_1);
                    } finally{
                        if (e_14) throw e_14.error;
                    }
                }
            }
        } catch (e_13_1) {
            e_13 = {
                error: e_13_1
            };
        } finally{
            try {
                if (remoteStreams_1_1 && !remoteStreams_1_1.done && (_b = remoteStreams_1.return)) _b.call(remoteStreams_1);
            } finally{
                if (e_13) throw e_13.error;
            }
        }
    }
    // The track is not in any remote source, so it's likely local
    return false;
}
/**
 * Get the standardized statistics for a particular MediaStreamTrack.
 * @param {RTCPeerConnection} peerConnection
 * @param {MediaStreamTrack} track
 * @param {object} [options] - Used for testing
 * @returns {Promise.<Array<StandardizedTrackStatsReport>>}
 */ function getTrackStats(peerConnection, track, options) {
    if (options === void 0) {
        options = {};
    }
    if (typeof options.testForChrome !== 'undefined' || isChrome) {
        return chromeOrSafariGetTrackStats(peerConnection, track, options);
    }
    if (typeof options.testForFirefox !== 'undefined' || isFirefox) {
        return firefoxGetTrackStats(peerConnection, track, options);
    }
    if (typeof options.testForSafari !== 'undefined' || isSafari) {
        if (typeof options.testForSafari !== 'undefined' || getSdpFormat() === 'unified') {
            return chromeOrSafariGetTrackStats(peerConnection, track, options);
        }
        // NOTE(syerrapragada): getStats() is not supported on
        // Safari versions where plan-b is the SDP format
        // due to this bug: https://bugs.webkit.org/show_bug.cgi?id=192601
        return Promise.reject(new Error([
            'getStats() is not supported on this version of Safari',
            'due to this bug: https://bugs.webkit.org/show_bug.cgi?id=192601'
        ].join(' ')));
    }
    return Promise.reject(new Error('RTCPeerConnection#getStats() not supported'));
}
/**
 * Get the standardized statistics for a particular MediaStreamTrack in Chrome or Safari.
 * @param {RTCPeerConnection} peerConnection
 * @param {MediaStreamTrack} track
 * @param {object} options - Used for testing
 * @returns {Promise.<Array<StandardizedTrackStatsReport>>}
 */ function chromeOrSafariGetTrackStats(peerConnection, track, options) {
    var log = options.log;
    if (chromeMajorVersion && chromeMajorVersion < 67) {
        return new Promise(function(resolve, reject) {
            peerConnection.getStats(function(response) {
                resolve([
                    standardizeChromeLegacyStats(response, track)
                ]);
            }, null, reject);
        });
    }
    return peerConnection.getStats(track).then(function(response) {
        log.info('getStats by track successful');
        return standardizeChromeOrSafariStats(response, options);
    }).catch(function() {
        // NOTE(lrivas): Citrix doesn't support track-specific getStats,
        // so this workaround tries getting all stats and filtering by track.
        log.warn('getStats by track failed. Getting default stats');
        return peerConnection.getStats().then(function(stats) {
            log.info('getStats by default successful');
            var isRemote = isRemoteTrack(peerConnection, track);
            log.info("Starting filtering stats for " + (isRemote ? 'remote' : 'local') + " track");
            var filteredStats = filterStatsByTrack(stats, track, isRemote);
            log.info("Completed filtering stats for " + (isRemote ? 'remote' : 'local') + " track");
            return standardizeChromeOrSafariStats(filteredStats, options);
        });
    });
}
/**
 * Get the standardized statistics for a particular MediaStreamTrack in Firefox.
 * @param {RTCPeerConnection} peerConnection
 * @param {MediaStreamTrack} track
 * @param {object} options
 * @returns {Promise.<Array<StandardizedTrackStatsReport>>}
 */ function firefoxGetTrackStats(peerConnection, track, options) {
    return peerConnection.getStats(track).then(function(response) {
        return [
            standardizeFirefoxStats(response, options)
        ];
    });
}
/**
 * Standardize the MediaStreamTrack's legacy statistics in Chrome.
 * @param {RTCStatsResponse} response
 * @param {MediaStreamTrack} track
 * @returns {StandardizedTrackStatsReport}
 */ function standardizeChromeLegacyStats(response, track) {
    var ssrcReport = response.result().find(function(report) {
        return report.type === 'ssrc' && report.stat('googTrackId') === track.id;
    });
    var standardizedStats = {};
    if (ssrcReport) {
        standardizedStats.timestamp = Math.round(Number(ssrcReport.timestamp));
        standardizedStats = ssrcReport.names().reduce(function(stats, name) {
            switch(name){
                case 'googCodecName':
                    stats.codecName = ssrcReport.stat(name);
                    break;
                case 'googRtt':
                    stats.roundTripTime = Number(ssrcReport.stat(name));
                    break;
                case 'googJitterReceived':
                    stats.jitter = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameWidthInput':
                    stats.frameWidthInput = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameHeightInput':
                    stats.frameHeightInput = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameWidthSent':
                    stats.frameWidthSent = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameHeightSent':
                    stats.frameHeightSent = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameWidthReceived':
                    stats.frameWidthReceived = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameHeightReceived':
                    stats.frameHeightReceived = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameRateInput':
                    stats.frameRateInput = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameRateSent':
                    stats.frameRateSent = Number(ssrcReport.stat(name));
                    break;
                case 'googFrameRateReceived':
                    stats.frameRateReceived = Number(ssrcReport.stat(name));
                    break;
                case 'ssrc':
                    stats[name] = ssrcReport.stat(name);
                    break;
                case 'bytesReceived':
                case 'bytesSent':
                case 'packetsLost':
                case 'packetsReceived':
                case 'packetsSent':
                case 'audioInputLevel':
                case 'audioOutputLevel':
                    stats[name] = Number(ssrcReport.stat(name));
                    break;
            }
            return stats;
        }, standardizedStats);
    }
    return standardizedStats;
}
/**
 * Standardize the MediaStreamTrack's statistics in Chrome or Safari.
 * @param {RTCStatsReport} response
 * @param {object} options - Used for testing
 * @returns {Array<StandardizedTrackStatsReport>}
 */ function standardizeChromeOrSafariStats(response, _a) {
    var _b = _a.simulateExceptionWhileStandardizingStats, simulateExceptionWhileStandardizingStats = _b === void 0 ? false : _b;
    if (simulateExceptionWhileStandardizingStats) {
        throw new Error('Error while gathering stats');
    }
    var inbound = null;
    // NOTE(mpatwardhan): We should expect more than one "outbound-rtp" stats for a
    // VP8 simulcast MediaStreamTrack.
    var outbound = [];
    var remoteInbound = null;
    var remoteOutbound = null;
    var track = null;
    var codec = null;
    var localMedia = null;
    response.forEach(function(stat) {
        var type = stat.type;
        switch(type){
            case 'inbound-rtp':
                inbound = stat;
                break;
            case 'outbound-rtp':
                outbound.push(stat);
                break;
            case 'media-source':
                localMedia = stat;
                break;
            case 'track':
                track = stat;
                break;
            case 'codec':
                codec = stat;
                break;
            case 'remote-inbound-rtp':
                remoteInbound = stat;
                break;
            case 'remote-outbound-rtp':
                remoteOutbound = stat;
                break;
        }
    });
    var isRemote = track ? track.remoteSource : !localMedia;
    var mainSources = isRemote ? [
        inbound
    ] : outbound;
    var stats = [];
    var remoteSource = isRemote ? remoteOutbound : remoteInbound; // remote rtp stats
    mainSources.forEach(function(source) {
        var standardizedStats = {};
        var statSources = [
            source,
            localMedia,
            track,
            codec,
            remoteSource && remoteSource.ssrc === source.ssrc ? remoteSource : null
        ];
        function getStatValue(name) {
            var sourceFound = statSources.find(function(statSource) {
                return statSource && typeof statSource[name] !== 'undefined';
            }) || null;
            return sourceFound ? sourceFound[name] : null;
        }
        var ssrc = getStatValue('ssrc');
        if (typeof ssrc === 'number') {
            standardizedStats.ssrc = String(ssrc);
        }
        var timestamp = getStatValue('timestamp');
        standardizedStats.timestamp = Math.round(timestamp);
        var mimeType = getStatValue('mimeType');
        if (typeof mimeType === 'string') {
            mimeType = mimeType.split('/');
            standardizedStats.codecName = mimeType[mimeType.length - 1];
        }
        var roundTripTime = getStatValue('roundTripTime');
        if (typeof roundTripTime === 'number') {
            standardizedStats.roundTripTime = Math.round(roundTripTime * 1000);
        }
        var jitter = getStatValue('jitter');
        if (typeof jitter === 'number') {
            standardizedStats.jitter = Math.round(jitter * 1000);
        }
        var frameWidth = getStatValue('frameWidth');
        if (typeof frameWidth === 'number') {
            if (isRemote) {
                standardizedStats.frameWidthReceived = frameWidth;
            } else {
                standardizedStats.frameWidthSent = frameWidth;
                standardizedStats.frameWidthInput = track ? track.frameWidth : localMedia.width;
            }
        }
        var frameHeight = getStatValue('frameHeight');
        if (typeof frameHeight === 'number') {
            if (isRemote) {
                standardizedStats.frameHeightReceived = frameHeight;
            } else {
                standardizedStats.frameHeightSent = frameHeight;
                standardizedStats.frameHeightInput = track ? track.frameHeight : localMedia.height;
            }
        }
        var framesPerSecond = getStatValue('framesPerSecond');
        if (typeof framesPerSecond === 'number') {
            standardizedStats[isRemote ? 'frameRateReceived' : 'frameRateSent'] = framesPerSecond;
        }
        var bytesReceived = getStatValue('bytesReceived');
        if (typeof bytesReceived === 'number') {
            standardizedStats.bytesReceived = bytesReceived;
        }
        var bytesSent = getStatValue('bytesSent');
        if (typeof bytesSent === 'number') {
            standardizedStats.bytesSent = bytesSent;
        }
        var packetsLost = getStatValue('packetsLost');
        if (typeof packetsLost === 'number') {
            standardizedStats.packetsLost = packetsLost;
        }
        var packetsReceived = getStatValue('packetsReceived');
        if (typeof packetsReceived === 'number') {
            standardizedStats.packetsReceived = packetsReceived;
        }
        var packetsSent = getStatValue('packetsSent');
        if (typeof packetsSent === 'number') {
            standardizedStats.packetsSent = packetsSent;
        }
        var audioLevel = getStatValue('audioLevel');
        if (typeof audioLevel === 'number') {
            audioLevel = Math.round(audioLevel * CHROME_LEGACY_MAX_AUDIO_LEVEL);
            if (isRemote) {
                standardizedStats.audioOutputLevel = audioLevel;
            } else {
                standardizedStats.audioInputLevel = audioLevel;
            }
        }
        var totalPacketSendDalay = getStatValue('totalPacketSendDelay');
        if (typeof totalPacketSendDalay === 'number') {
            standardizedStats.totalPacketSendDelay = totalPacketSendDalay;
        }
        var totalEncodeTime = getStatValue('totalEncodeTime');
        if (typeof totalEncodeTime === 'number') {
            standardizedStats.totalEncodeTime = totalEncodeTime;
        }
        var framesEncoded = getStatValue('framesEncoded');
        if (typeof framesEncoded === 'number') {
            standardizedStats.framesEncoded = framesEncoded;
        }
        var estimatedPlayoutTimestamp = getStatValue('estimatedPlayoutTimestamp');
        if (typeof estimatedPlayoutTimestamp === 'number') {
            standardizedStats.estimatedPlayoutTimestamp = estimatedPlayoutTimestamp;
        }
        var totalDecodeTime = getStatValue('totalDecodeTime');
        if (typeof totalDecodeTime === 'number') {
            standardizedStats.totalDecodeTime = totalDecodeTime;
        }
        var framesDecoded = getStatValue('framesDecoded');
        if (typeof framesDecoded === 'number') {
            standardizedStats.framesDecoded = framesDecoded;
        }
        var jitterBufferDelay = getStatValue('jitterBufferDelay');
        if (typeof jitterBufferDelay === 'number') {
            standardizedStats.jitterBufferDelay = jitterBufferDelay;
        }
        var jitterBufferEmittedCount = getStatValue('jitterBufferEmittedCount');
        if (typeof jitterBufferEmittedCount === 'number') {
            standardizedStats.jitterBufferEmittedCount = jitterBufferEmittedCount;
        }
        stats.push(standardizedStats);
    });
    return stats;
}
/**
 * Standardize the MediaStreamTrack's statistics in Firefox.
 * @param {RTCStatsReport} response
 * @param {object} options - Used for testing
 * @returns {StandardizedTrackStatsReport}
 */ function standardizeFirefoxStats(response, _a) {
    if (response === void 0) {
        response = new Map();
    }
    var isRemote = _a.isRemote, _b = _a.simulateExceptionWhileStandardizingStats, simulateExceptionWhileStandardizingStats = _b === void 0 ? false : _b;
    if (simulateExceptionWhileStandardizingStats) {
        throw new Error('Error while gathering stats');
    }
    // NOTE(mroberts): If getStats is called on a closed RTCPeerConnection,
    // Firefox returns undefined instead of an RTCStatsReport. We workaround this
    // here. See the following bug for more details:
    //
    //   https://bugzilla.mozilla.org/show_bug.cgi?id=1377225
    //
    var inbound = null;
    var outbound = null;
    // NOTE(mmalavalli): Starting from Firefox 63, RTC{Inbound, Outbound}RTPStreamStats.isRemote
    // will be deprecated, followed by its removal in Firefox 66. Also, trying to
    // access members of the remote RTC{Inbound, Outbound}RTPStreamStats without
    // using RTCStatsReport.get(remoteId) will trigger console warnings. So, we
    // no longer depend on "isRemote", and we call RTCStatsReport.get(remoteId)
    // to access the remote RTC{Inbound, Outbound}RTPStreamStats.
    //
    // Source: https://blog.mozilla.org/webrtc/getstats-isremote-65/
    //
    response.forEach(function(stat) {
        var isRemote = stat.isRemote, remoteId = stat.remoteId, type = stat.type;
        if (isRemote) {
            return;
        }
        switch(type){
            case 'inbound-rtp':
                inbound = stat;
                outbound = getStatById(response, remoteId);
                break;
            case 'outbound-rtp':
                outbound = stat;
                inbound = getStatById(response, remoteId);
                break;
        }
    });
    var first = isRemote ? inbound : outbound;
    var second = isRemote ? outbound : inbound;
    function getStatValue(name) {
        if (first && typeof first[name] !== 'undefined') {
            return first[name];
        }
        if (second && typeof second[name] !== 'undefined') {
            return second[name];
        }
        return null;
    }
    var standardizedStats = {};
    var timestamp = getStatValue('timestamp');
    standardizedStats.timestamp = Math.round(timestamp);
    var ssrc = getStatValue('ssrc');
    if (typeof ssrc === 'number') {
        standardizedStats.ssrc = String(ssrc);
    }
    var bytesSent = getStatValue('bytesSent');
    if (typeof bytesSent === 'number') {
        standardizedStats.bytesSent = bytesSent;
    }
    var packetsLost = getStatValue('packetsLost');
    if (typeof packetsLost === 'number') {
        standardizedStats.packetsLost = packetsLost;
    }
    var packetsSent = getStatValue('packetsSent');
    if (typeof packetsSent === 'number') {
        standardizedStats.packetsSent = packetsSent;
    }
    var roundTripTime = getStatValue('roundTripTime');
    if (typeof roundTripTime === 'number') {
        // roundTripTime is double - measured in seconds.
        // https://www.w3.org/TR/webrtc-stats/#dom-rtcremoteinboundrtpstreamstats-roundtriptime
        // cover it to milliseconds (and make it integer)
        standardizedStats.roundTripTime = Math.round(roundTripTime * 1000);
    }
    var jitter = getStatValue('jitter');
    if (typeof jitter === 'number') {
        standardizedStats.jitter = Math.round(jitter * 1000);
    }
    var frameRateSent = getStatValue('framerateMean');
    if (typeof frameRateSent === 'number') {
        standardizedStats.frameRateSent = Math.round(frameRateSent);
    }
    var bytesReceived = getStatValue('bytesReceived');
    if (typeof bytesReceived === 'number') {
        standardizedStats.bytesReceived = bytesReceived;
    }
    var packetsReceived = getStatValue('packetsReceived');
    if (typeof packetsReceived === 'number') {
        standardizedStats.packetsReceived = packetsReceived;
    }
    var frameRateReceived = getStatValue('framerateMean');
    if (typeof frameRateReceived === 'number') {
        standardizedStats.frameRateReceived = Math.round(frameRateReceived);
    }
    var totalPacketSendDalay = getStatValue('totalPacketSendDelay');
    if (typeof totalPacketSendDalay === 'number') {
        standardizedStats.totalPacketSendDelay = totalPacketSendDalay;
    }
    var totalEncodeTime = getStatValue('totalEncodeTime');
    if (typeof totalEncodeTime === 'number') {
        standardizedStats.totalEncodeTime = totalEncodeTime;
    }
    var framesEncoded = getStatValue('framesEncoded');
    if (typeof framesEncoded === 'number') {
        standardizedStats.framesEncoded = framesEncoded;
    }
    var estimatedPlayoutTimestamp = getStatValue('estimatedPlayoutTimestamp');
    if (typeof estimatedPlayoutTimestamp === 'number') {
        standardizedStats.estimatedPlayoutTimestamp = estimatedPlayoutTimestamp;
    }
    var totalDecodeTime = getStatValue('totalDecodeTime');
    if (typeof totalDecodeTime === 'number') {
        standardizedStats.totalDecodeTime = totalDecodeTime;
    }
    var framesDecoded = getStatValue('framesDecoded');
    if (typeof framesDecoded === 'number') {
        standardizedStats.framesDecoded = framesDecoded;
    }
    var jitterBufferDelay = getStatValue('jitterBufferDelay');
    if (typeof jitterBufferDelay === 'number') {
        standardizedStats.jitterBufferDelay = jitterBufferDelay;
    }
    var jitterBufferEmittedCount = getStatValue('jitterBufferEmittedCount');
    if (typeof jitterBufferEmittedCount === 'number') {
        standardizedStats.jitterBufferEmittedCount = jitterBufferEmittedCount;
    }
    return standardizedStats;
}
/**
 * Standardized RTCIceCandidate statistics.
 * @typedef {object} StandardizedIceCandidateStatsReport
 * @property {'host'|'prflx'|'relay'|'srflx'} candidateType
 * @property {string} ip
 * @property {number} port
 * @property {number} priority
 * @property {'tcp'|'udp'} protocol
 * @property {string} url
 */ /**
 * Standardized local RTCIceCandidate statistics.
 * @typedef {StandardizedIceCandidateStatsReport} StandardizedLocalIceCandidateStatsReport
 * @property {boolean} [deleted=false]
 * @property {'tcp'|'tls'|'udp'} relayProtocol
 */ /**
 * Standardized active RTCIceCandidate pair statistics.
 * @typedef {object} StandardizedActiveIceCandidatePairStatsReport
 * @property {number} availableIncomingBitrate
 * @property {number} availableOutgoingBitrate
 * @property {number} bytesReceived
 * @property {number} bytesSent
 * @property {number} consentRequestsSent
 * @property {number} currentRoundTripTime
 * @property {number} lastPacketReceivedTimestamp
 * @property {number} lastPacketSentTimestamp
 * @property {StandardizedLocalIceCandidateStatsReport} localCandidate
 * @property {boolean} nominated
 * @property {number} priority
 * @property {boolean} readable
 * @property {StandardizedIceCandidateStatsReport} remoteCandidate
 * @property {number} requestsReceived
 * @property {number} requestsSent
 * @property {number} responsesReceived
 * @property {number} responsesSent
 * @property {number} retransmissionsReceived
 * @property {number} retransmissionsSent
 * @property {'frozen'|'waiting'|'in-progress'|'failed'|'succeeded'} state
 * @property {number} totalRoundTripTime
 * @property {string} transportId
 * @property {boolean} writable
 */ /**
 * Standardized {@link RTCPeerConnection} statistics.
 * @typedef {Object} StandardizedStatsResponse
 * @property {StandardizedActiveIceCandidatePairStatsReport} activeIceCandidatePair - Stats for active ICE candidate pair
 * @property Array<StandardizedTrackStatsReport> localAudioTrackStats - Stats for local audio MediaStreamTracks
 * @property Array<StandardizedTrackStatsReport> localVideoTrackStats - Stats for local video MediaStreamTracks
 * @property Array<StandardizedTrackStatsReport> remoteAudioTrackStats - Stats for remote audio MediaStreamTracks
 * @property Array<StandardizedTrackStatsReport> remoteVideoTrackStats - Stats for remote video MediaStreamTracks
 */ /**
 * Standardized MediaStreamTrack statistics.
 * @typedef {Object} StandardizedTrackStatsReport
 * @property {string} trackId - MediaStreamTrack ID
 * @property {string} ssrc - SSRC of the MediaStreamTrack
 * @property {number} timestamp - The Unix timestamp in milliseconds
 * @property {string} [codecName] - Name of the codec used to encode the MediaStreamTrack's media
 * @property {number} [roundTripTime] - Round trip time in milliseconds
 * @property {number} [jitter] - Jitter in milliseconds
 * @property {number} [frameWidthInput] - Width in pixels of the local video MediaStreamTrack's captured frame
 * @property {number} [frameHeightInput] - Height in pixels of the local video MediaStreamTrack's captured frame
 * @property {number} [frameWidthSent] - Width in pixels of the local video MediaStreamTrack's encoded frame
 * @property {number} [frameHeightSent] - Height in pixels of the local video MediaStreamTrack's encoded frame
 * @property {number} [frameWidthReceived] - Width in pixels of the remote video MediaStreamTrack's received frame
 * @property {number} [frameHeightReceived] - Height in pixels of the remote video MediaStreamTrack's received frame
 * @property {number} [frameRateInput] - Captured frames per second of the local video MediaStreamTrack
 * @property {number} [frameRateSent] - Frames per second of the local video MediaStreamTrack's encoded video
 * @property {number} [frameRateReceived] - Frames per second of the remote video MediaStreamTrack's received video
 * @property {number} [bytesReceived] - Number of bytes of the remote MediaStreamTrack's media received
 * @property {number} [bytesSent] - Number of bytes of the local MediaStreamTrack's media sent
 * @property {number} [packetsLost] - Number of packets of the MediaStreamTrack's media lost
 * @property {number} [packetsReceived] - Number of packets of the remote MediaStreamTrack's media received
 * @property {number} [packetsSent] - Number of packets of the local MediaStreamTrack's media sent
 * @property {number} [totalPacketSendDelay] - The total number of seconds that the local MediaStreamTrack's packets
 *  have spent buffered locally before being sent over the network
 * @property {number} [totalEncodeTime] - The total number of seconds spent on encoding the local MediaStreamTrack's frames
 * @property {number} [framesEncoded] - The total number of frames of the local MediaStreamTrack that have been encoded sor far
 * @property {number} [estimatedPlayoutTimestamp] - The estimated playout time of the remote MediaStreamTrack
 * @property {number} [totalDecodeTime] - The total number of seconds spent on decoding the remote MediaStreamTrack's frames
 * @property {number} [framesDecoded] - The total number of frames of the remote MediaStreamTrack that have been decoded sor far
 * @property {number} [jitterBufferDelay] - The sum of the time, in seconds, each audio sample or a video frame of the remote
 *   MediaStreamTrack takes from the time the first packet is received by the jitter buffer to the time it exits the jitter buffer
 * @property {number} [jitterBufferEmittedCount] - The total number of audio samples or video frames that have come out of the jitter buffer
 * @property {AudioLevel} [audioInputLevel] - The {@link AudioLevel} of the local audio MediaStreamTrack
 * @property {AudioLevel} [audioOutputLevel] - The {@link AudioLevel} of the remote video MediaStreamTrack
 */ module.exports = getStats; //# sourceMappingURL=getstats.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/getusermedia.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals navigator */ 'use strict';
/**
 * This function is very similar to <code>navigator.mediaDevices.getUserMedia</code>
 * except that if no MediaStreamConstraints are provided, then bot audio and video
 * are requested.
 * @function getUserMedia
 * @param {MediaStreamConstraints} [constraints={audio:true,video:true}] - the
 *   MediaStreamConstraints object specifying what kind of MediaStream to
 *   request from the browser (by default both audio and video)
 * @returns Promise<MediaStream>
 */ function getUserMedia(constraints) {
    if (constraints === void 0) {
        constraints = {
            audio: true,
            video: true
        };
    }
    if (typeof navigator === 'object' && typeof navigator.mediaDevices === 'object' && typeof navigator.mediaDevices.getUserMedia === 'function') {
        return navigator.mediaDevices.getUserMedia(constraints);
    }
    return Promise.reject(new Error('getUserMedia is not supported'));
}
module.exports = getUserMedia; //# sourceMappingURL=getusermedia.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/mediastream.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals MediaStream */ 'use strict';
if (typeof MediaStream === 'function') {
    module.exports = MediaStream;
} else {
    module.exports = function MediaStream1() {
        throw new Error('MediaStream is not supported');
    };
} //# sourceMappingURL=mediastream.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/mediastreamtrack.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* global MediaStreamTrack */ 'use strict';
if (typeof MediaStreamTrack === 'function') {
    module.exports = MediaStreamTrack;
} else {
    module.exports = function MediaStreamTrack1() {
        throw new Error('MediaStreamTrack is not supported');
    };
} //# sourceMappingURL=mediastreamtrack.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcicecandidate.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* global RTCIceCandidate */ 'use strict';
if (typeof RTCIceCandidate === 'function') {
    module.exports = RTCIceCandidate;
} else {
    module.exports = function RTCIceCandidate1() {
        throw new Error('RTCIceCandidate is not supported');
    };
} //# sourceMappingURL=rtcicecandidate.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/chrome.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCSessionDescription */ 'use strict';
// This class wraps Chrome's RTCSessionDescription implementation. It provides
// one piece of functionality not currently present in Chrome, namely
//
//   1. Rollback support
//      https://bugs.chromium.org/p/webrtc/issues/detail?id=4676
//
var ChromeRTCSessionDescription = function() {
    function ChromeRTCSessionDescription(descriptionInitDict) {
        this.descriptionInitDict = descriptionInitDict;
        // If this constructor is called with an object with a .type property set to
        // "rollback", we should not call Chrome's RTCSessionDescription constructor,
        // because this would throw an RTCSdpType error.
        var description = descriptionInitDict && descriptionInitDict.type === 'rollback' ? null : new RTCSessionDescription(descriptionInitDict);
        Object.defineProperties(this, {
            _description: {
                get: function() {
                    return description;
                }
            }
        });
    }
    Object.defineProperty(ChromeRTCSessionDescription.prototype, "sdp", {
        get: function() {
            return this._description ? this._description.sdp : this.descriptionInitDict.sdp;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChromeRTCSessionDescription.prototype, "type", {
        get: function() {
            return this._description ? this._description.type : this.descriptionInitDict.type;
        },
        enumerable: false,
        configurable: true
    });
    return ChromeRTCSessionDescription;
}();
module.exports = ChromeRTCSessionDescription; //# sourceMappingURL=chrome.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/util/latch.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var defer = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").defer;
var states = {
    high: new Set([
        'low'
    ]),
    low: new Set([
        'high'
    ])
};
/**
 * Construct a {@link Latch}.
 * @class
 * @classdesc A {@link Latch} has two states ("high" and "low") and methods for
 * transitioning between them ({@link Latch#raise} and {@link Latch#lower}).
 * @param {string} [initialState="low"] - either "high" or "low"
 */ var Latch = function() {
    function Latch(initialState) {
        if (initialState === void 0) {
            initialState = 'low';
        }
        var state = initialState;
        Object.defineProperties(this, {
            _state: {
                set: function(_state) {
                    var _this = this;
                    if (state !== _state) {
                        state = _state;
                        var whenDeferreds = this._whenDeferreds.get(state);
                        whenDeferreds.forEach(function(deferred) {
                            return deferred.resolve(_this);
                        });
                        whenDeferreds.clear();
                    }
                },
                get: function() {
                    return state;
                }
            },
            _whenDeferreds: {
                value: new Map([
                    [
                        'high',
                        new Set()
                    ],
                    [
                        'low',
                        new Set()
                    ]
                ])
            }
        });
    }
    Object.defineProperty(Latch.prototype, "state", {
        get: function() {
            return this._state;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Transition to "low".
     * @returns {this}
     * @throws {Error}
     */ Latch.prototype.lower = function() {
        return this.transition('low');
    };
    /**
     * Transition to "high".
     * @returns {this}
     * @throws {Error}
     */ Latch.prototype.raise = function() {
        return this.transition('high');
    };
    /**
     * Transition to a new state.
     * @param {string} newState
     * @returns {this}
     * @throws {Error}
     */ Latch.prototype.transition = function(newState) {
        if (!states[this.state].has(newState)) {
            throw createUnreachableStateError(this.state, newState);
        }
        this._state = newState;
        return this;
    };
    /**
     * Return a Promise that resolves when the {@link Latch} transitions to
     * the specified state.
     * @param {string} state
     * @returns {Promise<this>}
     */ Latch.prototype.when = function(state) {
        if (this.state === state) {
            return Promise.resolve(this);
        }
        if (!states[this.state].has(state)) {
            return Promise.reject(createUnreachableStateError(this.state, state));
        }
        var deferred = defer();
        this._whenDeferreds.get(state).add(deferred);
        return deferred.promise;
    };
    return Latch;
}();
/**
   * Create an unreachable state Error.
   * @param {string} from - state to be transitioned from
   * @param {string} to - state to be transitioned to
   * @return {Error}
   */ function createUnreachableStateError(from, to) {
    return new Error("Cannot transition from \"" + from + "\" to \"" + to + "\"");
}
module.exports = Latch; //# sourceMappingURL=latch.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcrtpsender.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * RTCRtpSender shim.
 * @param {MediaStreamTrack} track
 * @property {MediaStreamTrack} track
 */ var RTCRtpSenderShim = function() {
    function RTCRtpSenderShim(track) {
        Object.defineProperties(this, {
            track: {
                enumerable: true,
                value: track,
                writable: true
            }
        });
    }
    return RTCRtpSenderShim;
}();
// NOTE(mmalavalli): Because of the way we will be using this shim, there
// are a couple of use cases that will not be covered:
//
// /* Case 1 */
// const sender = pc.addTrack(track);
// assert.equal(sender.track, track);
// pc.removeTrack(sender);
// assert.equal(sender.track, null); /* Error */
//
// /* Case 2 */
// const sender = pc.addTrack(track);
// const senders1 = new Set(pc.getSenders());
// assert(senders1.has(sender));
// pc.removeTrack(track);
// const senders2 = new Set(pc.getSenders());
// assert(senders2.has(sender)); /* Error */
//
// For now, since we only use senders for passing them to RTCPeerConnection#removeTrack(),
// we will omit handling these use cases for now, and revisit them when we start
// using the RTCRtpSender APIs.
module.exports = RTCRtpSenderShim; //# sourceMappingURL=rtcrtpsender.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/chrome.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCDataChannel, RTCPeerConnection, RTCSessionDescription */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var ChromeRTCSessionDescription = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/chrome.js [app-client] (ecmascript)");
var EventTarget = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventtarget.js [app-client] (ecmascript)");
var Latch = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/latch.js [app-client] (ecmascript)");
var MediaStream = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/mediastream.js [app-client] (ecmascript)");
var RTCRtpSenderShim = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcrtpsender.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)"), getSdpFormat = _a.getSdpFormat, updatePlanBTrackIdsToSSRCs = _a.updatePlanBTrackIdsToSSRCs, updateUnifiedPlanTrackIdsToSSRCs = _a.updateUnifiedPlanTrackIdsToSSRCs;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), delegateMethods = _b.delegateMethods, interceptEvent = _b.interceptEvent, isIOSChrome = _b.isIOSChrome, legacyPromise = _b.legacyPromise, proxyProperties = _b.proxyProperties;
var isUnifiedPlan = getSdpFormat() === 'unified';
// NOTE(mroberts): This class wraps Chrome's RTCPeerConnection implementation.
// It provides some functionality not currently present in Chrome, namely the
// abilities to
//
//   1. Rollback, per the workaround suggested here:
//      https://bugs.chromium.org/p/webrtc/issues/detail?id=5738#c3
//
//   2. Listen for track events, per the adapter.js workaround.
//
//   3. Set iceTransportPolicy.
//
var ChromeRTCPeerConnection = function(_super) {
    __extends(ChromeRTCPeerConnection, _super);
    function ChromeRTCPeerConnection(configuration, constraints) {
        if (configuration === void 0) {
            configuration = {};
        }
        var _this = _super.call(this) || this;
        var newConfiguration = Object.assign(configuration.iceTransportPolicy ? {
            iceTransports: configuration.iceTransportPolicy
        } : {}, configuration);
        interceptEvent(_this, 'datachannel');
        interceptEvent(_this, 'signalingstatechange');
        var sdpFormat = getSdpFormat(newConfiguration.sdpSemantics);
        var peerConnection = new RTCPeerConnection(newConfiguration, constraints);
        Object.defineProperties(_this, {
            _appliedTracksToSSRCs: {
                value: new Map(),
                writable: true
            },
            _localStream: {
                value: new MediaStream()
            },
            _peerConnection: {
                value: peerConnection
            },
            _pendingLocalOffer: {
                value: null,
                writable: true
            },
            _pendingRemoteOffer: {
                value: null,
                writable: true
            },
            _rolledBackTracksToSSRCs: {
                value: new Map(),
                writable: true
            },
            _sdpFormat: {
                value: sdpFormat
            },
            _senders: {
                value: new Map()
            },
            _signalingStateLatch: {
                value: new Latch()
            },
            _tracksToSSRCs: {
                value: new Map(),
                writable: true
            }
        });
        peerConnection.addEventListener('datachannel', function(event) {
            shimDataChannel(event.channel);
            _this.dispatchEvent(event);
        });
        peerConnection.addEventListener('signalingstatechange', function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            if (peerConnection.signalingState === 'stable') {
                _this._appliedTracksToSSRCs = new Map(_this._tracksToSSRCs);
            }
            if (!_this._pendingLocalOffer && !_this._pendingRemoteOffer) {
                _this.dispatchEvent.apply(_this, __spreadArray([], __read(args)));
            }
        });
        peerConnection.ontrack = function() {
        // NOTE(mroberts): adapter.js's "track" event shim only kicks off if we set
        // the ontrack property of the RTCPeerConnection.
        };
        if (typeof peerConnection.addTrack !== 'function') {
            peerConnection.addStream(_this._localStream);
        }
        proxyProperties(RTCPeerConnection.prototype, _this, peerConnection);
        return _this;
    }
    Object.defineProperty(ChromeRTCPeerConnection.prototype, "localDescription", {
        get: function() {
            return this._pendingLocalOffer ? this._pendingLocalOffer : this._peerConnection.localDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChromeRTCPeerConnection.prototype, "remoteDescription", {
        get: function() {
            return this._pendingRemoteOffer ? this._pendingRemoteOffer : this._peerConnection.remoteDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChromeRTCPeerConnection.prototype, "signalingState", {
        get: function() {
            if (this._pendingLocalOffer) {
                return 'have-local-offer';
            } else if (this._pendingRemoteOffer) {
                return 'have-remote-offer';
            }
            return this._peerConnection.signalingState;
        },
        enumerable: false,
        configurable: true
    });
    // NOTE(mmalavalli): This shim supports our limited case of adding
    // all MediaStreamTracks to one MediaStream. It has been implemented this
    // keeping in mind that this is to be maintained only until "addTrack" is
    // supported natively in Chrome.
    ChromeRTCPeerConnection.prototype.addTrack = function(track) {
        var _a;
        var rest = [];
        for(var _i = 1; _i < arguments.length; _i++){
            rest[_i - 1] = arguments[_i];
        }
        if (typeof this._peerConnection.addTrack === 'function') {
            return (_a = this._peerConnection).addTrack.apply(_a, __spreadArray([
                track
            ], __read(rest)));
        }
        if (this._peerConnection.signalingState === 'closed') {
            throw new Error("Cannot add MediaStreamTrack [" + track.id + ", \n        " + track.kind + "]: RTCPeerConnection is closed");
        }
        var sender = this._senders.get(track);
        if (sender && sender.track) {
            throw new Error("Cannot add MediaStreamTrack ['" + track.id + ", \n        " + track.kind + "]: RTCPeerConnection already has it");
        }
        this._peerConnection.removeStream(this._localStream);
        this._localStream.addTrack(track);
        this._peerConnection.addStream(this._localStream);
        sender = new RTCRtpSenderShim(track);
        this._senders.set(track, sender);
        return sender;
    };
    // NOTE(mmalavalli): This shim supports our limited case of removing
    // MediaStreamTracks from one MediaStream. It has been implemented this
    // keeping in mind that this is to be maintained only until "removeTrack" is
    // supported natively in Chrome.
    ChromeRTCPeerConnection.prototype.removeTrack = function(sender) {
        if (this._peerConnection.signalingState === 'closed') {
            throw new Error('Cannot remove MediaStreamTrack: RTCPeerConnection is closed');
        }
        if (typeof this._peerConnection.addTrack === 'function') {
            try {
                return this._peerConnection.removeTrack(sender);
            } catch (e) {
            // NOTE(mhuynh): Do nothing. In Chrome, will throw if a 'sender was not
            // created by this peer connection'. This behavior does not seem to be
            // spec compliant, so a temporary shim is introduced. A bug has been filed,
            // and is tracked here:
            // https://bugs.chromium.org/p/chromium/issues/detail?id=860853
            }
        } else {
            var track = sender.track;
            if (!track) {
                return;
            }
            sender = this._senders.get(track);
            if (sender && sender.track) {
                sender.track = null;
                this._peerConnection.removeStream(this._localStream);
                this._localStream.removeTrack(track);
                this._peerConnection.addStream(this._localStream);
            }
        }
    };
    ChromeRTCPeerConnection.prototype.getSenders = function() {
        if (typeof this._peerConnection.addTrack === 'function') {
            return this._peerConnection.getSenders();
        }
        return Array.from(this._senders.values());
    };
    ChromeRTCPeerConnection.prototype.addIceCandidate = function(candidate) {
        var _this = this;
        var rest = [];
        for(var _i = 1; _i < arguments.length; _i++){
            rest[_i - 1] = arguments[_i];
        }
        var promise;
        if (this.signalingState === 'have-remote-offer') {
            // NOTE(mroberts): Because the ChromeRTCPeerConnection simulates the
            // "have-remote-offer" signalingStates, we only want to invoke the true
            // addIceCandidates method when the remote description has been applied.
            promise = this._signalingStateLatch.when('low').then(function() {
                return _this._peerConnection.addIceCandidate(candidate);
            });
        } else {
            promise = this._peerConnection.addIceCandidate(candidate);
        }
        return rest.length > 0 ? legacyPromise.apply(void 0, __spreadArray([
            promise
        ], __read(rest))) : promise;
    };
    // NOTE(mroberts): The WebRTC spec does not specify that close should throw an
    // Error; however, in Chrome it does. We workaround this by checking the
    // signalingState manually.
    ChromeRTCPeerConnection.prototype.close = function() {
        if (this.signalingState !== 'closed') {
            this._pendingLocalOffer = null;
            this._pendingRemoteOffer = null;
            this._peerConnection.close();
        }
    };
    // NOTE(mroberts): Because we workaround Chrome's lack of rollback support by
    // "faking" setRemoteDescription, we cannot create an answer until we actually
    // apply the remote description. This means, once you call createAnswer, you
    // can no longer rollback. This is acceptable for our use case because we will
    // apply the newly-created answer almost immediately; however, this may be
    // unacceptable for other use cases.
    ChromeRTCPeerConnection.prototype.createAnswer = function() {
        var _this = this;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var promise;
        if (this._pendingRemoteOffer) {
            promise = this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then(function() {
                // NOTE(mroberts): The signalingStates between the ChromeRTCPeerConnection
                // and the underlying RTCPeerConnection implementation have converged. We
                // can unblock any pending calls to addIceCandidate now.
                _this._signalingStateLatch.lower();
                return _this._peerConnection.createAnswer();
            }).then(function(answer) {
                _this._pendingRemoteOffer = null;
                // NOTE(mmalavalli): If createAnswer() is called immediately after rolling back, then we no
                // longer need to retain the rolled back tracks to SSRCs Map.
                _this._rolledBackTracksToSSRCs.clear();
                return new ChromeRTCSessionDescription({
                    type: 'answer',
                    sdp: updateTrackIdsToSSRCs(_this._sdpFormat, _this._tracksToSSRCs, answer.sdp)
                });
            }, function(error) {
                _this._pendingRemoteOffer = null;
                throw error;
            });
        } else {
            promise = this._peerConnection.createAnswer().then(function(answer) {
                // NOTE(mmalavalli): If createAnswer() is called immediately after rolling back, then we no
                // longer need to retain the rolled back tracks to SSRCs Map.
                _this._rolledBackTracksToSSRCs.clear();
                return new ChromeRTCSessionDescription({
                    type: 'answer',
                    sdp: updateTrackIdsToSSRCs(_this._sdpFormat, _this._tracksToSSRCs, answer.sdp)
                });
            });
        }
        return args.length > 1 ? legacyPromise.apply(void 0, __spreadArray([
            promise
        ], __read(args))) : promise;
    };
    ChromeRTCPeerConnection.prototype.createOffer = function() {
        var _this = this;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _a = __read(args, 3), arg1 = _a[0], arg2 = _a[1], arg3 = _a[2];
        var options = arg3 || arg1 || {};
        if (isIOSChrome()) {
            // NOTE (joma): From SafariRTCPeerConnection in order to support iOS Chrome.
            if (options.offerToReceiveVideo && !this._audioTransceiver && !(isUnifiedPlan && hasReceiversForTracksOfKind(this, 'audio'))) {
                delete options.offerToReceiveAudio;
                try {
                    this._audioTransceiver = isUnifiedPlan ? this.addTransceiver('audio', {
                        direction: 'recvonly'
                    }) : this.addTransceiver('audio');
                } catch (e) {
                    return Promise.reject(e);
                }
            }
            if (options.offerToReceiveVideo && !this._videoTransceiver && !(isUnifiedPlan && hasReceiversForTracksOfKind(this, 'video'))) {
                delete options.offerToReceiveVideo;
                try {
                    this._videoTransceiver = isUnifiedPlan ? this.addTransceiver('video', {
                        direction: 'recvonly'
                    }) : this.addTransceiver('video');
                } catch (e) {
                    return Promise.reject(e);
                }
            }
        }
        var promise = this._peerConnection.createOffer(options).then(function(offer) {
            // NOTE(mmalavalli): If createOffer() is called immediately after rolling back, then we no
            // longer need to retain the rolled back tracks to SSRCs Map.
            _this._rolledBackTracksToSSRCs.clear();
            return new ChromeRTCSessionDescription({
                type: offer.type,
                sdp: updateTrackIdsToSSRCs(_this._sdpFormat, _this._tracksToSSRCs, offer.sdp)
            });
        });
        return args.length > 1 ? legacyPromise(promise, arg1, arg2) : promise;
    };
    ChromeRTCPeerConnection.prototype.createDataChannel = function(label, dataChannelDict) {
        dataChannelDict = shimDataChannelInit(dataChannelDict);
        var dataChannel = this._peerConnection.createDataChannel(label, dataChannelDict);
        shimDataChannel(dataChannel);
        return dataChannel;
    };
    ChromeRTCPeerConnection.prototype.setLocalDescription = function() {
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _a = __read(args, 3), description = _a[0], arg1 = _a[1], arg2 = _a[2];
        // NOTE(mmalavalli): If setLocalDescription() is called immediately after rolling back,
        // then we need to restore the rolled back tracks to SSRCs Map.
        if (this._rolledBackTracksToSSRCs.size > 0) {
            this._tracksToSSRCs = new Map(this._rolledBackTracksToSSRCs);
            this._rolledBackTracksToSSRCs.clear();
        }
        var promise = setDescription(this, true, description);
        return args.length > 1 ? legacyPromise(promise, arg1, arg2) : promise;
    };
    ChromeRTCPeerConnection.prototype.setRemoteDescription = function() {
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _a = __read(args, 3), description = _a[0], arg1 = _a[1], arg2 = _a[2];
        // NOTE(mmalavalli): If setRemoteDescription() is called immediately after rolling back,
        // then we no longer need to retain the rolled back tracks to SSRCs Map.
        this._rolledBackTracksToSSRCs.clear();
        var promise = setDescription(this, false, description);
        return args.length > 1 ? legacyPromise(promise, arg1, arg2) : promise;
    };
    return ChromeRTCPeerConnection;
}(EventTarget);
delegateMethods(RTCPeerConnection.prototype, ChromeRTCPeerConnection.prototype, '_peerConnection');
// NOTE(mroberts): We workaround Chrome's lack of rollback support, per the
// workaround suggested here: https://bugs.chromium.org/p/webrtc/issues/detail?id=5738#c3
// Namely, we "fake" setting the local or remote description and instead buffer
// it. If we receive or create an answer, then we will actually apply the
// description. Until we receive or create an answer, we will be able to
// "rollback" by simply discarding the buffer description.
function setDescription(peerConnection, local, description) {
    function setPendingLocalOffer(offer) {
        if (local) {
            peerConnection._pendingLocalOffer = offer;
        } else {
            peerConnection._pendingRemoteOffer = offer;
        }
    }
    function clearPendingLocalOffer() {
        if (local) {
            peerConnection._pendingLocalOffer = null;
        } else {
            peerConnection._pendingRemoteOffer = null;
        }
    }
    var pendingLocalOffer = local ? peerConnection._pendingLocalOffer : peerConnection._pendingRemoteOffer;
    var pendingRemoteOffer = local ? peerConnection._pendingRemoteOffer : peerConnection._pendingLocalOffer;
    var intermediateState = local ? 'have-local-offer' : 'have-remote-offer';
    var setLocalDescription = local ? 'setLocalDescription' : 'setRemoteDescription';
    var promise;
    if (!local && pendingRemoteOffer && description.type === 'answer') {
        promise = setRemoteAnswer(peerConnection, description);
    } else if (description.type === 'offer') {
        if (peerConnection.signalingState !== intermediateState && peerConnection.signalingState !== 'stable') {
            // NOTE(mroberts): Error message copied from Firefox.
            return Promise.reject(new Error("Cannot set " + (local ? 'local' : 'remote') + " offer in state " + peerConnection.signalingState));
        }
        // We need to save this local offer in case of a rollback. We also need to
        // check to see if the signalingState between the ChromeRTCPeerConnection
        // and the underlying RTCPeerConnection implementation are about to diverge.
        // If so, we need to ensure subsequent calls to addIceCandidate will block.
        if (!pendingLocalOffer && peerConnection._signalingStateLatch.state === 'low') {
            peerConnection._signalingStateLatch.raise();
        }
        var previousSignalingState = peerConnection.signalingState;
        setPendingLocalOffer(unwrap(description));
        promise = Promise.resolve();
        // Only dispatch a signalingstatechange event if we transitioned.
        if (peerConnection.signalingState !== previousSignalingState) {
            promise.then(function() {
                return peerConnection.dispatchEvent(new Event('signalingstatechange'));
            });
        }
    } else if (description.type === 'rollback') {
        if (peerConnection.signalingState !== intermediateState) {
            // NOTE(mroberts): Error message copied from Firefox.
            promise = Promise.reject(new Error("Cannot rollback " + (local ? 'local' : 'remote') + " description in " + peerConnection.signalingState));
        } else {
            // Reset the pending offer.
            clearPendingLocalOffer();
            // NOTE(mmalavalli): We store the rolled back tracks to SSRCs Map here in case
            // setLocalDescription() is called immediately after a rollback (without calling
            // createOffer() or createAnswer()), in which case this roll back is not due to a
            // glare scenario and this Map should be restored.
            peerConnection._rolledBackTracksToSSRCs = new Map(peerConnection._tracksToSSRCs);
            peerConnection._tracksToSSRCs = new Map(peerConnection._appliedTracksToSSRCs);
            promise = Promise.resolve();
            promise.then(function() {
                return peerConnection.dispatchEvent(new Event('signalingstatechange'));
            });
        }
    }
    return promise || peerConnection._peerConnection[setLocalDescription](unwrap(description));
}
function setRemoteAnswer(peerConnection, answer) {
    // Apply the pending local offer.
    var pendingLocalOffer = peerConnection._pendingLocalOffer;
    return peerConnection._peerConnection.setLocalDescription(pendingLocalOffer).then(function() {
        peerConnection._pendingLocalOffer = null;
        return peerConnection.setRemoteDescription(answer);
    }).then(function() {
        // NOTE(mroberts): The signalingStates between the ChromeRTCPeerConnection
        // and the underlying RTCPeerConnection implementation have converged. We
        // can unblock any pending calls to addIceCandidate now.
        peerConnection._signalingStateLatch.lower();
    });
}
/**
 * Whether a ChromeRTCPeerConnection has any RTCRtpReceivers(s) for the given
 * MediaStreamTrack kind.
 * @param {ChromeRTCPeerConnection} peerConnection
 * @param {'audio' | 'video'} kind
 * @returns {boolean}
 */ function hasReceiversForTracksOfKind(peerConnection, kind) {
    return !!peerConnection.getTransceivers().find(function(_a) {
        var _b = _a.receiver, receiver = _b === void 0 ? {} : _b;
        var _c = receiver.track, track = _c === void 0 ? {} : _c;
        return track.kind === kind;
    });
}
function unwrap(description) {
    if (description instanceof ChromeRTCSessionDescription) {
        if (description._description) {
            return description._description;
        }
    }
    return new RTCSessionDescription(description);
}
/**
 * Check whether or not we need to apply our maxPacketLifeTime shim. We are
 * pretty conservative: we'll only apply it if the legacy maxRetransmitTime
 * property is available _and_ the standard maxPacketLifeTime property is _not_
 * available (the thinking being that Chrome will land the standards-compliant
 * property).
 * @returns {boolean}
 */ function needsMaxPacketLifeTimeShim() {
    return 'maxRetransmitTime' in RTCDataChannel.prototype && !('maxPacketLifeTime' in RTCDataChannel.prototype);
}
/**
 * Shim an RTCDataChannelInit dictionary (if necessary). This function returns
 * a copy of the original RTCDataChannelInit.
 * @param {RTCDataChannelInit} dataChannelDict
 * @returns {RTCDataChannelInit}
 */ function shimDataChannelInit(dataChannelDict) {
    dataChannelDict = Object.assign({}, dataChannelDict);
    if (needsMaxPacketLifeTimeShim() && 'maxPacketLifeTime' in dataChannelDict) {
        dataChannelDict.maxRetransmitTime = dataChannelDict.maxPacketLifeTime;
    }
    return dataChannelDict;
}
/**
 * Shim an RTCDataChannel (if necessary). This function mutates the
 * RTCDataChannel.
 * @param {RTCDataChannel} dataChannel
 * @returns {RTCDataChannel}
 */ function shimDataChannel(dataChannel) {
    Object.defineProperty(dataChannel, 'maxRetransmits', {
        value: dataChannel.maxRetransmits === 65535 ? null : dataChannel.maxRetransmits
    });
    if (needsMaxPacketLifeTimeShim()) {
        // NOTE(mroberts): We can rename `maxRetransmitTime` to `maxPacketLifeTime`.
        //
        //   https://bugs.chromium.org/p/chromium/issues/detail?id=696681
        //
        Object.defineProperty(dataChannel, 'maxPacketLifeTime', {
            value: dataChannel.maxRetransmitTime === 65535 ? null : dataChannel.maxRetransmitTime
        });
    }
    return dataChannel;
}
/**
 * Update the mappings from MediaStreamTrack IDs to SSRCs as indicated by both
 * the Map from MediaStreamTrack IDs to SSRCs and the SDP itself. This method
 * ensures that SSRCs never change once announced.
 * @param {'planb'|'unified'} sdpFormat
 * @param {Map<string, Set<string>>} tracksToSSRCs
 * @param {string} sdp - an SDP whose format is determined by `sdpSemantics`
 * @returns {string} updatedSdp - updated SDP
 */ function updateTrackIdsToSSRCs(sdpFormat, tracksToSSRCs, sdp) {
    return sdpFormat === 'unified' ? updateUnifiedPlanTrackIdsToSSRCs(tracksToSSRCs, sdp) : updatePlanBTrackIdsToSSRCs(tracksToSSRCs, sdp);
}
module.exports = ChromeRTCPeerConnection; //# sourceMappingURL=chrome.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/firefox.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCSessionDescription */ 'use strict';
module.exports = RTCSessionDescription; //# sourceMappingURL=firefox.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/firefox.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCPeerConnection */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventTarget = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventtarget.js [app-client] (ecmascript)");
var FirefoxRTCSessionDescription = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/firefox.js [app-client] (ecmascript)");
var updateTracksToSSRCs = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)").updateUnifiedPlanTrackIdsToSSRCs;
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), delegateMethods = _a.delegateMethods, interceptEvent = _a.interceptEvent, legacyPromise = _a.legacyPromise, proxyProperties = _a.proxyProperties;
// NOTE(mroberts): This class wraps Firefox's RTCPeerConnection implementation.
// It provides some functionality not currently present in Firefox, namely the
// abilities to
//
//   1. Call setLocalDescription and setRemoteDescription with new offers in
//      signalingStates "have-local-offer" and "have-remote-offer",
//      respectively.
//
//   2. The ability to call createOffer in signalingState "have-local-offer".
//
// Both of these are implemented using rollbacks to workaround the following
// bug:
//
//   https://bugzilla.mozilla.org/show_bug.cgi?id=1072388
//
// We also provide a workaround for a bug where Firefox may change the
// previously-negotiated DTLS role in an answer, which breaks Chrome:
//
//     https://bugzilla.mozilla.org/show_bug.cgi?id=1240897
//
var FirefoxRTCPeerConnection = function(_super) {
    __extends(FirefoxRTCPeerConnection, _super);
    function FirefoxRTCPeerConnection(configuration) {
        var _this = _super.call(this) || this;
        interceptEvent(_this, 'signalingstatechange');
        /* eslint new-cap:0 */ var peerConnection = new RTCPeerConnection(configuration);
        Object.defineProperties(_this, {
            _initiallyNegotiatedDtlsRole: {
                value: null,
                writable: true
            },
            _isClosed: {
                value: false,
                writable: true
            },
            _peerConnection: {
                value: peerConnection
            },
            _rollingBack: {
                value: false,
                writable: true
            },
            _tracksToSSRCs: {
                value: new Map()
            },
            // NOTE(mmalavalli): Firefox throws a TypeError when the PeerConnection's
            // prototype's "peerIdentity" property is accessed. In order to overcome
            // this, we ignore this property while delegating methods.
            // Reference: https://bugzilla.mozilla.org/show_bug.cgi?id=1363815
            peerIdentity: {
                enumerable: true,
                value: Promise.resolve({
                    idp: '',
                    name: ''
                })
            }
        });
        var previousSignalingState;
        peerConnection.addEventListener('signalingstatechange', function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            if (!_this._rollingBack && _this.signalingState !== previousSignalingState) {
                previousSignalingState = _this.signalingState;
                // NOTE(mmalavalli): In Firefox, 'signalingstatechange' event is
                // triggered synchronously in the same tick after
                // RTCPeerConnection#close() is called. So we mimic Chrome's behavior
                // by triggering 'signalingstatechange' on the next tick.
                if (_this._isClosed) {
                    setTimeout(function() {
                        return _this.dispatchEvent.apply(_this, __spreadArray([], __read(args)));
                    });
                } else {
                    _this.dispatchEvent.apply(_this, __spreadArray([], __read(args)));
                }
            }
        });
        proxyProperties(RTCPeerConnection.prototype, _this, peerConnection);
        return _this;
    }
    Object.defineProperty(FirefoxRTCPeerConnection.prototype, "iceGatheringState", {
        get: function() {
            return this._isClosed ? 'complete' : this._peerConnection.iceGatheringState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(FirefoxRTCPeerConnection.prototype, "localDescription", {
        get: function() {
            return overwriteWithInitiallyNegotiatedDtlsRole(this._peerConnection.localDescription, this._initiallyNegotiatedDtlsRole);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(FirefoxRTCPeerConnection.prototype, "signalingState", {
        get: function() {
            return this._isClosed ? 'closed' : this._peerConnection.signalingState;
        },
        enumerable: false,
        configurable: true
    });
    FirefoxRTCPeerConnection.prototype.createAnswer = function() {
        var _this = this;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var promise;
        promise = this._peerConnection.createAnswer().then(function(answer) {
            saveInitiallyNegotiatedDtlsRole(_this, answer);
            return overwriteWithInitiallyNegotiatedDtlsRole(answer, _this._initiallyNegotiatedDtlsRole);
        });
        return typeof args[0] === 'function' ? legacyPromise.apply(void 0, __spreadArray([
            promise
        ], __read(args))) : promise;
    };
    // NOTE(mroberts): The WebRTC spec allows you to call createOffer from any
    // signalingState other than "closed"; however, Firefox has not yet implemented
    // this (https://bugzilla.mozilla.org/show_bug.cgi?id=1072388). We workaround
    // this by rolling back if we are in state "have-local-offer" or
    // "have-remote-offer". This is acceptable for our use case because we will
    // apply the newly-created offer almost immediately; however, this may be
    // unacceptable for other use cases.
    FirefoxRTCPeerConnection.prototype.createOffer = function() {
        var _this = this;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _a = __read(args, 3), arg1 = _a[0], arg2 = _a[1], arg3 = _a[2];
        var options = arg3 || arg1 || {};
        var promise;
        if (this.signalingState === 'have-local-offer' || this.signalingState === 'have-remote-offer') {
            var local = this.signalingState === 'have-local-offer';
            promise = rollback(this, local, function() {
                return _this.createOffer(options);
            });
        } else {
            promise = this._peerConnection.createOffer(options);
        }
        promise = promise.then(function(offer) {
            return new FirefoxRTCSessionDescription({
                type: offer.type,
                sdp: updateTracksToSSRCs(_this._tracksToSSRCs, offer.sdp)
            });
        });
        return args.length > 1 ? legacyPromise(promise, arg1, arg2) : promise;
    };
    // NOTE(mroberts): While Firefox will reject the Promise returned by
    // setLocalDescription when called from signalingState "have-local-offer" with
    // an answer, it still updates the .localDescription property. We workaround
    // this by explicitly handling this case.
    FirefoxRTCPeerConnection.prototype.setLocalDescription = function() {
        var _a;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _b = __read(args), description = _b[0], rest = _b.slice(1);
        var promise;
        if (description && description.type === 'answer' && this.signalingState === 'have-local-offer') {
            promise = Promise.reject(new Error('Cannot set local answer in state have-local-offer'));
        }
        if (promise) {
            return args.length > 1 ? legacyPromise.apply(void 0, __spreadArray([
                promise
            ], __read(rest))) : promise;
        }
        return (_a = this._peerConnection).setLocalDescription.apply(_a, __spreadArray([], __read(args)));
    };
    // NOTE(mroberts): The WebRTC spec allows you to call setRemoteDescription with
    // an offer multiple times in signalingState "have-remote-offer"; however,
    // Firefox has not yet implemented this (https://bugzilla.mozilla.org/show_bug.cgi?id=1072388).
    // We workaround this by rolling back if we are in state "have-remote-offer".
    // This is acceptable for our use case; however, this may be unacceptable for
    // other use cases.
    //
    // While Firefox will reject the Promise returned by setRemoteDescription when
    // called from signalingState "have-remote-offer" with an answer, it sill
    // updates the .remoteDescription property. We workaround this by explicitly
    // handling this case.
    FirefoxRTCPeerConnection.prototype.setRemoteDescription = function() {
        var _this = this;
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        var _a = __read(args), description = _a[0], rest = _a.slice(1);
        var promise;
        if (description && this.signalingState === 'have-remote-offer') {
            if (description.type === 'answer') {
                promise = Promise.reject(new Error('Cannot set remote answer in state have-remote-offer'));
            } else if (description.type === 'offer') {
                promise = rollback(this, false, function() {
                    return _this._peerConnection.setRemoteDescription(description);
                });
            }
        }
        if (!promise) {
            promise = this._peerConnection.setRemoteDescription(description);
        }
        promise = promise.then(function() {
            return saveInitiallyNegotiatedDtlsRole(_this, description, true);
        });
        return args.length > 1 ? legacyPromise.apply(void 0, __spreadArray([
            promise
        ], __read(rest))) : promise;
    };
    // NOTE(mroberts): The WebRTC spec specifies that the PeerConnection's internal
    // isClosed slot should immediately be set to true; however, in Firefox it
    // occurs in the next tick. We workaround this by tracking isClosed manually.
    FirefoxRTCPeerConnection.prototype.close = function() {
        if (this.signalingState !== 'closed') {
            this._isClosed = true;
            this._peerConnection.close();
        }
    };
    return FirefoxRTCPeerConnection;
}(EventTarget);
delegateMethods(RTCPeerConnection.prototype, FirefoxRTCPeerConnection.prototype, '_peerConnection');
function rollback(peerConnection, local, onceRolledBack) {
    var setLocalDescription = local ? 'setLocalDescription' : 'setRemoteDescription';
    peerConnection._rollingBack = true;
    return peerConnection._peerConnection[setLocalDescription](new FirefoxRTCSessionDescription({
        type: 'rollback'
    })).then(onceRolledBack).then(function(result) {
        peerConnection._rollingBack = false;
        return result;
    }, function(error) {
        peerConnection._rollingBack = false;
        throw error;
    });
}
/**
 * Extract the initially negotiated DTLS role out of an RTCSessionDescription's
 * sdp property and save it on the FirefoxRTCPeerConnection if and only if
 *
 *   1. A DTLS role was not already saved on the FirefoxRTCPeerConnection, and
 *   2. The description is an answer.
 *
 * @private
 * @param {FirefoxRTCPeerConnection} peerConnection
 * @param {RTCSessionDescription} description
 * @param {boolean} [remote=false] - if true, save the inverse of the DTLS role,
 *   e.g. "active" instead of "passive" and vice versa
 * @returns {undefined}
 */ function saveInitiallyNegotiatedDtlsRole(peerConnection, description, remote) {
    // NOTE(mroberts): JSEP specifies that offers always offer "actpass" as the
    // DTLS role. We need to inspect answers to figure out the negotiated DTLS
    // role.
    if (peerConnection._initiallyNegotiatedDtlsRole || description.type === 'offer') {
        return;
    }
    var match = description.sdp.match(/a=setup:([a-z]+)/);
    if (!match) {
        return;
    }
    var dtlsRole = match[1];
    peerConnection._initiallyNegotiatedDtlsRole = remote ? ({
        active: 'passive',
        passive: 'active'
    })[dtlsRole] : dtlsRole;
}
/**
 * Overwrite the DTLS role in the sdp property of an RTCSessionDescription if
 * and only if
 *
 *   1. The description is an answer, and
 *   2. A DTLS role is provided.
 *
 * @private
 * @param {RTCSessionDescription} [description]
 * @param {string} [dtlsRole] - one of "active" or "passive"
 * @returns {?RTCSessionDescription} description
 */ function overwriteWithInitiallyNegotiatedDtlsRole(description, dtlsRole) {
    if (description && description.type === 'answer' && dtlsRole) {
        return new FirefoxRTCSessionDescription({
            type: description.type,
            sdp: description.sdp.replace(/a=setup:[a-z]+/g, 'a=setup:' + dtlsRole)
        });
    }
    return description;
}
module.exports = FirefoxRTCPeerConnection; //# sourceMappingURL=firefox.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/safari.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCPeerConnection, RTCSessionDescription */ 'use strict';
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from) {
    for(var i = 0, il = from.length, j = to.length; i < il; i++, j++)to[j] = from[i];
    return to;
};
var EventTarget = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/eventtarget.js [app-client] (ecmascript)");
var Latch = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/latch.js [app-client] (ecmascript)");
var _a = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/sdp.js [app-client] (ecmascript)"), getSdpFormat = _a.getSdpFormat, updatePlanBTrackIdsToSSRCs = _a.updatePlanBTrackIdsToSSRCs, updateUnifiedPlanTrackIdsToSSRCs = _a.updateUnifiedPlanTrackIdsToSSRCs;
var _b = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)"), delegateMethods = _b.delegateMethods, interceptEvent = _b.interceptEvent, proxyProperties = _b.proxyProperties;
var isUnifiedPlan = getSdpFormat() === 'unified';
var updateTrackIdsToSSRCs = isUnifiedPlan ? updateUnifiedPlanTrackIdsToSSRCs : updatePlanBTrackIdsToSSRCs;
var SafariRTCPeerConnection = function(_super) {
    __extends(SafariRTCPeerConnection, _super);
    function SafariRTCPeerConnection(configuration) {
        var _this = _super.call(this) || this;
        interceptEvent(_this, 'datachannel');
        interceptEvent(_this, 'iceconnectionstatechange');
        interceptEvent(_this, 'signalingstatechange');
        interceptEvent(_this, 'track');
        var peerConnection = new RTCPeerConnection(configuration);
        Object.defineProperties(_this, {
            _appliedTracksToSSRCs: {
                value: new Map(),
                writable: true
            },
            _audioTransceiver: {
                value: null,
                writable: true
            },
            _isClosed: {
                value: false,
                writable: true
            },
            _peerConnection: {
                value: peerConnection
            },
            _pendingLocalOffer: {
                value: null,
                writable: true
            },
            _pendingRemoteOffer: {
                value: null,
                writable: true
            },
            _rolledBackTracksToSSRCs: {
                value: new Map(),
                writable: true
            },
            _signalingStateLatch: {
                value: new Latch()
            },
            _tracksToSSRCs: {
                value: new Map(),
                writable: true
            },
            _videoTransceiver: {
                value: null,
                writable: true
            }
        });
        peerConnection.addEventListener('datachannel', function(event) {
            shimDataChannel(event.channel);
            _this.dispatchEvent(event);
        });
        peerConnection.addEventListener('iceconnectionstatechange', function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            if (_this._isClosed) {
                return;
            }
            _this.dispatchEvent.apply(_this, __spreadArray([], __read(args)));
        });
        peerConnection.addEventListener('signalingstatechange', function() {
            var args = [];
            for(var _i = 0; _i < arguments.length; _i++){
                args[_i] = arguments[_i];
            }
            if (_this._isClosed) {
                return;
            }
            if (peerConnection.signalingState === 'stable') {
                _this._appliedTracksToSSRCs = new Map(_this._tracksToSSRCs);
            }
            if (!_this._pendingLocalOffer && !_this._pendingRemoteOffer) {
                _this.dispatchEvent.apply(_this, __spreadArray([], __read(args)));
            }
        });
        // NOTE(syerrapragada): This ensures that SafariRTCPeerConnection's "remoteDescription", when accessed
        // in an RTCTrackEvent listener, will point to the underlying RTCPeerConnection's
        // "remoteDescription". Before this fix, this was still pointing to "_pendingRemoteOffer"
        // even though a new remote RTCSessionDescription had already been applied.
        peerConnection.addEventListener('track', function(event) {
            _this._pendingRemoteOffer = null;
            _this.dispatchEvent(event);
        });
        proxyProperties(RTCPeerConnection.prototype, _this, peerConnection);
        return _this;
    }
    Object.defineProperty(SafariRTCPeerConnection.prototype, "localDescription", {
        get: function() {
            return this._pendingLocalOffer || this._peerConnection.localDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SafariRTCPeerConnection.prototype, "iceConnectionState", {
        get: function() {
            return this._isClosed ? 'closed' : this._peerConnection.iceConnectionState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SafariRTCPeerConnection.prototype, "iceGatheringState", {
        get: function() {
            return this._isClosed ? 'complete' : this._peerConnection.iceGatheringState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SafariRTCPeerConnection.prototype, "remoteDescription", {
        get: function() {
            return this._pendingRemoteOffer || this._peerConnection.remoteDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SafariRTCPeerConnection.prototype, "signalingState", {
        get: function() {
            if (this._isClosed) {
                return 'closed';
            } else if (this._pendingLocalOffer) {
                return 'have-local-offer';
            } else if (this._pendingRemoteOffer) {
                return 'have-remote-offer';
            }
            return this._peerConnection.signalingState;
        },
        enumerable: false,
        configurable: true
    });
    SafariRTCPeerConnection.prototype.addIceCandidate = function(candidate) {
        var _this = this;
        if (this.signalingState === 'have-remote-offer') {
            return this._signalingStateLatch.when('low').then(function() {
                return _this._peerConnection.addIceCandidate(candidate);
            });
        }
        return this._peerConnection.addIceCandidate(candidate);
    };
    SafariRTCPeerConnection.prototype.createOffer = function(options) {
        var _this = this;
        options = Object.assign({}, options);
        // NOTE(mroberts): In general, this is not the way to do this; however, it's
        // good enough for our application.
        if (options.offerToReceiveVideo && !this._audioTransceiver && !(isUnifiedPlan && hasReceiversForTracksOfKind(this, 'audio'))) {
            delete options.offerToReceiveAudio;
            try {
                this._audioTransceiver = isUnifiedPlan ? this.addTransceiver('audio', {
                    direction: 'recvonly'
                }) : this.addTransceiver('audio');
            } catch (e) {
                return Promise.reject(e);
            }
        }
        if (options.offerToReceiveVideo && !this._videoTransceiver && !(isUnifiedPlan && hasReceiversForTracksOfKind(this, 'video'))) {
            delete options.offerToReceiveVideo;
            try {
                this._videoTransceiver = isUnifiedPlan ? this.addTransceiver('video', {
                    direction: 'recvonly'
                }) : this.addTransceiver('video');
            } catch (e) {
                return Promise.reject(e);
            }
        }
        return this._peerConnection.createOffer(options).then(function(offer) {
            // NOTE(mmalavalli): If createOffer() is called immediately after rolling back,
            // then we no longer need to retain the rolled back tracks to SSRCs Map.
            _this._rolledBackTracksToSSRCs.clear();
            return new RTCSessionDescription({
                type: offer.type,
                sdp: updateTrackIdsToSSRCs(_this._tracksToSSRCs, offer.sdp)
            });
        });
    };
    SafariRTCPeerConnection.prototype.createAnswer = function(options) {
        var _this = this;
        if (this._pendingRemoteOffer) {
            return this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then(function() {
                _this._signalingStateLatch.lower();
                return _this._peerConnection.createAnswer();
            }).then(function(answer) {
                _this._pendingRemoteOffer = null;
                // NOTE(mmalavalli): If createAnswer() is called immediately after rolling back, then we no
                // longer need to retain the rolled back tracks to SSRCs Map.
                _this._rolledBackTracksToSSRCs.clear();
                return isUnifiedPlan ? new RTCSessionDescription({
                    type: answer.type,
                    sdp: updateTrackIdsToSSRCs(_this._tracksToSSRCs, answer.sdp)
                }) : answer;
            }, function(error) {
                _this._pendingRemoteOffer = null;
                throw error;
            });
        }
        return this._peerConnection.createAnswer(options).then(function(answer) {
            // NOTE(mmalavalli): If createAnswer() is called immediately after rolling back, then we no
            // longer need to retain the rolled back tracks to SSRCs Map.
            _this._rolledBackTracksToSSRCs.clear();
            return isUnifiedPlan ? new RTCSessionDescription({
                type: answer.type,
                sdp: updateTrackIdsToSSRCs(_this._tracksToSSRCs, answer.sdp)
            }) : answer;
        });
    };
    SafariRTCPeerConnection.prototype.createDataChannel = function(label, dataChannelDict) {
        var dataChannel = this._peerConnection.createDataChannel(label, dataChannelDict);
        shimDataChannel(dataChannel);
        return dataChannel;
    };
    SafariRTCPeerConnection.prototype.removeTrack = function(sender) {
        sender.replaceTrack(null);
        this._peerConnection.removeTrack(sender);
    };
    SafariRTCPeerConnection.prototype.setLocalDescription = function(description) {
        // NOTE(mmalavalli): If setLocalDescription() is called immediately after rolling back,
        // then we need to restore the rolled back tracks to SSRCs Map.
        if (this._rolledBackTracksToSSRCs.size > 0) {
            this._tracksToSSRCs = new Map(this._rolledBackTracksToSSRCs);
            this._rolledBackTracksToSSRCs.clear();
        }
        return setDescription(this, true, description);
    };
    SafariRTCPeerConnection.prototype.setRemoteDescription = function(description) {
        // NOTE(mmalavalli): If setRemoteDescription() is called immediately after rolling back,
        // then we no longer need to retain the rolled back tracks to SSRCs Map.
        this._rolledBackTracksToSSRCs.clear();
        return setDescription(this, false, description);
    };
    SafariRTCPeerConnection.prototype.close = function() {
        var _this = this;
        if (this._isClosed) {
            return;
        }
        this._isClosed = true;
        this._peerConnection.close();
        setTimeout(function() {
            _this.dispatchEvent(new Event('iceconnectionstatechange'));
            _this.dispatchEvent(new Event('signalingstatechange'));
        });
    };
    return SafariRTCPeerConnection;
}(EventTarget);
delegateMethods(RTCPeerConnection.prototype, SafariRTCPeerConnection.prototype, '_peerConnection');
function setDescription(peerConnection, local, description) {
    function setPendingLocalOffer(offer) {
        if (local) {
            peerConnection._pendingLocalOffer = offer;
        } else {
            peerConnection._pendingRemoteOffer = offer;
        }
    }
    function clearPendingLocalOffer() {
        if (local) {
            peerConnection._pendingLocalOffer = null;
        } else {
            peerConnection._pendingRemoteOffer = null;
        }
    }
    var pendingLocalOffer = local ? peerConnection._pendingLocalOffer : peerConnection._pendingRemoteOffer;
    var pendingRemoteOffer = local ? peerConnection._pendingRemoteOffer : peerConnection._pendingLocalOffer;
    var intermediateState = local ? 'have-local-offer' : 'have-remote-offer';
    var setLocalDescription = local ? 'setLocalDescription' : 'setRemoteDescription';
    if (!local && pendingRemoteOffer && description.type === 'answer') {
        return setRemoteAnswer(peerConnection, description);
    } else if (description.type === 'offer') {
        if (peerConnection.signalingState !== intermediateState && peerConnection.signalingState !== 'stable') {
            return Promise.reject(new Error("Cannot set " + (local ? 'local' : 'remote') + "\n        offer in state " + peerConnection.signalingState));
        }
        if (!pendingLocalOffer && peerConnection._signalingStateLatch.state === 'low') {
            peerConnection._signalingStateLatch.raise();
        }
        var previousSignalingState = peerConnection.signalingState;
        setPendingLocalOffer(description);
        // Only dispatch a signalingstatechange event if we transitioned.
        if (peerConnection.signalingState !== previousSignalingState) {
            return Promise.resolve().then(function() {
                return peerConnection.dispatchEvent(new Event('signalingstatechange'));
            });
        }
        return Promise.resolve();
    } else if (description.type === 'rollback') {
        if (peerConnection.signalingState !== intermediateState) {
            return Promise.reject(new Error("Cannot rollback \n        " + (local ? 'local' : 'remote') + " description in " + peerConnection.signalingState));
        }
        clearPendingLocalOffer();
        // NOTE(mmalavalli): We store the rolled back tracks to SSRCs Map here in case
        // setLocalDescription() is called immediately aftera rollback (without calling
        // createOffer() or createAnswer()), in which case this roll back is not due to
        // a glare scenario and this Map should be restored.
        peerConnection._rolledBackTracksToSSRCs = new Map(peerConnection._tracksToSSRCs);
        peerConnection._tracksToSSRCs = new Map(peerConnection._appliedTracksToSSRCs);
        return Promise.resolve().then(function() {
            return peerConnection.dispatchEvent(new Event('signalingstatechange'));
        });
    }
    return peerConnection._peerConnection[setLocalDescription](description);
}
function setRemoteAnswer(peerConnection, answer) {
    var pendingLocalOffer = peerConnection._pendingLocalOffer;
    return peerConnection._peerConnection.setLocalDescription(pendingLocalOffer).then(function() {
        peerConnection._pendingLocalOffer = null;
        return peerConnection.setRemoteDescription(answer);
    }).then(function() {
        return peerConnection._signalingStateLatch.lower();
    });
}
/**
 * Whether a SafariRTCPeerConnection has any RTCRtpReceivers(s) for the given
 * MediaStreamTrack kind.
 * @param {SafariRTCPeerConnection} peerConnection
 * @param {'audio' | 'video'} kind
 * @returns {boolean}
 */ function hasReceiversForTracksOfKind(peerConnection, kind) {
    return !!peerConnection.getTransceivers().find(function(_a) {
        var _b = _a.receiver, receiver = _b === void 0 ? {} : _b;
        var _c = receiver.track, track = _c === void 0 ? {} : _c;
        return track.kind === kind;
    });
}
/**
 * Shim an RTCDataChannel. This function mutates the RTCDataChannel.
 * @param {RTCDataChannel} dataChannel
 * @returns {RTCDataChannel}
 */ function shimDataChannel(dataChannel) {
    return Object.defineProperties(dataChannel, {
        maxPacketLifeTime: {
            value: dataChannel.maxPacketLifeTime === 65535 ? null : dataChannel.maxPacketLifeTime
        },
        maxRetransmits: {
            value: dataChannel.maxRetransmits === 65535 ? null : dataChannel.maxRetransmits
        }
    });
}
module.exports = SafariRTCPeerConnection; //# sourceMappingURL=safari.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
if (typeof RTCPeerConnection === 'function') {
    var guessBrowser = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").guessBrowser;
    switch(guessBrowser()){
        case 'chrome':
            module.exports = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/chrome.js [app-client] (ecmascript)");
            break;
        case 'firefox':
            module.exports = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/firefox.js [app-client] (ecmascript)");
            break;
        case 'safari':
            module.exports = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/safari.js [app-client] (ecmascript)");
            break;
        default:
            module.exports = RTCPeerConnection;
            break;
    }
} else {
    module.exports = function RTCPeerConnection1() {
        throw new Error('RTCPeerConnection is not supported');
    };
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/* globals RTCSessionDescription */ 'use strict';
if (typeof RTCSessionDescription === 'function') {
    var guessBrowser = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/util/index.js [app-client] (ecmascript)").guessBrowser;
    switch(guessBrowser()){
        case 'chrome':
            module.exports = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/chrome.js [app-client] (ecmascript)");
            break;
        case 'firefox':
            module.exports = __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/firefox.js [app-client] (ecmascript)");
            break;
        default:
            module.exports = RTCSessionDescription;
            break;
    }
} else {
    module.exports = function RTCSessionDescription1() {
        throw new Error('RTCSessionDescription is not supported');
    };
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/twilio-video/es5/webrtc/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
var WebRTC = {};
Object.defineProperties(WebRTC, {
    getStats: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/getstats.js [app-client] (ecmascript)")
    },
    getUserMedia: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/getusermedia.js [app-client] (ecmascript)")
    },
    MediaStream: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/mediastream.js [app-client] (ecmascript)")
    },
    MediaStreamTrack: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/mediastreamtrack.js [app-client] (ecmascript)")
    },
    RTCIceCandidate: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcicecandidate.js [app-client] (ecmascript)")
    },
    RTCPeerConnection: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcpeerconnection/index.js [app-client] (ecmascript)")
    },
    RTCSessionDescription: {
        enumerable: true,
        value: __turbopack_context__.r("[project]/node_modules/twilio-video/es5/webrtc/rtcsessiondescription/index.js [app-client] (ecmascript)")
    }
});
module.exports = WebRTC; //# sourceMappingURL=index.js.map
}}),
}]);

//# sourceMappingURL=node_modules_twilio-video_es5_webrtc_bb2510ae._.js.map