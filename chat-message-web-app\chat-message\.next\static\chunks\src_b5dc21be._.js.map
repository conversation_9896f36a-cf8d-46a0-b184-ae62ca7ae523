{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/types/chat.ts"], "sourcesContent": ["export interface ParticipantInfoDetailResponse {\r\n    userId: string;\r\n    username: string;\r\n    avatar: string | null;\r\n}\r\n\r\nexport enum ConversationType {\r\n    PRIVATE = 'PRIVATE',\r\n    GROUP = 'GROUP'\r\n}\r\n\r\nexport interface ConversationCreationRequest {\r\n    participantIds: string[];\r\n    conversationType: ConversationType;\r\n    conversationName?: string;\r\n    conversationAvatar?: string;\r\n}\r\n\r\nexport interface ConversationCreationResponse {\r\n    id: string;\r\n    conversationType: \"PRIVATE\" | \"GROUP\";\r\n    participantHash: string;\r\n    conversationAvatar: string | null;\r\n    conversationName: string;\r\n    participantInfo: ParticipantInfoDetailResponse[];\r\n    createdAt: string;\r\n}\r\n\r\nexport interface ChatMessage {\r\n    id: string;\r\n    tempId?: string;\r\n    conversationId: string;\r\n    me: boolean;\r\n    username?: string; // Backend field for sender username\r\n    content: string;\r\n    status: \"SENDING\" | \"SENT\";\r\n    createdAt: string;\r\n    read?: boolean; // Backend uses 'read' not 'isRead'\r\n    mediaUrl?: string[]; // Keep for backward compatibility\r\n    mediaAttachments?: MediaAttachment[]; // New field from backend\r\n    messageType?: \"TEXT\" | \"FILE\";\r\n}\r\n\r\nimport { MediaAttachment, MessageType } from './file';\r\n\r\nexport interface ChatRequest {\r\n    conversationId: string;\r\n    sender?: string;\r\n    content: string;\r\n    tempId?: string; // Optional - BE will generate if not provided\r\n    mediaAttachments?: MediaAttachment[];\r\n    messageType: MessageType;\r\n}\r\n\r\nexport interface ApiResponse<T> {\r\n    code: number;\r\n    data: T;\r\n    message?: string;\r\n}\r\n\r\nexport interface PageResponse<T> {\r\n    currentPages: number;\r\n    pageSizes: number;\r\n    totalPages: number;\r\n    totalElements: number;\r\n    data: T[];\r\n}\r\n\r\nexport interface ConnectionStatus {\r\n    isConnected: boolean;\r\n    reconnectAttempts: number;\r\n}\r\n"], "names": [], "mappings": ";;;AAMO,IAAA,AAAK,0CAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/api/axios.ts"], "sourcesContent": ["import axios, { AxiosResponse, AxiosError, AxiosProgressEvent } from \"axios\";\r\nimport { ApiResponse, ConversationCreationResponse, ChatMessage, ParticipantInfoDetailResponse, PageResponse, ConversationType } from \"@/types/chat\";\r\nimport { FileMetaDataResponse, UploadProgress } from \"@/types/file\";\r\nimport { TwilioAccessToken, VideoCallInvitation } from \"@/types/video\";\r\n\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8080\";\r\n\r\nconst apiClient = axios.create({\r\n    baseURL: API_BASE_URL,\r\n    timeout: 10000,\r\n    headers: {\r\n        'Content-Type': 'application/json',\r\n    },\r\n});\r\n\r\napiClient.interceptors.request.use(\r\n    (config) => {\r\n        const token = localStorage.getItem(\"accessToken\");\r\n        if (token) {\r\n            config.headers[\"Authorization\"] = `Bearer ${token}`;\r\n        }\r\n        return config;\r\n    },\r\n    (error: AxiosError) => {\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\napiClient.interceptors.response.use(\r\n    (response: AxiosResponse) => response,\r\n    (error: AxiosError) => {\r\n        if (error.response?.status === 401) {\r\n            localStorage.removeItem(\"accessToken\");\r\n            window.location.href = \"/auth/login\";\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\nexport class ApiService {\r\n    static async getConversations(): Promise<ConversationCreationResponse[]> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<ConversationCreationResponse[]>>(\"/conversations\");\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n\r\n\r\n    static async getMessages(conversationId: string, page: number = 1, size: number = 15): Promise<PageResponse<ChatMessage>> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<PageResponse<ChatMessage>>>(\r\n                `/chat/${conversationId}?page=${page}&size=${size}`\r\n            );\r\n\r\n            const pageData = response.data.data;\r\n\r\n            if (!pageData) {\r\n                return {\r\n                    currentPages: 1,\r\n                    pageSizes: size,\r\n                    totalPages: 0,\r\n                    totalElements: 0,\r\n                    data: []\r\n                };\r\n            }\r\n\r\n            return pageData;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async searchUsers(username: string): Promise<ParticipantInfoDetailResponse[]> {\r\n        try {\r\n            const response = await apiClient.get<ApiResponse<ParticipantInfoDetailResponse[]>>(\r\n                `/users?username=${encodeURIComponent(username)}`\r\n            );\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async createConversation(\r\n        participantIds: string[],\r\n        conversationType: ConversationType = ConversationType.PRIVATE,\r\n        conversationName?: string,\r\n        conversationAvatar?: string\r\n    ): Promise<ConversationCreationResponse> {\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<ConversationCreationResponse>>(\"/conversations\", {\r\n                participantIds,\r\n                conversationType,\r\n                conversationName,\r\n                conversationAvatar\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async deleteConversation(conversationId: string): Promise<void> {\r\n        try {\r\n            await apiClient.delete<ApiResponse<void>>(`/conversations/${conversationId}`);\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async updateFcmToken(fcmToken: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(\"/users/register-fcm-token\", { fcmToken });\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async markAsRead(conversationId: string, messageId: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(`/chat/${conversationId}/read/${messageId}`);\r\n        } catch (error) {\r\n            console.error('❌ Failed to mark as read via HTTP:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async login(email: string, password: string): Promise<{ accessToken: string; refreshToken: string }> {\r\n        try {\r\n            const response = await apiClient.post(\"/auth/sign-in\", {\r\n                email,\r\n                password\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async register(email: string, username: string, password: string): Promise<void> {\r\n        try {\r\n            await apiClient.post(\"/users\", {\r\n                email,\r\n                username,\r\n                password\r\n            });\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesSync(\r\n        files: File[],\r\n        onProgress?: (progressEvent: AxiosProgressEvent) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        const formData = new FormData();\r\n\r\n        files.forEach((file) => {\r\n            formData.append('files', file);\r\n        });\r\n\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<FileMetaDataResponse[]>>(\r\n                '/files/upload-media-sync',\r\n                formData,\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'multipart/form-data',\r\n                    },\r\n                    onUploadProgress: onProgress\r\n                }\r\n            );\r\n\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesSyncWithProgress(\r\n        files: File[],\r\n        onProgress?: (progress: UploadProgress[]) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        try {\r\n            const result = await this.uploadFilesSync(files, (progressEvent) => {\r\n                if (onProgress && progressEvent.total) {\r\n                    const progress = Math.round(\r\n                        (progressEvent.loaded * 100) / progressEvent.total\r\n                    );\r\n\r\n                    // Create progress for all files (simplified for sync upload)\r\n                    const progressArray: UploadProgress[] = files.map((file, index) => ({\r\n                        fileId: `${file.name}-${index}`,\r\n                        progress: progress,\r\n                        status: progress === 100 ? 'completed' : 'uploading'\r\n                    }));\r\n\r\n                    onProgress(progressArray);\r\n                }\r\n            });\r\n\r\n            return result;\r\n        } catch (error) {\r\n            console.error('File upload error:', error);\r\n            throw new Error('Upload failed');\r\n        }\r\n    }\r\n\r\n    static async uploadFilesAsync(\r\n        files: File[],\r\n        onProgress?: (progressEvent: AxiosProgressEvent) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        const formData = new FormData();\r\n\r\n        files.forEach((file) => {\r\n            formData.append('files', file);\r\n        });\r\n\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<FileMetaDataResponse[]>>(\r\n                '/files/upload-media-async',\r\n                formData,\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'multipart/form-data',\r\n                    },\r\n                    onUploadProgress: onProgress\r\n                }\r\n            );\r\n\r\n            return response.data.data || [];\r\n        } catch (error) {\r\n            console.error('Error uploading files async:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async uploadFilesAsyncWithProgress(\r\n        files: File[],\r\n        onProgress?: (progress: UploadProgress[]) => void\r\n    ): Promise<FileMetaDataResponse[]> {\r\n        try {\r\n            const result = await this.uploadFilesAsync(files, (progressEvent) => {\r\n                if (onProgress && progressEvent.total) {\r\n                    const progress = Math.round(\r\n                        (progressEvent.loaded * 100) / progressEvent.total\r\n                    );\r\n\r\n                    const progressArray: UploadProgress[] = files.map((file, index) => ({\r\n                        fileId: `${file.name}-${index}`,\r\n                        progress: progress,\r\n                        status: progress === 100 ? 'completed' : 'uploading'\r\n                    }));\r\n\r\n                    onProgress(progressArray);\r\n                }\r\n            });\r\n\r\n            return result;\r\n        } catch (error) {\r\n            console.error('File upload error:', error);\r\n\r\n            // Preserve original error message\r\n            if (error instanceof Error) {\r\n                throw error;\r\n            }\r\n\r\n            // For axios errors, extract meaningful message\r\n            if (error && typeof error === 'object' && 'response' in error) {\r\n                const axiosError = error as any;\r\n                const message = axiosError.response?.data?.message ||\r\n                    axiosError.response?.data?.error ||\r\n                    axiosError.message ||\r\n                    'Upload failed';\r\n                throw new Error(message);\r\n            }\r\n\r\n            throw new Error('Upload failed');\r\n        }\r\n    }\r\n\r\n    static validateFile(file: File): { isValid: boolean; error?: string } {\r\n        const maxSize = 100 * 1024 * 1024; // 100MB\r\n\r\n        if (file.size > maxSize) {\r\n            return {\r\n                isValid: false,\r\n                error: `File \"${file.name}\" exceeds the 100MB size limit (${this.formatFileSize(file.size)}). Please choose a smaller file.`\r\n            };\r\n        }\r\n\r\n        const allowedTypes = [\r\n            // Images\r\n            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff',\r\n            // Videos\r\n            'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv',\r\n            // Audio\r\n            'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac', 'audio/m4a', 'audio/wma',\r\n            // Documents\r\n            'application/pdf', 'application/msword',\r\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n            'application/rtf', 'application/vnd.oasis.opendocument.text',\r\n            // Spreadsheets\r\n            'application/vnd.ms-excel',\r\n            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n            'application/vnd.oasis.opendocument.spreadsheet',\r\n            // Presentations\r\n            'application/vnd.ms-powerpoint',\r\n            'application/vnd.openxmlformats-officedocument.presentationml.presentation',\r\n            'application/vnd.oasis.opendocument.presentation',\r\n            // Archives\r\n            'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip',\r\n            // Text\r\n            'text/plain', 'text/csv', 'text/html', 'text/css', 'text/javascript', 'text/xml', 'application/json', 'text/markdown'\r\n        ];\r\n\r\n        if (!allowedTypes.includes(file.type)) {\r\n            const error = `🚫 File \"${file.name}\" format is not supported. Please choose a different file type.`;\r\n            return {\r\n                isValid: false,\r\n                error\r\n            };\r\n        }\r\n\r\n        return { isValid: true };\r\n    }\r\n\r\n    static validateFiles(files: File[]): { isValid: boolean; errors: string[] } {\r\n        const maxFiles = 10;\r\n        const errors: string[] = [];\r\n\r\n        if (files.length > maxFiles) {\r\n            errors.push(`📂 Maximum ${maxFiles} files allowed per message. Please select fewer files.`);\r\n        }\r\n\r\n        files.forEach((file, index) => {\r\n            const validation = this.validateFile(file);\r\n            if (!validation.isValid) {\r\n                errors.push(validation.error || `❌ File ${index + 1} (${file.name}): Invalid file`);\r\n            }\r\n        });\r\n\r\n        return {\r\n            isValid: errors.length === 0,\r\n            errors\r\n        };\r\n    }\r\n\r\n    static formatFileSize(bytes: number): string {\r\n        if (bytes === 0) return '0 Bytes';\r\n\r\n        const k = 1024;\r\n        const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    }\r\n\r\n    static createImagePreview(file: File): Promise<string> {\r\n        return new Promise((resolve, reject) => {\r\n            if (!file.type.startsWith('image/')) {\r\n                reject(new Error('File is not an image'));\r\n                return;\r\n            }\r\n\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                resolve(e.target?.result as string);\r\n            };\r\n            reader.onerror = () => {\r\n                reject(new Error('Failed to read file'));\r\n            };\r\n            reader.readAsDataURL(file);\r\n        });\r\n    }\r\n\r\n    // Twilio Video API methods\r\n    static async getTwilioAccessToken(conversationId: string): Promise<TwilioAccessToken> {\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<TwilioAccessToken>>('/video/token', {\r\n                conversationId\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            console.error('Failed to get Twilio access token:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async createVideoCallInvitation(conversationId: string, participantIds: string[]): Promise<VideoCallInvitation> {\r\n        try {\r\n            const response = await apiClient.post<ApiResponse<VideoCallInvitation>>('/video/invite', {\r\n                conversationId,\r\n                participantIds\r\n            });\r\n            return response.data.data;\r\n        } catch (error) {\r\n            console.error('Failed to create video call invitation:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async respondToVideoCallInvitation(invitationId: string, response: 'accept' | 'decline'): Promise<void> {\r\n        try {\r\n            await apiClient.post(`/video/invite/${invitationId}/respond`, {\r\n                response\r\n            });\r\n        } catch (error) {\r\n            console.error('Failed to respond to video call invitation:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    static async endVideoCall(roomName: string): Promise<void> {\r\n        try {\r\n            await apiClient.post('/video/end', {\r\n                roomName\r\n            });\r\n        } catch (error) {\r\n            console.error('Failed to end video call:', error);\r\n            throw error;\r\n        }\r\n    }\r\n}\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;;AAMqB;AANrB;AACA;;;AAKA,MAAM,eAAe,6DAAwC;AAE7D,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,SAAS;IACT,SAAS;IACT,SAAS;QACL,gBAAgB;IACpB;AACJ;AAEA,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC;IACG,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACP,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvD;IACA,OAAO;AACX,GACA,CAAC;IACG,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGJ,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC/B,CAAC,WAA4B,UAC7B,CAAC;IACG,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAChC,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IAC3B;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGG,MAAM;IACT,aAAa,mBAA4D;QACrE,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAA8C;YAClF,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAIA,aAAa,YAAY,cAAsB,EAAE,OAAe,CAAC,EAAE,OAAe,EAAE,EAAsC;QACtH,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAChC,CAAC,MAAM,EAAE,eAAe,MAAM,EAAE,KAAK,MAAM,EAAE,MAAM;YAGvD,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;YAEnC,IAAI,CAAC,UAAU;gBACX,OAAO;oBACH,cAAc;oBACd,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,MAAM,EAAE;gBACZ;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,YAAY,QAAgB,EAA4C;QACjF,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,GAAG,CAChC,CAAC,gBAAgB,EAAE,mBAAmB,WAAW;YAErD,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,mBACT,cAAwB,EACxB,mBAAqC,uHAAA,CAAA,mBAAgB,CAAC,OAAO,EAC7D,gBAAyB,EACzB,kBAA2B,EACU;QACrC,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAA4C,kBAAkB;gBAC/F;gBACA;gBACA;gBACA;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,mBAAmB,cAAsB,EAAiB;QACnE,IAAI;YACA,MAAM,UAAU,MAAM,CAAoB,CAAC,eAAe,EAAE,gBAAgB;QAChF,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,eAAe,QAAgB,EAAiB;QACzD,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,6BAA6B;gBAAE;YAAS;QACjE,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,WAAW,cAAsB,EAAE,SAAiB,EAAiB;QAC9E,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe,MAAM,EAAE,WAAW;QACpE,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACV;IACJ;IAEA,aAAa,MAAM,KAAa,EAAE,QAAgB,EAA0D;QACxG,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,iBAAiB;gBACnD;gBACA;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,SAAS,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAiB;QACpF,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,UAAU;gBAC3B;gBACA;gBACA;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,gBACT,KAAa,EACb,UAAwD,EACzB;QAC/B,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAC;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CACjC,4BACA,UACA;gBACI,SAAS;oBACL,gBAAgB;gBACpB;gBACA,kBAAkB;YACtB;YAGJ,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,MAAM;QACV;IACJ;IAEA,aAAa,4BACT,KAAa,EACb,UAAiD,EAClB;QAC/B,IAAI;YACA,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBAC9C,IAAI,cAAc,cAAc,KAAK,EAAE;oBACnC,MAAM,WAAW,KAAK,KAAK,CACvB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAGtD,6DAA6D;oBAC7D,MAAM,gBAAkC,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;4BAChE,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;4BAC/B,UAAU;4BACV,QAAQ,aAAa,MAAM,cAAc;wBAC7C,CAAC;oBAED,WAAW;gBACf;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,IAAI,MAAM;QACpB;IACJ;IAEA,aAAa,iBACT,KAAa,EACb,UAAwD,EACzB;QAC/B,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAC;YACX,SAAS,MAAM,CAAC,SAAS;QAC7B;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CACjC,6BACA,UACA;gBACI,SAAS;oBACL,gBAAgB;gBACpB;gBACA,kBAAkB;YACtB;YAGJ,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACV;IACJ;IAEA,aAAa,6BACT,KAAa,EACb,UAAiD,EAClB;QAC/B,IAAI;YACA,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC/C,IAAI,cAAc,cAAc,KAAK,EAAE;oBACnC,MAAM,WAAW,KAAK,KAAK,CACvB,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAGtD,MAAM,gBAAkC,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;4BAChE,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;4BAC/B,UAAU;4BACV,QAAQ,aAAa,MAAM,cAAc;wBAC7C,CAAC;oBAED,WAAW;gBACf;YACJ;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YAEpC,kCAAkC;YAClC,IAAI,iBAAiB,OAAO;gBACxB,MAAM;YACV;YAEA,+CAA+C;YAC/C,IAAI,SAAS,OAAO,UAAU,YAAY,cAAc,OAAO;gBAC3D,MAAM,aAAa;gBACnB,MAAM,UAAU,WAAW,QAAQ,EAAE,MAAM,WACvC,WAAW,QAAQ,EAAE,MAAM,SAC3B,WAAW,OAAO,IAClB;gBACJ,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,IAAI,MAAM;QACpB;IACJ;IAEA,OAAO,aAAa,IAAU,EAAwC;QAClE,MAAM,UAAU,MAAM,OAAO,MAAM,QAAQ;QAE3C,IAAI,KAAK,IAAI,GAAG,SAAS;YACrB,OAAO;gBACH,SAAS;gBACT,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,gCAAgC,CAAC;YAChI;QACJ;QAEA,MAAM,eAAe;YACjB,SAAS;YACT;YAAc;YAAa;YAAa;YAAc;YAAiB;YAAa;YACpF,SAAS;YACT;YAAa;YAAc;YAAa;YAAa;YAAa;YAAa;YAAa;YAC5F,QAAQ;YACR;YAAa;YAAa;YAAa;YAAa;YAAc;YAAa;YAC/E,YAAY;YACZ;YAAmB;YACnB;YACA;YAAmB;YACnB,eAAe;YACf;YACA;YACA;YACA,gBAAgB;YAChB;YACA;YACA;YACA,WAAW;YACX;YAAmB;YAAgC;YAA+B;YAAqB;YACvG,OAAO;YACP;YAAc;YAAY;YAAa;YAAY;YAAmB;YAAY;YAAoB;SACzG;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,MAAM,QAAQ,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,+DAA+D,CAAC;YACpG,OAAO;gBACH,SAAS;gBACT;YACJ;QACJ;QAEA,OAAO;YAAE,SAAS;QAAK;IAC3B;IAEA,OAAO,cAAc,KAAa,EAA0C;QACxE,MAAM,WAAW;QACjB,MAAM,SAAmB,EAAE;QAE3B,IAAI,MAAM,MAAM,GAAG,UAAU;YACzB,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,sDAAsD,CAAC;QAC9F;QAEA,MAAM,OAAO,CAAC,CAAC,MAAM;YACjB,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;YACrC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACrB,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;YACtF;QACJ;QAEA,OAAO;YACH,SAAS,OAAO,MAAM,KAAK;YAC3B;QACJ;IACJ;IAEA,OAAO,eAAe,KAAa,EAAU;QACzC,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,OAAO,mBAAmB,IAAU,EAAmB;QACnD,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjC,OAAO,IAAI,MAAM;gBACjB;YACJ;YAEA,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACb,QAAQ,EAAE,MAAM,EAAE;YACtB;YACA,OAAO,OAAO,GAAG;gBACb,OAAO,IAAI,MAAM;YACrB;YACA,OAAO,aAAa,CAAC;QACzB;IACJ;IAEA,2BAA2B;IAC3B,aAAa,qBAAqB,cAAsB,EAA8B;QAClF,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAAiC,gBAAgB;gBAClF;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACV;IACJ;IAEA,aAAa,0BAA0B,cAAsB,EAAE,cAAwB,EAAgC;QACnH,IAAI;YACA,MAAM,WAAW,MAAM,UAAU,IAAI,CAAmC,iBAAiB;gBACrF;gBACA;YACJ;YACA,OAAO,SAAS,IAAI,CAAC,IAAI;QAC7B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACV;IACJ;IAEA,aAAa,6BAA6B,YAAoB,EAAE,QAA8B,EAAiB;QAC3G,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa,QAAQ,CAAC,EAAE;gBAC1D;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACV;IACJ;IAEA,aAAa,aAAa,QAAgB,EAAiB;QACvD,IAAI;YACA,MAAM,UAAU,IAAI,CAAC,cAAc;gBAC/B;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACV;IACJ;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/libs/websocket.ts"], "sourcesContent": ["import { Client, IMessage, StompSubscription } from \"@stomp/stompjs\";\r\nimport SockJ<PERSON> from \"sockjs-client\";\r\nimport { ChatRequest, ChatMessage, ConnectionStatus, ConversationCreationResponse } from \"@/types/chat\";\r\n\r\nexport interface ConversationUpdateEvent {\r\n    type: 'CONVERSATION_CREATED' | 'CONVERSATION_UPDATED' | 'CONVERSATION_DELETED';\r\n    conversation: ConversationCreationResponse;\r\n    userId?: string; // Who triggered the action (from backend)\r\n    timestamp: number;\r\n}\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8080\";\r\nconst SOCKET_URL = `${API_BASE_URL}/ws`;\r\n\r\nconst CONNECTION_CONFIG = {\r\n    heartbeatIncoming: 10000,\r\n    heartbeatOutgoing: 10000,\r\n    reconnectDelay: 1000, // Start with 1s - ổn định hơn\r\n    maxReconnectDelay: 10000, // Max 10 seconds - <PERSON><PERSON> định\r\n    maxReconnectAttempts: 10, // <PERSON><PERSON> attempts hơn để tránh spam\r\n    connectionTimeout: 10000, // 10 seconds timeout - đ<PERSON> thời gian\r\n    debug: process.env.NODE_ENV === 'development'\r\n};\r\n\r\nexport class WebSocketService {\r\n    private static instance: WebSocketService;\r\n    private stompClient: Client | null = null;\r\n    private messageSubscription: StompSubscription | null = null;\r\n    private conversationSubscription: StompSubscription | null = null;\r\n\r\n    private connectionStatus: ConnectionStatus = {\r\n        isConnected: false,\r\n        reconnectAttempts: 0\r\n    };\r\n\r\n    private onMessageCallback?: (message: ChatMessage) => void;\r\n    private onStatusChangeCallback?: (status: ConnectionStatus) => void;\r\n    private onConversationUpdateCallback?: (event: ConversationUpdateEvent) => void;\r\n\r\n    private reconnectTimer?: NodeJS.Timeout;\r\n    private connectionTimer?: NodeJS.Timeout;\r\n    private isConnecting = false;\r\n    private shouldReconnect = true;\r\n\r\n    static getInstance(): WebSocketService {\r\n        if (!WebSocketService.instance) {\r\n            WebSocketService.instance = new WebSocketService();\r\n        }\r\n        return WebSocketService.instance;\r\n    }\r\n\r\n    connect(\r\n        onMessage: (message: ChatMessage) => void,\r\n        onStatusChange?: (status: ConnectionStatus) => void,\r\n        onConversationUpdate?: (event: ConversationUpdateEvent) => void\r\n    ): void {\r\n        if (this.stompClient && this.stompClient.connected) {\r\n            return;\r\n        }\r\n\r\n        if (this.isConnecting) {\r\n            return;\r\n        }\r\n\r\n        this.onMessageCallback = onMessage;\r\n        this.onStatusChangeCallback = onStatusChange;\r\n        this.onConversationUpdateCallback = onConversationUpdate;\r\n        this.shouldReconnect = true;\r\n\r\n        // Reset connection status\r\n        this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };\r\n        this.onStatusChangeCallback?.(this.connectionStatus);\r\n\r\n        this.createConnection();\r\n    }\r\n\r\n    private clearTimers(): void {\r\n        if (this.reconnectTimer) {\r\n            clearTimeout(this.reconnectTimer);\r\n            this.reconnectTimer = undefined;\r\n        }\r\n        if (this.connectionTimer) {\r\n            clearTimeout(this.connectionTimer);\r\n            this.connectionTimer = undefined;\r\n        }\r\n    }\r\n\r\n    private cleanupConnection(): void {\r\n        if (this.stompClient) {\r\n            try {\r\n                this.messageSubscription?.unsubscribe();\r\n                this.conversationSubscription?.unsubscribe();\r\n                this.messageSubscription = null;\r\n                this.conversationSubscription = null;\r\n                this.stompClient.deactivate();\r\n            } catch (error) {\r\n                console.warn('⚠️ Error cleaning up existing connection:', error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private createConnection(): void {\r\n        if (this.isConnecting) {\r\n            return;\r\n        }\r\n\r\n        const accessToken = localStorage.getItem(\"accessToken\");\r\n        if (!accessToken) {\r\n            this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };\r\n            this.onStatusChangeCallback?.(this.connectionStatus);\r\n            return;\r\n        }\r\n\r\n        this.isConnecting = true;\r\n\r\n        // Clear any existing timers\r\n        this.clearTimers();\r\n\r\n\r\n        // Cleanup existing connection\r\n        this.cleanupConnection();\r\n\r\n        // Connection timeout - CHỈ timeout nếu thực sự không connect được\r\n        this.connectionTimer = setTimeout(() => {\r\n            if (!this.stompClient?.connected) {\r\n                this.handleConnectionError();\r\n            }\r\n        }, CONNECTION_CONFIG.connectionTimeout);\r\n\r\n        this.stompClient = new Client({\r\n            webSocketFactory: () => new SockJS(SOCKET_URL),\r\n            connectHeaders: {\r\n                Authorization: `Bearer ${accessToken}`,\r\n            },\r\n            heartbeatIncoming: CONNECTION_CONFIG.heartbeatIncoming,\r\n            heartbeatOutgoing: CONNECTION_CONFIG.heartbeatOutgoing,\r\n            debug: CONNECTION_CONFIG.debug ? console.log : undefined,\r\n            onConnect: () => {\r\n                this.clearTimers();\r\n                this.isConnecting = false;\r\n                this.connectionStatus = { isConnected: true, reconnectAttempts: 0 };\r\n                this.onStatusChangeCallback?.(this.connectionStatus);\r\n\r\n                this.messageSubscription = this.stompClient?.subscribe(\"/user/queue/messages\", (message: IMessage) => {\r\n                    try {\r\n                        const chatMessage: ChatMessage = JSON.parse(message.body);\r\n                        this.onMessageCallback?.(chatMessage);\r\n                    } catch (error) {\r\n                    }\r\n                }) || null;\r\n\r\n                this.conversationSubscription = this.stompClient?.subscribe(\"/user/queue/conversation-updates\", (message: IMessage) => {\r\n                    try {\r\n                        if (CONNECTION_CONFIG.debug) {\r\n                            console.log('📨 Received conversation update:', message.body);\r\n                        }\r\n                        const conversationEvent: ConversationUpdateEvent = JSON.parse(message.body);\r\n                        this.onConversationUpdateCallback?.(conversationEvent);\r\n                    } catch (error) {\r\n                        if (CONNECTION_CONFIG.debug) {\r\n                            console.error('❌ Failed to parse conversation update:', error);\r\n                        }\r\n                    }\r\n                }) || null;\r\n\r\n            },\r\n            onStompError: (frame) => {\r\n                console.error('🔴 WebSocket STOMP error:', frame);\r\n                this.handleConnectionError();\r\n            },\r\n            onWebSocketClose: (event) => {\r\n                console.warn('🟡 WebSocket connection closed:', {\r\n                    code: event.code,\r\n                    reason: event.reason,\r\n                    wasClean: event.wasClean\r\n                });\r\n                this.isConnecting = false;\r\n                this.connectionStatus.isConnected = false;\r\n                this.onStatusChangeCallback?.(this.connectionStatus);\r\n\r\n                // Only auto-reconnect if it wasn't a clean close\r\n                if (!event.wasClean && this.shouldReconnect) {\r\n                    this.handleConnectionError();\r\n                }\r\n            },\r\n            onWebSocketError: (event) => {\r\n                console.error('🔴 WebSocket error:', event);\r\n                this.isConnecting = false;\r\n                this.handleConnectionError();\r\n            },\r\n            reconnectDelay: 0, // Disable auto-reconnect, we handle it manually\r\n        });\r\n\r\n        try {\r\n            this.stompClient.activate();\r\n        } catch (error) {\r\n            console.error('❌ Failed to activate WebSocket:', error);\r\n            this.handleConnectionError();\r\n        }\r\n    }\r\n\r\n    private handleConnectionError(): void {\r\n        this.isConnecting = false;\r\n        this.clearTimers();\r\n        this.connectionStatus.isConnected = false;\r\n        this.onStatusChangeCallback?.(this.connectionStatus);\r\n\r\n        if (!this.shouldReconnect) return;\r\n\r\n        // Auto-reconnect if under max attempts\r\n        if (this.connectionStatus.reconnectAttempts < CONNECTION_CONFIG.maxReconnectAttempts) {\r\n            this.connectionStatus.reconnectAttempts++;\r\n\r\n            const baseDelay = Math.min(\r\n                CONNECTION_CONFIG.reconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts - 1),\r\n                CONNECTION_CONFIG.maxReconnectDelay\r\n            );\r\n            const jitter = Math.random() * 1000; // Add up to 1 second jitter\r\n            const backoffDelay = baseDelay + jitter;\r\n\r\n            this.reconnectTimer = setTimeout(() => {\r\n                if (this.shouldReconnect) {\r\n                    this.createConnection();\r\n                }\r\n            }, backoffDelay);\r\n        } else {\r\n            console.error('❌ Max reconnection attempts reached');\r\n            this.shouldReconnect = false;\r\n        }\r\n    }\r\n\r\n    disconnect(): void {\r\n        this.shouldReconnect = false;\r\n        this.clearTimers();\r\n        this.cleanupConnection();\r\n        this.connectionStatus = { isConnected: false, reconnectAttempts: 0 };\r\n        this.onStatusChangeCallback?.(this.connectionStatus);\r\n        console.log('🔌 WebSocket disconnected');\r\n    }\r\n\r\n    sendMessage(request: ChatRequest): boolean {\r\n        if (!this.stompClient || !this.stompClient.connected) {\r\n            console.warn('⚠️ Cannot send message: WebSocket not connected');\r\n            return false;\r\n        }\r\n\r\n        try {\r\n            this.stompClient.publish({\r\n                destination: \"/app/chat\",\r\n                body: JSON.stringify(request),\r\n            });\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Failed to send message:', error);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    markAsRead(conversationId: string, messageId: string): boolean {\r\n        if (!this.stompClient?.connected) {\r\n            console.warn('⚠️ Cannot mark as read: WebSocket not connected');\r\n            return false;\r\n        }\r\n\r\n        // Check if user is authenticated\r\n        const accessToken = localStorage.getItem(\"accessToken\");\r\n        if (!accessToken) {\r\n            console.warn('⚠️ Cannot mark as read: No access token');\r\n            return false;\r\n        }\r\n\r\n        try {\r\n            this.stompClient.publish({\r\n                destination: `/app/chat/${conversationId}/read/${messageId}`,\r\n                body: '',\r\n            });\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Failed to mark message as read:', error);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    getConnectionStatus(): ConnectionStatus {\r\n        return { ...this.connectionStatus };\r\n    }\r\n\r\n    isConnected(): boolean {\r\n        return this.connectionStatus.isConnected && this.stompClient?.connected === true;\r\n    }\r\n\r\n    // Manual reconnect method\r\n    reconnect(): void {\r\n        this.shouldReconnect = true;\r\n        this.connectionStatus.reconnectAttempts = 0; // Reset attempts\r\n        this.disconnect();\r\n        setTimeout(() => {\r\n            this.createConnection();\r\n        }, 500);\r\n    }\r\n\r\n    forceReconnect(): void {\r\n        this.shouldReconnect = true;\r\n        this.connectionStatus.reconnectAttempts = 0;\r\n        this.clearTimers();\r\n        this.cleanupConnection();\r\n\r\n        setTimeout(() => {\r\n            this.createConnection();\r\n        }, 100);\r\n    }\r\n}\r\n\r\n// Export singleton instance for production use\r\nexport const webSocketService = WebSocketService.getInstance();\r\n\r\n// Export for testing and configuration\r\nexport { CONNECTION_CONFIG };\r\n"], "names": [], "mappings": ";;;;;AAWqB;AAXrB;AACA;;;AAUA,MAAM,eAAe,6DAAwC;AAC7D,MAAM,aAAa,GAAG,aAAa,GAAG,CAAC;AAEvC,MAAM,oBAAoB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,sBAAsB;IACtB,mBAAmB;IACnB,OAAO,oDAAyB;AACpC;AAEO,MAAM;IACT,OAAe,SAA2B;IAClC,cAA6B,KAAK;IAClC,sBAAgD,KAAK;IACrD,2BAAqD,KAAK;IAE1D,mBAAqC;QACzC,aAAa;QACb,mBAAmB;IACvB,EAAE;IAEM,kBAAmD;IACnD,uBAA4D;IAC5D,6BAAwE;IAExE,eAAgC;IAChC,gBAAiC;IACjC,eAAe,MAAM;IACrB,kBAAkB,KAAK;IAE/B,OAAO,cAAgC;QACnC,IAAI,CAAC,iBAAiB,QAAQ,EAAE;YAC5B,iBAAiB,QAAQ,GAAG,IAAI;QACpC;QACA,OAAO,iBAAiB,QAAQ;IACpC;IAEA,QACI,SAAyC,EACzC,cAAmD,EACnD,oBAA+D,EAC3D;QACJ,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAChD;QACJ;QAEA,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB;QACJ;QAEA,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,4BAA4B,GAAG;QACpC,IAAI,CAAC,eAAe,GAAG;QAEvB,0BAA0B;QAC1B,IAAI,CAAC,gBAAgB,GAAG;YAAE,aAAa;YAAO,mBAAmB;QAAE;QACnE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;QAEnD,IAAI,CAAC,gBAAgB;IACzB;IAEQ,cAAoB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,aAAa,IAAI,CAAC,cAAc;YAChC,IAAI,CAAC,cAAc,GAAG;QAC1B;QACA,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,aAAa,IAAI,CAAC,eAAe;YACjC,IAAI,CAAC,eAAe,GAAG;QAC3B;IACJ;IAEQ,oBAA0B;QAC9B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI;gBACA,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,mBAAmB,GAAG;gBAC3B,IAAI,CAAC,wBAAwB,GAAG;gBAChC,IAAI,CAAC,WAAW,CAAC,UAAU;YAC/B,EAAE,OAAO,OAAO;gBACZ,QAAQ,IAAI,CAAC,6CAA6C;YAC9D;QACJ;IACJ;IAEQ,mBAAyB;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB;QACJ;QAEA,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,CAAC,aAAa;YACd,IAAI,CAAC,gBAAgB,GAAG;gBAAE,aAAa;gBAAO,mBAAmB;YAAE;YACnE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;YACnD;QACJ;QAEA,IAAI,CAAC,YAAY,GAAG;QAEpB,4BAA4B;QAC5B,IAAI,CAAC,WAAW;QAGhB,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB;QAEtB,kEAAkE;QAClE,IAAI,CAAC,eAAe,GAAG,WAAW;YAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW;gBAC9B,IAAI,CAAC,qBAAqB;YAC9B;QACJ,GAAG,kBAAkB,iBAAiB;QAEtC,IAAI,CAAC,WAAW,GAAG,IAAI,uJAAA,CAAA,SAAM,CAAC;YAC1B,kBAAkB,IAAM,IAAI,mJAAA,CAAA,UAAM,CAAC;YACnC,gBAAgB;gBACZ,eAAe,CAAC,OAAO,EAAE,aAAa;YAC1C;YACA,mBAAmB,kBAAkB,iBAAiB;YACtD,mBAAmB,kBAAkB,iBAAiB;YACtD,OAAO,kBAAkB,KAAK,GAAG,QAAQ,GAAG,GAAG;YAC/C,WAAW;gBACP,IAAI,CAAC,WAAW;gBAChB,IAAI,CAAC,YAAY,GAAG;gBACpB,IAAI,CAAC,gBAAgB,GAAG;oBAAE,aAAa;oBAAM,mBAAmB;gBAAE;gBAClE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;gBAEnD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,EAAE,UAAU,wBAAwB,CAAC;oBAC5E,IAAI;wBACA,MAAM,cAA2B,KAAK,KAAK,CAAC,QAAQ,IAAI;wBACxD,IAAI,CAAC,iBAAiB,GAAG;oBAC7B,EAAE,OAAO,OAAO,CAChB;gBACJ,MAAM;gBAEN,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,WAAW,EAAE,UAAU,oCAAoC,CAAC;oBAC7F,IAAI;wBACA,IAAI,kBAAkB,KAAK,EAAE;4BACzB,QAAQ,GAAG,CAAC,oCAAoC,QAAQ,IAAI;wBAChE;wBACA,MAAM,oBAA6C,KAAK,KAAK,CAAC,QAAQ,IAAI;wBAC1E,IAAI,CAAC,4BAA4B,GAAG;oBACxC,EAAE,OAAO,OAAO;wBACZ,IAAI,kBAAkB,KAAK,EAAE;4BACzB,QAAQ,KAAK,CAAC,0CAA0C;wBAC5D;oBACJ;gBACJ,MAAM;YAEV;YACA,cAAc,CAAC;gBACX,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,IAAI,CAAC,qBAAqB;YAC9B;YACA,kBAAkB,CAAC;gBACf,QAAQ,IAAI,CAAC,mCAAmC;oBAC5C,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,UAAU,MAAM,QAAQ;gBAC5B;gBACA,IAAI,CAAC,YAAY,GAAG;gBACpB,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG;gBACpC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;gBAEnD,iDAAiD;gBACjD,IAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;oBACzC,IAAI,CAAC,qBAAqB;gBAC9B;YACJ;YACA,kBAAkB,CAAC;gBACf,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,IAAI,CAAC,YAAY,GAAG;gBACpB,IAAI,CAAC,qBAAqB;YAC9B;YACA,gBAAgB;QACpB;QAEA,IAAI;YACA,IAAI,CAAC,WAAW,CAAC,QAAQ;QAC7B,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,qBAAqB;QAC9B;IACJ;IAEQ,wBAA8B;QAClC,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG;QACpC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;QAEnD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;QAE3B,uCAAuC;QACvC,IAAI,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,kBAAkB,oBAAoB,EAAE;YAClF,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;YAEvC,MAAM,YAAY,KAAK,GAAG,CACtB,kBAAkB,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,IACzF,kBAAkB,iBAAiB;YAEvC,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,4BAA4B;YACjE,MAAM,eAAe,YAAY;YAEjC,IAAI,CAAC,cAAc,GAAG,WAAW;gBAC7B,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,gBAAgB;gBACzB;YACJ,GAAG;QACP,OAAO;YACH,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,eAAe,GAAG;QAC3B;IACJ;IAEA,aAAmB;QACf,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,gBAAgB,GAAG;YAAE,aAAa;YAAO,mBAAmB;QAAE;QACnE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB;QACnD,QAAQ,GAAG,CAAC;IAChB;IAEA,YAAY,OAAoB,EAAW;QACvC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAClD,QAAQ,IAAI,CAAC;YACb,OAAO;QACX;QAEA,IAAI;YACA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACrB,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACzB;YACA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACX;IACJ;IAEA,WAAW,cAAsB,EAAE,SAAiB,EAAW;QAC3D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW;YAC9B,QAAQ,IAAI,CAAC;YACb,OAAO;QACX;QAEA,iCAAiC;QACjC,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,CAAC,aAAa;YACd,QAAQ,IAAI,CAAC;YACb,OAAO;QACX;QAEA,IAAI;YACA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACrB,aAAa,CAAC,UAAU,EAAE,eAAe,MAAM,EAAE,WAAW;gBAC5D,MAAM;YACV;YACA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACX;IACJ;IAEA,sBAAwC;QACpC,OAAO;YAAE,GAAG,IAAI,CAAC,gBAAgB;QAAC;IACtC;IAEA,cAAuB;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,cAAc;IAChF;IAEA,0BAA0B;IAC1B,YAAkB;QACd,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,GAAG,iBAAiB;QAC9D,IAAI,CAAC,UAAU;QACf,WAAW;YACP,IAAI,CAAC,gBAAgB;QACzB,GAAG;IACP;IAEA,iBAAuB;QACnB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,GAAG;QAC1C,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,iBAAiB;QAEtB,WAAW;YACP,IAAI,CAAC,gBAAgB;QACzB,GAAG;IACP;AACJ;AAGO,MAAM,mBAAmB,iBAAiB,WAAW", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/types/file.ts"], "sourcesContent": ["export interface FileMetaDataResponse {\n    name: string;\n    contentType: string;\n    size: number;\n    url: string;\n    displayOrder?: number;\n}\n\nexport interface MediaAttachment {\n    mediaUrl: string;\n    mediaName: string;\n    mediaSize: number;\n    mediaType: string;\n    displayOrder?: number;\n}\n\nexport enum MessageType {\n    TEXT = 'TEXT',\n    FILE = 'FILE'\n}\n\nexport interface FilePreview {\n    file: File;\n    id: string;\n    name: string;\n    size: number;\n    type: string;\n    preview?: string;\n    isUploading?: boolean;\n    uploadProgress?: number;\n    error?: string;\n}\n\nexport interface UploadProgress {\n    fileId: string;\n    progress: number;\n    status: 'uploading' | 'completed' | 'error';\n    error?: string;\n}\n\nexport const FILE_CATEGORIES = {\n    IMAGE: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff'],\n    VIDEO: ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv'],\n    AUDIO: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac', 'audio/m4a', 'audio/wma'],\n    DOCUMENT: [\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/rtf',\n        'application/vnd.oasis.opendocument.text'\n    ],\n    SPREADSHEET: [\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.oasis.opendocument.spreadsheet',\n        'text/csv'\n    ],\n    PRESENTATION: [\n        'application/vnd.ms-powerpoint',\n        'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n        'application/vnd.oasis.opendocument.presentation'\n    ],\n    ARCHIVE: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip'],\n    TEXT: ['text/plain', 'text/csv', 'text/html', 'text/css', 'text/javascript', 'text/xml', 'application/json', 'text/markdown']\n} as const;\n\nexport const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB\nexport const MAX_FILES_PER_MESSAGE = 10;\n\nexport const getFileCategory = (mimeType: string): keyof typeof FILE_CATEGORIES | 'OTHER' => {\n    for (const [category, types] of Object.entries(FILE_CATEGORIES)) {\n        if ((types as readonly string[]).includes(mimeType)) {\n            return category as keyof typeof FILE_CATEGORIES;\n        }\n    }\n    return 'OTHER';\n};\n\nexport const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const getFileIcon = (mimeType: string): string => {\n    const category = getFileCategory(mimeType);\n\n    switch (category) {\n        case 'IMAGE':\n            return '🖼️';\n        case 'VIDEO':\n            return '🎥';\n        case 'AUDIO':\n            return '🎵';\n        case 'DOCUMENT':\n            return '📄';\n        case 'SPREADSHEET':\n            return '📊';\n        case 'PRESENTATION':\n            return '�️';\n        case 'ARCHIVE':\n            return '📦';\n        case 'TEXT':\n            return '📝';\n        default:\n            return '📎';\n    }\n};\n\nexport const getFileIconColor = (mimeType: string): string => {\n    const category = getFileCategory(mimeType);\n\n    switch (category) {\n        case 'IMAGE':\n            return 'text-green-500';\n        case 'VIDEO':\n            return 'text-red-500';\n        case 'AUDIO':\n            return 'text-purple-500';\n        case 'DOCUMENT':\n            return 'text-blue-500';\n        case 'SPREADSHEET':\n            return 'text-green-600';\n        case 'PRESENTATION':\n            return 'text-orange-500';\n        case 'ARCHIVE':\n            return 'text-yellow-600';\n        case 'TEXT':\n            return 'text-gray-600';\n        default:\n            return 'text-gray-500';\n    }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAgBO,IAAA,AAAK,qCAAA;;;WAAA;;AAwBL,MAAM,kBAAkB;IAC3B,OAAO;QAAC;QAAc;QAAa;QAAa;QAAc;QAAiB;QAAa;KAAa;IACzG,OAAO;QAAC;QAAa;QAAc;QAAa;QAAa;QAAa;QAAa;QAAa;KAAY;IAChH,OAAO;QAAC;QAAa;QAAa;QAAa;QAAa;QAAc;QAAa;KAAY;IACnG,UAAU;QACN;QACA;QACA;QACA;QACA;KACH;IACD,aAAa;QACT;QACA;QACA;QACA;KACH;IACD,cAAc;QACV;QACA;QACA;KACH;IACD,SAAS;QAAC;QAAmB;QAAgC;QAA+B;QAAqB;KAAmB;IACpI,MAAM;QAAC;QAAc;QAAY;QAAa;QAAY;QAAmB;QAAY;QAAoB;KAAgB;AACjI;AAEO,MAAM,gBAAgB,KAAK,OAAO,MAAM,OAAO;AAC/C,MAAM,wBAAwB;AAE9B,MAAM,kBAAkB,CAAC;IAC5B,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,iBAAkB;QAC7D,IAAI,AAAC,MAA4B,QAAQ,CAAC,WAAW;YACjD,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEO,MAAM,iBAAiB,CAAC;IAC3B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,MAAM,cAAc,CAAC;IACxB,MAAM,WAAW,gBAAgB;IAEjC,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AAEO,MAAM,mBAAmB,CAAC;IAC7B,MAAM,WAAW,gBAAgB;IAEjC,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/utils/messageUtils.ts"], "sourcesContent": ["import { ChatMessage, ChatRequest } from '@/types/chat';\nimport { FilePreview as FilePreviewType, MessageType, MediaAttachment } from '@/types/file';\n\n/**\n * Utility functions for handling chat messages\n */\n\n/**\n * Generate a unique temporary ID for messages\n */\nexport const generateTempId = (): string => {\n    return `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n};\n\n/**\n * Check if a message is temporary (still being sent)\n */\nexport const isTemporaryMessage = (message: ChatMessage): boolean => {\n    return message.status === \"SENDING\" && !!message.tempId;\n};\n\n/**\n * Sort messages by creation time\n */\nexport const sortMessagesByTime = (messages: ChatMessage[] | null | undefined): ChatMessage[] => {\n    // Handle null, undefined, or non-array inputs\n    if (!messages || !Array.isArray(messages)) {\n        console.warn('sortMessagesByTime received non-array input:', messages);\n        return [];\n    }\n\n    return [...messages].sort((a, b) =>\n        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n    );\n};\n\n/**\n * Create a temporary message for immediate UI feedback\n */\nexport const createTemporaryMessage = (\n    conversationId: string,\n    content: string,\n    tempId: string,\n    files?: FilePreviewType[],\n    currentUsername?: string\n): ChatMessage => {\n    const mediaUrls = files?.map(file => file.preview || URL.createObjectURL(file.file)) || [];\n\n    return {\n        id: tempId, // Use tempId as temporary id until we get real ID\n        tempId,\n        conversationId,\n        me: true,\n        username: currentUsername, // Use backend field\n        content,\n        status: \"SENDING\",\n        createdAt: new Date().toISOString(),\n        read: true,\n        mediaUrl: mediaUrls.length > 0 ? mediaUrls : undefined,\n        messageType: files && files.length > 0 ? \"FILE\" : \"TEXT\"\n    };\n};\n\n/**\n * Create a chat request for sending (with uploaded file URLs)\n * Note: tempId will be generated by backend\n */\nexport const createChatRequest = (\n    conversationId: string,\n    content: string,\n    uploadedFiles?: { url: string; name: string; size: number; type: string; displayOrder?: number }[]\n): ChatRequest => {\n    const hasFiles = uploadedFiles && uploadedFiles.length > 0;\n\n    const request: ChatRequest = {\n        conversationId,\n        content,\n        messageType: hasFiles ? MessageType.FILE : MessageType.TEXT\n    };\n\n    // Only add mediaAttachments if there are actually files\n    if (hasFiles) {\n        request.mediaAttachments = uploadedFiles.map((file, index) => ({\n            mediaUrl: file.url,\n            mediaName: file.name,\n            mediaSize: file.size,\n            mediaType: file.type,\n            displayOrder: file.displayOrder || index + 1\n        }));\n    }\n\n    return request;\n};\n\n/**\n * Update temporary message with server response\n */\nexport const updateTempMessage = (\n    tempMessage: ChatMessage,\n    serverMessage: ChatMessage\n): ChatMessage => {\n    return {\n        ...tempMessage,\n        id: serverMessage.id,\n        status: serverMessage.status,\n        read: serverMessage.read,\n        createdAt: serverMessage.createdAt,\n        mediaAttachments: serverMessage.mediaAttachments, // Update with server media attachments\n        tempId: undefined // Remove tempId after update\n    };\n};\n\n/**\n * Check if two messages are the same (for deduplication)\n */\nexport const isSameMessage = (msg1: ChatMessage, msg2: ChatMessage): boolean => {\n    // Same ID\n    if (msg1.id === msg2.id) return true;\n\n    // Same tempId\n    if (msg1.tempId && msg2.tempId && msg1.tempId === msg2.tempId) return true;\n\n    // Same content and time (fallback)\n    if (msg1.content === msg2.content &&\n        msg1.conversationId === msg2.conversationId &&\n        Math.abs(new Date(msg1.createdAt).getTime() - new Date(msg2.createdAt).getTime()) < 1000) {\n        return true;\n    }\n\n    return false;\n};\n"], "names": [], "mappings": ";;;;;;;;;AACA;;AASO,MAAM,iBAAiB;IAC1B,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;AAC9E;AAKO,MAAM,qBAAqB,CAAC;IAC/B,OAAO,QAAQ,MAAM,KAAK,aAAa,CAAC,CAAC,QAAQ,MAAM;AAC3D;AAKO,MAAM,qBAAqB,CAAC;IAC/B,8CAA8C;IAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,WAAW;QACvC,QAAQ,IAAI,CAAC,gDAAgD;QAC7D,OAAO,EAAE;IACb;IAEA,OAAO;WAAI;KAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAC1B,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAEvE;AAKO,MAAM,yBAAyB,CAClC,gBACA,SACA,QACA,OACA;IAEA,MAAM,YAAY,OAAO,IAAI,CAAA,OAAQ,KAAK,OAAO,IAAI,IAAI,eAAe,CAAC,KAAK,IAAI,MAAM,EAAE;IAE1F,OAAO;QACH,IAAI;QACJ;QACA;QACA,IAAI;QACJ,UAAU;QACV;QACA,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,MAAM;QACN,UAAU,UAAU,MAAM,GAAG,IAAI,YAAY;QAC7C,aAAa,SAAS,MAAM,MAAM,GAAG,IAAI,SAAS;IACtD;AACJ;AAMO,MAAM,oBAAoB,CAC7B,gBACA,SACA;IAEA,MAAM,WAAW,iBAAiB,cAAc,MAAM,GAAG;IAEzD,MAAM,UAAuB;QACzB;QACA;QACA,aAAa,WAAW,uHAAA,CAAA,cAAW,CAAC,IAAI,GAAG,uHAAA,CAAA,cAAW,CAAC,IAAI;IAC/D;IAEA,wDAAwD;IACxD,IAAI,UAAU;QACV,QAAQ,gBAAgB,GAAG,cAAc,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC3D,UAAU,KAAK,GAAG;gBAClB,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,IAAI;gBACpB,cAAc,KAAK,YAAY,IAAI,QAAQ;YAC/C,CAAC;IACL;IAEA,OAAO;AACX;AAKO,MAAM,oBAAoB,CAC7B,aACA;IAEA,OAAO;QACH,GAAG,WAAW;QACd,IAAI,cAAc,EAAE;QACpB,QAAQ,cAAc,MAAM;QAC5B,MAAM,cAAc,IAAI;QACxB,WAAW,cAAc,SAAS;QAClC,kBAAkB,cAAc,gBAAgB;QAChD,QAAQ,UAAU,6BAA6B;IACnD;AACJ;AAKO,MAAM,gBAAgB,CAAC,MAAmB;IAC7C,UAAU;IACV,IAAI,KAAK,EAAE,KAAK,KAAK,EAAE,EAAE,OAAO;IAEhC,cAAc;IACd,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE,OAAO;IAEtE,mCAAmC;IACnC,IAAI,KAAK,OAAO,KAAK,KAAK,OAAO,IAC7B,KAAK,cAAc,KAAK,KAAK,cAAc,IAC3C,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,KAAK,SAAS,EAAE,OAAO,MAAM,MAAM;QAC1F,OAAO;IACX;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/WebSocketStatus.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { ConnectionStatus } from \"@/types/chat\";\nimport { webSocketService } from \"@/libs/websocket\";\n\ninterface WebSocketStatusProps {\n    connectionStatus: ConnectionStatus;\n    className?: string;\n}\n\nexport const WebSocketStatus: React.FC<WebSocketStatusProps> = ({\n    connectionStatus,\n    className = \"\"\n}) => {\n    const handleReconnect = () => {\n        webSocketService.reconnect();\n    };\n\n    if (connectionStatus.isConnected) {\n        return (\n            <div className={`flex items-center gap-2 ${className}`}>\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-green-600 font-medium\">Connected</span>\n            </div>\n        );\n    }\n\n    return (\n        <div className={`flex items-center gap-2 ${className}`}>\n            <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n            <span className=\"text-xs text-red-600 font-medium\">\n                {connectionStatus.reconnectAttempts > 0 \n                    ? `Reconnecting... (${connectionStatus.reconnectAttempts})`\n                    : 'Disconnected'\n                }\n            </span>\n            <button\n                onClick={handleReconnect}\n                className=\"text-xs text-blue-600 hover:text-blue-700 font-medium underline ml-1\"\n                title=\"Click to reconnect\"\n            >\n                Retry\n            </button>\n        </div>\n    );\n};\n\n// Compact version for header\nexport const WebSocketStatusCompact: React.FC<WebSocketStatusProps> = ({\n    connectionStatus,\n    className = \"\"\n}) => {\n    const handleReconnect = () => {\n        webSocketService.reconnect();\n    };\n\n    if (connectionStatus.isConnected) {\n        return (\n            <div className={`flex items-center ${className}`} title=\"WebSocket Connected\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            </div>\n        );\n    }\n\n    return (\n        <button\n            onClick={handleReconnect}\n            className={`flex items-center gap-1 p-1.5 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-colors duration-150 ${className}`}\n            title={`WebSocket ${connectionStatus.reconnectAttempts > 0 ? 'Reconnecting...' : 'Disconnected'} - Click to reconnect`}\n        >\n            <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n        </button>\n    );\n};\n"], "names": [], "mappings": ";;;;;AAIA;AAJA;;;AAWO,MAAM,kBAAkD,CAAC,EAC5D,gBAAgB,EAChB,YAAY,EAAE,EACjB;IACG,MAAM,kBAAkB;QACpB,2HAAA,CAAA,mBAAgB,CAAC,SAAS;IAC9B;IAEA,IAAI,iBAAiB,WAAW,EAAE;QAC9B,qBACI,6LAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;8BAClD,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAqC;;;;;;;;;;;;IAGjE;IAEA,qBACI,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BAClD,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAK,WAAU;0BACX,iBAAiB,iBAAiB,GAAG,IAChC,CAAC,iBAAiB,EAAE,iBAAiB,iBAAiB,CAAC,CAAC,CAAC,GACzD;;;;;;0BAGV,6LAAC;gBACG,SAAS;gBACT,WAAU;gBACV,OAAM;0BACT;;;;;;;;;;;;AAKb;KAnCa;AAsCN,MAAM,yBAAyD,CAAC,EACnE,gBAAgB,EAChB,YAAY,EAAE,EACjB;IACG,MAAM,kBAAkB;QACpB,2HAAA,CAAA,mBAAgB,CAAC,SAAS;IAC9B;IAEA,IAAI,iBAAiB,WAAW,EAAE;QAC9B,qBACI,6LAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;YAAE,OAAM;sBACpD,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAG3B;IAEA,qBACI,6LAAC;QACG,SAAS;QACT,WAAW,CAAC,mIAAmI,EAAE,WAAW;QAC5J,OAAO,CAAC,UAAU,EAAE,iBAAiB,iBAAiB,GAAG,IAAI,oBAAoB,eAAe,qBAAqB,CAAC;;0BAEtH,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;;;;;;;AAIrF;MA5Ba", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/services/twilioVideoService.ts"], "sourcesContent": ["import { connect, Room, LocalParticipant, RemoteParticipant, LocalVideoTrack, LocalAudioTrack, RemoteVideoTrack, RemoteAudioTrack } from 'twilio-video';\nimport { VideoCallParticipant, VideoCallRoom, TwilioAccessToken, VideoCallSettings } from '@/types/video';\nimport { ApiService } from '@/api/axios';\n\nexport class TwilioVideoService {\n    private room: Room | null = null;\n    private localVideoTrack: LocalVideoTrack | null = null;\n    private localAudioTrack: LocalAudioTrack | null = null;\n    private participants: Map<string, VideoCallParticipant> = new Map();\n\n    // Event callbacks\n    private onParticipantConnected?: (participant: VideoCallParticipant) => void;\n    private onParticipantDisconnected?: (participantId: string) => void;\n    private onRoomConnected?: (room: VideoCallRoom) => void;\n    private onRoomDisconnected?: (room: VideoCallRoom) => void;\n    private onTrackSubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;\n    private onTrackUnsubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;\n\n    constructor() {\n        this.setupEventHandlers();\n    }\n\n    /**\n     * Initialize video calling with event callbacks\n     */\n    initialize(callbacks: {\n        onParticipantConnected?: (participant: VideoCallParticipant) => void;\n        onParticipantDisconnected?: (participantId: string) => void;\n        onRoomConnected?: (room: VideoCallRoom) => void;\n        onRoomDisconnected?: (room: VideoCallRoom) => void;\n        onTrackSubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;\n        onTrackUnsubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;\n    }) {\n        this.onParticipantConnected = callbacks.onParticipantConnected;\n        this.onParticipantDisconnected = callbacks.onParticipantDisconnected;\n        this.onRoomConnected = callbacks.onRoomConnected;\n        this.onRoomDisconnected = callbacks.onRoomDisconnected;\n        this.onTrackSubscribed = callbacks.onTrackSubscribed;\n        this.onTrackUnsubscribed = callbacks.onTrackUnsubscribed;\n    }\n\n    /**\n     * Join a video call room\n     */\n    async joinRoom(conversationId: string, settings: VideoCallSettings): Promise<VideoCallRoom> {\n        try {\n            // Get access token from backend\n            const tokenData = await ApiService.getTwilioAccessToken(conversationId);\n\n            // Create local tracks based on settings\n            const tracks = [];\n\n            if (settings.video) {\n                this.localVideoTrack = await this.createLocalVideoTrack();\n                tracks.push(this.localVideoTrack);\n            }\n\n            if (settings.audio) {\n                this.localAudioTrack = await this.createLocalAudioTrack();\n                tracks.push(this.localAudioTrack);\n            }\n\n            // Connect to Twilio room\n            this.room = await connect(tokenData.token, {\n                name: tokenData.roomName,\n                tracks: tracks,\n                video: settings.video,\n                audio: settings.audio\n            });\n\n            // Setup room event handlers\n            this.setupRoomEventHandlers();\n\n            // Add existing participants\n            this.room.participants.forEach(participant => {\n                this.handleParticipantConnected(participant);\n            });\n\n            // Add local participant to participants map\n            const localParticipant: VideoCallParticipant = {\n                id: this.room.localParticipant.sid,\n                username: this.room.localParticipant.identity,\n                isLocal: true,\n                isMuted: !settings.audio,\n                isVideoEnabled: settings.video\n            };\n            this.participants.set(this.room.localParticipant.sid, localParticipant);\n\n            const roomData: VideoCallRoom = {\n                roomId: this.room.sid,\n                roomName: this.room.name,\n                participants: this.getParticipantsArray(),\n                status: 'connected',\n                startTime: new Date()\n            };\n\n            this.onRoomConnected?.(roomData);\n            return roomData;\n\n        } catch (error) {\n            console.error('Failed to join room:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * Leave the current video call room\n     */\n    async leaveRoom(): Promise<void> {\n        if (this.room) {\n            this.room.disconnect();\n            this.room = null;\n        }\n\n        // Clean up local tracks\n        if (this.localVideoTrack) {\n            this.localVideoTrack.stop();\n            this.localVideoTrack = null;\n        }\n\n        if (this.localAudioTrack) {\n            this.localAudioTrack.stop();\n            this.localAudioTrack = null;\n        }\n\n        this.participants.clear();\n    }\n\n    /**\n     * Toggle local video on/off\n     */\n    async toggleVideo(): Promise<boolean> {\n        if (!this.room) return false;\n\n        if (this.localVideoTrack) {\n            // Disable video\n            this.room.localParticipant.unpublishTrack(this.localVideoTrack);\n            this.localVideoTrack.stop();\n            this.localVideoTrack = null;\n            return false;\n        } else {\n            // Enable video\n            this.localVideoTrack = await this.createLocalVideoTrack();\n            this.room.localParticipant.publishTrack(this.localVideoTrack);\n            return true;\n        }\n    }\n\n    /**\n     * Toggle local audio on/off\n     */\n    async toggleAudio(): Promise<boolean> {\n        if (!this.room) return false;\n\n        if (this.localAudioTrack) {\n            // Mute audio\n            this.room.localParticipant.unpublishTrack(this.localAudioTrack);\n            this.localAudioTrack.stop();\n            this.localAudioTrack = null;\n            return false;\n        } else {\n            // Unmute audio\n            this.localAudioTrack = await this.createLocalAudioTrack();\n            this.room.localParticipant.publishTrack(this.localAudioTrack);\n            return true;\n        }\n    }\n\n    /**\n     * Get current room information\n     */\n    getCurrentRoom(): VideoCallRoom | null {\n        if (!this.room) return null;\n\n        return {\n            roomId: this.room.sid,\n            roomName: this.room.name,\n            participants: this.getParticipantsArray(),\n            status: this.room.state === 'connected' ? 'connected' : 'disconnected',\n            startTime: new Date() // You might want to track this properly\n        };\n    }\n\n    /**\n     * Get local video track for preview\n     */\n    getLocalVideoTrack(): LocalVideoTrack | null {\n        return this.localVideoTrack;\n    }\n\n    /**\n     * Get local audio track\n     */\n    getLocalAudioTrack(): LocalAudioTrack | null {\n        return this.localAudioTrack;\n    }\n\n    private async createLocalVideoTrack(): Promise<LocalVideoTrack> {\n        const { createLocalVideoTrack } = await import('twilio-video');\n        return createLocalVideoTrack({\n            width: 640,\n            height: 480,\n            frameRate: 24\n        });\n    }\n\n    private async createLocalAudioTrack(): Promise<LocalAudioTrack> {\n        const { createLocalAudioTrack } = await import('twilio-video');\n        return createLocalAudioTrack();\n    }\n\n    private setupRoomEventHandlers(): void {\n        if (!this.room) return;\n\n        // Handle participant connected\n        this.room.on('participantConnected', this.handleParticipantConnected);\n\n        // Handle participant disconnected\n        this.room.on('participantDisconnected', this.handleParticipantDisconnected);\n\n        // Handle room disconnected\n        this.room.on('disconnected', (room) => {\n            const roomData: VideoCallRoom = {\n                roomId: room.sid,\n                roomName: room.name,\n                participants: [],\n                status: 'disconnected',\n                startTime: new Date()\n            };\n            this.onRoomDisconnected?.(roomData);\n        });\n    }\n\n    private getParticipantsArray(): VideoCallParticipant[] {\n        return Array.from(this.participants.values());\n    }\n\n    private handleParticipantConnected = (participant: RemoteParticipant): void => {\n        const participantData: VideoCallParticipant = {\n            id: participant.sid,\n            username: participant.identity,\n            isLocal: false,\n            isMuted: !participant.audioTracks.size,\n            isVideoEnabled: !!participant.videoTracks.size\n        };\n\n        this.participants.set(participant.sid, participantData);\n        this.onParticipantConnected?.(participantData);\n\n        // Subscribe to participant's tracks\n        participant.tracks.forEach(publication => {\n            if (publication.isSubscribed) {\n                this.handleTrackSubscribed(publication.track!, participantData);\n            }\n        });\n\n        participant.on('trackSubscribed', (track) => {\n            this.handleTrackSubscribed(track, participantData);\n        });\n\n        participant.on('trackUnsubscribed', (track) => {\n            this.handleTrackUnsubscribed(track, participantData);\n        });\n    };\n\n    private handleParticipantDisconnected = (participant: RemoteParticipant): void => {\n        this.participants.delete(participant.sid);\n        this.onParticipantDisconnected?.(participant.sid);\n    };\n\n    private handleTrackSubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant): void => {\n        this.onTrackSubscribed?.(track, participant);\n    };\n\n    private handleTrackUnsubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant): void => {\n        this.onTrackUnsubscribed?.(track, participant);\n    };\n}\n\n// Export singleton instance\nexport const twilioVideoService = new TwilioVideoService();\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEO,MAAM;IACD,OAAoB,KAAK;IACzB,kBAA0C,KAAK;IAC/C,kBAA0C,KAAK;IAC/C,eAAkD,IAAI,MAAM;IAEpE,kBAAkB;IACV,uBAAqE;IACrE,0BAA4D;IAC5D,gBAAgD;IAChD,mBAAmD;IACnD,kBAA4G;IAC5G,oBAA8G;IAEtH,aAAc;QACV,IAAI,CAAC,kBAAkB;IAC3B;IAEA;;KAEC,GACD,WAAW,SAOV,EAAE;QACC,IAAI,CAAC,sBAAsB,GAAG,UAAU,sBAAsB;QAC9D,IAAI,CAAC,yBAAyB,GAAG,UAAU,yBAAyB;QACpE,IAAI,CAAC,eAAe,GAAG,UAAU,eAAe;QAChD,IAAI,CAAC,kBAAkB,GAAG,UAAU,kBAAkB;QACtD,IAAI,CAAC,iBAAiB,GAAG,UAAU,iBAAiB;QACpD,IAAI,CAAC,mBAAmB,GAAG,UAAU,mBAAmB;IAC5D;IAEA;;KAEC,GACD,MAAM,SAAS,cAAsB,EAAE,QAA2B,EAA0B;QACxF,IAAI;YACA,gCAAgC;YAChC,MAAM,YAAY,MAAM,sHAAA,CAAA,aAAU,CAAC,oBAAoB,CAAC;YAExD,wCAAwC;YACxC,MAAM,SAAS,EAAE;YAEjB,IAAI,SAAS,KAAK,EAAE;gBAChB,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB;gBACvD,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe;YACpC;YAEA,IAAI,SAAS,KAAK,EAAE;gBAChB,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB;gBACvD,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe;YACpC;YAEA,yBAAyB;YACzB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,KAAK,EAAE;gBACvC,MAAM,UAAU,QAAQ;gBACxB,QAAQ;gBACR,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;YACzB;YAEA,4BAA4B;YAC5B,IAAI,CAAC,sBAAsB;YAE3B,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI,CAAC,0BAA0B,CAAC;YACpC;YAEA,4CAA4C;YAC5C,MAAM,mBAAyC;gBAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG;gBAClC,UAAU,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ;gBAC7C,SAAS;gBACT,SAAS,CAAC,SAAS,KAAK;gBACxB,gBAAgB,SAAS,KAAK;YAClC;YACA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;YAEtD,MAAM,WAA0B;gBAC5B,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG;gBACrB,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI;gBACxB,cAAc,IAAI,CAAC,oBAAoB;gBACvC,QAAQ;gBACR,WAAW,IAAI;YACnB;YAEA,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO;QAEX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACV;IACJ;IAEA;;KAEC,GACD,MAAM,YAA2B;QAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,UAAU;YACpB,IAAI,CAAC,IAAI,GAAG;QAChB;QAEA,wBAAwB;QACxB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI;YACzB,IAAI,CAAC,eAAe,GAAG;QAC3B;QAEA,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI;YACzB,IAAI,CAAC,eAAe,GAAG;QAC3B;QAEA,IAAI,CAAC,YAAY,CAAC,KAAK;IAC3B;IAEA;;KAEC,GACD,MAAM,cAAgC;QAClC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;QAEvB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,gBAAgB;YAChB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;YAC9D,IAAI,CAAC,eAAe,CAAC,IAAI;YACzB,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO;QACX,OAAO;YACH,eAAe;YACf,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB;YACvD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe;YAC5D,OAAO;QACX;IACJ;IAEA;;KAEC,GACD,MAAM,cAAgC;QAClC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;QAEvB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,aAAa;YACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;YAC9D,IAAI,CAAC,eAAe,CAAC,IAAI;YACzB,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO;QACX,OAAO;YACH,eAAe;YACf,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB;YACvD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe;YAC5D,OAAO;QACX;IACJ;IAEA;;KAEC,GACD,iBAAuC;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;QAEvB,OAAO;YACH,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG;YACrB,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI;YACxB,cAAc,IAAI,CAAC,oBAAoB;YACvC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,cAAc,cAAc;YACxD,WAAW,IAAI,OAAO,wCAAwC;QAClE;IACJ;IAEA;;KAEC,GACD,qBAA6C;QACzC,OAAO,IAAI,CAAC,eAAe;IAC/B;IAEA;;KAEC,GACD,qBAA6C;QACzC,OAAO,IAAI,CAAC,eAAe;IAC/B;IAEA,MAAc,wBAAkD;QAC5D,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAClC,OAAO,sBAAsB;YACzB,OAAO;YACP,QAAQ;YACR,WAAW;QACf;IACJ;IAEA,MAAc,wBAAkD;QAC5D,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAClC,OAAO;IACX;IAEQ,yBAA+B;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QAEhB,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,wBAAwB,IAAI,CAAC,0BAA0B;QAEpE,kCAAkC;QAClC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,2BAA2B,IAAI,CAAC,6BAA6B;QAE1E,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC;YAC1B,MAAM,WAA0B;gBAC5B,QAAQ,KAAK,GAAG;gBAChB,UAAU,KAAK,IAAI;gBACnB,cAAc,EAAE;gBAChB,QAAQ;gBACR,WAAW,IAAI;YACnB;YACA,IAAI,CAAC,kBAAkB,GAAG;QAC9B;IACJ;IAEQ,uBAA+C;QACnD,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM;IAC9C;IAEQ,6BAA6B,CAAC;QAClC,MAAM,kBAAwC;YAC1C,IAAI,YAAY,GAAG;YACnB,UAAU,YAAY,QAAQ;YAC9B,SAAS;YACT,SAAS,CAAC,YAAY,WAAW,CAAC,IAAI;YACtC,gBAAgB,CAAC,CAAC,YAAY,WAAW,CAAC,IAAI;QAClD;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,GAAG,EAAE;QACvC,IAAI,CAAC,sBAAsB,GAAG;QAE9B,oCAAoC;QACpC,YAAY,MAAM,CAAC,OAAO,CAAC,CAAA;YACvB,IAAI,YAAY,YAAY,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,YAAY,KAAK,EAAG;YACnD;QACJ;QAEA,YAAY,EAAE,CAAC,mBAAmB,CAAC;YAC/B,IAAI,CAAC,qBAAqB,CAAC,OAAO;QACtC;QAEA,YAAY,EAAE,CAAC,qBAAqB,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,OAAO;QACxC;IACJ,EAAE;IAEM,gCAAgC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,GAAG;QACxC,IAAI,CAAC,yBAAyB,GAAG,YAAY,GAAG;IACpD,EAAE;IAEM,wBAAwB,CAAC,OAA4C;QACzE,IAAI,CAAC,iBAAiB,GAAG,OAAO;IACpC,EAAE;IAEM,0BAA0B,CAAC,OAA4C;QAC3E,IAAI,CAAC,mBAAmB,GAAG,OAAO;IACtC,EAAE;AACN;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/VideoCall/VideoCallModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { XMarkIcon, VideoCameraIcon, VideoCameraSlashIcon, MicrophoneIcon, PhoneXMarkIcon } from '@heroicons/react/24/outline';\nimport { twilioVideoService } from '@/services/twilioVideoService';\nimport { VideoCallRoom, VideoCallParticipant, VideoCallSettings } from '@/types/video';\nimport { RemoteVideoTrack, RemoteAudioTrack } from 'twilio-video';\nimport toast from 'react-hot-toast';\n\ninterface VideoCallModalProps {\n    isOpen: boolean;\n    onClose: () => void;\n    conversationId: string;\n    conversationName: string;\n    initialSettings?: VideoCallSettings;\n}\n\nexport const VideoCallModal: React.FC<VideoCallModalProps> = ({\n    isOpen,\n    onClose,\n    conversationId,\n    conversationName,\n    initialSettings = { video: true, audio: true, screenShare: false }\n}) => {\n    const [room, setRoom] = useState<VideoCallRoom | null>(null);\n    const [participants, setParticipants] = useState<VideoCallParticipant[]>([]);\n    const [settings, setSettings] = useState<VideoCallSettings>(initialSettings);\n    const [isConnecting, setIsConnecting] = useState(false);\n    const [callDuration, setCallDuration] = useState(0);\n\n    const localVideoRef = useRef<HTMLVideoElement>(null);\n    const remoteVideoRefs = useRef<Map<string, HTMLVideoElement>>(new Map());\n    const callStartTime = useRef<Date | null>(null);\n    const durationInterval = useRef<NodeJS.Timeout | null>(null);\n\n    useEffect(() => {\n        const handleEffect = async () => {\n            if (isOpen) {\n                await initializeVideoCall();\n            } else {\n                await cleanup();\n            }\n        };\n\n        handleEffect();\n\n        return () => {\n            cleanup();\n        };\n    }, [isOpen]);\n\n    useEffect(() => {\n        // Initialize Twilio service callbacks\n        twilioVideoService.initialize({\n            onParticipantConnected: handleParticipantConnected,\n            onParticipantDisconnected: handleParticipantDisconnected,\n            onRoomConnected: handleRoomConnected,\n            onRoomDisconnected: handleRoomDisconnected,\n            onTrackSubscribed: handleTrackSubscribed,\n            onTrackUnsubscribed: handleTrackUnsubscribed\n        });\n    }, []);\n\n    const initializeVideoCall = async () => {\n        setIsConnecting(true);\n        try {\n            console.log('Initializing video call for conversation:', conversationId);\n            const roomData = await twilioVideoService.joinRoom(conversationId, settings);\n            console.log('Room data received:', roomData);\n\n            setRoom(roomData);\n            setParticipants(roomData.participants || []);\n\n            // Attach local video track\n            const localVideoTrack = twilioVideoService.getLocalVideoTrack();\n            if (localVideoTrack && localVideoRef.current) {\n                localVideoTrack.attach(localVideoRef.current);\n            }\n\n            // Start call duration timer\n            callStartTime.current = new Date();\n            durationInterval.current = setInterval(() => {\n                if (callStartTime.current) {\n                    const duration = Math.floor((Date.now() - callStartTime.current.getTime()) / 1000);\n                    setCallDuration(duration);\n                }\n            }, 1000);\n\n            toast.success('Connected to video call');\n        } catch (error) {\n            console.error('Failed to join video call:', error);\n            toast.error('Failed to join video call');\n            onClose();\n        } finally {\n            setIsConnecting(false);\n        }\n    };\n\n    const cleanup = async () => {\n        if (durationInterval.current) {\n            clearInterval(durationInterval.current);\n            durationInterval.current = null;\n        }\n\n        callStartTime.current = null;\n        setCallDuration(0);\n\n        await twilioVideoService.leaveRoom();\n        setRoom(null);\n        setParticipants([]);\n\n        // Clean up video elements\n        remoteVideoRefs.current.clear();\n    };\n\n    const handleParticipantConnected = (participant: VideoCallParticipant) => {\n        console.log('Participant connected:', participant);\n        setParticipants(prev => {\n            // Check if participant already exists\n            const exists = prev.find(p => p.id === participant.id);\n            if (exists) {\n                return prev;\n            }\n            return [...prev, participant];\n        });\n        toast.success(`${participant.username} joined the call`);\n    };\n\n    const handleParticipantDisconnected = (participantId: string) => {\n        console.log('Participant disconnected:', participantId);\n        setParticipants(prev => {\n            const participant = prev.find(p => p.id === participantId);\n            if (participant) {\n                toast.success(`${participant.username} left the call`);\n            }\n            return prev.filter(p => p.id !== participantId);\n        });\n\n        // Clean up video element\n        remoteVideoRefs.current.delete(participantId);\n    };\n\n    const handleRoomConnected = (roomData: VideoCallRoom) => {\n        setRoom(roomData);\n    };\n\n    const handleRoomDisconnected = (_roomData: VideoCallRoom) => {\n        setRoom(null);\n        toast.success('Call ended');\n        onClose();\n    };\n\n    const handleTrackSubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => {\n        if (track.kind === 'video') {\n            const videoElement = remoteVideoRefs.current.get(participant.id);\n            if (videoElement) {\n                track.attach(videoElement);\n            }\n        }\n    };\n\n    const handleTrackUnsubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, _participant: VideoCallParticipant) => {\n        track.detach();\n    };\n\n    const toggleVideo = async () => {\n        try {\n            const isVideoEnabled = await twilioVideoService.toggleVideo();\n            setSettings(prev => ({ ...prev, video: isVideoEnabled }));\n\n            // Update local video display\n            const localVideoTrack = twilioVideoService.getLocalVideoTrack();\n            if (localVideoTrack && localVideoRef.current) {\n                localVideoTrack.attach(localVideoRef.current);\n            } else if (localVideoRef.current) {\n                localVideoRef.current.srcObject = null;\n            }\n        } catch (error) {\n            console.error('Failed to toggle video:', error);\n            toast.error('Failed to toggle video');\n        }\n    };\n\n    const toggleAudio = async () => {\n        try {\n            const isAudioEnabled = await twilioVideoService.toggleAudio();\n            setSettings(prev => ({ ...prev, audio: isAudioEnabled }));\n        } catch (error) {\n            console.error('Failed to toggle audio:', error);\n            toast.error('Failed to toggle audio');\n        }\n    };\n\n    const endCall = async () => {\n        try {\n            await cleanup();\n            onClose();\n        } catch (error) {\n            console.error('Failed to end call:', error);\n            toast.error('Failed to end call');\n        }\n    };\n\n    const formatDuration = (seconds: number): string => {\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor((seconds % 3600) / 60);\n        const secs = seconds % 60;\n\n        if (hours > 0) {\n            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n        }\n        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    if (!isOpen) return null;\n\n    return (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75\">\n            <div className=\"bg-gray-900 rounded-lg shadow-2xl w-full h-full max-w-6xl max-h-[90vh] flex flex-col\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n                    <div className=\"flex items-center space-x-3\">\n                        <h2 className=\"text-xl font-semibold text-white\">{conversationName}</h2>\n                        {room && (\n                            <span className=\"text-sm text-gray-400\">\n                                {formatDuration(callDuration)}\n                            </span>\n                        )}\n                    </div>\n                    <button\n                        onClick={onClose}\n                        className=\"text-gray-400 hover:text-white transition-colors\"\n                    >\n                        <XMarkIcon className=\"h-6 w-6\" />\n                    </button>\n                </div>\n\n                {/* Video Grid */}\n                <div className=\"flex-1 p-4\">\n                    {isConnecting ? (\n                        <div className=\"flex items-center justify-center h-full\">\n                            <div className=\"text-center\">\n                                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n                                <p className=\"text-white\">Connecting to call...</p>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 h-full\">\n                            {/* Debug info */}\n                            {participants.length === 0 && (\n                                <div className=\"col-span-full text-center text-white\">\n                                    <p>No participants found for video call</p>\n                                    <p className=\"text-sm text-gray-400 mt-2\">\n                                        Room: {room?.roomName || 'Not connected'} |\n                                        Participants: {participants.length}\n                                    </p>\n                                </div>\n                            )}\n\n                            {/* All Participants (including local) */}\n                            {participants.map((participant) => (\n                                <div key={participant.id} className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n                                    {participant.isLocal ? (\n                                        <video\n                                            ref={localVideoRef}\n                                            autoPlay\n                                            muted\n                                            playsInline\n                                            className=\"w-full h-full object-cover\"\n                                        />\n                                    ) : (\n                                        <video\n                                            ref={(el) => {\n                                                if (el) {\n                                                    remoteVideoRefs.current.set(participant.id, el);\n                                                }\n                                            }}\n                                            autoPlay\n                                            playsInline\n                                            className=\"w-full h-full object-cover\"\n                                        />\n                                    )}\n                                    <div className=\"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm\">\n                                        {participant.isLocal ? 'You' : participant.username}\n                                    </div>\n                                    {!participant.isVideoEnabled && (\n                                        <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                                            <VideoCameraSlashIcon className=\"h-12 w-12 text-gray-400\" />\n                                        </div>\n                                    )}\n                                    {participant.isMuted && (\n                                        <div className=\"absolute top-2 right-2 bg-red-500 rounded-full p-1\">\n                                            <MicrophoneIcon className=\"h-4 w-4 text-white opacity-50\" />\n                                        </div>\n                                    )}\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n\n                {/* Controls */}\n                <div className=\"flex items-center justify-center space-x-4 p-4 border-t border-gray-700\">\n                    <button\n                        onClick={toggleAudio}\n                        className={`p-3 rounded-full transition-colors ${settings.audio\n                            ? 'bg-gray-600 hover:bg-gray-500 text-white'\n                            : 'bg-red-500 hover:bg-red-600 text-white'\n                            }`}\n                        title={settings.audio ? 'Mute' : 'Unmute'}\n                    >\n                        {settings.audio ? (\n                            <MicrophoneIcon className=\"h-6 w-6\" />\n                        ) : (\n                            <MicrophoneIcon className=\"h-6 w-6 opacity-50\" />\n                        )}\n                    </button>\n\n                    <button\n                        onClick={toggleVideo}\n                        className={`p-3 rounded-full transition-colors ${settings.video\n                            ? 'bg-gray-600 hover:bg-gray-500 text-white'\n                            : 'bg-red-500 hover:bg-red-600 text-white'\n                            }`}\n                        title={settings.video ? 'Turn off camera' : 'Turn on camera'}\n                    >\n                        {settings.video ? (\n                            <VideoCameraIcon className=\"h-6 w-6\" />\n                        ) : (\n                            <VideoCameraSlashIcon className=\"h-6 w-6\" />\n                        )}\n                    </button>\n\n                    <button\n                        onClick={endCall}\n                        className=\"p-3 rounded-full bg-red-500 hover:bg-red-600 text-white transition-colors\"\n                        title=\"End call\"\n                    >\n                        <PhoneXMarkIcon className=\"h-6 w-6\" />\n                    </button>\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;;;AAPA;;;;;AAiBO,MAAM,iBAAgD,CAAC,EAC1D,MAAM,EACN,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,kBAAkB;IAAE,OAAO;IAAM,OAAO;IAAM,aAAa;AAAM,CAAC,EACrE;;IACG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAClE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,MAAM;yDAAe;oBACjB,IAAI,QAAQ;wBACR,MAAM;oBACV,OAAO;wBACH,MAAM;oBACV;gBACJ;;YAEA;YAEA;4CAAO;oBACH;gBACJ;;QACJ;mCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,sCAAsC;YACtC,wIAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC;gBAC1B,wBAAwB;gBACxB,2BAA2B;gBAC3B,iBAAiB;gBACjB,oBAAoB;gBACpB,mBAAmB;gBACnB,qBAAqB;YACzB;QACJ;mCAAG,EAAE;IAEL,MAAM,sBAAsB;QACxB,gBAAgB;QAChB,IAAI;YACA,QAAQ,GAAG,CAAC,6CAA6C;YACzD,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,QAAQ,CAAC,gBAAgB;YACnE,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,QAAQ;YACR,gBAAgB,SAAS,YAAY,IAAI,EAAE;YAE3C,2BAA2B;YAC3B,MAAM,kBAAkB,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YAC7D,IAAI,mBAAmB,cAAc,OAAO,EAAE;gBAC1C,gBAAgB,MAAM,CAAC,cAAc,OAAO;YAChD;YAEA,4BAA4B;YAC5B,cAAc,OAAO,GAAG,IAAI;YAC5B,iBAAiB,OAAO,GAAG,YAAY;gBACnC,IAAI,cAAc,OAAO,EAAE;oBACvB,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,cAAc,OAAO,CAAC,OAAO,EAAE,IAAI;oBAC7E,gBAAgB;gBACpB;YACJ,GAAG;YAEH,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACJ,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,MAAM,UAAU;QACZ,IAAI,iBAAiB,OAAO,EAAE;YAC1B,cAAc,iBAAiB,OAAO;YACtC,iBAAiB,OAAO,GAAG;QAC/B;QAEA,cAAc,OAAO,GAAG;QACxB,gBAAgB;QAEhB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,SAAS;QAClC,QAAQ;QACR,gBAAgB,EAAE;QAElB,0BAA0B;QAC1B,gBAAgB,OAAO,CAAC,KAAK;IACjC;IAEA,MAAM,6BAA6B,CAAC;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QACtC,gBAAgB,CAAA;YACZ,sCAAsC;YACtC,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;YACrD,IAAI,QAAQ;gBACR,OAAO;YACX;YACA,OAAO;mBAAI;gBAAM;aAAY;QACjC;QACA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,YAAY,QAAQ,CAAC,gBAAgB,CAAC;IAC3D;IAEA,MAAM,gCAAgC,CAAC;QACnC,QAAQ,GAAG,CAAC,6BAA6B;QACzC,gBAAgB,CAAA;YACZ,MAAM,cAAc,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,aAAa;gBACb,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,YAAY,QAAQ,CAAC,cAAc,CAAC;YACzD;YACA,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrC;QAEA,yBAAyB;QACzB,gBAAgB,OAAO,CAAC,MAAM,CAAC;IACnC;IAEA,MAAM,sBAAsB,CAAC;QACzB,QAAQ;IACZ;IAEA,MAAM,yBAAyB,CAAC;QAC5B,QAAQ;QACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACd;IACJ;IAEA,MAAM,wBAAwB,CAAC,OAA4C;QACvE,IAAI,MAAM,IAAI,KAAK,SAAS;YACxB,MAAM,eAAe,gBAAgB,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;YAC/D,IAAI,cAAc;gBACd,MAAM,MAAM,CAAC;YACjB;QACJ;IACJ;IAEA,MAAM,0BAA0B,CAAC,OAA4C;QACzE,MAAM,MAAM;IAChB;IAEA,MAAM,cAAc;QAChB,IAAI;YACA,MAAM,iBAAiB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,WAAW;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAe,CAAC;YAEvD,6BAA6B;YAC7B,MAAM,kBAAkB,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YAC7D,IAAI,mBAAmB,cAAc,OAAO,EAAE;gBAC1C,gBAAgB,MAAM,CAAC,cAAc,OAAO;YAChD,OAAO,IAAI,cAAc,OAAO,EAAE;gBAC9B,cAAc,OAAO,CAAC,SAAS,GAAG;YACtC;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,cAAc;QAChB,IAAI;YACA,MAAM,iBAAiB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,WAAW;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAe,CAAC;QAC3D,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,UAAU;QACZ,IAAI;YACA,MAAM;YACN;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,MAAM,OAAO,UAAU;QAEvB,IAAI,QAAQ,GAAG;YACX,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAC5H;QACA,OAAO,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACvF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BAEX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;gCACjD,sBACG,6LAAC;oCAAK,WAAU;8CACX,eAAe;;;;;;;;;;;;sCAI5B,6LAAC;4BACG,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBAAI,WAAU;8BACV,6BACG,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAa;;;;;;;;;;;;;;;;6CAIlC,6LAAC;wBAAI,WAAU;;4BAEV,aAAa,MAAM,KAAK,mBACrB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;;4CAA6B;4CAC/B,MAAM,YAAY;4CAAgB;4CAC1B,aAAa,MAAM;;;;;;;;;;;;;4BAM7C,aAAa,GAAG,CAAC,CAAC,4BACf,6LAAC;oCAAyB,WAAU;;wCAC/B,YAAY,OAAO,iBAChB,6LAAC;4CACG,KAAK;4CACL,QAAQ;4CACR,KAAK;4CACL,WAAW;4CACX,WAAU;;;;;iEAGd,6LAAC;4CACG,KAAK,CAAC;gDACF,IAAI,IAAI;oDACJ,gBAAgB,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE;gDAChD;4CACJ;4CACA,QAAQ;4CACR,WAAW;4CACX,WAAU;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;sDACV,YAAY,OAAO,GAAG,QAAQ,YAAY,QAAQ;;;;;;wCAEtD,CAAC,YAAY,cAAc,kBACxB,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,0OAAA,CAAA,uBAAoB;gDAAC,WAAU;;;;;;;;;;;wCAGvC,YAAY,OAAO,kBAChB,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;mCA/B5B,YAAY,EAAE;;;;;;;;;;;;;;;;8BAyCxC,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,SAAS;4BACT,WAAW,CAAC,mCAAmC,EAAE,SAAS,KAAK,GACzD,6CACA,0CACA;4BACN,OAAO,SAAS,KAAK,GAAG,SAAS;sCAEhC,SAAS,KAAK,iBACX,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;qDAE1B,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;sCAIlC,6LAAC;4BACG,SAAS;4BACT,WAAW,CAAC,mCAAmC,EAAE,SAAS,KAAK,GACzD,6CACA,0CACA;4BACN,OAAO,SAAS,KAAK,GAAG,oBAAoB;sCAE3C,SAAS,KAAK,iBACX,6LAAC,gOAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;qDAE3B,6LAAC,0OAAA,CAAA,uBAAoB;gCAAC,WAAU;;;;;;;;;;;sCAIxC,6LAAC;4BACG,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlD;GAvUa;KAAA", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/VideoCall/VideoCallInvitation.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { VideoCameraIcon, PhoneXMarkIcon, UserIcon } from '@heroicons/react/24/outline';\nimport { VideoCallInvitation as VideoCallInvitationType } from '@/types/video';\nimport { ApiService } from '@/api/axios';\nimport toast from 'react-hot-toast';\n\ninterface VideoCallInvitationProps {\n    invitation: VideoCallInvitationType;\n    onAccept: (invitation: VideoCallInvitationType) => void;\n    onDecline: (invitation: VideoCallInvitationType) => void;\n    onExpire: (invitationId: string) => void;\n}\n\nexport const VideoCallInvitation: React.FC<VideoCallInvitationProps> = ({\n    invitation,\n    onAccept,\n    onDecline,\n    onExpire\n}) => {\n    const [timeLeft, setTimeLeft] = useState<number>(0);\n    const [isResponding, setIsResponding] = useState(false);\n\n    useEffect(() => {\n        // Calculate initial time left\n        const expiresAt = new Date(invitation.expiresAt).getTime();\n        const now = Date.now();\n        const initialTimeLeft = Math.max(0, Math.floor((expiresAt - now) / 1000));\n        \n        setTimeLeft(initialTimeLeft);\n\n        if (initialTimeLeft <= 0) {\n            onExpire(invitation.id);\n            return;\n        }\n\n        // Start countdown timer\n        const timer = setInterval(() => {\n            setTimeLeft(prev => {\n                const newTimeLeft = prev - 1;\n                if (newTimeLeft <= 0) {\n                    clearInterval(timer);\n                    onExpire(invitation.id);\n                    return 0;\n                }\n                return newTimeLeft;\n            });\n        }, 1000);\n\n        return () => clearInterval(timer);\n    }, [invitation.expiresAt, invitation.id, onExpire]);\n\n    const handleAccept = async () => {\n        if (isResponding) return;\n        \n        setIsResponding(true);\n        try {\n            await ApiService.respondToVideoCallInvitation(invitation.id, 'accept');\n            onAccept(invitation);\n            toast.success('Joining video call...');\n        } catch (error) {\n            console.error('Failed to accept video call:', error);\n            toast.error('Failed to join video call');\n        } finally {\n            setIsResponding(false);\n        }\n    };\n\n    const handleDecline = async () => {\n        if (isResponding) return;\n        \n        setIsResponding(true);\n        try {\n            await ApiService.respondToVideoCallInvitation(invitation.id, 'decline');\n            onDecline(invitation);\n            toast.success('Video call declined');\n        } catch (error) {\n            console.error('Failed to decline video call:', error);\n            toast.error('Failed to decline video call');\n        } finally {\n            setIsResponding(false);\n        }\n    };\n\n    const formatTimeLeft = (seconds: number): string => {\n        const minutes = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${minutes}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    if (invitation.status !== 'pending' || timeLeft <= 0) {\n        return null;\n    }\n\n    return (\n        <div className=\"fixed top-4 right-4 z-50 bg-white rounded-lg shadow-2xl border border-gray-200 p-6 max-w-sm w-full animate-slide-in-right\">\n            <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <VideoCameraIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                        <UserIcon className=\"h-4 w-4 text-gray-400\" />\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                            {invitation.inviterUsername}\n                        </p>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 mb-3\">\n                        is inviting you to a video call\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between mb-4\">\n                        <span className=\"text-xs text-gray-500\">\n                            Expires in {formatTimeLeft(timeLeft)}\n                        </span>\n                        <div className=\"w-16 h-1 bg-gray-200 rounded-full overflow-hidden\">\n                            <div \n                                className=\"h-full bg-red-500 transition-all duration-1000 ease-linear\"\n                                style={{ \n                                    width: `${Math.max(0, (timeLeft / 30) * 100)}%` // Assuming 30 seconds expiry\n                                }}\n                            />\n                        </div>\n                    </div>\n                    \n                    <div className=\"flex space-x-3\">\n                        <button\n                            onClick={handleAccept}\n                            disabled={isResponding}\n                            className=\"flex-1 bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2\"\n                        >\n                            {isResponding ? (\n                                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                            ) : (\n                                <>\n                                    <VideoCameraIcon className=\"h-4 w-4\" />\n                                    <span>Accept</span>\n                                </>\n                            )}\n                        </button>\n                        \n                        <button\n                            onClick={handleDecline}\n                            disabled={isResponding}\n                            className=\"flex-1 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2\"\n                        >\n                            {isResponding ? (\n                                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                            ) : (\n                                <>\n                                    <PhoneXMarkIcon className=\"h-4 w-4\" />\n                                    <span>Decline</span>\n                                </>\n                            )}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\n// CSS for animation (add to your global CSS or Tailwind config)\nconst styles = `\n@keyframes slide-in-right {\n    from {\n        transform: translateX(100%);\n        opacity: 0;\n    }\n    to {\n        transform: translateX(0);\n        opacity: 1;\n    }\n}\n\n.animate-slide-in-right {\n    animation: slide-in-right 0.3s ease-out;\n}\n`;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;;;AANA;;;;;AAeO,MAAM,sBAA0D,CAAC,EACpE,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACX;;IACG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN,8BAA8B;YAC9B,MAAM,YAAY,IAAI,KAAK,WAAW,SAAS,EAAE,OAAO;YACxD,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,kBAAkB,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI;YAEnE,YAAY;YAEZ,IAAI,mBAAmB,GAAG;gBACtB,SAAS,WAAW,EAAE;gBACtB;YACJ;YAEA,wBAAwB;YACxB,MAAM,QAAQ;uDAAY;oBACtB;+DAAY,CAAA;4BACR,MAAM,cAAc,OAAO;4BAC3B,IAAI,eAAe,GAAG;gCAClB,cAAc;gCACd,SAAS,WAAW,EAAE;gCACtB,OAAO;4BACX;4BACA,OAAO;wBACX;;gBACJ;sDAAG;YAEH;iDAAO,IAAM,cAAc;;QAC/B;wCAAG;QAAC,WAAW,SAAS;QAAE,WAAW,EAAE;QAAE;KAAS;IAElD,MAAM,eAAe;QACjB,IAAI,cAAc;QAElB,gBAAgB;QAChB,IAAI;YACA,MAAM,sHAAA,CAAA,aAAU,CAAC,4BAA4B,CAAC,WAAW,EAAE,EAAE;YAC7D,SAAS;YACT,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,MAAM,gBAAgB;QAClB,IAAI,cAAc;QAElB,gBAAgB;QAChB,IAAI;YACA,MAAM,sHAAA,CAAA,aAAU,CAAC,4BAA4B,CAAC,WAAW,EAAE,EAAE;YAC7D,UAAU;YACV,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC3D;IAEA,IAAI,WAAW,MAAM,KAAK,aAAa,YAAY,GAAG;QAClD,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAInC,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CACR,WAAW,eAAe;;;;;;;;;;;;sCAInC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAK,WAAU;;wCAAwB;wCACxB,eAAe;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,AAAC,WAAW,KAAM,KAAK,CAAC,CAAC,CAAC,6BAA6B;wCACjF;;;;;;;;;;;;;;;;;sCAKZ,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACG,6LAAC;wCAAI,WAAU;;;;;6DAEf;;0DACI,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;0DAAK;;;;;;;;;;;;;8CAKlB,6LAAC;oCACG,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACG,6LAAC;wCAAI,WAAU;;;;;6DAEf;;0DACI,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;GAtJa;KAAA;AAwJb,gEAAgE;AAChE,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;AAehB,CAAC", "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/VideoCall/VideoCallButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { VideoCameraIcon } from '@heroicons/react/24/outline';\nimport { ApiService } from '@/api/axios';\nimport { ConversationCreationResponse } from '@/types/chat';\nimport toast from 'react-hot-toast';\n\ninterface VideoCallButtonProps {\n    conversation: ConversationCreationResponse;\n    onStartCall: (conversationId: string) => void;\n    disabled?: boolean;\n    className?: string;\n}\n\nexport const VideoCallButton: React.FC<VideoCallButtonProps> = ({\n    conversation,\n    onStartCall,\n    disabled = false,\n    className = \"\"\n}) => {\n    const [isStarting, setIsStarting] = useState(false);\n\n    const handleStartCall = async () => {\n        if (isStarting || disabled) return;\n\n        setIsStarting(true);\n        try {\n            // Get participant IDs from conversation\n            const participantIds = conversation.participants\n                ?.map(p => p.id)\n                .filter(id => id !== undefined) || [];\n\n            if (participantIds.length === 0) {\n                toast.error('No participants found for video call');\n                return;\n            }\n\n            // Create video call invitation\n            await ApiService.createVideoCallInvitation(conversation.id, participantIds);\n            \n            // Start the call\n            onStartCall(conversation.id);\n            \n            toast.success('Starting video call...');\n        } catch (error) {\n            console.error('Failed to start video call:', error);\n            toast.error('Failed to start video call');\n        } finally {\n            setIsStarting(false);\n        }\n    };\n\n    return (\n        <button\n            onClick={handleStartCall}\n            disabled={disabled || isStarting}\n            className={`\n                inline-flex items-center justify-center p-2 rounded-lg transition-all duration-200\n                ${disabled || isStarting\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                    : 'bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 active:bg-blue-200'\n                }\n                ${className}\n            `}\n            title=\"Start video call\"\n        >\n            {isStarting ? (\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600\"></div>\n            ) : (\n                <VideoCameraIcon className=\"h-5 w-5\" />\n            )}\n        </button>\n    );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAeO,MAAM,kBAAkD,CAAC,EAC5D,YAAY,EACZ,WAAW,EACX,WAAW,KAAK,EAChB,YAAY,EAAE,EACjB;;IACG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACpB,IAAI,cAAc,UAAU;QAE5B,cAAc;QACd,IAAI;YACA,wCAAwC;YACxC,MAAM,iBAAiB,aAAa,YAAY,EAC1C,IAAI,CAAA,IAAK,EAAE,EAAE,EACd,OAAO,CAAA,KAAM,OAAO,cAAc,EAAE;YAEzC,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC7B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACJ;YAEA,+BAA+B;YAC/B,MAAM,sHAAA,CAAA,aAAU,CAAC,yBAAyB,CAAC,aAAa,EAAE,EAAE;YAE5D,iBAAiB;YACjB,YAAY,aAAa,EAAE;YAE3B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,cAAc;QAClB;IACJ;IAEA,qBACI,6LAAC;QACG,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAC;;gBAER,EAAE,YAAY,aACR,iDACA,oFACL;gBACD,EAAE,UAAU;YAChB,CAAC;QACD,OAAM;kBAEL,2BACG,6LAAC;YAAI,WAAU;;;;;iCAEf,6LAAC,gOAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAI3C;GA3Da;KAAA", "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/VideoCall/index.ts"], "sourcesContent": ["export { VideoCallModal } from './VideoCallModal';\nexport { VideoCallInvitation } from './VideoCallInvitation';\nexport { VideoCallButton } from './VideoCallButton';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/ChatHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { ConversationCreationResponse, ConnectionStatus } from \"@/types/chat\";\nimport { WebSocketStatusCompact } from \"./WebSocketStatus\";\nimport { VideoCallButton } from \"./VideoCall\";\n\ninterface ChatHeaderProps {\n    conversation: ConversationCreationResponse | undefined;\n    connectionStatus: ConnectionStatus;\n    onStartVideoCall?: (conversationId: string) => void;\n}\n\nexport const ChatHeader: React.FC<ChatHeaderProps> = ({\n    conversation,\n    connectionStatus,\n    onStartVideoCall\n}) => {\n    if (!conversation) {\n        return (\n            <></>\n        );\n    }\n    const displayName = conversation.conversationName || 'Unknown';\n    const displayInfo = `${conversation.participantInfo?.length || 0} participants`;\n    const displayInitial = displayName.charAt(0).toUpperCase();\n\n    return (\n        <div className=\"h-16 bg-white/95 backdrop-blur-sm border-b border-slate-200/60 flex items-center justify-between px-6 shadow-sm\">\n            <div className=\"flex items-center gap-4\">\n                <div className=\"relative\">\n                    {conversation.conversationAvatar ? (\n                        <img\n                            src={conversation.conversationAvatar}\n                            alt={displayName}\n                            className=\"w-11 h-11 rounded-full object-cover shadow-sm\"\n                        />\n                    ) : (\n                        <div className=\"w-11 h-11 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold shadow-sm\">\n                            {displayInitial}\n                        </div>\n                    )}\n                    <div className=\"absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-white shadow-sm\"></div>\n                </div>\n                <div>\n                    <h2 className=\"font-semibold text-slate-900 text-lg\">\n                        {displayName}\n                    </h2>\n                    <p className=\"text-sm text-slate-500 font-medium\">\n                        {displayInfo} • Active now\n                    </p>\n                </div>\n            </div>\n\n            <div className=\"flex items-center gap-3\">\n                {/* WebSocket Connection Status */}\n                <WebSocketStatusCompact\n                    connectionStatus={connectionStatus}\n                    className=\"animate-pulse\"\n                />\n\n                <button\n                    className=\"p-2 rounded-lg bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 transition-colors duration-150\"\n                    title=\"Voice call\"\n                >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                    </svg>\n                </button>\n\n                <VideoCallButton\n                    conversation={conversation}\n                    onStartCall={onStartVideoCall || (() => { })}\n                    disabled={!connectionStatus.isConnected}\n                />\n\n                <button\n                    className=\"p-2 rounded-lg bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-700 transition-colors duration-150\"\n                    title=\"More options\"\n                >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\" />\n                    </svg>\n                </button>\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AALA;;;;AAaO,MAAM,aAAwC,CAAC,EAClD,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EACnB;IACG,IAAI,CAAC,cAAc;QACf,qBACI;IAER;IACA,MAAM,cAAc,aAAa,gBAAgB,IAAI;IACrD,MAAM,cAAc,GAAG,aAAa,eAAe,EAAE,UAAU,EAAE,aAAa,CAAC;IAC/E,MAAM,iBAAiB,YAAY,MAAM,CAAC,GAAG,WAAW;IAExD,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;4BACV,aAAa,kBAAkB,iBAC5B,6LAAC;gCACG,KAAK,aAAa,kBAAkB;gCACpC,KAAK;gCACL,WAAU;;;;;qDAGd,6LAAC;gCAAI,WAAU;0CACV;;;;;;0CAGT,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEnB,6LAAC;;0CACG,6LAAC;gCAAG,WAAU;0CACT;;;;;;0CAEL,6LAAC;gCAAE,WAAU;;oCACR;oCAAY;;;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC,wIAAA,CAAA,yBAAsB;wBACnB,kBAAkB;wBAClB,WAAU;;;;;;kCAGd,6LAAC;wBACG,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/D,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAI7E,6LAAC,qJAAA,CAAA,kBAAe;wBACZ,cAAc;wBACd,aAAa,oBAAoB,CAAC,KAAQ,CAAC;wBAC3C,UAAU,CAAC,iBAAiB,WAAW;;;;;;kCAG3C,6LAAC;wBACG,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/D,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7F;KA1Ea", "debugId": null}}, {"offset": {"line": 2441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/ImagePreviewModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { XMarkIcon, ChevronLeftIcon, ChevronRightIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';\nimport toast from 'react-hot-toast';\n\ninterface ImagePreviewModalProps {\n    images: string[];\n    initialIndex: number;\n    isOpen: boolean;\n    onClose: () => void;\n}\n\nexport const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({\n    images,\n    initialIndex,\n    isOpen,\n    onClose\n}) => {\n    const [currentIndex, setCurrentIndex] = useState(initialIndex);\n\n    useEffect(() => {\n        setCurrentIndex(initialIndex);\n    }, [initialIndex]);\n\n    useEffect(() => {\n        const handleKeyDown = (e: KeyboardEvent) => {\n            if (!isOpen) return;\n\n            switch (e.key) {\n                case 'Escape':\n                    onClose();\n                    break;\n                case 'ArrowLeft':\n                    goToPrevious();\n                    break;\n                case 'ArrowRight':\n                    goToNext();\n                    break;\n            }\n        };\n\n        document.addEventListener('keydown', handleKeyDown);\n        return () => document.removeEventListener('keydown', handleKeyDown);\n    }, [isOpen, currentIndex]);\n\n    const goToPrevious = () => {\n        setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));\n    };\n\n    const goToNext = () => {\n        setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));\n    };\n\n    const extractFilename = (url: string, fallback: string): string => {\n        try {\n            const urlParts = url.split('/');\n            const encodedFilename = urlParts[urlParts.length - 1];\n            if (!encodedFilename) return fallback;\n\n            // Decode and remove query parameters\n            const decodedName = decodeURIComponent(encodedFilename).split('?')[0];\n            return decodedName || fallback;\n        } catch (e) {\n            return fallback;\n        }\n    };\n\n    const downloadImage = (url: string, index: number) => {\n        try {\n            if (!url) {\n                toast.error('Invalid image URL');\n                return;\n            }\n\n            const link = document.createElement('a');\n            link.href = url;\n            const fileName = extractFilename(url, `image-${index + 1}-${Date.now()}.jpg`);\n            link.download = fileName;\n            link.target = '_blank';\n            link.rel = 'noopener noreferrer';\n\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n\n            toast.success('Image downloaded successfully!', {\n                duration: 3000,\n            });\n        } catch (error) {\n            toast.error('Failed to download image. Please try again.');\n        }\n    };\n\n    if (!isOpen) return null;\n\n    return (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n            <div\n                className=\"absolute inset-0 bg-black/90\"\n                onClick={onClose}\n            />\n\n            <div className=\"relative z-10 w-screen h-screen flex items-center justify-center\">\n                {/* Close button */}\n                <button\n                    onClick={onClose}\n                    className=\"absolute top-6 right-6 z-20 w-12 h-12 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm\"\n                >\n                    <XMarkIcon className=\"w-6 h-6\" />\n                </button>\n\n                {/* Download button */}\n                <button\n                    onClick={() => downloadImage(images[currentIndex], currentIndex)}\n                    className=\"absolute top-6 right-20 z-10 p-2 bg-white/10 hover:bg-white/20 text-white rounded-full transition-colors duration-200 backdrop-blur-sm\"\n                    title=\"Download image\"\n                >\n                    <ArrowDownTrayIcon className=\"w-6 h-6\" />\n                </button>\n\n                {/* Navigation buttons */}\n                {images.length > 1 && (\n                    <>\n                        <button\n                            onClick={goToPrevious}\n                            className=\"absolute left-6 top-1/2 transform -translate-y-1/2 z-20 w-14 h-14 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm\"\n                        >\n                            <ChevronLeftIcon className=\"w-7 h-7\" />\n                        </button>\n\n                        <button\n                            onClick={goToNext}\n                            className=\"absolute right-6 top-1/2 transform -translate-y-1/2 z-20 w-14 h-14 bg-white/10 hover:bg-white/20 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 backdrop-blur-sm\"\n                        >\n                            <ChevronRightIcon className=\"w-7 h-7\" />\n                        </button>\n                    </>\n                )}\n\n                {/* Image container */}\n                <div className=\"flex items-center justify-center w-full h-full px-24 py-20\">\n                    <img\n                        src={images[currentIndex]}\n                        alt={`Preview ${currentIndex + 1}`}\n                        className=\"max-w-full max-h-full w-auto h-auto object-contain rounded-lg shadow-2xl\"\n                        onClick={(e) => e.stopPropagation()}\n                    />\n                </div>\n\n                {/* Image counter and thumbnails */}\n                {images.length > 1 && (\n                    <div className=\"absolute bottom-6 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-3\">\n                        {/* Counter */}\n                        <div className=\"bg-white/10 text-white px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm\">\n                            {currentIndex + 1} / {images.length}\n                        </div>\n\n                        {/* Thumbnails */}\n                        <div className=\"flex gap-2 bg-white/10 p-2 rounded-lg backdrop-blur-sm\">\n                            {images.map((image, index) => (\n                                <button\n                                    key={index}\n                                    onClick={() => setCurrentIndex(index)}\n                                    className={`w-12 h-12 rounded-lg overflow-hidden transition-all duration-200 ${index === currentIndex\n                                        ? 'ring-2 ring-white scale-110'\n                                        : 'opacity-60 hover:opacity-100'\n                                        }`}\n                                >\n                                    <img\n                                        src={image}\n                                        alt={`Thumbnail ${index + 1}`}\n                                        className=\"w-full h-full object-cover\"\n                                    />\n                                </button>\n                            ))}\n                        </div>\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AASO,MAAM,oBAAsD,CAAC,EAChE,MAAM,EACN,YAAY,EACZ,MAAM,EACN,OAAO,EACV;;IACG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN,gBAAgB;QACpB;sCAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN,MAAM;6DAAgB,CAAC;oBACnB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,EAAE,GAAG;wBACT,KAAK;4BACD;4BACA;wBACJ,KAAK;4BACD;4BACA;wBACJ,KAAK;4BACD;4BACA;oBACR;gBACJ;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;+CAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACzD;sCAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,eAAe;QACjB,gBAAgB,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM,GAAG;IACrE;IAEA,MAAM,WAAW;QACb,gBAAgB,CAAC,OAAU,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,IAAI;IACrE;IAEA,MAAM,kBAAkB,CAAC,KAAa;QAClC,IAAI;YACA,MAAM,WAAW,IAAI,KAAK,CAAC;YAC3B,MAAM,kBAAkB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACrD,IAAI,CAAC,iBAAiB,OAAO;YAE7B,qCAAqC;YACrC,MAAM,cAAc,mBAAmB,iBAAiB,KAAK,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,eAAe;QAC1B,EAAE,OAAO,GAAG;YACR,OAAO;QACX;IACJ;IAEA,MAAM,gBAAgB,CAAC,KAAa;QAChC,IAAI;YACA,IAAI,CAAC,KAAK;gBACN,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACJ;YAEA,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,MAAM,WAAW,gBAAgB,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;YAC5E,KAAK,QAAQ,GAAG;YAChB,KAAK,MAAM,GAAG;YACd,KAAK,GAAG,GAAG;YAEX,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,kCAAkC;gBAC5C,UAAU;YACd;QACJ,EAAE,OAAO,OAAO;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBACG,WAAU;gBACV,SAAS;;;;;;0BAGb,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBACG,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAIzB,6LAAC;wBACG,SAAS,IAAM,cAAc,MAAM,CAAC,aAAa,EAAE;wBACnD,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;oBAIhC,OAAO,MAAM,GAAG,mBACb;;0CACI,6LAAC;gCACG,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,gOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;0CAG/B,6LAAC;gCACG,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;kCAMxC,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BACG,KAAK,MAAM,CAAC,aAAa;4BACzB,KAAK,CAAC,QAAQ,EAAE,eAAe,GAAG;4BAClC,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;oBAKxC,OAAO,MAAM,GAAG,mBACb,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;;oCACV,eAAe;oCAAE;oCAAI,OAAO,MAAM;;;;;;;0CAIvC,6LAAC;gCAAI,WAAU;0CACV,OAAO,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;wCAEG,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,iEAAiE,EAAE,UAAU,eACnF,gCACA,gCACA;kDAEN,cAAA,6LAAC;4CACG,KAAK;4CACL,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;4CAC7B,WAAU;;;;;;uCAVT;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBzC;GAzKa;KAAA", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n    size?: 'sm' | 'md' | 'lg';\n    color?: 'blue' | 'white' | 'gray';\n    className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n    size = 'md',\n    color = 'blue',\n    className = ''\n}) => {\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8'\n    };\n\n    const colorClasses = {\n        blue: 'border-blue-500',\n        white: 'border-white',\n        gray: 'border-gray-500'\n    };\n\n    return (\n        <div\n            className={`${sizeClasses[size]} border-2 ${colorClasses[color]} border-t-transparent rounded-full animate-spin ${className}`}\n        />\n    );\n};\n\ninterface LoadingDotsProps {\n    size?: 'sm' | 'md' | 'lg';\n    color?: 'blue' | 'white' | 'gray';\n    className?: string;\n}\n\nexport const LoadingDots: React.FC<LoadingDotsProps> = ({\n    size = 'md',\n    color = 'blue',\n    className = ''\n}) => {\n    const sizeClasses = {\n        sm: 'w-1 h-1',\n        md: 'w-1.5 h-1.5',\n        lg: 'w-2 h-2'\n    };\n\n    const colorClasses = {\n        blue: 'bg-blue-500',\n        white: 'bg-white',\n        gray: 'bg-gray-500'\n    };\n\n    return (\n        <div className={`flex items-center space-x-1 ${className}`}>\n            <div className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-pulse`}></div>\n            <div className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-pulse`} style={{ animationDelay: '0.2s' }}></div>\n            <div className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-pulse`} style={{ animationDelay: '0.4s' }}></div>\n        </div>\n    );\n};\n\ninterface LoadingOverlayProps {\n    isVisible: boolean;\n    message?: string;\n    className?: string;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n    isVisible,\n    message = 'Loading...',\n    className = ''\n}) => {\n    if (!isVisible) return null;\n\n    return (\n        <div className={`absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10 ${className}`}>\n            <div className=\"flex flex-col items-center space-y-3\">\n                <LoadingSpinner size=\"lg\" color=\"blue\" />\n                <p className=\"text-sm font-medium text-slate-600\">{message}</p>\n            </div>\n        </div>\n    );\n};\n\ninterface SkeletonProps {\n    className?: string;\n    lines?: number;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({\n    className = '',\n    lines = 1\n}) => {\n    return (\n        <div className={`animate-pulse ${className}`}>\n            {Array.from({ length: lines }).map((_, index) => (\n                <div\n                    key={index}\n                    className={`bg-slate-200 rounded ${index === lines - 1 ? 'w-3/4' : 'w-full'} h-4 ${index > 0 ? 'mt-2' : ''}`}\n                />\n            ))}\n        </div>\n    );\n};\n\ninterface MessageSkeletonProps {\n    isMe?: boolean;\n    className?: string;\n}\n\nexport const MessageSkeleton: React.FC<MessageSkeletonProps> = ({\n    isMe = false,\n    className = ''\n}) => {\n    return (\n        <div className={`flex ${isMe ? 'justify-end' : 'justify-start'} ${className}`}>\n            <div className={`max-w-xs lg:max-w-md p-4 rounded-2xl ${isMe ? 'bg-blue-100 rounded-br-md' : 'bg-slate-100 rounded-bl-md'\n                }`}>\n                <Skeleton lines={2} />\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;;;;;;AAQO,MAAM,iBAAgD,CAAC,EAC1D,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,YAAY,EAAE,EACjB;IACG,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,MAAM,eAAe;QACjB,MAAM;QACN,OAAO;QACP,MAAM;IACV;IAEA,qBACI,6LAAC;QACG,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,gDAAgD,EAAE,WAAW;;;;;;AAGzI;KAtBa;AA8BN,MAAM,cAA0C,CAAC,EACpD,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,YAAY,EAAE,EACjB;IACG,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,MAAM,eAAe;QACjB,MAAM;QACN,OAAO;QACP,MAAM;IACV;IAEA,qBACI,6LAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;0BACtD,6LAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,2BAA2B,CAAC;;;;;;0BACxF,6LAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,2BAA2B,CAAC;gBAAE,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;0BAC1H,6LAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,2BAA2B,CAAC;gBAAE,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;;;;;;;AAGtI;MAxBa;AAgCN,MAAM,iBAAgD,CAAC,EAC1D,SAAS,EACT,UAAU,YAAY,EACtB,YAAY,EAAE,EACjB;IACG,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACI,6LAAC;QAAI,WAAW,CAAC,oFAAoF,EAAE,WAAW;kBAC9G,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAe,MAAK;oBAAK,OAAM;;;;;;8BAChC,6LAAC;oBAAE,WAAU;8BAAsC;;;;;;;;;;;;;;;;;AAInE;MAfa;AAsBN,MAAM,WAAoC,CAAC,EAC9C,YAAY,EAAE,EACd,QAAQ,CAAC,EACZ;IACG,qBACI,6LAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;kBACvC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACnC,6LAAC;gBAEG,WAAW,CAAC,qBAAqB,EAAE,UAAU,QAAQ,IAAI,UAAU,SAAS,KAAK,EAAE,QAAQ,IAAI,SAAS,IAAI;eADvG;;;;;;;;;;AAMzB;MAda;AAqBN,MAAM,kBAAkD,CAAC,EAC5D,OAAO,KAAK,EACZ,YAAY,EAAE,EACjB;IACG,qBACI,6LAAC;QAAI,WAAW,CAAC,KAAK,EAAE,OAAO,gBAAgB,gBAAgB,CAAC,EAAE,WAAW;kBACzE,cAAA,6LAAC;YAAI,WAAW,CAAC,qCAAqC,EAAE,OAAO,8BAA8B,8BACvF;sBACF,cAAA,6LAAC;gBAAS,OAAO;;;;;;;;;;;;;;;;AAIjC;MAZa", "debugId": null}}, {"offset": {"line": 2873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/MessageList.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef, useState, useCallback } from \"react\";\nimport { ChatMessage } from \"@/types/chat\";\nimport { getFileIcon, formatFileSize } from \"@/types/file\";\nimport { ImagePreviewModal } from \"./ImagePreviewModal\";\nimport { LoadingSpinner } from \"./LoadingSpinner\";\nimport { ArrowDownTrayIcon } from \"@heroicons/react/24/outline\";\nimport toast from 'react-hot-toast';\n\ninterface MessageListProps {\n    messages: ChatMessage[];\n    loading?: boolean;\n    hasMoreMessages?: boolean;\n    onLoadMore?: () => void;\n    loadingMore?: boolean;\n    hasSelectedConversation?: boolean;\n}\n\nexport const MessageList: React.FC<MessageListProps> = ({\n    messages,\n    loading = false,\n    hasMoreMessages = false,\n    onLoadMore,\n    loadingMore = false,\n    hasSelectedConversation = false\n}) => {\n    const messagesEndRef = useRef<HTMLDivElement>(null);\n    const containerRef = useRef<HTMLDivElement>(null);\n    const prevMessageCountRef = useRef(0);\n    const isLoadingMoreRef = useRef(false);\n    const [isNearBottom, setIsNearBottom] = useState(true);\n    const [showScrollToBottom, setShowScrollToBottom] = useState(false);\n    const [previewModal, setPreviewModal] = useState<{\n        isOpen: boolean;\n        images: string[];\n        initialIndex: number;\n    }>({\n        isOpen: false,\n        images: [],\n        initialIndex: 0\n    });\n\n    useEffect(() => {\n        if (messages.length === 0) {\n            prevMessageCountRef.current = 0;\n            return;\n        }\n\n        const currentCount = messages.length;\n        const prevCount = prevMessageCountRef.current;\n\n        if (prevCount === 0 && currentCount > 0) {\n            setTimeout(() => {\n                messagesEndRef.current?.scrollIntoView({ behavior: \"instant\" });\n                setIsNearBottom(true);\n            }, 200);\n            prevMessageCountRef.current = currentCount;\n            return;\n        }\n\n        const isNewMessages = currentCount > prevCount && !isLoadingMoreRef.current;\n\n        if (isNewMessages && isNearBottom && messagesEndRef.current) {\n            setTimeout(() => {\n                messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n            }, 100);\n        }\n\n        prevMessageCountRef.current = currentCount;\n    }, [messages.length, isNearBottom]);\n\n    useEffect(() => {\n        if (!loadingMore) {\n            isLoadingMoreRef.current = false;\n        }\n    }, [loadingMore]);\n\n    useEffect(() => {\n        if (messages.length === 0) {\n            setIsNearBottom(true);\n            setShowScrollToBottom(false);\n            prevMessageCountRef.current = 0;\n            isLoadingMoreRef.current = false;\n        }\n    }, [messages.length === 0]);\n\n\n\n    const handleScroll = useCallback(() => {\n        if (!containerRef.current) return;\n\n        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;\n        const nearBottom = scrollHeight - scrollTop - clientHeight < 100;\n\n        setIsNearBottom(nearBottom);\n        setShowScrollToBottom(!nearBottom);\n\n        if (scrollTop < 100 && hasMoreMessages && !loadingMore) {\n            isLoadingMoreRef.current = true;\n            onLoadMore?.();\n        }\n    }, [hasMoreMessages, loadingMore, onLoadMore]);\n\n    const formatTime = (timestamp: string) => {\n        try {\n            const date = new Date(timestamp);\n            if (isNaN(date.getTime())) return 'Invalid Date';\n\n            const hours = date.getHours().toString().padStart(2, '0');\n            const minutes = date.getMinutes().toString().padStart(2, '0');\n            return `${hours}:${minutes}`;\n        } catch {\n            return 'Invalid Date';\n        }\n    };\n\n    const openImagePreview = (images: string[], initialIndex: number) => {\n        setPreviewModal({\n            isOpen: true,\n            images,\n            initialIndex\n        });\n    };\n\n    const closeImagePreview = () => {\n        setPreviewModal({\n            isOpen: false,\n            images: [],\n            initialIndex: 0\n        });\n    };\n\n    const scrollToBottomManually = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n            setIsNearBottom(true);\n            setShowScrollToBottom(false);\n            prevMessageCountRef.current = messages.length;\n        }\n    };\n\n    const extractFilename = (url: string, fallback: string): string => {\n        try {\n            const urlParts = url.split('/');\n            const encodedFilename = urlParts[urlParts.length - 1];\n            if (!encodedFilename) return fallback;\n\n            // Decode and remove query parameters\n            const decodedName = decodeURIComponent(encodedFilename).split('?')[0];\n            return decodedName || fallback;\n        } catch (e) {\n            return fallback;\n        }\n    };\n\n    const downloadFile = (url: string, filename?: string) => {\n        try {\n            // Simple direct download - works for most cases\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = filename || url.split('/').pop() || 'download';\n            link.target = '_blank';\n            link.rel = 'noopener noreferrer';\n\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n\n            toast.success(`${filename || 'File'} downloaded successfully!`, {\n                duration: 3000,\n            });\n        } catch (error) {\n            toast.error('Failed to download file. Please try again.');\n        }\n    };\n\n    const renderMediaAttachments = (mediaUrls: string[] | undefined, mediaAttachments: any[] | undefined, isMe: boolean) => {\n        // Handle both old format (mediaUrl) and new format (mediaAttachments)\n        let attachments: { url: string; name?: string; size?: number; type?: string }[] = [];\n\n        if (mediaAttachments && mediaAttachments.length > 0) {\n            // New format from backend\n            attachments = mediaAttachments.map(attachment => ({\n                url: attachment.mediaUrl,\n                name: attachment.mediaName,\n                size: attachment.mediaSize,\n                type: attachment.mediaType\n            }));\n        } else if (mediaUrls && mediaUrls.length > 0) {\n            // Old format for backward compatibility\n            attachments = mediaUrls.map(url => ({ url }));\n        }\n\n        if (attachments.length === 0) return null;\n\n        const images: { url: string; name?: string; size?: number; type?: string }[] = [];\n        const otherFiles: { url: string; name?: string; size?: number; type?: string }[] = [];\n\n        attachments.forEach(attachment => {\n            const isImage = attachment.type?.startsWith('image/') ||\n                attachment.url.match(/\\.(jpg|jpeg|png|gif|webp|svg)(\\?|$)/i);\n            if (isImage) {\n                images.push(attachment);\n            } else {\n                otherFiles.push(attachment);\n            }\n        });\n\n        return (\n            <div className=\"mt-2 space-y-2\">\n                {images.length > 0 && (\n                    <div className={`rounded-lg overflow-hidden shadow-sm ${images.length === 1 ? 'max-w-64' : 'max-w-80'\n                        }`}>\n                        {images.length === 1 ? (\n                            <div className=\"relative group\">\n                                <img\n                                    src={images[0].url}\n                                    alt=\"Attachment\"\n                                    className=\"w-full h-auto cursor-pointer hover:opacity-95 transition-opacity max-h-64 object-cover\"\n                                    onClick={() => openImagePreview(images.map(img => img.url), 0)}\n                                />\n                                <button\n                                    onClick={(e) => {\n                                        e.stopPropagation();\n                                        const fileName = images[0].name || extractFilename(images[0].url, `image-${Date.now()}.jpg`);\n                                        downloadFile(images[0].url, fileName);\n                                    }}\n                                    className=\"absolute top-2 right-2 w-8 h-8 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-sm\"\n                                    title=\"Download image\"\n                                >\n                                    <ArrowDownTrayIcon className=\"w-4 h-4\" />\n                                </button>\n                            </div>\n                        ) : images.length === 2 ? (\n                            <div className=\"grid grid-cols-2 gap-1\">\n                                {images.map((image, index) => (\n                                    <div key={index} className=\"relative group\">\n                                        <img\n                                            src={image.url}\n                                            alt={`Attachment ${index + 1}`}\n                                            className=\"w-full h-32 object-cover cursor-pointer hover:opacity-95 transition-opacity\"\n                                            onClick={() => openImagePreview(images.map(img => img.url), index)}\n                                        />\n                                        <button\n                                            onClick={(e) => {\n                                                e.stopPropagation();\n                                                const fileName = image.name || extractFilename(image.url, `image-${index + 1}-${Date.now()}.jpg`);\n                                                downloadFile(image.url, fileName);\n                                            }}\n                                            className=\"absolute top-1 right-1 w-6 h-6 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-sm\"\n                                            title=\"Download image\"\n                                        >\n                                            <ArrowDownTrayIcon className=\"w-3 h-3\" />\n                                        </button>\n                                    </div>\n                                ))}\n                            </div>\n                        ) : images.length === 3 ? (\n                            <div className=\"grid grid-cols-2 gap-1 h-64\">\n                                <img\n                                    src={images[0].url}\n                                    alt=\"Attachment 1\"\n                                    className=\"w-full h-full object-cover cursor-pointer hover:opacity-95 transition-opacity\"\n                                    onClick={() => openImagePreview(images.map(img => img.url), 0)}\n                                />\n                                <div className=\"grid grid-rows-2 gap-1\">\n                                    {images.slice(1).map((image, index) => (\n                                        <img\n                                            key={index + 1}\n                                            src={image.url}\n                                            alt={`Attachment ${index + 2}`}\n                                            className=\"w-full h-full object-cover cursor-pointer hover:opacity-95 transition-opacity\"\n                                            onClick={() => openImagePreview(images.map(img => img.url), index + 1)}\n                                        />\n                                    ))}\n                                </div>\n                            </div>\n                        ) : (\n                            <div className=\"grid grid-cols-2 gap-1 h-64\">\n                                {images.slice(0, 3).map((image, index) => (\n                                    <img\n                                        key={index}\n                                        src={image.url}\n                                        alt={`Attachment ${index + 1}`}\n                                        className=\"w-full h-full object-cover cursor-pointer hover:opacity-95 transition-opacity\"\n                                        onClick={() => openImagePreview(images.map(img => img.url), index)}\n                                    />\n                                ))}\n                                <div\n                                    className=\"relative cursor-pointer hover:opacity-95 transition-opacity\"\n                                    onClick={() => openImagePreview(images.map(img => img.url), 3)}\n                                >\n                                    <img\n                                        src={images[3].url}\n                                        alt=\"Attachment 4\"\n                                        className=\"w-full h-full object-cover\"\n                                    />\n                                    {images.length > 4 && (\n                                        <div className=\"absolute inset-0 bg-black/60 flex items-center justify-center\">\n                                            <span className=\"text-white font-semibold text-lg\">\n                                                +{images.length - 4}\n                                            </span>\n                                        </div>\n                                    )}\n                                </div>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {otherFiles.map((file, index) => {\n                    const isVideo = file.type?.startsWith('video/') || file.url.match(/\\.(mp4|webm|ogg|avi|mov)(\\?|$)/i);\n\n                    if (isVideo) {\n                        return (\n                            <div key={`video-${index}`} className=\"relative group rounded-lg overflow-hidden max-w-64 shadow-sm\">\n                                <video\n                                    src={file.url}\n                                    controls\n                                    className=\"w-full h-auto rounded-lg max-h-48 object-cover\"\n                                    preload=\"metadata\"\n                                />\n                                <button\n                                    onClick={(e) => {\n                                        e.stopPropagation();\n                                        const fileName = file.name || extractFilename(file.url, `video-${index + 1}-${Date.now()}.mp4`);\n                                        downloadFile(file.url, fileName);\n                                    }}\n                                    className=\"absolute top-2 right-2 w-8 h-8 bg-white/90 hover:bg-white text-gray-700 hover:text-gray-900 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-sm\"\n                                    title=\"Download video\"\n                                >\n                                    <ArrowDownTrayIcon className=\"w-4 h-4\" />\n                                </button>\n                            </div>\n                        );\n                    }\n\n                    const fileName = file.name || extractFilename(file.url, `File ${index + 1}`);\n                    return (\n                        <div\n                            key={`file-${index}`}\n                            className={`relative group flex items-center gap-3 p-3 rounded-lg cursor-pointer hover:opacity-90 transition-opacity shadow-sm max-w-64 ${isMe ? 'bg-blue-50' : 'bg-gray-50'\n                                }`}\n                            onClick={() => downloadFile(file.url, fileName)}\n                        >\n                            <div className=\"text-xl\">\n                                {getFileIcon(file.type || 'application/octet-stream')}\n                            </div>\n                            <div className=\"flex-1 min-w-0\">\n                                <p className={`text-sm font-medium truncate ${isMe ? 'text-blue-800' : 'text-gray-900'\n                                    }`}>\n                                    {fileName}\n                                </p>\n                                <p className={`text-xs ${isMe ? 'text-blue-600' : 'text-gray-500'\n                                    }`}>\n                                    {file.size ? formatFileSize(file.size) : 'Click to download'}\n                                </p>\n                            </div>\n                            <button\n                                onClick={(e) => {\n                                    e.stopPropagation();\n                                    downloadFile(file.url, fileName);\n                                }}\n                                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100 ${isMe ? 'bg-blue-200 hover:bg-blue-300 text-blue-700' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                title=\"Download file\"\n                            >\n                                <ArrowDownTrayIcon className=\"w-4 h-4\" />\n                            </button>\n                        </div>\n                    );\n                })}\n            </div>\n        );\n    };\n\n    const getStatusIcon = (status: string) => {\n        switch (status) {\n            case 'SENDING':\n                return (\n                    <div className=\"flex items-center gap-1\">\n                        <div className=\"w-1 h-1 bg-blue-400 rounded-full animate-pulse\"></div>\n                        <div className=\"w-1 h-1 bg-blue-400 rounded-full animate-pulse animation-delay-100\"></div>\n                        <div className=\"w-1 h-1 bg-blue-400 rounded-full animate-pulse animation-delay-200\"></div>\n                    </div>\n                );\n            case 'SENT':\n                return <span className=\"text-slate-400 text-xs\">✓</span>;\n            case 'DELIVERED':\n                return <span className=\"text-slate-500 text-xs\">✓✓</span>;\n            case 'READ':\n                return <span className=\"text-blue-400 text-xs\">✓✓</span>;\n            default:\n                return null;\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"flex-1 flex items-center justify-center p-8 relative\">\n                <div className=\"absolute inset-0 overflow-hidden\">\n                    <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-blue-100/30 rounded-full blur-3xl animate-pulse\"></div>\n                    <div className=\"absolute bottom-1/4 right-1/4 w-40 h-40 bg-purple-100/30 rounded-full blur-3xl animate-pulse animation-delay-1000\"></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-24 h-24 bg-pink-100/30 rounded-full blur-3xl animate-pulse animation-delay-2000\"></div>\n                </div>\n\n                <div className=\"text-center max-w-sm relative z-10\">\n                    <div className=\"w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\n                        <div className=\"w-8 h-8 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin\"></div>\n                    </div>\n                    <h3 className=\"text-slate-800 font-bold text-xl mb-3\">Loading messages...</h3>\n                    <p className=\"text-slate-600 leading-relaxed mb-4\">Please wait while we fetch your conversation</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (messages.length === 0) {\n        if (!hasSelectedConversation) {\n            return (\n                <div className=\"flex-1 flex items-center justify-center p-8 relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40\">\n                    <div className=\"absolute inset-0\">\n                        <div className=\"absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-blue-400/8 to-indigo-400/8 rounded-full blur-3xl animate-pulse\"></div>\n                        <div className=\"absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-br from-purple-400/6 to-pink-400/6 rounded-full blur-3xl animate-pulse animation-delay-1000\"></div>\n                        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-br from-cyan-400/4 to-blue-400/4 rounded-full blur-3xl animate-pulse animation-delay-2000\"></div>\n\n                        <div className=\"absolute top-1/4 right-1/4 w-2 h-2 bg-blue-400/20 rounded-full animate-bounce animation-delay-500\"></div>\n                        <div className=\"absolute bottom-1/3 left-1/3 w-1 h-1 bg-purple-400/30 rounded-full animate-bounce animation-delay-1500\"></div>\n                        <div className=\"absolute top-2/3 right-1/3 w-1.5 h-1.5 bg-indigo-400/25 rounded-full animate-bounce animation-delay-2500\"></div>\n                    </div>\n\n                    <div className=\"text-center max-w-2xl relative z-10\">\n                        <div className=\"relative mb-8\">\n                            <div className=\"w-32 h-32 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-700 rounded-3xl flex items-center justify-center mx-auto shadow-2xl transform hover:scale-105 transition-all duration-500 hover:rotate-3\">\n                                <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl\"></div>\n                                <svg className=\"w-16 h-16 text-white relative z-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                                </svg>\n                            </div>\n                            <div className=\"absolute inset-0 w-32 h-32 mx-auto bg-blue-500/20 rounded-3xl blur-xl animate-pulse\"></div>\n                        </div>\n\n                        <h1 className=\"text-4xl font-bold text-slate-800 mb-4 bg-gradient-to-r from-slate-800 via-blue-800 to-indigo-800 bg-clip-text text-transparent\">\n                            Welcome to Chat\n                        </h1>\n                        <p className=\"text-slate-600 text-lg leading-relaxed mb-8 max-w-lg mx-auto\">\n                            Select a conversation from the sidebar to start chatting, or search for users to begin a new conversation\n                        </p>\n\n                        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 mt-12\">\n                            <div className=\"group flex flex-col items-center p-6 rounded-2xl bg-white/60 backdrop-blur-sm border border-slate-200/50 hover:bg-white/80 hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n                                <div className=\"w-14 h-14 bg-gradient-to-br from-emerald-400 to-green-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                                    <svg className=\"w-7 h-7 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                                    </svg>\n                                </div>\n                                <h3 className=\"font-semibold text-slate-800 mb-2\">Secure & Private</h3>\n                                <p className=\"text-sm text-slate-600 text-center leading-relaxed\">End-to-end encrypted messages for complete privacy</p>\n                            </div>\n\n                            <div className=\"group flex flex-col items-center p-6 rounded-2xl bg-white/60 backdrop-blur-sm border border-slate-200/50 hover:bg-white/80 hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n                                <div className=\"w-14 h-14 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                                    <svg className=\"w-7 h-7 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                                    </svg>\n                                </div>\n                                <h3 className=\"font-semibold text-slate-800 mb-2\">Lightning Fast</h3>\n                                <p className=\"text-sm text-slate-600 text-center leading-relaxed\">Real-time messaging with instant delivery</p>\n                            </div>\n\n                            <div className=\"group flex flex-col items-center p-6 rounded-2xl bg-white/60 backdrop-blur-sm border border-slate-200/50 hover:bg-white/80 hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n                                <div className=\"w-14 h-14 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                                    <svg className=\"w-7 h-7 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                                    </svg>\n                                </div>\n                                <h3 className=\"font-semibold text-slate-800 mb-2\">Rich Media</h3>\n                                <p className=\"text-sm text-slate-600 text-center leading-relaxed\">Share photos, files, and more with ease</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            );\n        }\n\n        return (\n            <div className=\"flex-1 flex items-center justify-center p-8 relative\">\n                <div className=\"absolute inset-0 overflow-hidden\">\n                    <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-blue-100/30 rounded-full blur-3xl animate-pulse\"></div>\n                    <div className=\"absolute bottom-1/4 right-1/4 w-40 h-40 bg-purple-100/30 rounded-full blur-3xl animate-pulse animation-delay-1000\"></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-24 h-24 bg-pink-100/30 rounded-full blur-3xl animate-pulse animation-delay-2000\"></div>\n                </div>\n\n                <div className=\"text-center max-w-sm relative z-10\">\n                    <div className=\"w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\n                        <svg className=\"w-12 h-12 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                        </svg>\n                    </div>\n                    <h3 className=\"text-slate-800 font-bold text-xl mb-3\">No messages yet</h3>\n                    <p className=\"text-slate-600 leading-relaxed mb-4\">Start the conversation by sending your first message!</p>\n                    <div className=\"flex items-center justify-center gap-2 text-slate-400 text-sm\">\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                        </svg>\n                        <span>Messages are end-to-end encrypted</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div\n            ref={containerRef}\n            className=\"absolute inset-0 overflow-y-auto overflow-x-hidden px-6 py-4 space-y-3\"\n            onScroll={handleScroll}\n        >\n            {loadingMore && (\n                <div className=\"flex justify-center py-4\">\n                    <div className=\"flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-sm border border-slate-200\">\n                        <LoadingSpinner size=\"sm\" color=\"blue\" />\n                        <span className=\"text-sm text-slate-600\">Loading...</span>\n                    </div>\n                </div>\n            )}\n\n\n\n            {messages.map((msg, index) => {\n                const isMe = msg.me;\n                const showTime = index === 0 || (\n                    messages[index - 1] &&\n                    Math.abs(new Date(msg.createdAt).getTime() - new Date(messages[index - 1].createdAt).getTime()) > 300000\n                );\n                return (\n                    <div key={msg.id || msg.tempId || index} className=\"message-enter\">\n                        {showTime && (\n                            <div className=\"flex justify-center mb-6\">\n                                <span className=\"text-xs text-slate-500 bg-slate-100/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200/60 shadow-sm\">\n                                    {formatTime(msg.createdAt)}\n                                </span>\n                            </div>\n                        )}\n\n                        <div className={`flex flex-col ${isMe ? 'items-end' : 'items-start'} mb-1 space-y-1`}>\n                            {/* Show username for non-me messages in group chats */}\n                            {!isMe && msg.username && (\n                                <div className=\"flex items-center gap-2 px-2\">\n                                    <div className=\"w-5 h-5 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center text-white text-xs font-semibold\">\n                                        {msg.username.charAt(0).toUpperCase()}\n                                    </div>\n                                    <span className=\"text-xs font-medium text-slate-600\">\n                                        {msg.username}\n                                    </span>\n                                </div>\n                            )}\n\n                            {msg.content && (\n                                <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm transition-all duration-200 ${isMe\n                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md'\n                                    : 'bg-white border border-slate-200/60 text-slate-800 rounded-bl-md'\n                                    }`}>\n                                    <p className=\"text-sm whitespace-pre-wrap break-words leading-relaxed\">\n                                        {msg.content}\n                                    </p>\n                                    <div className={`flex items-center justify-end gap-2 mt-2 ${isMe ? 'text-blue-100' : 'text-slate-500'\n                                        }`}>\n                                        <span className=\"text-xs font-medium\">\n                                            {formatTime(msg.createdAt)}\n                                        </span>\n                                        {isMe && getStatusIcon(msg.status || 'SENT')}\n                                    </div>\n                                </div>\n                            )}\n\n                            {(msg.mediaUrl || msg.mediaAttachments) && (\n                                <div className=\"space-y-1\">\n                                    {renderMediaAttachments(msg.mediaUrl, msg.mediaAttachments, isMe)}\n                                    {!msg.content && (\n                                        <div className={`flex items-center ${isMe ? 'justify-end' : 'justify-start'} gap-2 px-2`}>\n                                            <span className=\"text-xs text-slate-500 font-medium\">\n                                                {formatTime(msg.createdAt)}\n                                            </span>\n                                            {isMe && getStatusIcon(msg.status || 'SENT')}\n                                        </div>\n                                    )}\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                );\n            })}\n\n            <div ref={messagesEndRef} />\n\n            {showScrollToBottom && (\n                <button\n                    onClick={scrollToBottomManually}\n                    className=\"fixed bottom-20 right-6 w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-30 animate-bounce\"\n                    title=\"Scroll to bottom\"\n                >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n                    </svg>\n                </button>\n            )}\n\n            <ImagePreviewModal\n                images={previewModal.images}\n                initialIndex={previewModal.initialIndex}\n                isOpen={previewModal.isOpen}\n                onClose={closeImagePreview}\n            />\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAmBO,MAAM,cAA0C,CAAC,EACpD,QAAQ,EACR,UAAU,KAAK,EACf,kBAAkB,KAAK,EACvB,UAAU,EACV,cAAc,KAAK,EACnB,0BAA0B,KAAK,EAClC;;IACG,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI5C;QACC,QAAQ;QACR,QAAQ,EAAE;QACV,cAAc;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,oBAAoB,OAAO,GAAG;gBAC9B;YACJ;YAEA,MAAM,eAAe,SAAS,MAAM;YACpC,MAAM,YAAY,oBAAoB,OAAO;YAE7C,IAAI,cAAc,KAAK,eAAe,GAAG;gBACrC;6CAAW;wBACP,eAAe,OAAO,EAAE,eAAe;4BAAE,UAAU;wBAAU;wBAC7D,gBAAgB;oBACpB;4CAAG;gBACH,oBAAoB,OAAO,GAAG;gBAC9B;YACJ;YAEA,MAAM,gBAAgB,eAAe,aAAa,CAAC,iBAAiB,OAAO;YAE3E,IAAI,iBAAiB,gBAAgB,eAAe,OAAO,EAAE;gBACzD;6CAAW;wBACP,eAAe,OAAO,EAAE,eAAe;4BAAE,UAAU;wBAAS;oBAChE;4CAAG;YACP;YAEA,oBAAoB,OAAO,GAAG;QAClC;gCAAG;QAAC,SAAS,MAAM;QAAE;KAAa;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,CAAC,aAAa;gBACd,iBAAiB,OAAO,GAAG;YAC/B;QACJ;gCAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,gBAAgB;gBAChB,sBAAsB;gBACtB,oBAAoB,OAAO,GAAG;gBAC9B,iBAAiB,OAAO,GAAG;YAC/B;QACJ;gCAAG;QAAC,SAAS,MAAM,KAAK;KAAE;IAI1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,aAAa,OAAO;YACtE,MAAM,aAAa,eAAe,YAAY,eAAe;YAE7D,gBAAgB;YAChB,sBAAsB,CAAC;YAEvB,IAAI,YAAY,OAAO,mBAAmB,CAAC,aAAa;gBACpD,iBAAiB,OAAO,GAAG;gBAC3B;YACJ;QACJ;gDAAG;QAAC;QAAiB;QAAa;KAAW;IAE7C,MAAM,aAAa,CAAC;QAChB,IAAI;YACA,MAAM,OAAO,IAAI,KAAK;YACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;YAElC,MAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YACrD,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YACzD,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;QAChC,EAAE,OAAM;YACJ,OAAO;QACX;IACJ;IAEA,MAAM,mBAAmB,CAAC,QAAkB;QACxC,gBAAgB;YACZ,QAAQ;YACR;YACA;QACJ;IACJ;IAEA,MAAM,oBAAoB;QACtB,gBAAgB;YACZ,QAAQ;YACR,QAAQ,EAAE;YACV,cAAc;QAClB;IACJ;IAEA,MAAM,yBAAyB;QAC3B,IAAI,eAAe,OAAO,EAAE;YACxB,eAAe,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC3D,gBAAgB;YAChB,sBAAsB;YACtB,oBAAoB,OAAO,GAAG,SAAS,MAAM;QACjD;IACJ;IAEA,MAAM,kBAAkB,CAAC,KAAa;QAClC,IAAI;YACA,MAAM,WAAW,IAAI,KAAK,CAAC;YAC3B,MAAM,kBAAkB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACrD,IAAI,CAAC,iBAAiB,OAAO;YAE7B,qCAAqC;YACrC,MAAM,cAAc,mBAAmB,iBAAiB,KAAK,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,eAAe;QAC1B,EAAE,OAAO,GAAG;YACR,OAAO;QACX;IACJ;IAEA,MAAM,eAAe,CAAC,KAAa;QAC/B,IAAI;YACA,gDAAgD;YAChD,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,YAAY,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM;YACpD,KAAK,MAAM,GAAG;YACd,KAAK,GAAG,GAAG;YAEX,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,YAAY,OAAO,yBAAyB,CAAC,EAAE;gBAC5D,UAAU;YACd;QACJ,EAAE,OAAO,OAAO;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,yBAAyB,CAAC,WAAiC,kBAAqC;QAClG,sEAAsE;QACtE,IAAI,cAA8E,EAAE;QAEpF,IAAI,oBAAoB,iBAAiB,MAAM,GAAG,GAAG;YACjD,0BAA0B;YAC1B,cAAc,iBAAiB,GAAG,CAAC,CAAA,aAAc,CAAC;oBAC9C,KAAK,WAAW,QAAQ;oBACxB,MAAM,WAAW,SAAS;oBAC1B,MAAM,WAAW,SAAS;oBAC1B,MAAM,WAAW,SAAS;gBAC9B,CAAC;QACL,OAAO,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;YAC1C,wCAAwC;YACxC,cAAc,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;oBAAE;gBAAI,CAAC;QAC/C;QAEA,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;QAErC,MAAM,SAAyE,EAAE;QACjF,MAAM,aAA6E,EAAE;QAErF,YAAY,OAAO,CAAC,CAAA;YAChB,MAAM,UAAU,WAAW,IAAI,EAAE,WAAW,aACxC,WAAW,GAAG,CAAC,KAAK,CAAC;YACzB,IAAI,SAAS;gBACT,OAAO,IAAI,CAAC;YAChB,OAAO;gBACH,WAAW,IAAI,CAAC;YACpB;QACJ;QAEA,qBACI,6LAAC;YAAI,WAAU;;gBACV,OAAO,MAAM,GAAG,mBACb,6LAAC;oBAAI,WAAW,CAAC,qCAAqC,EAAE,OAAO,MAAM,KAAK,IAAI,aAAa,YACrF;8BACD,OAAO,MAAM,KAAK,kBACf,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCACG,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;gCAClB,KAAI;gCACJ,WAAU;gCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG;;;;;;0CAEhE,6LAAC;gCACG,SAAS,CAAC;oCACN,EAAE,eAAe;oCACjB,MAAM,WAAW,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,gBAAgB,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;oCAC3F,aAAa,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE;gCAChC;gCACA,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;;;;;+BAGrC,OAAO,MAAM,KAAK,kBAClB,6LAAC;wBAAI,WAAU;kCACV,OAAO,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;gCAAgB,WAAU;;kDACvB,6LAAC;wCACG,KAAK,MAAM,GAAG;wCACd,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG;wCAC9B,WAAU;wCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG;;;;;;kDAEhE,6LAAC;wCACG,SAAS,CAAC;4CACN,EAAE,eAAe;4CACjB,MAAM,WAAW,MAAM,IAAI,IAAI,gBAAgB,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;4CAChG,aAAa,MAAM,GAAG,EAAE;wCAC5B;wCACA,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;;;;;;;+BAhB3B;;;;;;;;;+BAqBlB,OAAO,MAAM,KAAK,kBAClB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCACG,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;gCAClB,KAAI;gCACJ,WAAU;gCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG;;;;;;0CAEhE,6LAAC;gCAAI,WAAU;0CACV,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;wCAEG,KAAK,MAAM,GAAG;wCACd,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG;wCAC9B,WAAU;wCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG,QAAQ;uCAJ/D,QAAQ;;;;;;;;;;;;;;;6CAU7B,6LAAC;wBAAI,WAAU;;4BACV,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;oCAEG,KAAK,MAAM,GAAG;oCACd,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG;oCAC9B,WAAU;oCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG;mCAJvD;;;;;0CAOb,6LAAC;gCACG,WAAU;gCACV,SAAS,IAAM,iBAAiB,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,GAAG;;kDAE5D,6LAAC;wCACG,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;wCAClB,KAAI;wCACJ,WAAU;;;;;;oCAEb,OAAO,MAAM,GAAG,mBACb,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAK,WAAU;;gDAAmC;gDAC7C,OAAO,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUjD,WAAW,GAAG,CAAC,CAAC,MAAM;oBACnB,MAAM,UAAU,KAAK,IAAI,EAAE,WAAW,aAAa,KAAK,GAAG,CAAC,KAAK,CAAC;oBAElE,IAAI,SAAS;wBACT,qBACI,6LAAC;4BAA2B,WAAU;;8CAClC,6LAAC;oCACG,KAAK,KAAK,GAAG;oCACb,QAAQ;oCACR,WAAU;oCACV,SAAQ;;;;;;8CAEZ,6LAAC;oCACG,SAAS,CAAC;wCACN,EAAE,eAAe;wCACjB,MAAM,WAAW,KAAK,IAAI,IAAI,gBAAgB,KAAK,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;wCAC9F,aAAa,KAAK,GAAG,EAAE;oCAC3B;oCACA,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;;;;;;;2BAhB3B,CAAC,MAAM,EAAE,OAAO;;;;;oBAoBlC;oBAEA,MAAM,WAAW,KAAK,IAAI,IAAI,gBAAgB,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG;oBAC3E,qBACI,6LAAC;wBAEG,WAAW,CAAC,4HAA4H,EAAE,OAAO,eAAe,cAC1J;wBACN,SAAS,IAAM,aAAa,KAAK,GAAG,EAAE;;0CAEtC,6LAAC;gCAAI,WAAU;0CACV,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI,IAAI;;;;;;0CAE9B,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAE,WAAW,CAAC,6BAA6B,EAAE,OAAO,kBAAkB,iBACjE;kDACD;;;;;;kDAEL,6LAAC;wCAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,kBAAkB,iBAC5C;kDACD,KAAK,IAAI,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI,IAAI;;;;;;;;;;;;0CAGjD,6LAAC;gCACG,SAAS,CAAC;oCACN,EAAE,eAAe;oCACjB,aAAa,KAAK,GAAG,EAAE;gCAC3B;gCACA,WAAW,CAAC,oHAAoH,EAAE,OAAO,gDAAgD,+CACnL;gCACN,OAAM;0CAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;uBA3B5B,CAAC,KAAK,EAAE,OAAO;;;;;gBA+BhC;;;;;;;IAGZ;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAQ;YACJ,KAAK;gBACD,qBACI,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;YAG3B,KAAK;gBACD,qBAAO,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;YACpD,KAAK;gBACD,qBAAO,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;YACpD,KAAK;gBACD,qBAAO,6LAAC;oBAAK,WAAU;8BAAwB;;;;;;YACnD;gBACI,OAAO;QACf;IACJ;IAEA,IAAI,SAAS;QACT,qBACI,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEnB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;IAInE;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACvB,IAAI,CAAC,yBAAyB;YAC1B,qBACI,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;gDAAqC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC1F,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAK,GAAE;;;;;;;;;;;;;;;;;kDAG/E,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGnB,6LAAC;gCAAG,WAAU;0CAAkI;;;;;;0CAGhJ,6LAAC;gCAAE,WAAU;0CAA+D;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC1E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAqD;;;;;;;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC1E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAqD;;;;;;;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC1E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAM1F;QAEA,qBACI,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;gCAA0B,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAK,GAAE;;;;;;;;;;;;;;;;sCAG/E,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;sCACnD,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/D,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEzE,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAK1B;IAEA,qBACI,6LAAC;QACG,KAAK;QACL,WAAU;QACV,UAAU;;YAET,6BACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,uIAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAK,OAAM;;;;;;sCAChC,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAOpD,SAAS,GAAG,CAAC,CAAC,KAAK;gBAChB,MAAM,OAAO,IAAI,EAAE;gBACnB,MAAM,WAAW,UAAU,KACvB,QAAQ,CAAC,QAAQ,EAAE,IACnB,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,OAAO,MAAM;gBAEtG,qBACI,6LAAC;oBAAwC,WAAU;;wBAC9C,0BACG,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAK,WAAU;0CACX,WAAW,IAAI,SAAS;;;;;;;;;;;sCAKrC,6LAAC;4BAAI,WAAW,CAAC,cAAc,EAAE,OAAO,cAAc,cAAc,eAAe,CAAC;;gCAE/E,CAAC,QAAQ,IAAI,QAAQ,kBAClB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACV,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;sDAEvC,6LAAC;4CAAK,WAAU;sDACX,IAAI,QAAQ;;;;;;;;;;;;gCAKxB,IAAI,OAAO,kBACR,6LAAC;oCAAI,WAAW,CAAC,iFAAiF,EAAE,OAC9F,wEACA,oEACA;;sDACF,6LAAC;4CAAE,WAAU;sDACR,IAAI,OAAO;;;;;;sDAEhB,6LAAC;4CAAI,WAAW,CAAC,yCAAyC,EAAE,OAAO,kBAAkB,kBAC/E;;8DACF,6LAAC;oDAAK,WAAU;8DACX,WAAW,IAAI,SAAS;;;;;;gDAE5B,QAAQ,cAAc,IAAI,MAAM,IAAI;;;;;;;;;;;;;gCAKhD,CAAC,IAAI,QAAQ,IAAI,IAAI,gBAAgB,mBAClC,6LAAC;oCAAI,WAAU;;wCACV,uBAAuB,IAAI,QAAQ,EAAE,IAAI,gBAAgB,EAAE;wCAC3D,CAAC,IAAI,OAAO,kBACT,6LAAC;4CAAI,WAAW,CAAC,kBAAkB,EAAE,OAAO,gBAAgB,gBAAgB,WAAW,CAAC;;8DACpF,6LAAC;oDAAK,WAAU;8DACX,WAAW,IAAI,SAAS;;;;;;gDAE5B,QAAQ,cAAc,IAAI,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;mBAhDnD,IAAI,EAAE,IAAI,IAAI,MAAM,IAAI;;;;;YAwD1C;0BAEA,6LAAC;gBAAI,KAAK;;;;;;YAET,oCACG,6LAAC;gBACG,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAKjF,6LAAC,0IAAA,CAAA,oBAAiB;gBACd,QAAQ,aAAa,MAAM;gBAC3B,cAAc,aAAa,YAAY;gBACvC,QAAQ,aAAa,MAAM;gBAC3B,SAAS;;;;;;;;;;;;AAIzB;GAtlBa;KAAA", "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/FilePicker.tsx"], "sourcesContent": ["import React, { useRef, useState, useCallback } from 'react';\nimport { PaperClipIcon, PhotoIcon, DocumentIcon } from '@heroicons/react/24/outline';\nimport { FilePreview as FilePreviewType, MAX_FILES_PER_MESSAGE, MAX_FILE_SIZE } from '../types/file';\nimport { ApiService } from '../api/axios';\n\ninterface FilePickerProps {\n    onFilesSelected: (files: FilePreviewType[]) => void;\n    onError?: (error: string) => void;\n    maxFiles?: number;\n    acceptedTypes?: string[];\n    disabled?: boolean;\n    className?: string;\n}\n\nexport const FilePicker: React.FC<FilePickerProps> = ({\n    onFilesSelected,\n    onError,\n    maxFiles = MAX_FILES_PER_MESSAGE,\n    acceptedTypes,\n    disabled = false,\n    className = ''\n}) => {\n    const fileInputRef = useRef<HTMLInputElement>(null);\n    const [isDragOver, setIsDragOver] = useState(false);\n\n    const processFiles = useCallback(async (files: File[]) => {\n        if (files.length === 0) return;\n\n        const validation = ApiService.validateFiles(files);\n\n        if (!validation.isValid) {\n            const errorMessage = validation.errors.join('\\n');\n            onError?.(errorMessage);\n            return;\n        }\n\n        if (files.length > maxFiles) {\n            const errorMessage = `Maximum ${maxFiles} files allowed`;\n            onError?.(errorMessage);\n            return;\n        }\n\n        const filePreviews: FilePreviewType[] = [];\n\n        for (let i = 0; i < files.length; i++) {\n            const file = files[i];\n            const filePreview: FilePreviewType = {\n                file,\n                id: `${file.name}-${Date.now()}-${i}`,\n                name: file.name,\n                size: file.size,\n                type: file.type,\n                isUploading: false\n            };\n\n            if (file.type.startsWith('image/')) {\n                try {\n                    filePreview.preview = await ApiService.createImagePreview(file);\n                } catch (error) {\n                    console.warn('Failed to create image preview:', error);\n                }\n            }\n\n            filePreviews.push(filePreview);\n        }\n\n        onFilesSelected(filePreviews);\n    }, [onFilesSelected, onError, maxFiles]);\n\n    const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n        const files = Array.from(e.target.files || []);\n        processFiles(files);\n\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    }, [processFiles]);\n\n    const handleDragOver = useCallback((e: React.DragEvent) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (!disabled) {\n            setIsDragOver(true);\n        }\n    }, [disabled]);\n\n    const handleDragLeave = useCallback((e: React.DragEvent) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n    }, []);\n\n    const handleDrop = useCallback((e: React.DragEvent) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n\n        if (disabled) return;\n\n        const files = Array.from(e.dataTransfer.files);\n        processFiles(files);\n    }, [disabled, processFiles]);\n\n    const openFileDialog = useCallback(() => {\n        if (!disabled && fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    }, [disabled]);\n\n    const acceptString = acceptedTypes?.join(',') || '*/*';\n\n    return (\n        <div className={className}>\n            <input\n                ref={fileInputRef}\n                type=\"file\"\n                multiple\n                accept={acceptString}\n                onChange={handleFileSelect}\n                className=\"hidden\"\n                disabled={disabled}\n            />\n\n            <div\n                onDragOver={handleDragOver}\n                onDragLeave={handleDragLeave}\n                onDrop={handleDrop}\n                onClick={openFileDialog}\n                className={`\n                    relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n                    ${isDragOver\n                        ? 'border-blue-400 bg-blue-50'\n                        : 'border-gray-300 hover:border-gray-400'\n                    }\n                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n                `}\n            >\n                <div className=\"flex flex-col items-center gap-3\">\n                    <div className=\"flex items-center gap-2\">\n                        <PaperClipIcon className=\"w-8 h-8 text-gray-400\" />\n                        <PhotoIcon className=\"w-8 h-8 text-gray-400\" />\n                        <DocumentIcon className=\"w-8 h-8 text-gray-400\" />\n                    </div>\n\n                    <div>\n                        <p className=\"text-sm font-medium text-gray-900\">\n                            {isDragOver ? 'Drop files here' : 'Choose files or drag and drop'}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                            Up to {maxFiles} files, max {Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB each\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\ninterface QuickFilePickerProps {\n    onFilesSelected: (files: FilePreviewType[]) => void;\n    onError?: (error: string) => void;\n    disabled?: boolean;\n    className?: string;\n}\n\nexport const QuickFilePicker: React.FC<QuickFilePickerProps> = ({\n    onFilesSelected,\n    onError,\n    disabled = false,\n    className = ''\n}) => {\n    const fileInputRef = useRef<HTMLInputElement>(null);\n    const imageInputRef = useRef<HTMLInputElement>(null);\n\n    const processFiles = useCallback(async (files: File[]) => {\n        if (files.length === 0) return;\n\n        const validation = ApiService.validateFiles(files);\n        if (!validation.isValid) {\n            onError?.(validation.errors.join('\\n'));\n            return;\n        }\n\n        const filePreviews: FilePreviewType[] = [];\n\n        for (let i = 0; i < files.length; i++) {\n            const file = files[i];\n            const filePreview: FilePreviewType = {\n                file,\n                id: `${file.name}-${Date.now()}-${i}`,\n                name: file.name,\n                size: file.size,\n                type: file.type,\n                isUploading: false\n            };\n\n            if (file.type.startsWith('image/')) {\n                try {\n                    filePreview.preview = await ApiService.createImagePreview(file);\n                } catch (error) {\n                    console.warn('Failed to create image preview:', error);\n                }\n            }\n\n            filePreviews.push(filePreview);\n        }\n\n        onFilesSelected(filePreviews);\n    }, [onFilesSelected, onError]);\n\n    const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n        const files = Array.from(e.target.files || []);\n        processFiles(files);\n\n        if (e.target) {\n            e.target.value = '';\n        }\n    }, [processFiles]);\n\n    const openFileDialog = useCallback(() => {\n        if (!disabled && fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    }, [disabled]);\n\n    const openImageDialog = useCallback(() => {\n        if (!disabled && imageInputRef.current) {\n            imageInputRef.current.click();\n        }\n    }, [disabled]);\n\n    return (\n        <div className={`flex items-center gap-2 ${className}`}>\n            <input\n                ref={fileInputRef}\n                type=\"file\"\n                multiple\n                accept=\"*/*\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n                disabled={disabled}\n            />\n\n            <input\n                ref={imageInputRef}\n                type=\"file\"\n                multiple\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n                disabled={disabled}\n            />\n\n            <button\n                onClick={openImageDialog}\n                disabled={disabled}\n                className=\"p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                title=\"Add images\"\n            >\n                <PhotoIcon className=\"w-5 h-5\" />\n            </button>\n\n            <button\n                onClick={openFileDialog}\n                disabled={disabled}\n                className=\"p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                title=\"Add files\"\n            >\n                <PaperClipIcon className=\"w-5 h-5\" />\n            </button>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AAWO,MAAM,aAAwC,CAAC,EAClD,eAAe,EACf,OAAO,EACP,WAAW,uHAAA,CAAA,wBAAqB,EAChC,aAAa,EACb,WAAW,KAAK,EAChB,YAAY,EAAE,EACjB;;IACG,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YACpC,IAAI,MAAM,MAAM,KAAK,GAAG;YAExB,MAAM,aAAa,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;YAE5C,IAAI,CAAC,WAAW,OAAO,EAAE;gBACrB,MAAM,eAAe,WAAW,MAAM,CAAC,IAAI,CAAC;gBAC5C,UAAU;gBACV;YACJ;YAEA,IAAI,MAAM,MAAM,GAAG,UAAU;gBACzB,MAAM,eAAe,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC;gBACxD,UAAU;gBACV;YACJ;YAEA,MAAM,eAAkC,EAAE;YAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,cAA+B;oBACjC;oBACA,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG;oBACrC,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,aAAa;gBACjB;gBAEA,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBAChC,IAAI;wBACA,YAAY,OAAO,GAAG,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;oBAC9D,EAAE,OAAO,OAAO;wBACZ,QAAQ,IAAI,CAAC,mCAAmC;oBACpD;gBACJ;gBAEA,aAAa,IAAI,CAAC;YACtB;YAEA,gBAAgB;QACpB;+CAAG;QAAC;QAAiB;QAAS;KAAS;IAEvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAClC,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;YAC7C,aAAa;YAEb,IAAI,aAAa,OAAO,EAAE;gBACtB,aAAa,OAAO,CAAC,KAAK,GAAG;YACjC;QACJ;mDAAG;QAAC;KAAa;IAEjB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAChC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,CAAC,UAAU;gBACX,cAAc;YAClB;QACJ;iDAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;QAClB;kDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC5B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,IAAI,UAAU;YAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C,aAAa;QACjB;6CAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC/B,IAAI,CAAC,YAAY,aAAa,OAAO,EAAE;gBACnC,aAAa,OAAO,CAAC,KAAK;YAC9B;QACJ;iDAAG;QAAC;KAAS;IAEb,MAAM,eAAe,eAAe,KAAK,QAAQ;IAEjD,qBACI,6LAAC;QAAI,WAAW;;0BACZ,6LAAC;gBACG,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,WAAU;gBACV,UAAU;;;;;;0BAGd,6LAAC;gBACG,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS;gBACT,WAAW,CAAC;;oBAER,EAAE,aACI,+BACA,wCACL;oBACD,EAAE,WAAW,kCAAkC,GAAG;gBACtD,CAAC;0BAED,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;sCAG5B,6LAAC;;8CACG,6LAAC;oCAAE,WAAU;8CACR,aAAa,oBAAoB;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;;wCAA6B;wCAC/B;wCAAS;wCAAa,KAAK,KAAK,CAAC,uHAAA,CAAA,gBAAa,GAAG,CAAC,OAAO,IAAI;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnG;GA9Ia;KAAA;AAuJN,MAAM,kBAAkD,CAAC,EAC5D,eAAe,EACf,OAAO,EACP,WAAW,KAAK,EAChB,YAAY,EAAE,EACjB;;IACG,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACpC,IAAI,MAAM,MAAM,KAAK,GAAG;YAExB,MAAM,aAAa,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;YAC5C,IAAI,CAAC,WAAW,OAAO,EAAE;gBACrB,UAAU,WAAW,MAAM,CAAC,IAAI,CAAC;gBACjC;YACJ;YAEA,MAAM,eAAkC,EAAE;YAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,cAA+B;oBACjC;oBACA,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG;oBACrC,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,aAAa;gBACjB;gBAEA,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBAChC,IAAI;wBACA,YAAY,OAAO,GAAG,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;oBAC9D,EAAE,OAAO,OAAO;wBACZ,QAAQ,IAAI,CAAC,mCAAmC;oBACpD;gBACJ;gBAEA,aAAa,IAAI,CAAC;YACtB;YAEA,gBAAgB;QACpB;oDAAG;QAAC;QAAiB;KAAQ;IAE7B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAClC,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;YAC7C,aAAa;YAEb,IAAI,EAAE,MAAM,EAAE;gBACV,EAAE,MAAM,CAAC,KAAK,GAAG;YACrB;QACJ;wDAAG;QAAC;KAAa;IAEjB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC/B,IAAI,CAAC,YAAY,aAAa,OAAO,EAAE;gBACnC,aAAa,OAAO,CAAC,KAAK;YAC9B;QACJ;sDAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAChC,IAAI,CAAC,YAAY,cAAc,OAAO,EAAE;gBACpC,cAAc,OAAO,CAAC,KAAK;YAC/B;QACJ;uDAAG;QAAC;KAAS;IAEb,qBACI,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BAClD,6LAAC;gBACG,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,QAAO;gBACP,UAAU;gBACV,WAAU;gBACV,UAAU;;;;;;0BAGd,6LAAC;gBACG,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,QAAO;gBACP,UAAU;gBACV,WAAU;gBACV,UAAU;;;;;;0BAGd,6LAAC;gBACG,SAAS;gBACT,UAAU;gBACV,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;0BAGzB,6LAAC;gBACG,SAAS;gBACT,UAAU;gBACV,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,4NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIzC;IA3Ga;MAAA", "debugId": null}}, {"offset": {"line": 4549, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/FilePreview.tsx"], "sourcesContent": ["import React from 'react';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { FilePreview as FilePreviewType, formatFileSize, getFileIcon, getFileIconColor, getFileCategory } from '../types/file';\n\ninterface FilePreviewProps {\n    file: FilePreviewType;\n    onRemove?: (fileId: string) => void;\n    showRemoveButton?: boolean;\n    className?: string;\n}\n\nexport const FilePreview: React.FC<FilePreviewProps> = ({\n    file,\n    onRemove,\n    showRemoveButton = true,\n    className = ''\n}) => {\n    const fileCategory = getFileCategory(file.type);\n    const isImage = fileCategory === 'IMAGE';\n\n    const handleRemove = (e: React.MouseEvent) => {\n        e.stopPropagation();\n        if (onRemove) {\n            onRemove(file.id);\n        }\n    };\n\n    return (\n        <div className={`relative bg-white border border-slate-200 rounded-xl p-4 hover:border-slate-300 hover:shadow-md transition-all duration-200 ${className}`}>\n            {showRemoveButton && onRemove && (\n                <button\n                    onClick={handleRemove}\n                    className=\"absolute -top-2 -right-2 w-7 h-7 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-10 shadow-lg border-2 border-white\"\n                    title=\"Remove file\"\n                >\n                    <XMarkIcon className=\"w-4 h-4 stroke-2\" />\n                </button>\n            )}\n\n            <div className=\"flex items-start gap-3\">\n                <div className=\"flex-shrink-0\">\n                    {isImage && file.preview ? (\n                        <div className=\"w-14 h-14 rounded-xl overflow-hidden bg-slate-100 shadow-sm\">\n                            <img\n                                src={file.preview}\n                                alt={file.name}\n                                className=\"w-full h-full object-cover\"\n                            />\n                        </div>\n                    ) : (\n                        <div className=\"w-14 h-14 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center text-2xl shadow-sm\">\n                            {getFileIcon(file.type)}\n                        </div>\n                    )}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-start justify-between gap-2\">\n                        <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-semibold text-slate-800 truncate\" title={file.name}>\n                                {file.name}\n                            </p>\n                            <p className=\"text-xs text-slate-500 mt-1 font-medium\">\n                                {formatFileSize(file.size)} • {file.type.split('/')[1]?.toUpperCase() || 'FILE'}\n                            </p>\n                        </div>\n                    </div>\n\n                    {file.isUploading && (\n                        <div className=\"mt-2\">\n                            <div className=\"flex items-center justify-between text-xs text-gray-500 mb-1\">\n                                <span>Uploading...</span>\n                                <span>{file.uploadProgress || 0}%</span>\n                            </div>\n                            <div className=\"w-full bg-gray-200 rounded-full h-1.5\">\n                                <div\n                                    className=\"bg-blue-500 h-1.5 rounded-full transition-all duration-300\"\n                                    style={{ width: `${file.uploadProgress || 0}%` }}\n                                />\n                            </div>\n                        </div>\n                    )}\n\n\n                </div>\n            </div>\n        </div>\n    );\n};\n\ninterface FilePreviewListProps {\n    files: FilePreviewType[];\n    onRemoveFile?: (fileId: string) => void;\n    onImageClick?: (index: number) => void;\n    className?: string;\n}\n\nexport const FilePreviewList: React.FC<FilePreviewListProps> = ({\n    files,\n    onRemoveFile,\n    onImageClick,\n    className = ''\n}) => {\n    if (files.length === 0) return null;\n\n    const images = files.filter(file => file.type.startsWith('image/'));\n    const otherFiles = files.filter(file => !file.type.startsWith('image/'));\n\n    return (\n        <div className={`space-y-3 ${className}`}>\n            {images.length > 0 && (\n                <div className=\"space-y-2\">\n                    <p className=\"text-sm font-medium text-slate-600\">Images ({images.length})</p>\n                    <div className=\"flex flex-wrap gap-2 max-w-md p-2\">\n                        {images.map((file, index) => (\n                            <div key={file.id} className=\"relative\">\n                                <div className=\"w-20 h-20 rounded-lg overflow-hidden bg-slate-100 shadow-sm\">\n                                    <img\n                                        src={file.preview}\n                                        alt={file.name}\n                                        className=\"w-full h-full object-cover cursor-pointer\"\n                                        onClick={() => onImageClick?.(index)}\n                                    />\n                                </div>\n                                {onRemoveFile && (\n                                    <button\n                                        onClick={() => onRemoveFile(file.id)}\n                                        className=\"absolute top-0 right-0 w-5 h-5 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-20 shadow-lg border border-white\"\n                                        title=\"Remove image\"\n                                    >\n                                        <XMarkIcon className=\"w-3 h-3 stroke-2\" />\n                                    </button>\n                                )}\n                                {file.isUploading && (\n                                    <div className=\"absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center\">\n                                        <div className=\"text-white text-xs font-medium\">\n                                            {file.uploadProgress || 0}%\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n                        ))}\n                    </div>\n                </div>\n            )}\n\n            {otherFiles.length > 0 && (\n                <div className=\"space-y-2\">\n                    <p className=\"text-sm font-medium text-slate-600\">Files ({otherFiles.length})</p>\n                    <div className=\"flex flex-wrap gap-2 max-w-md p-2\">\n                        {otherFiles.map((file) => (\n                            <div key={file.id} className=\"relative\">\n                                <div className=\"w-20 h-20 rounded-lg overflow-hidden bg-slate-100 shadow-sm border border-slate-200 flex flex-col items-center justify-center p-2\">\n                                    <div className={`mb-1 text-lg ${getFileIconColor(file.type)}`}>\n                                        {getFileIcon(file.type)}\n                                    </div>\n                                    <div className=\"text-xs text-slate-600 text-center truncate w-full\">\n                                        {file.name.length > 8 ? file.name.substring(0, 8) + '...' : file.name}\n                                    </div>\n                                </div>\n                                {onRemoveFile && (\n                                    <button\n                                        onClick={() => onRemoveFile(file.id)}\n                                        className=\"absolute top-0 right-0 w-5 h-5 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-20 shadow-lg border border-white\"\n                                        title=\"Remove file\"\n                                    >\n                                        <XMarkIcon className=\"w-3 h-3 stroke-2\" />\n                                    </button>\n                                )}\n                                {file.isUploading && (\n                                    <div className=\"absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center\">\n                                        <div className=\"text-white text-xs font-medium\">\n                                            {file.uploadProgress || 0}%\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n                        ))}\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\ninterface CompactFilePreviewProps {\n    file: FilePreviewType;\n    onClick?: () => void;\n    className?: string;\n}\n\nexport const CompactFilePreview: React.FC<CompactFilePreviewProps> = ({\n    file,\n    onClick,\n    className = ''\n}) => {\n    const fileCategory = getFileCategory(file.type);\n    const isImage = fileCategory === 'IMAGE';\n\n    return (\n        <div\n            className={`flex items-center gap-2 p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer ${className}`}\n            onClick={onClick}\n        >\n            <div className=\"flex-shrink-0\">\n                {isImage && file.preview ? (\n                    <div className=\"w-8 h-8 rounded overflow-hidden\">\n                        <img\n                            src={file.preview}\n                            alt={file.name}\n                            className=\"w-full h-full object-cover\"\n                        />\n                    </div>\n                ) : (\n                    <div className=\"w-8 h-8 rounded bg-white flex items-center justify-center text-lg\">\n                        {getFileIcon(file.type)}\n                    </div>\n                )}\n            </div>\n\n            <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {file.name}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                    {formatFileSize(file.size)}\n                </p>\n            </div>\n        </div>\n    );\n};\n\ninterface FileGridPreviewProps {\n    files: FilePreviewType[];\n    onFileClick?: (file: FilePreviewType) => void;\n    onRemoveFile?: (fileId: string) => void;\n    maxDisplay?: number;\n    className?: string;\n}\n\nexport const FileGridPreview: React.FC<FileGridPreviewProps> = ({\n    files,\n    onFileClick,\n    onRemoveFile,\n    maxDisplay = 4,\n    className = ''\n}) => {\n    const displayFiles = files.slice(0, maxDisplay);\n    const remainingCount = files.length - maxDisplay;\n\n    return (\n        <div className={`grid grid-cols-2 gap-2 ${className}`}>\n            {displayFiles.map((file, index) => {\n                const fileCategory = getFileCategory(file.type);\n                const isImage = fileCategory === 'IMAGE';\n                const isLast = index === displayFiles.length - 1 && remainingCount > 0;\n\n                return (\n                    <div\n                        key={file.id}\n                        className=\"relative aspect-square rounded-lg overflow-hidden bg-gray-100 cursor-pointer hover:opacity-90 transition-opacity\"\n                        onClick={() => onFileClick?.(file)}\n                    >\n                        {isImage && file.preview ? (\n                            <img\n                                src={file.preview}\n                                alt={file.name}\n                                className=\"w-full h-full object-cover\"\n                            />\n                        ) : (\n                            <div className=\"w-full h-full flex flex-col items-center justify-center text-gray-600\">\n                                <div className=\"text-2xl mb-1\">{getFileIcon(file.type)}</div>\n                                <div className=\"text-xs text-center px-1 truncate w-full\">\n                                    {file.name}\n                                </div>\n                            </div>\n                        )}\n\n                        {isLast && (\n                            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n                                <span className=\"text-white font-semibold\">\n                                    +{remainingCount}\n                                </span>\n                            </div>\n                        )}\n\n                        {onRemoveFile && (\n                            <button\n                                onClick={(e) => {\n                                    e.stopPropagation();\n                                    onRemoveFile(file.id);\n                                }}\n                                className=\"absolute top-1 right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors\"\n                            >\n                                <XMarkIcon className=\"w-3 h-3\" />\n                            </button>\n                        )}\n                    </div>\n                );\n            })}\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AASO,MAAM,cAA0C,CAAC,EACpD,IAAI,EACJ,QAAQ,EACR,mBAAmB,IAAI,EACvB,YAAY,EAAE,EACjB;IACG,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;IAC9C,MAAM,UAAU,iBAAiB;IAEjC,MAAM,eAAe,CAAC;QAClB,EAAE,eAAe;QACjB,IAAI,UAAU;YACV,SAAS,KAAK,EAAE;QACpB;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAW,CAAC,4HAA4H,EAAE,WAAW;;YACrJ,oBAAoB,0BACjB,6LAAC;gBACG,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;0BAI7B,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACV,WAAW,KAAK,OAAO,iBACpB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCACG,KAAK,KAAK,OAAO;gCACjB,KAAK,KAAK,IAAI;gCACd,WAAU;;;;;;;;;;iDAIlB,6LAAC;4BAAI,WAAU;sCACV,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;4CAAgD,OAAO,KAAK,IAAI;sDACxE,KAAK,IAAI;;;;;;sDAEd,6LAAC;4CAAE,WAAU;;gDACR,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;gDAAE;gDAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;4BAKpF,KAAK,WAAW,kBACb,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,KAAK,cAAc,IAAI;oDAAE;;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CACG,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,KAAK,cAAc,IAAI,EAAE,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnF;KA7Ea;AAsFN,MAAM,kBAAkD,CAAC,EAC5D,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,YAAY,EAAE,EACjB;IACG,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;IACzD,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC;IAE9D,qBACI,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YACnC,OAAO,MAAM,GAAG,mBACb,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;4BAAqC;4BAAS,OAAO,MAAM;4BAAC;;;;;;;kCACzE,6LAAC;wBAAI,WAAU;kCACV,OAAO,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;gCAAkB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CACG,KAAK,KAAK,OAAO;4CACjB,KAAK,KAAK,IAAI;4CACd,WAAU;4CACV,SAAS,IAAM,eAAe;;;;;;;;;;;oCAGrC,8BACG,6LAAC;wCACG,SAAS,IAAM,aAAa,KAAK,EAAE;wCACnC,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCAG5B,KAAK,WAAW,kBACb,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;gDACV,KAAK,cAAc,IAAI;gDAAE;;;;;;;;;;;;;+BArBhC,KAAK,EAAE;;;;;;;;;;;;;;;;YA+BhC,WAAW,MAAM,GAAG,mBACjB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;4BAAqC;4BAAQ,WAAW,MAAM;4BAAC;;;;;;;kCAC5E,6LAAC;wBAAI,WAAU;kCACV,WAAW,GAAG,CAAC,CAAC,qBACb,6LAAC;gCAAkB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAW,CAAC,aAAa,EAAE,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI,GAAG;0DACxD,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;0DACV,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,QAAQ,KAAK,IAAI;;;;;;;;;;;;oCAG5E,8BACG,6LAAC;wCACG,SAAS,IAAM,aAAa,KAAK,EAAE;wCACnC,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCAG5B,KAAK,WAAW,kBACb,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;gDACV,KAAK,cAAc,IAAI;gDAAE;;;;;;;;;;;;;+BArBhC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAgC7C;MAtFa;AA8FN,MAAM,qBAAwD,CAAC,EAClE,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACjB;IACG,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;IAC9C,MAAM,UAAU,iBAAiB;IAEjC,qBACI,6LAAC;QACG,WAAW,CAAC,qGAAqG,EAAE,WAAW;QAC9H,SAAS;;0BAET,6LAAC;gBAAI,WAAU;0BACV,WAAW,KAAK,OAAO,iBACpB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBACG,KAAK,KAAK,OAAO;wBACjB,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;;;;;;yCAIlB,6LAAC;oBAAI,WAAU;8BACV,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;kCACR,KAAK,IAAI;;;;;;kCAEd,6LAAC;wBAAE,WAAU;kCACR,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;AAK7C;MAvCa;AAiDN,MAAM,kBAAkD,CAAC,EAC5D,KAAK,EACL,WAAW,EACX,YAAY,EACZ,aAAa,CAAC,EACd,YAAY,EAAE,EACjB;IACG,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG;IACpC,MAAM,iBAAiB,MAAM,MAAM,GAAG;IAEtC,qBACI,6LAAC;QAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW;kBAChD,aAAa,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;YAC9C,MAAM,UAAU,iBAAiB;YACjC,MAAM,SAAS,UAAU,aAAa,MAAM,GAAG,KAAK,iBAAiB;YAErE,qBACI,6LAAC;gBAEG,WAAU;gBACV,SAAS,IAAM,cAAc;;oBAE5B,WAAW,KAAK,OAAO,iBACpB,6LAAC;wBACG,KAAK,KAAK,OAAO;wBACjB,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;6CAGd,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CAAiB,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI;;;;;;0CACrD,6LAAC;gCAAI,WAAU;0CACV,KAAK,IAAI;;;;;;;;;;;;oBAKrB,wBACG,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAK,WAAU;;gCAA2B;gCACrC;;;;;;;;;;;;oBAKb,8BACG,6LAAC;wBACG,SAAS,CAAC;4BACN,EAAE,eAAe;4BACjB,aAAa,KAAK,EAAE;wBACxB;wBACA,WAAU;kCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;eAnCxB,KAAK,EAAE;;;;;QAwCxB;;;;;;AAGZ;MA9Da", "debugId": null}}, {"offset": {"line": 5113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/MessageInput.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { PaperAirplaneIcon, FaceSmileIcon } from \"@heroicons/react/24/outline\";\nimport { ConnectionStatus } from \"@/types/chat\";\nimport { FilePreview as FilePreviewType } from \"@/types/file\";\nimport { QuickFilePicker } from \"./FilePicker\";\nimport { FilePreviewList } from \"./FilePreview\";\nimport { ApiService } from \"@/api/axios\";\nimport toast from 'react-hot-toast';\n\ninterface MessageInputProps {\n    onSendMessage: (message: string, mediaAttachments?: { mediaUrl: string; mediaName: string; mediaSize: number; mediaType: string; displayOrder?: number }[]) => void;\n    connectionStatus: ConnectionStatus;\n    disabled?: boolean;\n    placeholder?: string;\n}\n\nexport const MessageInput: React.FC<MessageInputProps> = ({\n    onSendMessage,\n    connectionStatus,\n    disabled = false,\n    placeholder = \"Type a message...\"\n}) => {\n    const [message, setMessage] = useState(\"\");\n    const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n    const [selectedFiles, setSelectedFiles] = useState<FilePreviewType[]>([]);\n    const [isUploading, setIsUploading] = useState(false);\n    const [isClient, setIsClient] = useState(false);\n    const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n    useEffect(() => {\n        setIsClient(true);\n    }, []);\n\n    useEffect(() => {\n        const textarea = textareaRef.current;\n        if (textarea) {\n            textarea.style.height = 'auto';\n            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n        }\n    }, [message]);\n\n    const handleSubmit = async (e: React.FormEvent) => {\n        e.preventDefault();\n\n        const hasMessage = message.trim();\n        const hasFiles = selectedFiles.length > 0;\n\n        if ((!hasMessage && !hasFiles) || disabled || !connectionStatus.isConnected || isUploading) {\n            return;\n        }\n\n        try {\n            setIsUploading(true);\n\n            let mediaAttachments: { mediaUrl: string; mediaName: string; mediaSize: number; mediaType: string; displayOrder?: number }[] = [];\n            if (hasFiles) {\n                const filesToUpload = selectedFiles.map(f => f.file);\n                const validation = ApiService.validateFiles(filesToUpload);\n\n                if (!validation.isValid) {\n                    const errorMessage = validation.errors.join('\\n');\n                    throw new Error(errorMessage);\n                }\n\n                const filesWithUploadState = selectedFiles.map(file => ({\n                    ...file,\n                    isUploading: true,\n                    uploadProgress: 0\n                }));\n                setSelectedFiles(filesWithUploadState);\n                const uploadResults = await ApiService.uploadFilesAsyncWithProgress(filesToUpload, (progress) => {\n                    setSelectedFiles(prev => prev.map((file, index) => ({\n                        ...file,\n                        uploadProgress: progress[index]?.progress || 0,\n                        isUploading: progress[index]?.status === 'uploading'\n                    })));\n                });\n\n                mediaAttachments = uploadResults.map((result, index) => ({\n                    mediaUrl: result.url,\n                    mediaName: result.name,\n                    mediaSize: result.size,\n                    mediaType: result.contentType,\n                    displayOrder: result.displayOrder || index + 1\n                }));\n            }\n            onSendMessage(message.trim(), hasFiles ? mediaAttachments : undefined);\n\n            if (selectedFiles.length > 0) {\n                const fileCount = selectedFiles.length;\n                const successMessage = fileCount === 1\n                    ? `File uploaded successfully`\n                    : `${fileCount} files uploaded successfully`;\n\n                toast.success(successMessage, {\n                    duration: 3000,\n                });\n            }\n\n            setMessage(\"\");\n            setSelectedFiles([]);\n\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n            }\n        } catch (error) {\n            const errorMessage = 'Please choice file less than or equal 100MB';\n\n            toast.error(errorMessage);\n\n            setSelectedFiles(prev => prev.map(file => ({\n                ...file,\n                isUploading: false,\n                error: 'Upload failed'\n            })));\n        } finally {\n            setIsUploading(false);\n        }\n    };\n\n    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n        if (e.key === 'Enter' && !e.shiftKey && !isUploading) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n\n    const handleFilesSelected = (files: FilePreviewType[]) => {\n        setSelectedFiles(prev => [...prev, ...files]);\n    };\n\n    const handleRemoveFile = (fileId: string) => {\n        setSelectedFiles(prev => prev.filter(file => file.id !== fileId));\n    };\n\n    const handleFileError = (error: string) => {\n        toast.error(error);\n    };\n\n\n\n    const handleEmojiSelect = (emoji: string) => {\n        setMessage(prev => prev + emoji);\n        setShowEmojiPicker(false);\n        textareaRef.current?.focus();\n    };\n\n    const quickEmojis = ['😀', '😂', '😍', '🥰', '😊', '👍', '❤️', '🔥', '💯', '🎉'];\n\n    const canSend = (message.trim() || selectedFiles.length > 0) && connectionStatus.isConnected && !disabled && !isUploading;\n\n    return (\n        <div className=\"border-t border-gray-200 bg-white px-6 py-4\">\n            {isClient && !connectionStatus.isConnected && (\n                <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-xl\">\n                    <div className=\"text-sm text-red-700 flex items-center gap-2 font-medium\">\n                        <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                        {connectionStatus.reconnectAttempts > 0\n                            ? `Reconnecting... (${connectionStatus.reconnectAttempts})`\n                            : 'Connection lost. Trying to reconnect...'\n                        }\n                    </div>\n                </div>\n            )}\n\n\n\n            {selectedFiles.length > 0 && (\n                <div className=\"mb-4\">\n                    <FilePreviewList\n                        files={selectedFiles}\n                        onRemoveFile={handleRemoveFile}\n                    />\n                </div>\n            )}\n\n            {showEmojiPicker && (\n                <div className=\"mb-4 p-4 bg-white border border-gray-200 rounded-2xl shadow-sm\">\n                    <div className=\"flex flex-wrap gap-2\">\n                        {quickEmojis.map((emoji, index) => (\n                            <button\n                                key={index}\n                                onClick={() => handleEmojiSelect(emoji)}\n                                className=\"w-10 h-10 flex items-center justify-center hover:bg-blue-50 rounded-xl transition-all duration-200 text-xl hover:scale-110\"\n                            >\n                                {emoji}\n                            </button>\n                        ))}\n                    </div>\n                </div>\n            )}\n\n            <div className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-blue-400 focus-within:bg-white transition-all\">\n                <div className=\"flex items-center gap-2\">\n                    <QuickFilePicker\n                        onFilesSelected={handleFilesSelected}\n                        onError={handleFileError}\n                        disabled={disabled || !connectionStatus.isConnected || isUploading}\n                    />\n\n                    <button\n                        type=\"button\"\n                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                        disabled={disabled || !connectionStatus.isConnected || isUploading}\n                        className={`p-2 rounded-xl transition-all disabled:opacity-50 disabled:cursor-not-allowed ${showEmojiPicker\n                            ? 'text-blue-600 bg-blue-100'\n                            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-100'\n                            }`}\n                        title=\"Add emoji\"\n                    >\n                        <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n\n\n                </div>\n\n                <form onSubmit={handleSubmit} className=\"flex-1 flex items-center gap-3\">\n                    <textarea\n                        ref={textareaRef}\n                        value={message}\n                        onChange={(e) => setMessage(e.target.value)}\n                        onKeyDown={handleKeyDown}\n                        placeholder={disabled ? \"Select a conversation to start chatting\" : placeholder}\n                        disabled={disabled || !connectionStatus.isConnected || isUploading}\n                        rows={1}\n                        className=\"flex-1 bg-transparent border-0 resize-none focus:outline-none text-gray-800 placeholder:text-gray-500 leading-relaxed\"\n                        style={{\n                            minHeight: '24px',\n                            maxHeight: '96px'\n                        }}\n                    />\n\n                    <button\n                        type=\"submit\"\n                        disabled={!canSend}\n                        className={`p-2 rounded-lg transition-colors duration-150 ${canSend\n                            ? 'bg-blue-500 hover:bg-blue-600 text-white'\n                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                            }`}\n                        title={\n                            !connectionStatus.isConnected\n                                ? 'Disconnected'\n                                : isUploading\n                                    ? 'Uploading...'\n                                    : 'Send message'\n                        }\n                    >\n                        {isUploading ? (\n                            <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                        ) : (\n                            <PaperAirplaneIcon className=\"h-5 w-5\" />\n                        )}\n                    </button>\n                </form>\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAGA;AACA;AACA;AACA;;;AATA;;;;;;;AAkBO,MAAM,eAA4C,CAAC,EACtD,aAAa,EACb,gBAAgB,EAChB,WAAW,KAAK,EAChB,cAAc,mBAAmB,EACpC;;IACG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,YAAY;QAChB;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,MAAM,WAAW,YAAY,OAAO;YACpC,IAAI,UAAU;gBACV,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,SAAS,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,SAAS,YAAY,EAAE,OAAO;YACnE;QACJ;iCAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,MAAM,aAAa,QAAQ,IAAI;QAC/B,MAAM,WAAW,cAAc,MAAM,GAAG;QAExC,IAAI,AAAC,CAAC,cAAc,CAAC,YAAa,YAAY,CAAC,iBAAiB,WAAW,IAAI,aAAa;YACxF;QACJ;QAEA,IAAI;YACA,eAAe;YAEf,IAAI,mBAA2H,EAAE;YACjI,IAAI,UAAU;gBACV,MAAM,gBAAgB,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACnD,MAAM,aAAa,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;gBAE5C,IAAI,CAAC,WAAW,OAAO,EAAE;oBACrB,MAAM,eAAe,WAAW,MAAM,CAAC,IAAI,CAAC;oBAC5C,MAAM,IAAI,MAAM;gBACpB;gBAEA,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACpD,GAAG,IAAI;wBACP,aAAa;wBACb,gBAAgB;oBACpB,CAAC;gBACD,iBAAiB;gBACjB,MAAM,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,4BAA4B,CAAC,eAAe,CAAC;oBAChF,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gCAChD,GAAG,IAAI;gCACP,gBAAgB,QAAQ,CAAC,MAAM,EAAE,YAAY;gCAC7C,aAAa,QAAQ,CAAC,MAAM,EAAE,WAAW;4BAC7C,CAAC;gBACL;gBAEA,mBAAmB,cAAc,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;wBACrD,UAAU,OAAO,GAAG;wBACpB,WAAW,OAAO,IAAI;wBACtB,WAAW,OAAO,IAAI;wBACtB,WAAW,OAAO,WAAW;wBAC7B,cAAc,OAAO,YAAY,IAAI,QAAQ;oBACjD,CAAC;YACL;YACA,cAAc,QAAQ,IAAI,IAAI,WAAW,mBAAmB;YAE5D,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC1B,MAAM,YAAY,cAAc,MAAM;gBACtC,MAAM,iBAAiB,cAAc,IAC/B,CAAC,0BAA0B,CAAC,GAC5B,GAAG,UAAU,4BAA4B,CAAC;gBAEhD,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,gBAAgB;oBAC1B,UAAU;gBACd;YACJ;YAEA,WAAW;YACX,iBAAiB,EAAE;YAEnB,IAAI,YAAY,OAAO,EAAE;gBACrB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACvC;QACJ,EAAE,OAAO,OAAO;YACZ,MAAM,eAAe;YAErB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAEZ,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACvC,GAAG,IAAI;wBACP,aAAa;wBACb,OAAO;oBACX,CAAC;QACL,SAAU;YACN,eAAe;QACnB;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,aAAa;YAClD,EAAE,cAAc;YAChB,aAAa;QACjB;IACJ;IAEA,MAAM,sBAAsB,CAAC;QACzB,iBAAiB,CAAA,OAAQ;mBAAI;mBAAS;aAAM;IAChD;IAEA,MAAM,mBAAmB,CAAC;QACtB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC7D;IAEA,MAAM,kBAAkB,CAAC;QACrB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;IAChB;IAIA,MAAM,oBAAoB,CAAC;QACvB,WAAW,CAAA,OAAQ,OAAO;QAC1B,mBAAmB;QACnB,YAAY,OAAO,EAAE;IACzB;IAEA,MAAM,cAAc;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEhF,MAAM,UAAU,CAAC,QAAQ,IAAI,MAAM,cAAc,MAAM,GAAG,CAAC,KAAK,iBAAiB,WAAW,IAAI,CAAC,YAAY,CAAC;IAE9G,qBACI,6LAAC;QAAI,WAAU;;YACV,YAAY,CAAC,iBAAiB,WAAW,kBACtC,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;;;;;wBACd,iBAAiB,iBAAiB,GAAG,IAChC,CAAC,iBAAiB,EAAE,iBAAiB,iBAAiB,CAAC,CAAC,CAAC,GACzD;;;;;;;;;;;;YAQjB,cAAc,MAAM,GAAG,mBACpB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,oIAAA,CAAA,kBAAe;oBACZ,OAAO;oBACP,cAAc;;;;;;;;;;;YAKzB,iCACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACV,YAAY,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC;4BAEG,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCAET;2BAJI;;;;;;;;;;;;;;;0BAWzB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;gCACZ,iBAAiB;gCACjB,SAAS;gCACT,UAAU,YAAY,CAAC,iBAAiB,WAAW,IAAI;;;;;;0CAG3D,6LAAC;gCACG,MAAK;gCACL,SAAS,IAAM,mBAAmB,CAAC;gCACnC,UAAU,YAAY,CAAC,iBAAiB,WAAW,IAAI;gCACvD,WAAW,CAAC,8EAA8E,EAAE,kBACtF,8BACA,uDACA;gCACN,OAAM;0CAEN,cAAA,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMjC,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACpC,6LAAC;gCACG,KAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,WAAW;gCACX,aAAa,WAAW,4CAA4C;gCACpE,UAAU,YAAY,CAAC,iBAAiB,WAAW,IAAI;gCACvD,MAAM;gCACN,WAAU;gCACV,OAAO;oCACH,WAAW;oCACX,WAAW;gCACf;;;;;;0CAGJ,6LAAC;gCACG,MAAK;gCACL,UAAU,CAAC;gCACX,WAAW,CAAC,8CAA8C,EAAE,UACtD,6CACA,gDACA;gCACN,OACI,CAAC,iBAAiB,WAAW,GACvB,iBACA,cACI,iBACA;0CAGb,4BACG,6LAAC;oCAAI,WAAU;;;;;yDAEf,6LAAC,oOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD;GAjPa;KAAA", "debugId": null}}, {"offset": {"line": 5437, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/DeleteConversationModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { ExclamationTriangleIcon, XMarkIcon } from \"@heroicons/react/24/outline\";\n\ninterface DeleteConversationModalProps {\n    isOpen: boolean;\n    onClose: () => void;\n    onConfirm: () => void;\n    conversationName: string;\n    isDeleting?: boolean;\n}\n\nexport const DeleteConversationModal: React.FC<DeleteConversationModalProps> = ({\n    isOpen,\n    onClose,\n    onConfirm,\n    conversationName,\n    isDeleting = false\n}) => {\n    if (!isOpen) return null;\n\n    return (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div\n                className=\"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity\"\n                onClick={onClose}\n            />\n\n            <div className=\"flex min-h-full items-center justify-center p-4\">\n                <div className=\"relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all\">\n                    <button\n                        onClick={onClose}\n                        className=\"absolute top-4 right-4 p-1 rounded-full hover:bg-gray-100 transition-colors\"\n                        disabled={isDeleting}\n                    >\n                        <XMarkIcon className=\"w-5 h-5 text-gray-400\" />\n                    </button>\n\n                    <div className=\"p-6\">\n                        <div className=\"flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full\">\n                            <ExclamationTriangleIcon className=\"w-8 h-8 text-red-600\" />\n                        </div>\n\n                        <h3 className=\"text-xl font-semibold text-gray-900 text-center mb-2\">\n                            Delete Conversation\n                        </h3>\n\n                        <p className=\"text-gray-600 text-center mb-6 leading-relaxed\">\n                            Are you sure you want to delete the conversation with{\" \"}\n                            <span className=\"font-semibold text-gray-900\">\"{conversationName}\"</span>?\n                            <br />\n                            <span className=\"text-sm text-red-600 mt-2 block\">\n                                This action cannot be undone and all messages will be permanently deleted.\n                            </span>\n                        </p>\n\n                        <div className=\"flex gap-3\">\n                            <button\n                                onClick={onClose}\n                                disabled={isDeleting}\n                                className=\"flex-1 px-4 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                                Cancel\n                            </button>\n                            <button\n                                onClick={onConfirm}\n                                disabled={isDeleting}\n                                className=\"flex-1 px-4 py-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n                            >\n                                {isDeleting ? (\n                                    <>\n                                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                                        Deleting...\n                                    </>\n                                ) : (\n                                    \"Delete\"\n                                )}\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAaO,MAAM,0BAAkE,CAAC,EAC5E,MAAM,EACN,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,aAAa,KAAK,EACrB;IACG,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBACG,WAAU;gBACV,SAAS;;;;;;0BAGb,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,SAAS;4BACT,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAGzB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,gPAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;;;;;;8CAGvC,6LAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAIrE,6LAAC;oCAAE,WAAU;;wCAAiD;wCACJ;sDACtD,6LAAC;4CAAK,WAAU;;gDAA8B;gDAAE;gDAAiB;;;;;;;wCAAQ;sDACzE,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CACG,SAAS;4CACT,UAAU;4CACV,WAAU;sDACb;;;;;;sDAGD,6LAAC;4CACG,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,2BACG;;kEACI,6LAAC;wDAAI,WAAU;;;;;;oDAAiF;;+DAIpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;KAxEa", "debugId": null}}, {"offset": {"line": 5618, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/CreateGroupModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport { User } from \"@/types/user\";\nimport { ConversationType, ParticipantInfoDetailResponse } from \"@/types/chat\";\nimport { ApiService } from \"@/api/axios\";\nimport { XMarkIcon } from \"@heroicons/react/24/outline\";\nimport toast from 'react-hot-toast';\n\nconst useDebounce = (value: string, delay: number) => {\n    const [debouncedValue, setDebouncedValue] = useState(value);\n\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value);\n        }, delay);\n\n        return () => {\n            clearTimeout(handler);\n        };\n    }, [value, delay]);\n\n    return debouncedValue;\n};\n\ninterface CreateGroupModalProps {\n    isOpen: boolean;\n    onClose: () => void;\n    onGroupCreated: (conversationId: string) => void;\n}\n\nexport const CreateGroupModal: React.FC<CreateGroupModalProps> = ({\n    isOpen,\n    onClose,\n    onGroupCreated\n}) => {\n    const [groupName, setGroupName] = useState(\"\");\n    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);\n    const [searchQuery, setSearchQuery] = useState(\"\");\n    const [searchResults, setSearchResults] = useState<ParticipantInfoDetailResponse[]>([]);\n    const [isSearching, setIsSearching] = useState(false);\n    const [isCreating, setIsCreating] = useState(false);\n    const [groupAvatar, setGroupAvatar] = useState<File | null>(null);\n    const [avatarPreview, setAvatarPreview] = useState<string>(\"\");\n    const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);\n    const fileInputRef = useRef<HTMLInputElement>(null);\n    const [uploadProgress, setUploadProgress] = useState(0);\n    const [errors, setErrors] = useState<{\n        groupName?: string;\n        participants?: string;\n        avatar?: string;\n    }>({});\n\n    const debouncedSearchQuery = useDebounce(searchQuery, 500);\n\n    useEffect(() => {\n        if (isOpen) {\n            setGroupName(\"\");\n            setSelectedUsers([]);\n            setSearchQuery(\"\");\n            setSearchResults([]);\n            setGroupAvatar(null);\n            setAvatarPreview(\"\");\n            setIsUploadingAvatar(false);\n            setUploadProgress(0);\n            setErrors({});\n        }\n    }, [isOpen]);\n\n    const searchUsers = useCallback(async (query: string) => {\n        if (!query.trim()) {\n            setSearchResults([]);\n            setIsSearching(false);\n            return;\n        }\n\n        setIsSearching(true);\n        try {\n            const results = await ApiService.searchUsers(query.trim());\n            setSearchResults(results);\n        } catch (error) {\n            setSearchResults([]);\n            toast.error(\"Failed to search users\");\n        } finally {\n            setIsSearching(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        searchUsers(debouncedSearchQuery);\n    }, [debouncedSearchQuery, searchUsers]);\n\n    useEffect(() => {\n        return () => {\n            if (avatarPreview) {\n                URL.revokeObjectURL(avatarPreview);\n            }\n        };\n    }, [avatarPreview]);\n\n    const filteredSearchResults = useMemo(() => {\n        return searchResults.filter(result =>\n            !selectedUsers.some(selected => selected.id === result.userId)\n        );\n    }, [searchResults, selectedUsers]);\n\n    const handleUserSelect = useCallback((participant: ParticipantInfoDetailResponse) => {\n        const user: User = {\n            id: participant.userId,\n            username: participant.username,\n            email: participant.username,\n            avatar: participant.avatar || undefined\n        };\n\n        if (selectedUsers.some(selected => selected.id === user.id)) {\n            return;\n        }\n\n        setSelectedUsers(prev => [...prev, user]);\n        setSearchQuery(\"\");\n        setSearchResults([]);\n        setErrors(prev => ({ ...prev, participants: undefined }));\n    }, [selectedUsers]);\n\n    const handleUserRemove = useCallback((userId: string) => {\n        setSelectedUsers(prev => prev.filter(user => user.id !== userId));\n    }, []);\n\n    const handleAvatarChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {\n        const file = event.target.files?.[0];\n\n        if (!file) {\n            return;\n        }\n\n        if (!file.type.startsWith('image/')) {\n            setErrors(prev => ({ ...prev, avatar: 'Please select an image file (PNG, JPG, JPEG)' }));\n            toast.error('Please select an image file');\n            event.target.value = '';\n            return;\n        }\n\n        if (file.size > 10 * 1024 * 1024) {\n            setErrors(prev => ({ ...prev, avatar: 'Image size must be less than 10MB' }));\n            toast.error('Image size must be less than 10MB');\n            event.target.value = '';\n            return;\n        }\n\n        if (avatarPreview) {\n            URL.revokeObjectURL(avatarPreview);\n        }\n\n        const previewUrl = URL.createObjectURL(file);\n\n        setGroupAvatar(file);\n        setAvatarPreview(previewUrl);\n        setErrors(prev => ({ ...prev, avatar: undefined }));\n    }, [avatarPreview]);\n\n    const handleRemoveAvatar = useCallback(() => {\n        if (avatarPreview) {\n            URL.revokeObjectURL(avatarPreview);\n        }\n\n        setGroupAvatar(null);\n        setAvatarPreview(\"\");\n        setErrors(prev => ({ ...prev, avatar: undefined }));\n\n        const fileInput = document.getElementById('avatar-upload') as HTMLInputElement;\n        if (fileInput) {\n            fileInput.value = '';\n        }\n    }, [avatarPreview]);\n\n    const validateForm = (): boolean => {\n        const newErrors: typeof errors = {};\n\n        if (!groupName.trim()) {\n            newErrors.groupName = \"Group name is required\";\n        } else if (groupName.trim().length > 100) {\n            newErrors.groupName = \"Group name cannot exceed 100 characters\";\n        }\n\n        if (selectedUsers.length < 2) {\n            newErrors.participants = \"Group must have at least 2 other members\";\n        }\n\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n\n    const handleCreateGroup = async () => {\n        if (!validateForm()) return;\n\n        setIsCreating(true);\n        try {\n            const participantIds = selectedUsers.map(user => user.id || (user as any).userId).filter(Boolean);\n\n            let conversationAvatar: string | undefined;\n\n            setIsUploadingAvatar(true);\n            if (groupAvatar) {\n                setUploadProgress(0);\n\n                try {\n                    const uploadResponse = await ApiService.uploadFilesSync(\n                        [groupAvatar],\n                        (progressEvent) => {\n                            if (progressEvent.total) {\n                                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n                                setUploadProgress(progress);\n                            }\n                        }\n                    );\n\n                    if (uploadResponse.length > 0) {\n                        conversationAvatar = uploadResponse[0].url;\n                    } else {\n                        throw new Error(\"No file uploaded\");\n                    }\n                } catch (uploadError: any) {\n                    const errorMessage = uploadError.response?.data?.message || \"Failed to upload group avatar\";\n                    toast.error(errorMessage);\n                    return;\n                } finally {\n                    setIsUploadingAvatar(false);\n                    setUploadProgress(0);\n                }\n            }\n\n            const conversation = await ApiService.createConversation(\n                participantIds,\n                ConversationType.GROUP,\n                groupName.trim(),\n                conversationAvatar\n            );\n\n            toast.success(`Group \"${groupName}\" created successfully!`);\n            onGroupCreated(conversation.id);\n            onClose();\n        } catch (error: any) {\n            toast.error(error.response?.data?.message || \"Failed to create group\");\n        } finally {\n            setIsCreating(false);\n            setIsUploadingAvatar(false);\n        }\n    };\n\n    if (!isOpen) return null;\n\n    return (\n        <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n            <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-lg max-h-[85vh] flex flex-col overflow-hidden\">\n                <div className=\"relative flex items-center justify-center p-4 border-b border-gray-200\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">Create Group</h2>\n                    <button\n                        onClick={onClose}\n                        className=\"absolute right-4 w-9 h-9 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors text-gray-600 hover:text-gray-800\"\n                        disabled={isCreating}\n                    >\n                        <XMarkIcon className=\"w-5 h-5\" />\n                    </button>\n                </div>\n\n                <div className=\"flex-1 overflow-y-auto\">\n                    <div className=\"p-6 space-y-6\">\n                        <div className=\"flex flex-col items-center\">\n                            <div className=\"relative\">\n                                {avatarPreview ? (\n                                    <>\n                                        <div className=\"w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-lg\">\n                                            <img\n                                                src={avatarPreview}\n                                                alt=\"Group avatar\"\n                                                className=\"w-full h-full object-cover\"\n                                            />\n                                        </div>\n                                        <button\n                                            type=\"button\"\n                                            onClick={handleRemoveAvatar}\n                                            className=\"absolute top-0 right-0 w-7 h-7 bg-gray-600 hover:bg-gray-700 text-white rounded-full flex items-center justify-center transition-colors shadow-lg border-2 border-white z-10\"\n                                            disabled={isCreating}\n                                        >\n                                            <XMarkIcon className=\"w-4 h-4\" />\n                                        </button>\n                                    </>\n                                ) : (\n                                    <div\n                                        onClick={() => {\n                                            if (fileInputRef.current) {\n                                                fileInputRef.current.click();\n                                            }\n                                        }}\n                                        className={`w-24 h-24 bg-gray-100 rounded-full flex flex-col items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors border-2 border-dashed border-gray-300 hover:border-gray-400 ${isCreating ? 'opacity-50 cursor-not-allowed' : ''}`}\n                                    >\n                                        <svg className=\"w-8 h-8 text-gray-400 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                        </svg>\n                                        <span className=\"text-xs text-gray-500 font-medium\">Add Photo</span>\n                                    </div>\n                                )}\n\n                                <input\n                                    ref={fileInputRef}\n                                    type=\"file\"\n                                    accept=\"image/*\"\n                                    onChange={handleAvatarChange}\n                                    className=\"hidden\"\n                                    id=\"avatar-upload\"\n                                    disabled={isCreating}\n                                    style={{ display: 'none' }}\n                                />\n                            </div>\n                            {errors.avatar && (\n                                <p className=\"text-xs text-red-500 mt-2 text-center\">{errors.avatar}</p>\n                            )}\n                        </div>\n\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-900 mb-2\">\n                                Group Name <span className=\"text-red-500\">*</span>\n                            </label>\n                            <input\n                                type=\"text\"\n                                value={groupName}\n                                onChange={(e) => {\n                                    setGroupName(e.target.value);\n                                    if (errors.groupName) {\n                                        setErrors(prev => ({ ...prev, groupName: undefined }));\n                                    }\n                                }}\n                                placeholder=\"Enter group name...\"\n                                className={`w-full px-4 py-3 border-0 bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all text-gray-900 placeholder-gray-500 ${errors.groupName ? 'ring-2 ring-red-500 bg-red-50' : ''}`}\n                                disabled={isCreating}\n                                maxLength={100}\n                            />\n                            <div className=\"flex justify-between items-center mt-2\">\n                                <span className=\"text-xs text-gray-500\">{groupName.length}/100</span>\n                                {errors.groupName && (\n                                    <span className=\"text-xs text-red-500\">{errors.groupName}</span>\n                                )}\n                            </div>\n                        </div>\n                    </div>\n\n                    {selectedUsers.length > 0 && (\n                        <div className=\"px-6 pb-4\">\n                            <div className=\"bg-blue-50 rounded-lg p-3 border border-blue-200\">\n                                <div className=\"flex items-center justify-between mb-2\">\n                                    <h3 className=\"text-sm font-semibold text-blue-900\">\n                                        Selected Members ({selectedUsers.length})\n                                    </h3>\n                                    <div className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full\">\n                                        {selectedUsers.length >= 2 ? '✓ Ready' : `Need ${2 - selectedUsers.length} more`}\n                                    </div>\n                                </div>\n\n                                <div className=\"flex flex-wrap gap-2\">\n                                    {selectedUsers.map(user => (\n                                        <div\n                                            key={user.id}\n                                            className=\"flex items-center gap-2 bg-white px-3 py-2 rounded-full border border-blue-200 hover:border-blue-300 transition-colors group\"\n                                        >\n                                            <div className=\"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-xs\">\n                                                {user.username.charAt(0).toUpperCase()}\n                                            </div>\n                                            <span className=\"text-sm font-medium text-gray-900\">{user.username}</span>\n                                            <button\n                                                onClick={() => handleUserRemove(user.id)}\n                                                className=\"w-5 h-5 bg-gray-200 hover:bg-red-500 text-gray-500 hover:text-white rounded-full flex items-center justify-center transition-all ml-1\"\n                                                disabled={isCreating}\n                                            >\n                                                <XMarkIcon className=\"w-3 h-3\" />\n                                            </button>\n                                        </div>\n                                    ))}\n                                </div>\n\n                                {errors.participants && (\n                                    <p className=\"mt-2 text-sm text-red-600 bg-red-50 p-2 rounded\">{errors.participants}</p>\n                                )}\n                            </div>\n                        </div>\n                    )}\n\n                    <div className=\"flex-1 flex flex-col border-t border-gray-200\">\n                        <div className=\"p-6 pb-4\">\n                            <div className=\"mb-4\">\n                                <h3 className=\"text-sm font-semibold text-gray-900\">Add Members</h3>\n                                <p className=\"text-xs text-gray-500 mt-1\">Search and add people to your group</p>\n                            </div>\n\n                            <div className=\"relative\">\n                                <input\n                                    type=\"text\"\n                                    value={searchQuery}\n                                    onChange={(e) => setSearchQuery(e.target.value)}\n                                    placeholder=\"Search users to add...\"\n                                    className=\"w-full pl-10 pr-4 py-3 border-0 bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all text-gray-900 placeholder-gray-500\"\n                                    disabled={isCreating}\n                                />\n                                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3\">\n                                    <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                                    </svg>\n                                </div>\n                                {isSearching && (\n                                    <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                                        <div className=\"w-4 h-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin\"></div>\n                                    </div>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"flex-1 overflow-y-auto px-6 pb-6\">\n                            {isSearching ? (\n                                <div className=\"flex flex-col items-center justify-center py-8\">\n                                    <div className=\"w-8 h-8 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin\"></div>\n                                    <p className=\"text-sm text-gray-500 mt-3\">Searching users...</p>\n                                </div>\n                            ) : filteredSearchResults.length > 0 ? (\n                                <div className=\"space-y-2\">\n                                    {filteredSearchResults.map(participant => (\n                                        <button\n                                            key={participant.userId}\n                                            onClick={() => handleUserSelect(participant)}\n                                            className=\"w-full flex items-center gap-3 p-3 rounded-lg bg-gray-50 hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-all text-left group\"\n                                            disabled={isCreating}\n                                        >\n                                            <div className=\"w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center text-white font-medium\">\n                                                {participant.username.charAt(0).toUpperCase()}\n                                            </div>\n                                            <div className=\"flex-1\">\n                                                <p className=\"text-sm font-medium text-gray-900\">{participant.username}</p>\n                                                <p className=\"text-xs text-gray-500\">Tap to add to group</p>\n                                            </div>\n                                            <div className=\"w-8 h-8 rounded-full border-2 border-gray-300 group-hover:border-blue-500 group-hover:bg-blue-500 flex items-center justify-center transition-all\">\n                                                <svg className=\"w-4 h-4 text-transparent group-hover:text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                                                    <path fillRule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clipRule=\"evenodd\" />\n                                                </svg>\n                                            </div>\n                                        </button>\n                                    ))}\n                                </div>\n                            ) : searchQuery.trim() && !isSearching ? (\n                                <div className=\"text-center py-12\">\n                                    <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                                        <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                                        </svg>\n                                    </div>\n                                    <p className=\"text-sm text-gray-500\">No users found for \"{searchQuery}\"</p>\n                                    <p className=\"text-xs text-gray-400 mt-1\">Try a different search term</p>\n                                </div>\n                            ) : (\n                                <div className=\"text-center py-12\">\n                                    <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                                        <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                                        </svg>\n                                    </div>\n                                    <p className=\"text-sm text-gray-500\">Search for people to add</p>\n                                    <p className=\"text-xs text-gray-400 mt-1\">Type a name to get started</p>\n                                </div>\n                            )}\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"p-6 bg-white border-t border-gray-200\">\n                    <div className=\"flex gap-3\">\n                        <button\n                            onClick={onClose}\n                            className=\"flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                            disabled={isCreating || isUploadingAvatar}\n                        >\n                            Cancel\n                        </button>\n                        <button\n                            onClick={handleCreateGroup}\n                            disabled={isCreating || isUploadingAvatar || !groupName.trim() || selectedUsers.length < 2}\n                            className=\"flex-1 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all font-medium flex items-center justify-center gap-2 shadow-sm\"\n                        >\n                            {isUploadingAvatar ? (\n                                <>\n                                    <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                                    {uploadProgress > 0 ? `Uploading ${uploadProgress}%` : 'Uploading...'}\n                                </>\n                            ) : isCreating ? (\n                                <>\n                                    <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                                    Creating Group...\n                                </>\n                            ) : (\n                                <>\n                                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                                    </svg>\n                                    Create Group\n                                </>\n                            )}\n                        </button>\n                    </div>\n\n                    <div className=\"mt-3 text-center\">\n                        <p className=\"text-xs text-gray-500\">\n                            {!groupName.trim() ? 'Enter a group name' :\n                                selectedUsers.length < 2 ? `Add ${2 - selectedUsers.length} more member${2 - selectedUsers.length > 1 ? 's' : ''}` :\n                                    '✓ Ready to create group'}\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,cAAc,CAAC,OAAe;;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YAEH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACX;GAdM;AAsBC,MAAM,mBAAoD,CAAC,EAC9D,MAAM,EACN,OAAO,EACP,cAAc,EACjB;;IACG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC,EAAE;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIhC,CAAC;IAEJ,MAAM,uBAAuB,YAAY,aAAa;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,QAAQ;gBACR,aAAa;gBACb,iBAAiB,EAAE;gBACnB,eAAe;gBACf,iBAAiB,EAAE;gBACnB,eAAe;gBACf,iBAAiB;gBACjB,qBAAqB;gBACrB,kBAAkB;gBAClB,UAAU,CAAC;YACf;QACJ;qCAAG;QAAC;KAAO;IAEX,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACnC,IAAI,CAAC,MAAM,IAAI,IAAI;gBACf,iBAAiB,EAAE;gBACnB,eAAe;gBACf;YACJ;YAEA,eAAe;YACf,IAAI;gBACA,MAAM,UAAU,MAAM,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,IAAI;gBACvD,iBAAiB;YACrB,EAAE,OAAO,OAAO;gBACZ,iBAAiB,EAAE;gBACnB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB,SAAU;gBACN,eAAe;YACnB;QACJ;oDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,YAAY;QAChB;qCAAG;QAAC;QAAsB;KAAY;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN;8CAAO;oBACH,IAAI,eAAe;wBACf,IAAI,eAAe,CAAC;oBACxB;gBACJ;;QACJ;qCAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2DAAE;YAClC,OAAO,cAAc,MAAM;mEAAC,CAAA,SACxB,CAAC,cAAc,IAAI;2EAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO,MAAM;;;QAErE;0DAAG;QAAC;QAAe;KAAc;IAEjC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAClC,MAAM,OAAa;gBACf,IAAI,YAAY,MAAM;gBACtB,UAAU,YAAY,QAAQ;gBAC9B,OAAO,YAAY,QAAQ;gBAC3B,QAAQ,YAAY,MAAM,IAAI;YAClC;YAEA,IAAI,cAAc,IAAI;kEAAC,CAAA,WAAY,SAAS,EAAE,KAAK,KAAK,EAAE;kEAAG;gBACzD;YACJ;YAEA;kEAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAK;;YACxC,eAAe;YACf,iBAAiB,EAAE;YACnB;kEAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAU,CAAC;;QAC3D;yDAAG;QAAC;KAAc;IAElB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAClC;kEAAiB,CAAA,OAAQ,KAAK,MAAM;0EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;QAC7D;yDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACpC,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;YAEpC,IAAI,CAAC,MAAM;gBACP;YACJ;YAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjC;wEAAU,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAA+C,CAAC;;gBACtF,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,MAAM,MAAM,CAAC,KAAK,GAAG;gBACrB;YACJ;YAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAC9B;wEAAU,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAoC,CAAC;;gBAC3E,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,MAAM,MAAM,CAAC,KAAK,GAAG;gBACrB;YACJ;YAEA,IAAI,eAAe;gBACf,IAAI,eAAe,CAAC;YACxB;YAEA,MAAM,aAAa,IAAI,eAAe,CAAC;YAEvC,eAAe;YACf,iBAAiB;YACjB;oEAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,CAAC;;QACrD;2DAAG;QAAC;KAAc;IAElB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACnC,IAAI,eAAe;gBACf,IAAI,eAAe,CAAC;YACxB;YAEA,eAAe;YACf,iBAAiB;YACjB;oEAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,CAAC;;YAEjD,MAAM,YAAY,SAAS,cAAc,CAAC;YAC1C,IAAI,WAAW;gBACX,UAAU,KAAK,GAAG;YACtB;QACJ;2DAAG;QAAC;KAAc;IAElB,MAAM,eAAe;QACjB,MAAM,YAA2B,CAAC;QAElC,IAAI,CAAC,UAAU,IAAI,IAAI;YACnB,UAAU,SAAS,GAAG;QAC1B,OAAO,IAAI,UAAU,IAAI,GAAG,MAAM,GAAG,KAAK;YACtC,UAAU,SAAS,GAAG;QAC1B;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC1B,UAAU,YAAY,GAAG;QAC7B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,oBAAoB;QACtB,IAAI,CAAC,gBAAgB;QAErB,cAAc;QACd,IAAI;YACA,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE,IAAI,AAAC,KAAa,MAAM,EAAE,MAAM,CAAC;YAEzF,IAAI;YAEJ,qBAAqB;YACrB,IAAI,aAAa;gBACb,kBAAkB;gBAElB,IAAI;oBACA,MAAM,iBAAiB,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CACnD;wBAAC;qBAAY,EACb,CAAC;wBACG,IAAI,cAAc,KAAK,EAAE;4BACrB,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;4BAC9E,kBAAkB;wBACtB;oBACJ;oBAGJ,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC3B,qBAAqB,cAAc,CAAC,EAAE,CAAC,GAAG;oBAC9C,OAAO;wBACH,MAAM,IAAI,MAAM;oBACpB;gBACJ,EAAE,OAAO,aAAkB;oBACvB,MAAM,eAAe,YAAY,QAAQ,EAAE,MAAM,WAAW;oBAC5D,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACZ;gBACJ,SAAU;oBACN,qBAAqB;oBACrB,kBAAkB;gBACtB;YACJ;YAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CACpD,gBACA,uHAAA,CAAA,mBAAgB,CAAC,KAAK,EACtB,UAAU,IAAI,IACd;YAGJ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,uBAAuB,CAAC;YAC1D,eAAe,aAAa,EAAE;YAC9B;QACJ,EAAE,OAAO,OAAY;YACjB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,WAAW;QACjD,SAAU;YACN,cAAc;YACd,qBAAqB;QACzB;IACJ;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACG,SAAS;4BACT,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAI7B,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;gDACV,8BACG;;sEACI,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEACG,KAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;;;;;;sEAGlB,6LAAC;4DACG,MAAK;4DACL,SAAS;4DACT,WAAU;4DACV,UAAU;sEAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;iFAI7B,6LAAC;oDACG,SAAS;wDACL,IAAI,aAAa,OAAO,EAAE;4DACtB,aAAa,OAAO,CAAC,KAAK;wDAC9B;oDACJ;oDACA,WAAW,CAAC,6LAA6L,EAAE,aAAa,kCAAkC,IAAI;;sEAE9P,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EAClF,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAK,GAAE;;;;;;8EACvE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAK,GAAE;;;;;;;;;;;;sEAE3E,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAI5D,6LAAC;oDACG,KAAK;oDACL,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;oDACV,IAAG;oDACH,UAAU;oDACV,OAAO;wDAAE,SAAS;oDAAO;;;;;;;;;;;;wCAGhC,OAAO,MAAM,kBACV,6LAAC;4CAAE,WAAU;sDAAyC,OAAO,MAAM;;;;;;;;;;;;8CAI3E,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA+C;8DACjD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE9C,6LAAC;4CACG,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC;gDACP,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,IAAI,OAAO,SAAS,EAAE;oDAClB,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW;wDAAU,CAAC;gDACxD;4CACJ;4CACA,aAAY;4CACZ,WAAW,CAAC,qKAAqK,EAAE,OAAO,SAAS,GAAG,kCAAkC,IAAI;4CAC5O,UAAU;4CACV,WAAW;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAK,WAAU;;wDAAyB,UAAU,MAAM;wDAAC;;;;;;;gDACzD,OAAO,SAAS,kBACb,6LAAC;oDAAK,WAAU;8DAAwB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAMvE,cAAc,MAAM,GAAG,mBACpB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;;oDAAsC;oDAC7B,cAAc,MAAM;oDAAC;;;;;;;0DAE5C,6LAAC;gDAAI,WAAU;0DACV,cAAc,MAAM,IAAI,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,cAAc,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;kDAIxF,6LAAC;wCAAI,WAAU;kDACV,cAAc,GAAG,CAAC,CAAA,qBACf,6LAAC;gDAEG,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACV,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAExC,6LAAC;wDAAK,WAAU;kEAAqC,KAAK,QAAQ;;;;;;kEAClE,6LAAC;wDACG,SAAS,IAAM,iBAAiB,KAAK,EAAE;wDACvC,WAAU;wDACV,UAAU;kEAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;+CAZpB,KAAK,EAAE;;;;;;;;;;oCAkBvB,OAAO,YAAY,kBAChB,6LAAC;wCAAE,WAAU;kDAAmD,OAAO,YAAY;;;;;;;;;;;;;;;;;sCAMnG,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAG9C,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDACG,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,aAAY;oDACZ,WAAU;oDACV,UAAU;;;;;;8DAEd,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC7E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;gDAG5E,6BACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM/B,6LAAC;oCAAI,WAAU;8CACV,4BACG,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE9C,sBAAsB,MAAM,GAAG,kBAC/B,6LAAC;wCAAI,WAAU;kDACV,sBAAsB,GAAG,CAAC,CAAA,4BACvB,6LAAC;gDAEG,SAAS,IAAM,iBAAiB;gDAChC,WAAU;gDACV,UAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACV,YAAY,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAE/C,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAE,WAAU;0EAAqC,YAAY,QAAQ;;;;;;0EACtE,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;kEACX,cAAA,6LAAC;4DAAI,WAAU;4DAAkD,MAAK;4DAAe,SAAQ;sEACzF,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAwF,UAAS;;;;;;;;;;;;;;;;;+CAd/H,YAAY,MAAM;;;;;;;;;+CAoBnC,YAAY,IAAI,MAAM,CAAC,4BACvB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC7E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAqB;oDAAY;;;;;;;0DACtE,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;6DAG9C,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC7E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAG7E,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9D,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,SAAS;oCACT,WAAU;oCACV,UAAU,cAAc;8CAC3B;;;;;;8CAGD,6LAAC;oCACG,SAAS;oCACT,UAAU,cAAc,qBAAqB,CAAC,UAAU,IAAI,MAAM,cAAc,MAAM,GAAG;oCACzF,WAAU;8CAET,kCACG;;0DACI,6LAAC;gDAAI,WAAU;;;;;;4CACd,iBAAiB,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC,GAAG;;uDAE3D,2BACA;;0DACI,6LAAC;gDAAI,WAAU;;;;;;4CAAkF;;qEAIrG;;0DACI,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/D,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACnE;;;;;;;;;;;;;;sCAOtB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAE,WAAU;0CACR,CAAC,UAAU,IAAI,KAAK,uBACjB,cAAc,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,cAAc,MAAM,CAAC,YAAY,EAAE,IAAI,cAAc,MAAM,GAAG,IAAI,MAAM,IAAI,GAC9G;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;IAtea;;QAsBoB;;;KAtBpB", "debugId": null}}, {"offset": {"line": 6713, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/ChatSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect, useCallback } from \"react\";\nimport { MagnifyingGlassIcon, XMarkIcon, PaperAirplaneIcon, TrashIcon, UserGroupIcon } from \"@heroicons/react/24/outline\";\nimport { ChatMessage, ConversationCreationResponse, ParticipantInfoDetailResponse } from \"@/types/chat\";\nimport { ApiService } from \"@/api/axios\";\nimport { DeleteConversationModal } from \"./DeleteConversationModal\";\nimport { CreateGroupModal } from \"./CreateGroupModal\";\n\nfunction useDebounce<T>(value: T, delay: number): T {\n    const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value);\n        }, delay);\n\n        return () => {\n            clearTimeout(handler);\n        };\n    }, [value, delay]);\n\n    return debouncedValue;\n}\n\ninterface ChatSidebarProps {\n    conversations: ConversationCreationResponse[];\n    selectedConversation: string | null;\n    messages: Map<string, ChatMessage[]>;\n    onConversationSelect: (conversationId: string) => void;\n    onCreateConversation: (userId: string) => void;\n    onDeleteConversation: (conversationId: string) => void;\n    onRefreshConversations?: () => Promise<void>;\n    loading?: boolean;\n}\n\nexport const ChatSidebar: React.FC<ChatSidebarProps> = ({\n    conversations,\n    selectedConversation,\n    messages,\n    onConversationSelect,\n    onCreateConversation,\n    onDeleteConversation,\n    onRefreshConversations,\n    loading = false\n}) => {\n    const [searchQuery, setSearchQuery] = useState(\"\");\n    const [searchResults, setSearchResults] = useState<ParticipantInfoDetailResponse[]>([]);\n    const [searchLoading, setSearchLoading] = useState(false);\n    const [showSearchResults, setShowSearchResults] = useState(false);\n    const [error, setError] = useState<string | null>(null);\n    const [isClient, setIsClient] = useState(false);\n    const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);\n    const [showDeleteModal, setShowDeleteModal] = useState(false);\n    const [conversationToDelete, setConversationToDelete] = useState<ConversationCreationResponse | null>(null);\n    const [isDeleting, setIsDeleting] = useState(false);\n    const inputRef = useRef<HTMLInputElement>(null);\n\n    // Prevent hydration issues\n    useEffect(() => {\n        setIsClient(true);\n    }, []);\n\n\n\n    const debouncedSearchQuery = useDebounce(searchQuery, 300);\n\n    const searchUsers = useCallback(async (username: string) => {\n        if (!username.trim()) {\n            setSearchResults([]);\n            setSearchLoading(false);\n            return;\n        }\n\n        setSearchLoading(true);\n\n        try {\n            const users = await ApiService.searchUsers(username);\n            setSearchResults(users || []);\n        } catch (error) {\n            setSearchResults([]);\n            setError(\"Không thể tìm kiếm người dùng. Vui lòng thử lại.\");\n        } finally {\n            setSearchLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        if (debouncedSearchQuery.trim()) {\n            setShowSearchResults(true);\n            searchUsers(debouncedSearchQuery);\n        } else {\n            setSearchResults([]);\n            setSearchLoading(false);\n            setShowSearchResults(false);\n        }\n    }, [debouncedSearchQuery, searchUsers]);\n\n    useEffect(() => {\n        if (selectedConversation) {\n            setSearchQuery('');\n            setShowSearchResults(false);\n            setSearchResults([]);\n            setSearchLoading(false);\n        }\n    }, [selectedConversation]);\n\n    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n        const query = e.target.value;\n        setSearchQuery(query);\n\n        if (query.trim()) {\n            setSearchLoading(true);\n        } else {\n            setSearchLoading(false);\n            setSearchResults([]);\n            setShowSearchResults(false);\n        }\n    };\n\n    const clearSearch = () => {\n        setSearchQuery(\"\");\n        setSearchResults([]);\n        setSearchLoading(false);\n        setShowSearchResults(false);\n        inputRef.current?.focus();\n    };\n\n    const handleDeleteClick = (conversation: ConversationCreationResponse, e: React.MouseEvent) => {\n        e.stopPropagation();\n        setConversationToDelete(conversation);\n        setShowDeleteModal(true);\n    };\n\n    const handleDeleteConfirm = async () => {\n        if (!conversationToDelete) return;\n\n        try {\n            setIsDeleting(true);\n            await ApiService.deleteConversation(conversationToDelete.id);\n            onDeleteConversation(conversationToDelete.id);\n            setShowDeleteModal(false);\n            setConversationToDelete(null);\n        } catch (error) {\n            setError(\"Không thể xóa cuộc trò chuyện. Vui lòng thử lại.\");\n        } finally {\n            setIsDeleting(false);\n        }\n    };\n\n    const handleDeleteCancel = () => {\n        setShowDeleteModal(false);\n        setConversationToDelete(null);\n    };\n\n    const handleCreateConversation = async (userId: string) => {\n        try {\n            await onCreateConversation(userId);\n        } catch (error) {\n            setError(\"Không thể tạo cuộc trò chuyện. Vui lòng thử lại.\");\n        }\n    };\n\n    const handleGroupCreated = async (conversationId: string) => {\n        try {\n            if (onRefreshConversations) {\n                await onRefreshConversations();\n            }\n\n            onConversationSelect(conversationId);\n\n        } catch (error) {\n            console.error('❌ Failed to handle group creation:', error);\n        } finally {\n            setShowCreateGroupModal(false);\n        }\n    };\n\n    return (\n        <div className=\"w-80 h-screen bg-white border-r border-gray-200 flex flex-col\">\n            {isClient && error && (\n                <div className=\"fixed top-4 left-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center gap-2 z-50\">\n                    <span className=\"text-sm font-medium\">{error}</span>\n                    <button\n                        onClick={() => setError(null)}\n                        className=\"text-white hover:text-red-200 ml-2\"\n                    >\n                        <XMarkIcon className=\"h-4 w-4\" />\n                    </button>\n                </div>\n            )}\n\n            <div className=\"p-4 border-b border-gray-200\">\n                <div className=\"flex items-center justify-between mb-4\">\n                    <h1 className=\"text-2xl font-bold text-gray-900\">Chats</h1>\n                    <button\n                        onClick={() => setShowCreateGroupModal(true)}\n                        className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors duration-150\"\n                        title=\"Create Group\"\n                    >\n                        <UserGroupIcon className=\"h-5 w-5\" />\n                    </button>\n                </div>\n\n                <div className=\"relative\">\n                    <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                    <input\n                        ref={inputRef}\n                        type=\"text\"\n                        placeholder=\"Search Messenger\"\n                        value={searchQuery}\n                        onChange={handleSearchChange}\n                        className=\"w-full pl-10 pr-10 py-2.5 bg-gray-100 border-0 rounded-full focus:outline-none focus:bg-white focus:ring-1 focus:ring-blue-400 transition-colors duration-150 text-sm placeholder-gray-500\"\n                    />\n                    {searchQuery && (\n                        <button\n                            onClick={clearSearch}\n                            className=\"absolute right-3 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-200 transition-colors duration-150\"\n                        >\n                            <XMarkIcon className=\"h-4 w-4 text-gray-500\" />\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {showSearchResults && (\n                <div className=\"border-b border-gray-200 bg-white\">\n                    {searchLoading ? (\n                        <div className=\"p-4 text-center\">\n                            <div className=\"inline-flex items-center gap-2 text-gray-500\">\n                                <div className=\"w-4 h-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin\"></div>\n                                <span className=\"text-sm\">Searching...</span>\n                            </div>\n                        </div>\n                    ) : searchResults.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                            <p className=\"text-sm\">No users found for \"{searchQuery}\"</p>\n                        </div>\n                    ) : (\n                        <div className=\"max-h-60 overflow-y-auto\">\n                            {searchResults.map((user) => (\n                                <div\n                                    key={user.userId}\n                                    onClick={(e) => {\n                                        e.stopPropagation();\n                                        setSearchQuery('');\n                                        setShowSearchResults(false);\n                                        setSearchResults([]);\n                                        setSearchLoading(false);\n                                        handleCreateConversation(user.userId);\n                                    }}\n                                    className=\"flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors\"\n                                >\n                                    <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm shadow-sm\">\n                                        {user.username?.charAt(0).toUpperCase() || \"?\"}\n                                    </div>\n                                    <div className=\"flex-1\">\n                                        <p className=\"font-medium text-gray-900 text-sm\">{user.username || \"Unknown User\"}</p>\n                                        <p className=\"text-xs text-gray-500\">Start conversation</p>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n\n            <div className=\"flex-1 overflow-y-auto\">\n                {loading ? (\n                    <div className=\"p-8 text-center\">\n                        <div className=\"inline-flex items-center gap-3 text-gray-500\">\n                            <div className=\"w-6 h-6 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin\"></div>\n                            <span className=\"text-sm font-medium\">Loading conversations...</span>\n                        </div>\n                    </div>\n                ) : conversations.length === 0 ? (\n                    <div className=\"p-8 text-center\">\n                        <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                            </svg>\n                        </div>\n                        <p className=\"text-gray-600 font-medium mb-1\">No conversations yet</p>\n                        <p className=\"text-gray-400 text-sm\">Search for users to start chatting</p>\n                    </div>\n                ) : (\n                    conversations.map((conversation) => {\n                        const conversationMessages = messages.get(conversation.id) || [];\n                        const lastMessage = conversationMessages[conversationMessages.length - 1];\n\n                        return (\n                            <div\n                                key={conversation.id}\n                                className={`group relative flex items-start gap-3 px-4 py-4 cursor-pointer transition-all duration-200 hover:bg-gray-50/80 ${selectedConversation === conversation.id\n                                    ? \"bg-blue-50/50 border-r-2 border-blue-500\"\n                                    : \"\"\n                                    }`}\n                                onClick={() => onConversationSelect(conversation.id)}\n                            >\n                                <div className=\"relative flex-shrink-0 mt-0.5\">\n                                    {conversation.conversationAvatar ? (\n                                        <img\n                                            src={conversation.conversationAvatar}\n                                            alt={conversation.conversationName || \"Group\"}\n                                            className=\"w-11 h-11 rounded-full object-cover shadow-sm ring-2 ring-white\"\n                                        />\n                                    ) : (\n                                        <div className=\"w-11 h-11 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-medium text-sm shadow-sm ring-2 ring-white\">\n                                            {conversation.conversationName?.charAt(0).toUpperCase() || \"?\"}\n                                        </div>\n                                    )}\n                                    <div className=\"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n                                </div>\n\n                                <div className=\"flex-1 min-w-0 space-y-1\">\n                                    <div className=\"flex items-center justify-between\">\n                                        <h3 className=\"font-semibold text-gray-900 text-sm truncate pr-2\">\n                                            {conversation.conversationName || \"Unknown Conversation\"}\n                                        </h3>\n                                        <div className=\"flex items-center gap-2 flex-shrink-0\">\n                                            {lastMessage && (\n                                                <span className=\"text-xs text-gray-500 font-medium\">\n                                                    {(() => {\n                                                        try {\n                                                            const date = new Date(lastMessage.createdAt);\n                                                            if (isNaN(date.getTime())) return '';\n                                                            const hours = date.getHours();\n                                                            const minutes = date.getMinutes().toString().padStart(2, '0');\n                                                            const ampm = hours >= 12 ? 'PM' : 'AM';\n                                                            const displayHours = hours % 12 || 12;\n                                                            return `${displayHours}:${minutes} ${ampm}`;\n                                                        } catch {\n                                                            return '';\n                                                        }\n                                                    })()}\n                                                </span>\n                                            )}\n                                            <button\n                                                onClick={(e) => handleDeleteClick(conversation, e)}\n                                                className=\"p-1.5 rounded-md hover:bg-red-100 transition-colors duration-150 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-600\"\n                                                title=\"Delete conversation\"\n                                            >\n                                                <TrashIcon className=\"w-4.5 h-4.5\" />\n                                            </button>\n                                        </div>\n                                    </div>\n\n                                    {lastMessage && (\n                                        <div className=\"flex items-center gap-1.5\">\n                                            {lastMessage.me && (\n                                                <PaperAirplaneIcon className=\"h-3 w-3 text-blue-500 flex-shrink-0\" />\n                                            )}\n                                            <p className=\"text-sm text-gray-600 truncate leading-relaxed\">\n                                                {lastMessage.content}\n                                            </p>\n                                        </div>\n                                    )}\n                                </div>\n                            </div>\n                        );\n                    })\n                )}\n            </div>\n\n            <DeleteConversationModal\n                isOpen={showDeleteModal}\n                onClose={handleDeleteCancel}\n                onConfirm={handleDeleteConfirm}\n                conversationName={conversationToDelete?.conversationName || \"\"}\n                isDeleting={isDeleting}\n            />\n\n            <CreateGroupModal\n                isOpen={showCreateGroupModal}\n                onClose={() => setShowCreateGroupModal(false)}\n                onGroupCreated={handleGroupCreated}\n            />\n        </div>\n    );\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;;;AAPA;;;;;;AASA,SAAS,YAAe,KAAQ,EAAE,KAAa;;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YAEH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACX;GAdS;AA2BF,MAAM,cAA0C,CAAC,EACpD,aAAa,EACb,oBAAoB,EACpB,QAAQ,EACR,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,sBAAsB,EACtB,UAAU,KAAK,EAClB;;IACG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC,EAAE;IACtF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IACtG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,YAAY;QAChB;gCAAG,EAAE;IAIL,MAAM,uBAAuB,YAAY,aAAa;IAEtD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YACnC,IAAI,CAAC,SAAS,IAAI,IAAI;gBAClB,iBAAiB,EAAE;gBACnB,iBAAiB;gBACjB;YACJ;YAEA,iBAAiB;YAEjB,IAAI;gBACA,MAAM,QAAQ,MAAM,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC;gBAC3C,iBAAiB,SAAS,EAAE;YAChC,EAAE,OAAO,OAAO;gBACZ,iBAAiB,EAAE;gBACnB,SAAS;YACb,SAAU;gBACN,iBAAiB;YACrB;QACJ;+CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,qBAAqB,IAAI,IAAI;gBAC7B,qBAAqB;gBACrB,YAAY;YAChB,OAAO;gBACH,iBAAiB,EAAE;gBACnB,iBAAiB;gBACjB,qBAAqB;YACzB;QACJ;gCAAG;QAAC;QAAsB;KAAY;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,sBAAsB;gBACtB,eAAe;gBACf,qBAAqB;gBACrB,iBAAiB,EAAE;gBACnB,iBAAiB;YACrB;QACJ;gCAAG;QAAC;KAAqB;IAEzB,MAAM,qBAAqB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,IAAI,MAAM,IAAI,IAAI;YACd,iBAAiB;QACrB,OAAO;YACH,iBAAiB;YACjB,iBAAiB,EAAE;YACnB,qBAAqB;QACzB;IACJ;IAEA,MAAM,cAAc;QAChB,eAAe;QACf,iBAAiB,EAAE;QACnB,iBAAiB;QACjB,qBAAqB;QACrB,SAAS,OAAO,EAAE;IACtB;IAEA,MAAM,oBAAoB,CAAC,cAA4C;QACnE,EAAE,eAAe;QACjB,wBAAwB;QACxB,mBAAmB;IACvB;IAEA,MAAM,sBAAsB;QACxB,IAAI,CAAC,sBAAsB;QAE3B,IAAI;YACA,cAAc;YACd,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC,qBAAqB,EAAE;YAC3D,qBAAqB,qBAAqB,EAAE;YAC5C,mBAAmB;YACnB,wBAAwB;QAC5B,EAAE,OAAO,OAAO;YACZ,SAAS;QACb,SAAU;YACN,cAAc;QAClB;IACJ;IAEA,MAAM,qBAAqB;QACvB,mBAAmB;QACnB,wBAAwB;IAC5B;IAEA,MAAM,2BAA2B,OAAO;QACpC,IAAI;YACA,MAAM,qBAAqB;QAC/B,EAAE,OAAO,OAAO;YACZ,SAAS;QACb;IACJ;IAEA,MAAM,qBAAqB,OAAO;QAC9B,IAAI;YACA,IAAI,wBAAwB;gBACxB,MAAM;YACV;YAEA,qBAAqB;QAEzB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sCAAsC;QACxD,SAAU;YACN,wBAAwB;QAC5B;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;YACV,YAAY,uBACT,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;kCACvC,6LAAC;wBACG,SAAS,IAAM,SAAS;wBACxB,WAAU;kCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCACG,SAAS,IAAM,wBAAwB;gCACvC,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,wOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,6LAAC;gCACG,KAAK;gCACL,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,WAAU;;;;;;4BAEb,6BACG,6LAAC;gCACG,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMpC,mCACG,6LAAC;gBAAI,WAAU;0BACV,8BACG,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;2BAGlC,cAAc,MAAM,KAAK,kBACzB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAE,WAAU;;4BAAU;4BAAqB;4BAAY;;;;;;;;;;;yCAG5D,6LAAC;oBAAI,WAAU;8BACV,cAAc,GAAG,CAAC,CAAC,qBAChB,6LAAC;4BAEG,SAAS,CAAC;gCACN,EAAE,eAAe;gCACjB,eAAe;gCACf,qBAAqB;gCACrB,iBAAiB,EAAE;gCACnB,iBAAiB;gCACjB,yBAAyB,KAAK,MAAM;4BACxC;4BACA,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACV,KAAK,QAAQ,EAAE,OAAO,GAAG,iBAAiB;;;;;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAqC,KAAK,QAAQ,IAAI;;;;;;sDACnE,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;2BAhBpC,KAAK,MAAM;;;;;;;;;;;;;;;0BAyBxC,6LAAC;gBAAI,WAAU;0BACV,wBACG,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;2BAG9C,cAAc,MAAM,KAAK,kBACzB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC7E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAG7E,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;2BAGzC,cAAc,GAAG,CAAC,CAAC;oBACf,MAAM,uBAAuB,SAAS,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE;oBAChE,MAAM,cAAc,oBAAoB,CAAC,qBAAqB,MAAM,GAAG,EAAE;oBAEzE,qBACI,6LAAC;wBAEG,WAAW,CAAC,+GAA+G,EAAE,yBAAyB,aAAa,EAAE,GAC/J,6CACA,IACA;wBACN,SAAS,IAAM,qBAAqB,aAAa,EAAE;;0CAEnD,6LAAC;gCAAI,WAAU;;oCACV,aAAa,kBAAkB,iBAC5B,6LAAC;wCACG,KAAK,aAAa,kBAAkB;wCACpC,KAAK,aAAa,gBAAgB,IAAI;wCACtC,WAAU;;;;;6DAGd,6LAAC;wCAAI,WAAU;kDACV,aAAa,gBAAgB,EAAE,OAAO,GAAG,iBAAiB;;;;;;kDAGnE,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DACT,aAAa,gBAAgB,IAAI;;;;;;0DAEtC,6LAAC;gDAAI,WAAU;;oDACV,6BACG,6LAAC;wDAAK,WAAU;kEACX,CAAC;4DACE,IAAI;gEACA,MAAM,OAAO,IAAI,KAAK,YAAY,SAAS;gEAC3C,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;gEAClC,MAAM,QAAQ,KAAK,QAAQ;gEAC3B,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;gEACzD,MAAM,OAAO,SAAS,KAAK,OAAO;gEAClC,MAAM,eAAe,QAAQ,MAAM;gEACnC,OAAO,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;4DAC/C,EAAE,OAAM;gEACJ,OAAO;4DACX;wDACJ,CAAC;;;;;;kEAGT,6LAAC;wDACG,SAAS,CAAC,IAAM,kBAAkB,cAAc;wDAChD,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAKhC,6BACG,6LAAC;wCAAI,WAAU;;4CACV,YAAY,EAAE,kBACX,6LAAC,oOAAA,CAAA,oBAAiB;gDAAC,WAAU;;;;;;0DAEjC,6LAAC;gDAAE,WAAU;0DACR,YAAY,OAAO;;;;;;;;;;;;;;;;;;;uBA7D/B,aAAa,EAAE;;;;;gBAoEhC;;;;;;0BAIR,6LAAC,gJAAA,CAAA,0BAAuB;gBACpB,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,kBAAkB,sBAAsB,oBAAoB;gBAC5D,YAAY;;;;;;0BAGhB,6LAAC,yIAAA,CAAA,mBAAgB;gBACb,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,gBAAgB;;;;;;;;;;;;AAIhC;IAvVa;;QA6BoB;;;KA7BpB", "debugId": null}}, {"offset": {"line": 7388, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/constants/storage.ts"], "sourcesContent": ["\nexport const STORAGE_KEYS = {\n    NOTIFICATION_PERMISSION_ASKED: 'notification_permission_asked',\n    FCM_TOKEN_REGISTERED: 'fcm_token_registered',\n\n    USER_PREFERENCES: 'user_preferences',\n    THEME_PREFERENCE: 'theme_preference',\n\n    LAST_CONVERSATION_ID: 'last_conversation_id',\n    DRAFT_MESSAGES: 'draft_messages',\n\n    APP_VERSION: 'app_version',\n    FIRST_VISIT: 'first_visit',\n} as const;\n\n\nexport const ENV_CONFIG = {\n    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',\n\n    FIREBASE: {\n        API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'AIzaSyCbpWYbgEaamlHgRKkCY9I-j3rPpi1uy1E',\n        AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'fir-push-notification-d802d.firebaseapp.com',\n        PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'fir-push-notification-d802d',\n        STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'fir-push-notification-d802d.firebasestorage.app',\n        MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '584254158278',\n        APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '1:584254158278:web:4dec363a775f84d62f6ec7',\n        MEASUREMENT_ID: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'G-77WDGE653M',\n        VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 'BB1EzALF7GSWGywTFfNUq5xHmCFBxR9uYFCQ1qNfoI5S_tiLoPWR67W19q8MkQ3NTCrXrQbuB-t7VAmZ6u542Tw',\n    },\n\n    APP: {\n        NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Chat Message App',\n        VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        NODE_ENV: process.env.NODE_ENV || 'development',\n    }\n} as const;\n\n\nexport const StorageUtils = {\n    setItem: (key: string, value: string | object): boolean => {\n        try {\n            if (typeof window === 'undefined') return false;\n            const stringValue = typeof value === 'string' ? value : JSON.stringify(value);\n            localStorage.setItem(key, stringValue);\n            return true;\n        } catch (error) {\n            return false;\n        }\n    },\n\n    getItem: (key: string): string | null => {\n        try {\n            if (typeof window === 'undefined') return null;\n            return localStorage.getItem(key);\n        } catch (error) {\n            return null;\n        }\n    },\n\n\n    getJsonItem: <T>(key: string): T | null => {\n        try {\n            const item = StorageUtils.getItem(key);\n            return item ? JSON.parse(item) : null;\n        } catch (error) {\n            return null;\n        }\n    },\n\n    removeItem: (key: string): boolean => {\n        try {\n            if (typeof window === 'undefined') return false;\n            localStorage.removeItem(key);\n            return true;\n        } catch (error) {\n            return false;\n        }\n    },\n\n    hasItem: (key: string): boolean => {\n        return StorageUtils.getItem(key) !== null;\n    },\n\n    clear: (): boolean => {\n        try {\n            if (typeof window === 'undefined') return false;\n            localStorage.clear();\n            return true;\n        } catch (error) {\n            return false;\n        }\n    }\n};\n"], "names": [], "mappings": ";;;;;AAiBkB;AAhBX,MAAM,eAAe;IACxB,+BAA+B;IAC/B,sBAAsB;IAEtB,kBAAkB;IAClB,kBAAkB;IAElB,sBAAsB;IACtB,gBAAgB;IAEhB,aAAa;IACb,aAAa;AACjB;AAGO,MAAM,aAAa;IACtB,cAAc,6DAAwC;IAEtD,UAAU;QACN,SAAS,+EAA4C;QACrD,aAAa,mFAAgD;QAC7D,YAAY,mEAA+C;QAC3D,gBAAgB,uFAAmD;QACnE,qBAAqB,oDAAwD;QAC7E,QAAQ,iFAA2C;QACnD,gBAAgB,oDAAmD;QACnE,WAAW,+HAA8C;IAC7D;IAEA,KAAK;QACD,MAAM,wDAAoC;QAC1C,SAAS,6CAAuC;QAChD,UAAU,mDAAwB;IACtC;AACJ;AAGO,MAAM,eAAe;IACxB,SAAS,CAAC,KAAa;QACnB,IAAI;YACA,uCAAmC;;YAAY;YAC/C,MAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,KAAK,SAAS,CAAC;YACvE,aAAa,OAAO,CAAC,KAAK;YAC1B,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,OAAO;QACX;IACJ;IAEA,SAAS,CAAC;QACN,IAAI;YACA,uCAAmC;;YAAW;YAC9C,OAAO,aAAa,OAAO,CAAC;QAChC,EAAE,OAAO,OAAO;YACZ,OAAO;QACX;IACJ;IAGA,aAAa,CAAI;QACb,IAAI;YACA,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACrC,EAAE,OAAO,OAAO;YACZ,OAAO;QACX;IACJ;IAEA,YAAY,CAAC;QACT,IAAI;YACA,uCAAmC;;YAAY;YAC/C,aAAa,UAAU,CAAC;YACxB,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,OAAO;QACX;IACJ;IAEA,SAAS,CAAC;QACN,OAAO,aAAa,OAAO,CAAC,SAAS;IACzC;IAEA,OAAO;QACH,IAAI;YACA,uCAAmC;;YAAY;YAC/C,aAAa,KAAK;YAClB,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,OAAO;QACX;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 7488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/libs/firebase.ts"], "sourcesContent": ["import { initializeApp } from \"firebase/app\";\nimport { getMessaging, getToken } from \"firebase/messaging\";\nimport { ApiService } from \"@/api/axios\";\nimport { ENV_CONFIG, STORAGE_KEYS, StorageUtils } from \"@/constants/storage\";\n\nconst firebaseConfig = {\n    apiKey: ENV_CONFIG.FIREBASE.API_KEY,\n    authDomain: ENV_CONFIG.FIREBASE.AUTH_DOMAIN,\n    projectId: ENV_CONFIG.FIREBASE.PROJECT_ID,\n    storageBucket: ENV_CONFIG.FIREBASE.STORAGE_BUCKET,\n    messagingSenderId: ENV_CONFIG.FIREBASE.MESSAGING_SENDER_ID,\n    appId: ENV_CONFIG.FIREBASE.APP_ID,\n    measurementId: ENV_CONFIG.FIREBASE.MEASUREMENT_ID,\n};\n\nconst app = initializeApp(firebaseConfig);\n\nexport const messaging = typeof window !== 'undefined' ? getMessaging(app) : null;\n\nlet isRequestingToken = false;\n\nexport const requestForToken = async () => {\n    if (typeof window === 'undefined' || !messaging) return null;\n\n    if (isRequestingToken) return null;\n\n    isRequestingToken = true;\n\n    try {\n        const registrations = await navigator.serviceWorker.getRegistrations();\n        for (let registration of registrations) {\n            await registration.unregister();\n        }\n\n        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');\n        await navigator.serviceWorker.ready;\n\n        let permission = Notification.permission;\n        if (permission === 'default') {\n            permission = await Notification.requestPermission();\n        }\n\n        if (permission === 'granted') {\n            const token = await getToken(messaging, {\n                vapidKey: ENV_CONFIG.FIREBASE.VAPID_KEY,\n                serviceWorkerRegistration: registration\n            });\n\n            if (token) {\n                try {\n                    await ApiService.updateFcmToken(token);\n                    StorageUtils.setItem(STORAGE_KEYS.FCM_TOKEN_REGISTERED, 'true');\n                    return token;\n                } catch (error) {\n                    throw error;\n                }\n            } else {\n                return null;\n            }\n        } else {\n            return null;\n        }\n    } catch (error) {\n        throw error;\n    } finally {\n        isRequestingToken = false;\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB;IACnB,QAAQ,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,OAAO;IACnC,YAAY,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,WAAW;IAC3C,WAAW,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,UAAU;IACzC,eAAe,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,cAAc;IACjD,mBAAmB,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,mBAAmB;IAC1D,OAAO,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;IACjC,eAAe,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,cAAc;AACrD;AAEA,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;AAEnB,MAAM,YAAY,uCAAgC,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;AAEtE,IAAI,oBAAoB;AAEjB,MAAM,kBAAkB;IAC3B,IAAI,aAAkB,eAAe,CAAC,WAAW,OAAO;IAExD,IAAI,mBAAmB,OAAO;IAE9B,oBAAoB;IAEpB,IAAI;QACA,MAAM,gBAAgB,MAAM,UAAU,aAAa,CAAC,gBAAgB;QACpE,KAAK,IAAI,gBAAgB,cAAe;YACpC,MAAM,aAAa,UAAU;QACjC;QAEA,MAAM,eAAe,MAAM,UAAU,aAAa,CAAC,QAAQ,CAAC;QAC5D,MAAM,UAAU,aAAa,CAAC,KAAK;QAEnC,IAAI,aAAa,aAAa,UAAU;QACxC,IAAI,eAAe,WAAW;YAC1B,aAAa,MAAM,aAAa,iBAAiB;QACrD;QAEA,IAAI,eAAe,WAAW;YAC1B,MAAM,QAAQ,MAAM,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;gBACpC,UAAU,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS;gBACvC,2BAA2B;YAC/B;YAEA,IAAI,OAAO;gBACP,IAAI;oBACA,MAAM,sHAAA,CAAA,aAAU,CAAC,cAAc,CAAC;oBAChC,8HAAA,CAAA,eAAY,CAAC,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,oBAAoB,EAAE;oBACxD,OAAO;gBACX,EAAE,OAAO,OAAO;oBACZ,MAAM;gBACV;YACJ,OAAO;gBACH,OAAO;YACX;QACJ,OAAO;YACH,OAAO;QACX;IACJ,EAAE,OAAO,OAAO;QACZ,MAAM;IACV,SAAU;QACN,oBAAoB;IACxB;AACJ", "debugId": null}}, {"offset": {"line": 7563, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/components/NotificationPermission.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect } from \"react\";\nimport { requestForToken } from \"@/libs/firebase\";\nimport { STORAGE_KEYS, StorageUtils } from \"@/constants/storage\";\n\ninterface NotificationPermissionProps {\n    onPermissionHandled?: (granted: boolean) => void;\n}\n\nexport const NotificationPermission: React.FC<NotificationPermissionProps> = ({\n    onPermissionHandled\n}) => {\n    useEffect(() => {\n        if (typeof window === 'undefined') return;\n\n        const requestNotificationPermission = async () => {\n            const isRegistered = StorageUtils.hasItem(STORAGE_KEYS.FCM_TOKEN_REGISTERED);\n\n            if (!isRegistered && Notification.permission === 'default') {\n                try {\n                    const token = await requestForToken();\n\n                    if (token) {\n                        onPermissionHandled?.(true);\n                    } else {\n                        onPermissionHandled?.(false);\n                    }\n                } catch (error) {\n                    onPermissionHandled?.(false);\n                }\n            } else if (Notification.permission === 'granted' && !isRegistered) {\n                try {\n                    const token = await requestForToken();\n\n                    if (token) {\n                        onPermissionHandled?.(true);\n                    }\n                } catch (error) {\n                    onPermissionHandled?.(false);\n                }\n            } else if (Notification.permission === 'denied') {\n                onPermissionHandled?.(false);\n            }\n        };\n\n        const timeoutId = setTimeout(() => {\n            requestNotificationPermission();\n        }, 2000);\n\n        return () => clearTimeout(timeoutId);\n    }, []);\n\n    return null;\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAUO,MAAM,yBAAgE,CAAC,EAC1E,mBAAmB,EACtB;;IACG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACN,uCAAmC;;YAAM;YAEzC,MAAM;kFAAgC;oBAClC,MAAM,eAAe,8HAAA,CAAA,eAAY,CAAC,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,oBAAoB;oBAE3E,IAAI,CAAC,gBAAgB,aAAa,UAAU,KAAK,WAAW;wBACxD,IAAI;4BACA,MAAM,QAAQ,MAAM,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;4BAElC,IAAI,OAAO;gCACP,sBAAsB;4BAC1B,OAAO;gCACH,sBAAsB;4BAC1B;wBACJ,EAAE,OAAO,OAAO;4BACZ,sBAAsB;wBAC1B;oBACJ,OAAO,IAAI,aAAa,UAAU,KAAK,aAAa,CAAC,cAAc;wBAC/D,IAAI;4BACA,MAAM,QAAQ,MAAM,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;4BAElC,IAAI,OAAO;gCACP,sBAAsB;4BAC1B;wBACJ,EAAE,OAAO,OAAO;4BACZ,sBAAsB;wBAC1B;oBACJ,OAAO,IAAI,aAAa,UAAU,KAAK,UAAU;wBAC7C,sBAAsB;oBAC1B;gBACJ;;YAEA,MAAM,YAAY;8DAAW;oBACzB;gBACJ;6DAAG;YAEH;oDAAO,IAAM,aAAa;;QAC9B;2CAAG,EAAE;IAEL,OAAO;AACX;GA5Ca;KAAA", "debugId": null}}, {"offset": {"line": 7634, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Chat-Service/chat-message-web-app/chat-message/src/app/chat/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef, useCallback, useMemo } from \"react\";\r\nimport { ChatMessage, ConversationCreationResponse, ConnectionStatus } from \"@/types/chat\";\r\nimport { ApiService } from \"@/api/axios\";\r\nimport { webSocketService, ConversationUpdateEvent } from \"@/libs/websocket\";\r\nimport { createChatRequest, sortMessagesByTime } from \"@/utils/messageUtils\";\r\nimport { ChatHeader } from \"@/components/ChatHeader\";\r\nimport { MessageList } from \"@/components/MessageList\";\r\nimport { MessageInput } from \"@/components/MessageInput\";\r\nimport { ChatSidebar } from \"@/components/ChatSidebar\";\r\nimport { NotificationPermission } from \"@/components/NotificationPermission\";\r\nimport { VideoCallModal, VideoCallInvitation } from \"@/components/VideoCall\";\r\nimport { VideoCallInvitation as VideoCallInvitationType } from \"@/types/video\";\r\n\r\nexport default function ChatPage() {\r\n    const [conversations, setConversations] = useState<ConversationCreationResponse[]>([]);\r\n    const [messages, setMessages] = useState<Map<string, ChatMessage[]>>(new Map());\r\n    const [selectedConversation, setSelectedConversation] = useState<string | null>(null);\r\n\r\n    const [messagePagination, setMessagePagination] = useState<Map<string, {\r\n        currentPage: number;\r\n        totalPages: number;\r\n        hasMore: boolean;\r\n    }>>(new Map());\r\n\r\n    const [loading, setLoading] = useState(false);\r\n    const [loadingMore, setLoadingMore] = useState(false);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [isClient, setIsClient] = useState(false);\r\n    const loadedConversationsRef = useRef<Set<string>>(new Set());\r\n\r\n    const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({\r\n        isConnected: false,\r\n        reconnectAttempts: 0\r\n    });\r\n\r\n    // Video call states\r\n    const [isVideoCallOpen, setIsVideoCallOpen] = useState(false);\r\n    const [videoCallConversationId, setVideoCallConversationId] = useState<string | null>(null);\r\n    const [videoCallInvitations, setVideoCallInvitations] = useState<VideoCallInvitationType[]>([]);\r\n\r\n    useEffect(() => {\r\n        setIsClient(true);\r\n    }, []);\r\n\r\n    const currentMessages = useMemo(() => {\r\n        return selectedConversation ? messages.get(selectedConversation) || [] : [];\r\n    }, [messages, selectedConversation]);\r\n\r\n    const currentConversation = useMemo(() => {\r\n        return conversations.find(conv => conv.id === selectedConversation);\r\n    }, [conversations, selectedConversation]);\r\n\r\n    const selectedConversationRef = useRef(selectedConversation);\r\n    selectedConversationRef.current = selectedConversation;\r\n\r\n    const handleWebSocketMessage = useCallback((message: ChatMessage) => {\r\n        setMessages(prev => {\r\n            const newMessages = new Map(prev);\r\n            const conversationMessages = newMessages.get(message.conversationId) || [];\r\n\r\n            if (message.tempId && message.me) {\r\n                const tempIndex = conversationMessages.findIndex(msg => msg.tempId === message.tempId);\r\n                if (tempIndex !== -1) {\r\n                    const updatedMessages = [...conversationMessages];\r\n                    const existingMessage = updatedMessages[tempIndex];\r\n\r\n                    updatedMessages[tempIndex] = {\r\n                        ...existingMessage,\r\n                        ...message,\r\n                        tempId: message.tempId,\r\n                        status: message.status || existingMessage.status,\r\n                        id: message.id || existingMessage.id\r\n                    };\r\n\r\n                    newMessages.set(message.conversationId, updatedMessages);\r\n                    return newMessages;\r\n                } else {\r\n                    const tempMessage: ChatMessage = {\r\n                        ...message,\r\n                        id: message.tempId,\r\n                    };\r\n                    const updatedMessages = sortMessagesByTime([...conversationMessages, tempMessage]);\r\n                    newMessages.set(message.conversationId, updatedMessages);\r\n                    return newMessages;\r\n                }\r\n            }\r\n\r\n            if (message.tempId && !message.me) {\r\n                return newMessages;\r\n            }\r\n\r\n            if (!message.me) {\r\n                const existingIndex = conversationMessages.findIndex(msg => msg.id === message.id);\r\n                if (existingIndex === -1) {\r\n                    const updatedMessages = sortMessagesByTime([...conversationMessages, message]);\r\n                    newMessages.set(message.conversationId, updatedMessages);\r\n                } else {\r\n                    const updatedMessages = [...conversationMessages];\r\n                    updatedMessages[existingIndex] = { ...updatedMessages[existingIndex], ...message };\r\n                    newMessages.set(message.conversationId, updatedMessages);\r\n                }\r\n                return newMessages;\r\n            }\r\n\r\n            return newMessages;\r\n        });\r\n\r\n        // Auto mark as read for incoming WebSocket messages\r\n        // Backend validates sender - won't mark sender's own messages as read\r\n        const isUnread = !message.read;\r\n        const isViewingConversation = message.conversationId === selectedConversationRef.current;\r\n        const isRealMessage = !message.tempId; // Don't mark temp messages as read\r\n\r\n        if (!message.me && isUnread && isViewingConversation && isRealMessage) {\r\n            setTimeout(async () => {\r\n                // Try WebSocket first, fallback to HTTP API\r\n                const success = webSocketService.markAsRead(message.conversationId, message.id);\r\n                if (!success) {\r\n                    try {\r\n                        await ApiService.markAsRead(message.conversationId, message.id);\r\n                    } catch (error) {\r\n                        // Silent error handling\r\n                    }\r\n                }\r\n            }, 500);\r\n        }\r\n    }, []);\r\n\r\n    const handleConversationUpdate = useCallback((event: ConversationUpdateEvent) => {\r\n        console.log('🔄 Processing conversation update:', {\r\n            type: event.type,\r\n            conversationId: event.conversation.id,\r\n            triggeredBy: event.userId\r\n        });\r\n\r\n        switch (event.type) {\r\n            case 'CONVERSATION_CREATED':\r\n                setConversations(prev => {\r\n                    const existingIndex = prev.findIndex(conv => conv.id === event.conversation.id);\r\n\r\n                    if (existingIndex >= 0) {\r\n                        const updated = [...prev];\r\n                        updated[existingIndex] = { ...updated[existingIndex], ...event.conversation };\r\n                        updated.unshift(updated.splice(existingIndex, 1)[0]);\r\n                        return updated;\r\n                    } else {\r\n                        const updated = [event.conversation, ...prev];\r\n\r\n                        return updated.sort((a, b) =>\r\n                            new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()\r\n                        );\r\n                    }\r\n                });\r\n\r\n                const currentUserId = localStorage.getItem('userId');\r\n                if (!selectedConversation || event.userId === currentUserId) {\r\n                    setSelectedConversation(event.conversation.id);\r\n                }\r\n                break;\r\n\r\n            case 'CONVERSATION_UPDATED':\r\n                setConversations(prev =>\r\n                    prev.map(conv =>\r\n                        conv.id === event.conversation.id\r\n                            ? { ...conv, ...event.conversation }\r\n                            : conv\r\n                    )\r\n                );\r\n                break;\r\n\r\n            case 'CONVERSATION_DELETED':\r\n                setConversations(prev => {\r\n                    const filtered = prev.filter(conv => conv.id !== event.conversation.id);\r\n\r\n                    if (selectedConversation === event.conversation.id) {\r\n                        const nextConversation = filtered.length > 0 ? filtered[0].id : null;\r\n                        setSelectedConversation(nextConversation);\r\n                    }\r\n\r\n                    return filtered;\r\n                });\r\n\r\n                // Clear messages and pagination for deleted conversation\r\n                setMessages(prev => {\r\n                    const newMessages = new Map(prev);\r\n                    newMessages.delete(event.conversation.id);\r\n                    return newMessages;\r\n                });\r\n\r\n                setMessagePagination(prev => {\r\n                    const newPagination = new Map(prev);\r\n                    newPagination.delete(event.conversation.id);\r\n                    return newPagination;\r\n                });\r\n                break;\r\n        }\r\n    }, [selectedConversation]);\r\n\r\n    useEffect(() => {\r\n\r\n        webSocketService.connect(\r\n            handleWebSocketMessage,     // Real-time messages\r\n            setConnectionStatus,        // Connection status\r\n            handleConversationUpdate    // Real-time conversation updates\r\n        );\r\n\r\n        return () => {\r\n            webSocketService.disconnect();\r\n        };\r\n    }, []); // EMPTY DEPS - chỉ connect 1 lần khi mount\r\n\r\n\r\n    const loadConversations = useCallback(async () => {\r\n        try {\r\n            setLoading(true);\r\n\r\n            const data = await ApiService.getConversations();\r\n\r\n            const sortedData = data.sort((a, b) =>\r\n                new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()\r\n            );\r\n\r\n            setConversations(sortedData);\r\n\r\n            if (!webSocketService.isConnected()) {\r\n                webSocketService.forceReconnect();\r\n            } else {\r\n                console.log('🔌 WebSocket check: Already connected ✅');\r\n            }\r\n        } catch (error) {\r\n            console.error('❌ API: Failed to load conversations:', error);\r\n            setError(\"Không thể tải danh sách cuộc trò chuyện\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, []);\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        loadConversations();\r\n    }, [loadConversations]);\r\n\r\n    const loadMessages = useCallback(async (conversationId: string, page: number = 1, append: boolean = false) => {\r\n        if (!append && loading) {\r\n            return;\r\n        }\r\n        if (append && loadingMore) {\r\n            return;\r\n        }\r\n\r\n        try {\r\n            if (!append) setLoading(true);\r\n            else setLoadingMore(true);\r\n\r\n            const pageData = await ApiService.getMessages(conversationId, page);\r\n\r\n            setMessages(prev => {\r\n                const newMessages = new Map(prev);\r\n                const existingMessages = prev.get(conversationId) || [];\r\n\r\n                if (append) {\r\n                    const combinedMessages = [...pageData.data, ...existingMessages];\r\n                    const sortedMessages = sortMessagesByTime(combinedMessages);\r\n                    newMessages.set(conversationId, sortedMessages);\r\n                } else {\r\n                    const sortedMessages = sortMessagesByTime(pageData.data);\r\n                    newMessages.set(conversationId, sortedMessages);\r\n                }\r\n\r\n                return newMessages;\r\n            });\r\n            setMessagePagination(prev => {\r\n                const newPagination = new Map(prev);\r\n                newPagination.set(conversationId, {\r\n                    currentPage: pageData.currentPages,\r\n                    totalPages: pageData.totalPages,\r\n                    hasMore: pageData.currentPages < pageData.totalPages\r\n                });\r\n                return newPagination;\r\n            });\r\n\r\n        } catch (error) {\r\n            setError(\"Không thể tải tin nhắn\");\r\n        } finally {\r\n            setLoading(false);\r\n            setLoadingMore(false);\r\n            if (!append) {\r\n                loadedConversationsRef.current.delete(conversationId);\r\n            }\r\n        }\r\n    }, []);\r\n\r\n\r\n    const handleLoadMore = useCallback(() => {\r\n        if (!selectedConversation) return;\r\n\r\n        const pagination = messagePagination.get(selectedConversation);\r\n        if (!pagination || !pagination.hasMore || loadingMore) return;\r\n\r\n        const nextPage = pagination.currentPage + 1;\r\n        loadMessages(selectedConversation, nextPage, true);\r\n    }, [selectedConversation, messagePagination, loadingMore]);\r\n\r\n    useEffect(() => {\r\n        if (error) {\r\n            const timer = setTimeout(() => setError(null), 5000);\r\n            return () => clearTimeout(timer);\r\n        }\r\n    }, [error]);\r\n\r\n    const handleCreateConversation = useCallback(async (userId: string) => {\r\n        try {\r\n            setLoading(true);\r\n\r\n            const conversation = await ApiService.createConversation([userId]);\r\n\r\n            try {\r\n                await loadMessages(conversation.id, 1, false);\r\n            } catch (messageError) {\r\n                console.warn('⚠️ Failed to load initial messages:', messageError);\r\n            }\r\n\r\n            const fallbackTimer = setTimeout(() => {\r\n                setConversations(prev => {\r\n                    const exists = prev.some(conv => conv.id === conversation.id);\r\n                    if (!exists) {\r\n                        const updated = [conversation, ...prev].sort((a, b) =>\r\n                            new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()\r\n                        );\r\n                        setSelectedConversation(conversation.id);\r\n                        return updated;\r\n                    }\r\n                    return prev;\r\n                });\r\n            }, 2000);\r\n\r\n            return () => clearTimeout(fallbackTimer);\r\n\r\n        } catch (error) {\r\n            console.error('❌ Failed to create conversation:', error);\r\n            setError(\"Không thể tạo cuộc trò chuyện\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [loadMessages]);\r\n\r\n    const handleConversationSelect = useCallback((conversationId: string) => {\r\n\r\n        setSelectedConversation(conversationId);\r\n        selectedConversationRef.current = conversationId;\r\n\r\n        const existingMessages = messages.get(conversationId);\r\n        if (!existingMessages || existingMessages.length === 0) {\r\n            if (!loadedConversationsRef.current.has(conversationId)) {\r\n                loadedConversationsRef.current.add(conversationId);\r\n\r\n                loadMessages(conversationId, 1, false).catch(error => {\r\n                    console.error('❌ API failed to load messages:', error);\r\n                    loadedConversationsRef.current.delete(conversationId);\r\n                });\r\n            }\r\n        } else {\r\n            console.log('📋 Messages already loaded from cache');\r\n        }\r\n    }, [messages, loadMessages]);\r\n\r\n    const handleDeleteConversation = useCallback(async (conversationId: string) => {\r\n\r\n        try {\r\n            await ApiService.deleteConversation(conversationId);\r\n        } catch (error) {\r\n            console.error('❌ Failed to delete conversation:', error);\r\n\r\n            setConversations(prev => prev.filter(conv => conv.id !== conversationId));\r\n\r\n            setMessages(prev => {\r\n                const newMessages = new Map(prev);\r\n                newMessages.delete(conversationId);\r\n                return newMessages;\r\n            });\r\n\r\n            setMessagePagination(prev => {\r\n                const newPagination = new Map(prev);\r\n                newPagination.delete(conversationId);\r\n                return newPagination;\r\n            });\r\n\r\n            if (selectedConversation === conversationId) {\r\n                setSelectedConversation(null);\r\n            }\r\n\r\n            throw error;\r\n        }\r\n    }, [selectedConversation]);\r\n\r\n    const handleSendMessage = useCallback((messageContent: string, mediaAttachments?: { mediaUrl: string; mediaName: string; mediaSize: number; mediaType: string; displayOrder?: number }[]) => {\r\n        if (!selectedConversation || !connectionStatus.isConnected) return;\r\n\r\n        // Convert MediaAttachment to old format for createChatRequest compatibility\r\n        const uploadedFiles = mediaAttachments?.map(attachment => ({\r\n            url: attachment.mediaUrl,\r\n            name: attachment.mediaName,\r\n            size: attachment.mediaSize,\r\n            type: attachment.mediaType,\r\n            displayOrder: attachment.displayOrder\r\n        }));\r\n\r\n        const request = createChatRequest(selectedConversation, messageContent, uploadedFiles);\r\n        const success = webSocketService.sendMessage(request);\r\n\r\n        if (!success) {\r\n            setError(\"Không thể gửi tin nhắn. Vui lòng kiểm tra kết nối.\");\r\n        }\r\n    }, [selectedConversation, connectionStatus.isConnected]);\r\n\r\n    // Video call handlers\r\n    const handleStartVideoCall = useCallback((conversationId: string) => {\r\n        setVideoCallConversationId(conversationId);\r\n        setIsVideoCallOpen(true);\r\n    }, []);\r\n\r\n    const handleCloseVideoCall = useCallback(() => {\r\n        setIsVideoCallOpen(false);\r\n        setVideoCallConversationId(null);\r\n    }, []);\r\n\r\n    const handleAcceptVideoCall = useCallback((invitation: VideoCallInvitationType) => {\r\n        setVideoCallInvitations(prev => prev.filter(inv => inv.id !== invitation.id));\r\n        setVideoCallConversationId(invitation.conversationId);\r\n        setIsVideoCallOpen(true);\r\n    }, []);\r\n\r\n    const handleDeclineVideoCall = useCallback((invitation: VideoCallInvitationType) => {\r\n        setVideoCallInvitations(prev => prev.filter(inv => inv.id !== invitation.id));\r\n    }, []);\r\n\r\n    const handleVideoCallExpire = useCallback((invitationId: string) => {\r\n        setVideoCallInvitations(prev => prev.filter(inv => inv.id !== invitationId));\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"flex h-screen overflow-hidden bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100\">\r\n            <div className=\"absolute inset-0 opacity-30\">\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]\"></div>\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.1),transparent_50%)]\"></div>\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(236,72,153,0.1),transparent_50%)]\"></div>\r\n            </div>\r\n\r\n            <NotificationPermission />\r\n\r\n            <div className=\"relative flex w-full h-full\">\r\n                <ChatSidebar\r\n                    conversations={conversations}\r\n                    selectedConversation={selectedConversation}\r\n                    messages={messages}\r\n                    onConversationSelect={handleConversationSelect}\r\n                    onCreateConversation={handleCreateConversation}\r\n                    onDeleteConversation={handleDeleteConversation}\r\n                    onRefreshConversations={loadConversations}\r\n                    loading={loading}\r\n                />\r\n\r\n                <div className=\"flex-1 flex flex-col bg-white/40 backdrop-blur-sm overflow-hidden\">\r\n                    <ChatHeader\r\n                        conversation={currentConversation}\r\n                        connectionStatus={connectionStatus}\r\n                        onStartVideoCall={handleStartVideoCall}\r\n                    />\r\n\r\n                    <div className=\"flex-1 relative min-h-0\">\r\n                        <div className=\"absolute inset-0 opacity-20\">\r\n                            <div className=\"absolute inset-0\" style={{\r\n                                backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\r\n                            }}></div>\r\n                        </div>\r\n\r\n                        <MessageList\r\n                            messages={currentMessages}\r\n                            loading={loading}\r\n                            hasMoreMessages={selectedConversation ? messagePagination.get(selectedConversation)?.hasMore || false : false}\r\n                            onLoadMore={handleLoadMore}\r\n                            loadingMore={loadingMore}\r\n                            hasSelectedConversation={!!selectedConversation}\r\n                        />\r\n                    </div>\r\n\r\n                    {selectedConversation && (\r\n                        <MessageInput\r\n                            onSendMessage={handleSendMessage}\r\n                            connectionStatus={connectionStatus}\r\n                        />\r\n                    )}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Video Call Modal */}\r\n            {isVideoCallOpen && videoCallConversationId && (\r\n                <VideoCallModal\r\n                    isOpen={isVideoCallOpen}\r\n                    onClose={handleCloseVideoCall}\r\n                    conversationId={videoCallConversationId}\r\n                    conversationName={currentConversation?.conversationName || 'Video Call'}\r\n                />\r\n            )}\r\n\r\n            {/* Video Call Invitations */}\r\n            {videoCallInvitations.map((invitation) => (\r\n                <VideoCallInvitation\r\n                    key={invitation.id}\r\n                    invitation={invitation}\r\n                    onAccept={handleAcceptVideoCall}\r\n                    onDecline={handleDeclineVideoCall}\r\n                    onExpire={handleVideoCallExpire}\r\n                />\r\n            ))}\r\n\r\n            {\r\n                isClient && error && (\r\n                    <div className=\"fixed top-6 right-6 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-2xl flex items-center gap-3 backdrop-blur-sm border border-red-400/20 z-50\">\r\n                        <div className=\"w-2 h-2 bg-red-200 rounded-full animate-pulse\"></div>\r\n                        <span className=\"font-medium\">{error}</span>\r\n                        <button\r\n                            onClick={() => setError(null)}\r\n                            className=\"text-red-100 hover:text-white transition-colors p-1 rounded-full hover:bg-red-400/20\"\r\n                        >\r\n                            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                            </svg>\r\n                        </button>\r\n                    </div>\r\n                )\r\n            }\r\n\r\n        </div >\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAZA;;;;;;;;;;;AAee,SAAS;;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC,EAAE;IACrF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,IAAI;IACzE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIrD,IAAI;IAER,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IAEvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACvE,aAAa;QACb,mBAAmB;IACvB;IAEA,oBAAoB;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAE9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,YAAY;QAChB;6BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YAC5B,OAAO,uBAAuB,SAAS,GAAG,CAAC,yBAAyB,EAAE,GAAG,EAAE;QAC/E;4CAAG;QAAC;QAAU;KAAqB;IAEnC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAChC,OAAO,cAAc,IAAI;yDAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;QAClD;gDAAG;QAAC;QAAe;KAAqB;IAExC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvC,wBAAwB,OAAO,GAAG;IAElC,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACxC;gEAAY,CAAA;oBACR,MAAM,cAAc,IAAI,IAAI;oBAC5B,MAAM,uBAAuB,YAAY,GAAG,CAAC,QAAQ,cAAc,KAAK,EAAE;oBAE1E,IAAI,QAAQ,MAAM,IAAI,QAAQ,EAAE,EAAE;wBAC9B,MAAM,YAAY,qBAAqB,SAAS;sFAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM;;wBACrF,IAAI,cAAc,CAAC,GAAG;4BAClB,MAAM,kBAAkB;mCAAI;6BAAqB;4BACjD,MAAM,kBAAkB,eAAe,CAAC,UAAU;4BAElD,eAAe,CAAC,UAAU,GAAG;gCACzB,GAAG,eAAe;gCAClB,GAAG,OAAO;gCACV,QAAQ,QAAQ,MAAM;gCACtB,QAAQ,QAAQ,MAAM,IAAI,gBAAgB,MAAM;gCAChD,IAAI,QAAQ,EAAE,IAAI,gBAAgB,EAAE;4BACxC;4BAEA,YAAY,GAAG,CAAC,QAAQ,cAAc,EAAE;4BACxC,OAAO;wBACX,OAAO;4BACH,MAAM,cAA2B;gCAC7B,GAAG,OAAO;gCACV,IAAI,QAAQ,MAAM;4BACtB;4BACA,MAAM,kBAAkB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE;mCAAI;gCAAsB;6BAAY;4BACjF,YAAY,GAAG,CAAC,QAAQ,cAAc,EAAE;4BACxC,OAAO;wBACX;oBACJ;oBAEA,IAAI,QAAQ,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE;wBAC/B,OAAO;oBACX;oBAEA,IAAI,CAAC,QAAQ,EAAE,EAAE;wBACb,MAAM,gBAAgB,qBAAqB,SAAS;0FAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,EAAE;;wBACjF,IAAI,kBAAkB,CAAC,GAAG;4BACtB,MAAM,kBAAkB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE;mCAAI;gCAAsB;6BAAQ;4BAC7E,YAAY,GAAG,CAAC,QAAQ,cAAc,EAAE;wBAC5C,OAAO;4BACH,MAAM,kBAAkB;mCAAI;6BAAqB;4BACjD,eAAe,CAAC,cAAc,GAAG;gCAAE,GAAG,eAAe,CAAC,cAAc;gCAAE,GAAG,OAAO;4BAAC;4BACjF,YAAY,GAAG,CAAC,QAAQ,cAAc,EAAE;wBAC5C;wBACA,OAAO;oBACX;oBAEA,OAAO;gBACX;;YAEA,oDAAoD;YACpD,sEAAsE;YACtE,MAAM,WAAW,CAAC,QAAQ,IAAI;YAC9B,MAAM,wBAAwB,QAAQ,cAAc,KAAK,wBAAwB,OAAO;YACxF,MAAM,gBAAgB,CAAC,QAAQ,MAAM,EAAE,mCAAmC;YAE1E,IAAI,CAAC,QAAQ,EAAE,IAAI,YAAY,yBAAyB,eAAe;gBACnE;oEAAW;wBACP,4CAA4C;wBAC5C,MAAM,UAAU,2HAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,QAAQ,cAAc,EAAE,QAAQ,EAAE;wBAC9E,IAAI,CAAC,SAAS;4BACV,IAAI;gCACA,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU,CAAC,QAAQ,cAAc,EAAE,QAAQ,EAAE;4BAClE,EAAE,OAAO,OAAO;4BACZ,wBAAwB;4BAC5B;wBACJ;oBACJ;mEAAG;YACP;QACJ;uDAAG,EAAE;IAEL,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAC1C,QAAQ,GAAG,CAAC,sCAAsC;gBAC9C,MAAM,MAAM,IAAI;gBAChB,gBAAgB,MAAM,YAAY,CAAC,EAAE;gBACrC,aAAa,MAAM,MAAM;YAC7B;YAEA,OAAQ,MAAM,IAAI;gBACd,KAAK;oBACD;0EAAiB,CAAA;4BACb,MAAM,gBAAgB,KAAK,SAAS;gGAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,YAAY,CAAC,EAAE;;4BAE9E,IAAI,iBAAiB,GAAG;gCACpB,MAAM,UAAU;uCAAI;iCAAK;gCACzB,OAAO,CAAC,cAAc,GAAG;oCAAE,GAAG,OAAO,CAAC,cAAc;oCAAE,GAAG,MAAM,YAAY;gCAAC;gCAC5E,QAAQ,OAAO,CAAC,QAAQ,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;gCACnD,OAAO;4BACX,OAAO;gCACH,MAAM,UAAU;oCAAC,MAAM,YAAY;uCAAK;iCAAK;gCAE7C,OAAO,QAAQ,IAAI;sFAAC,CAAC,GAAG,IACpB,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO;;4BAEjF;wBACJ;;oBAEA,MAAM,gBAAgB,aAAa,OAAO,CAAC;oBAC3C,IAAI,CAAC,wBAAwB,MAAM,MAAM,KAAK,eAAe;wBACzD,wBAAwB,MAAM,YAAY,CAAC,EAAE;oBACjD;oBACA;gBAEJ,KAAK;oBACD;0EAAiB,CAAA,OACb,KAAK,GAAG;kFAAC,CAAA,OACL,KAAK,EAAE,KAAK,MAAM,YAAY,CAAC,EAAE,GAC3B;wCAAE,GAAG,IAAI;wCAAE,GAAG,MAAM,YAAY;oCAAC,IACjC;;;oBAGd;gBAEJ,KAAK;oBACD;0EAAiB,CAAA;4BACb,MAAM,WAAW,KAAK,MAAM;2FAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,YAAY,CAAC,EAAE;;4BAEtE,IAAI,yBAAyB,MAAM,YAAY,CAAC,EAAE,EAAE;gCAChD,MAAM,mBAAmB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG;gCAChE,wBAAwB;4BAC5B;4BAEA,OAAO;wBACX;;oBAEA,yDAAyD;oBACzD;0EAAY,CAAA;4BACR,MAAM,cAAc,IAAI,IAAI;4BAC5B,YAAY,MAAM,CAAC,MAAM,YAAY,CAAC,EAAE;4BACxC,OAAO;wBACX;;oBAEA;0EAAqB,CAAA;4BACjB,MAAM,gBAAgB,IAAI,IAAI;4BAC9B,cAAc,MAAM,CAAC,MAAM,YAAY,CAAC,EAAE;4BAC1C,OAAO;wBACX;;oBACA;YACR;QACJ;yDAAG;QAAC;KAAqB;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YAEN,2HAAA,CAAA,mBAAgB,CAAC,OAAO,CACpB,wBACA,qBACA,yBAA4B,iCAAiC;;YAGjE;sCAAO;oBACH,2HAAA,CAAA,mBAAgB,CAAC,UAAU;gBAC/B;;QACJ;6BAAG,EAAE,GAAG,2CAA2C;IAGnD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAClC,IAAI;gBACA,WAAW;gBAEX,MAAM,OAAO,MAAM,sHAAA,CAAA,aAAU,CAAC,gBAAgB;gBAE9C,MAAM,aAAa,KAAK,IAAI;0EAAC,CAAC,GAAG,IAC7B,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO;;gBAG7E,iBAAiB;gBAEjB,IAAI,CAAC,2HAAA,CAAA,mBAAgB,CAAC,WAAW,IAAI;oBACjC,2HAAA,CAAA,mBAAgB,CAAC,cAAc;gBACnC,OAAO;oBACH,QAAQ,GAAG,CAAC;gBAChB;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,SAAS;YACb,SAAU;gBACN,WAAW;YACf;QACJ;kDAAG,EAAE;IAIL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN;QACJ;6BAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO,gBAAwB,OAAe,CAAC,EAAE,SAAkB,KAAK;YACrG,IAAI,CAAC,UAAU,SAAS;gBACpB;YACJ;YACA,IAAI,UAAU,aAAa;gBACvB;YACJ;YAEA,IAAI;gBACA,IAAI,CAAC,QAAQ,WAAW;qBACnB,eAAe;gBAEpB,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,gBAAgB;gBAE9D;0DAAY,CAAA;wBACR,MAAM,cAAc,IAAI,IAAI;wBAC5B,MAAM,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,EAAE;wBAEvD,IAAI,QAAQ;4BACR,MAAM,mBAAmB;mCAAI,SAAS,IAAI;mCAAK;6BAAiB;4BAChE,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE;4BAC1C,YAAY,GAAG,CAAC,gBAAgB;wBACpC,OAAO;4BACH,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,IAAI;4BACvD,YAAY,GAAG,CAAC,gBAAgB;wBACpC;wBAEA,OAAO;oBACX;;gBACA;0DAAqB,CAAA;wBACjB,MAAM,gBAAgB,IAAI,IAAI;wBAC9B,cAAc,GAAG,CAAC,gBAAgB;4BAC9B,aAAa,SAAS,YAAY;4BAClC,YAAY,SAAS,UAAU;4BAC/B,SAAS,SAAS,YAAY,GAAG,SAAS,UAAU;wBACxD;wBACA,OAAO;oBACX;;YAEJ,EAAE,OAAO,OAAO;gBACZ,SAAS;YACb,SAAU;gBACN,WAAW;gBACX,eAAe;gBACf,IAAI,CAAC,QAAQ;oBACT,uBAAuB,OAAO,CAAC,MAAM,CAAC;gBAC1C;YACJ;QACJ;6CAAG,EAAE;IAGL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC/B,IAAI,CAAC,sBAAsB;YAE3B,MAAM,aAAa,kBAAkB,GAAG,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,IAAI,aAAa;YAEvD,MAAM,WAAW,WAAW,WAAW,GAAG;YAC1C,aAAa,sBAAsB,UAAU;QACjD;+CAAG;QAAC;QAAsB;QAAmB;KAAY;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,OAAO;gBACP,MAAM,QAAQ;gDAAW,IAAM,SAAS;+CAAO;gBAC/C;0CAAO,IAAM,aAAa;;YAC9B;QACJ;6BAAG;QAAC;KAAM;IAEV,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO;YAChD,IAAI;gBACA,WAAW;gBAEX,MAAM,eAAe,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;oBAAC;iBAAO;gBAEjE,IAAI;oBACA,MAAM,aAAa,aAAa,EAAE,EAAE,GAAG;gBAC3C,EAAE,OAAO,cAAc;oBACnB,QAAQ,IAAI,CAAC,uCAAuC;gBACxD;gBAEA,MAAM,gBAAgB;oFAAW;wBAC7B;4FAAiB,CAAA;gCACb,MAAM,SAAS,KAAK,IAAI;2GAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa,EAAE;;gCAC5D,IAAI,CAAC,QAAQ;oCACT,MAAM,UAAU;wCAAC;2CAAiB;qCAAK,CAAC,IAAI;gHAAC,CAAC,GAAG,IAC7C,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG,OAAO;;oCAE7E,wBAAwB,aAAa,EAAE;oCACvC,OAAO;gCACX;gCACA,OAAO;4BACX;;oBACJ;mFAAG;gBAEH;sEAAO,IAAM,aAAa;;YAE9B,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS;YACb,SAAU;gBACN,WAAW;YACf;QACJ;yDAAG;QAAC;KAAa;IAEjB,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAE1C,wBAAwB;YACxB,wBAAwB,OAAO,GAAG;YAElC,MAAM,mBAAmB,SAAS,GAAG,CAAC;YACtC,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;gBACpD,IAAI,CAAC,uBAAuB,OAAO,CAAC,GAAG,CAAC,iBAAiB;oBACrD,uBAAuB,OAAO,CAAC,GAAG,CAAC;oBAEnC,aAAa,gBAAgB,GAAG,OAAO,KAAK;0EAAC,CAAA;4BACzC,QAAQ,KAAK,CAAC,kCAAkC;4BAChD,uBAAuB,OAAO,CAAC,MAAM,CAAC;wBAC1C;;gBACJ;YACJ,OAAO;gBACH,QAAQ,GAAG,CAAC;YAChB;QACJ;yDAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO;YAEhD,IAAI;gBACA,MAAM,sHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;YACxC,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;gBAElD;sEAAiB,CAAA,OAAQ,KAAK,MAAM;8EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;gBAEzD;sEAAY,CAAA;wBACR,MAAM,cAAc,IAAI,IAAI;wBAC5B,YAAY,MAAM,CAAC;wBACnB,OAAO;oBACX;;gBAEA;sEAAqB,CAAA;wBACjB,MAAM,gBAAgB,IAAI,IAAI;wBAC9B,cAAc,MAAM,CAAC;wBACrB,OAAO;oBACX;;gBAEA,IAAI,yBAAyB,gBAAgB;oBACzC,wBAAwB;gBAC5B;gBAEA,MAAM;YACV;QACJ;yDAAG;QAAC;KAAqB;IAEzB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC,gBAAwB;YAC3D,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,WAAW,EAAE;YAE5D,4EAA4E;YAC5E,MAAM,gBAAgB,kBAAkB;2DAAI,CAAA,aAAc,CAAC;wBACvD,KAAK,WAAW,QAAQ;wBACxB,MAAM,WAAW,SAAS;wBAC1B,MAAM,WAAW,SAAS;wBAC1B,MAAM,WAAW,SAAS;wBAC1B,cAAc,WAAW,YAAY;oBACzC,CAAC;;YAED,MAAM,UAAU,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,sBAAsB,gBAAgB;YACxE,MAAM,UAAU,2HAAA,CAAA,mBAAgB,CAAC,WAAW,CAAC;YAE7C,IAAI,CAAC,SAAS;gBACV,SAAS;YACb;QACJ;kDAAG;QAAC;QAAsB,iBAAiB,WAAW;KAAC;IAEvD,sBAAsB;IACtB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC,2BAA2B;YAC3B,mBAAmB;QACvB;qDAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACrC,mBAAmB;YACnB,2BAA2B;QAC/B;qDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC;+DAAwB,CAAA,OAAQ,KAAK,MAAM;uEAAC,CAAA,MAAO,IAAI,EAAE,KAAK,WAAW,EAAE;;;YAC3E,2BAA2B,WAAW,cAAc;YACpD,mBAAmB;QACvB;sDAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACxC;gEAAwB,CAAA,OAAQ,KAAK,MAAM;wEAAC,CAAA,MAAO,IAAI,EAAE,KAAK,WAAW,EAAE;;;QAC/E;uDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC;+DAAwB,CAAA,OAAQ,KAAK,MAAM;uEAAC,CAAA,MAAO,IAAI,EAAE,KAAK;;;QAClE;sDAAG,EAAE;IAEL,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGnB,6LAAC,+IAAA,CAAA,yBAAsB;;;;;0BAEvB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oIAAA,CAAA,cAAW;wBACR,eAAe;wBACf,sBAAsB;wBACtB,UAAU;wBACV,sBAAsB;wBACtB,sBAAsB;wBACtB,sBAAsB;wBACtB,wBAAwB;wBACxB,SAAS;;;;;;kCAGb,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,mIAAA,CAAA,aAAU;gCACP,cAAc;gCACd,kBAAkB;gCAClB,kBAAkB;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;4CAAmB,OAAO;gDACrC,iBAAiB,CAAC,8PAA8P,CAAC;4CACrR;;;;;;;;;;;kDAGJ,6LAAC,oIAAA,CAAA,cAAW;wCACR,UAAU;wCACV,SAAS;wCACT,iBAAiB,uBAAuB,kBAAkB,GAAG,CAAC,uBAAuB,WAAW,QAAQ;wCACxG,YAAY;wCACZ,aAAa;wCACb,yBAAyB,CAAC,CAAC;;;;;;;;;;;;4BAIlC,sCACG,6LAAC,qIAAA,CAAA,eAAY;gCACT,eAAe;gCACf,kBAAkB;;;;;;;;;;;;;;;;;;YAOjC,mBAAmB,yCAChB,6LAAC,oJAAA,CAAA,iBAAc;gBACX,QAAQ;gBACR,SAAS;gBACT,gBAAgB;gBAChB,kBAAkB,qBAAqB,oBAAoB;;;;;;YAKlE,qBAAqB,GAAG,CAAC,CAAC,2BACvB,6LAAC,yJAAA,CAAA,sBAAmB;oBAEhB,YAAY;oBACZ,UAAU;oBACV,WAAW;oBACX,UAAU;mBAJL,WAAW,EAAE;;;;;YAStB,YAAY,uBACR,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAe;;;;;;kCAC/B,6LAAC;wBACG,SAAS,IAAM,SAAS;wBACxB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/D,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrG;GA3gBwB;KAAA", "debugId": null}}]}