package vn.khanhduc.chatmessage.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import vn.khanhduc.chatmessage.dto.ApiResponse;
import vn.khanhduc.chatmessage.dto.videocall.*;
import vn.khanhduc.chatmessage.service.VideoCallService;
import vn.khanhduc.chatmessage.service.TwilioVideoService;

import java.util.List;

@RestController
@RequestMapping("/video")
@RequiredArgsConstructor
@Slf4j
public class VideoCallController {

    private final VideoCallService videoCallService;
    private final TwilioVideoService twilioVideoService;

    @PostMapping("/token")
    public ResponseEntity<ApiResponse<VideoCallTokenResponse>> getTwilioAccessToken(
            @RequestBody VideoCallTokenRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("Getting Twilio access token for user: {} in conversation: {}", userId, request.getConversationId());
            
            String token = twilioVideoService.generateAccessToken(userId, request.getConversationId());
            
            VideoCallTokenResponse response = VideoCallTokenResponse.builder()
                    .token(token)
                    .roomName(request.getConversationId())
                    .identity(userId)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting Twilio access token", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get access token: " + e.getMessage()));
        }
    }

    @PostMapping("/join")
    public ResponseEntity<ApiResponse<VideoCallRoomResponse>> joinRoom(
            @RequestBody VideoCallJoinRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} joining video call room: {}", userId, request.getConversationId());
            
            VideoCallRoomResponse response = videoCallService.joinRoom(
                    request.getConversationId(), 
                    userId, 
                    request
            );
            
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error joining video call room", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to join room: " + e.getMessage()));
        }
    }

    @PostMapping("/leave")
    public ResponseEntity<ApiResponse<Void>> leaveRoom(
            @RequestBody VideoCallLeaveRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} leaving video call room: {}", userId, request.getConversationId());
            
            videoCallService.leaveRoom(request.getConversationId(), userId);
            
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error leaving video call room", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to leave room: " + e.getMessage()));
        }
    }

    @GetMapping("/{conversationId}/participants")
    public ResponseEntity<ApiResponse<VideoCallParticipantsResponse>> getRoomParticipants(
            @PathVariable String conversationId,
            Authentication authentication) {
        try {
            log.info("Getting participants for video call room: {}", conversationId);
            
            VideoCallParticipantsResponse response = videoCallService.getRoomParticipants(conversationId);
            
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting room participants", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get participants: " + e.getMessage()));
        }
    }

    @PostMapping("/invite")
    public ResponseEntity<ApiResponse<Void>> inviteToCall(
            @RequestBody VideoCallInviteRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} inviting participants to video call: {}", userId, request.getConversationId());
            
            videoCallService.inviteToCall(
                    request.getConversationId(), 
                    userId, 
                    request.getParticipantIds()
            );
            
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error inviting to video call", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to invite to call: " + e.getMessage()));
        }
    }

    @PostMapping("/end")
    public ResponseEntity<ApiResponse<Void>> endCall(
            @RequestBody VideoCallEndRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} ending video call: {}", userId, request.getConversationId());
            
            videoCallService.endCall(request.getConversationId(), userId);
            
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("Error ending video call", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to end call: " + e.getMessage()));
        }
    }
}
