{"name": "@types/twilio-video", "version": "2.7.3", "description": "TypeScript definitions for twilio-video", "license": "MIT", "contributors": [{"name": "MindDoc", "url": "https://github.com/minddocdev", "githubUsername": "minddocdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/darioblanco", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "katashin", "url": "https://github.com/ktsn", "githubUsername": "ktsn"}, {"name": "<PERSON>", "url": "https://github.com/ben8p", "githubUsername": "ben8p"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/nifled", "githubUsername": "nifled"}, {"name": "<PERSON>", "url": "https://github.com/howitzer-industries", "githubUsername": "howitzer-industries"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ksocha", "githubUsername": "ksocha"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/twilio-video"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "462c4be4500d7ed717ff3d3fda86490eed4ee81d75e66414024b7a240c92bf84", "typeScriptVersion": "3.3"}