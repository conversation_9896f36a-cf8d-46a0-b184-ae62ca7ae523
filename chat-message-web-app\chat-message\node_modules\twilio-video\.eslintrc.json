{"env": {"browser": true, "es6": true, "node": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly", "globalThis": false}, "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "rules": {"@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-this-alias": "off", "accessor-pairs": "error", "array-bracket-newline": "off", "array-bracket-spacing": ["error", "never"], "array-callback-return": "error", "array-element-newline": "off", "arrow-body-style": "off", "arrow-parens": ["error", "as-needed"], "arrow-spacing": ["error", {"after": true, "before": true}], "block-scoped-var": "error", "block-spacing": ["error", "always"], "brace-style": ["error", "1tbs", {"allowSingleLine": true}], "callback-return": "error", "camelcase": "error", "capitalized-comments": "off", "class-methods-use-this": "off", "comma-dangle": "off", "comma-spacing": ["error", {"after": true, "before": false}], "comma-style": ["error", "last"], "complexity": "off", "computed-property-spacing": ["error", "never"], "consistent-return": "warn", "consistent-this": ["error", "self"], "curly": "error", "default-case": "off", "dot-location": ["error", "property"], "dot-notation": ["error", {"allowKeywords": true}], "eol-last": "error", "eqeqeq": "error", "func-call-spacing": "error", "func-name-matching": "off", "func-names": "off", "func-style": ["error", "declaration", {"allowArrowFunctions": true}], "function-call-argument-newline": "off", "function-paren-newline": "off", "generator-star-spacing": "error", "global-require": "off", "guard-for-in": "off", "handle-callback-err": "error", "id-blacklist": "error", "id-length": "off", "id-match": "error", "implicit-arrow-linebreak": "off", "indent": ["error", 2, {"SwitchCase": 1}], "indent-legacy": "off", "init-declarations": "off", "jsx-quotes": "error", "key-spacing": "error", "keyword-spacing": "error", "line-comment-position": "off", "linebreak-style": ["error", "unix"], "lines-around-comment": "off", "lines-around-directive": "off", "lines-between-class-members": "off", "max-classes-per-file": "off", "max-depth": "error", "max-len": "off", "max-lines": "off", "max-lines-per-function": "off", "max-nested-callbacks": "error", "max-params": "off", "max-statements": "off", "max-statements-per-line": "off", "multiline-comment-style": "off", "multiline-ternary": "off", "new-cap": "error", "new-parens": "error", "newline-after-var": "off", "newline-before-return": "off", "newline-per-chained-call": "off", "no-alert": "error", "no-array-constructor": "error", "no-await-in-loop": "error", "no-bitwise": "off", "no-buffer-constructor": "error", "no-caller": "error", "no-catch-shadow": "off", "no-confusing-arrow": "off", "no-console": "error", "no-const-assign": "error", "no-continue": "off", "no-div-regex": "error", "no-duplicate-imports": "error", "no-else-return": ["error", {"allowElseIf": true}], "no-empty-function": "off", "no-eq-null": "error", "no-eval": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-label": "error", "no-extra-parens": "off", "no-floating-decimal": "error", "no-implicit-globals": "error", "no-implied-eval": "error", "no-inline-comments": "off", "no-inner-declarations": ["error", "functions"], "no-invalid-this": "error", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-lonely-if": "error", "no-loop-func": "error", "no-magic-numbers": "off", "no-mixed-operators": "off", "no-mixed-requires": "error", "no-multi-assign": "off", "no-multi-spaces": "off", "no-multi-str": "off", "no-multiple-empty-lines": "error", "no-native-reassign": "error", "no-negated-condition": "off", "no-negated-in-lhs": "error", "no-nested-ternary": "off", "no-new": "error", "no-new-func": "error", "no-new-object": "error", "no-new-require": "error", "no-new-wrappers": "error", "no-octal-escape": "error", "no-param-reassign": "off", "no-path-concat": "error", "no-plusplus": "off", "no-process-env": "error", "no-process-exit": "error", "no-proto": "error", "no-redeclare": ["error", {"builtinGlobals": false}], "no-restricted-globals": "error", "no-restricted-imports": "error", "no-restricted-modules": "error", "no-restricted-properties": "error", "no-restricted-syntax": "error", "no-return-assign": "error", "no-return-await": "error", "no-script-url": "error", "no-self-compare": "error", "no-sequences": "error", "no-shadow": "off", "no-spaced-func": "error", "no-sync": "off", "no-tabs": "error", "no-template-curly-in-string": "error", "no-ternary": "off", "no-throw-literal": "error", "no-trailing-spaces": "error", "no-undef": ["error", {"typeof": false}], "no-undef-init": "error", "no-undefined": "error", "no-underscore-dangle": "off", "no-unmodified-loop-condition": "error", "no-unneeded-ternary": "error", "no-unused-expressions": "error", "no-use-before-define": ["error", {"functions": false}], "no-useless-call": "error", "no-useless-computed-key": "error", "no-useless-concat": "error", "no-useless-constructor": "off", "no-useless-rename": "error", "no-useless-return": "off", "no-var": "off", "no-void": "error", "no-warning-comments": "warn", "no-with": "error", "no-whitespace-before-property": "off", "nonblock-statement-body-position": "error", "object-curly-newline": "error", "object-curly-spacing": ["error", "always"], "object-shorthand": "off", "one-var": ["error", "never"], "one-var-declaration-per-line": "error", "operator-assignment": "error", "operator-linebreak": "off", "padded-blocks": "off", "padding-line-between-statements": "error", "prefer-arrow-callback": "off", "prefer-const": "off", "prefer-destructuring": "off", "prefer-named-capture-group": "off", "prefer-numeric-literals": "error", "prefer-object-spread": "off", "prefer-promise-reject-errors": "off", "prefer-reflect": "off", "prefer-rest-params": "off", "prefer-spread": "off", "prefer-template": "off", "quote-props": ["error", "consistent"], "quotes": ["error", "single"], "radix": "off", "require-await": "error", "require-jsdoc": "off", "require-unicode-regexp": "off", "rest-spread-spacing": ["error", "never"], "semi": "error", "semi-spacing": "error", "semi-style": ["error", "last"], "sort-imports": "error", "sort-keys": "off", "sort-vars": "error", "space-before-blocks": "error", "space-before-function-paren": ["error", {"anonymous": "never", "named": "never", "asyncArrow": "always"}], "space-in-parens": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": "error", "spaced-comment": ["error", "always"], "strict": "off", "switch-colon-spacing": ["error", {"after": true, "before": false}], "symbol-description": "error", "template-curly-spacing": ["error", "never"], "template-tag-spacing": "error", "unicode-bom": ["error", "never"], "valid-jsdoc": "off", "vars-on-top": "off", "wrap-iife": "error", "wrap-regex": "off", "yield-star-spacing": "error"}}