{"version": 3, "file": "cancelableroompromise.js", "sourceRoot": "", "sources": ["../lib/cancelableroompromise.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,IAAM,iBAAiB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAE9D;;;;;;;;GAQG;AACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,UAAU;IAC1G,IAAI,8BAA8B,CAAC;IACnC,IAAM,iBAAiB,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;IAEhD,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU;QACxE,IAAI,gBAAgB,CAAC;QACrB,cAAc,CAAC,SAAS,uBAAuB,CAAC,WAAW;YACzD,IAAI,UAAU,EAAE,EAAE;gBAChB,OAAO,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;aACpD;YACD,gBAAgB,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;YACvD,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,4BAA4B,CAAC,iCAAiC;gBACvH,IAAI,UAAU,EAAE,EAAE;oBAChB,MAAM,iBAAiB,CAAC;iBACzB;gBACD,8BAA8B,GAAG,iCAAiC,EAAE,CAAC;gBACrE,OAAO,8BAA8B,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,sBAAsB,CAAC,aAAa;YACnD,IAAI,UAAU,EAAE,EAAE;gBAChB,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,iBAAiB,CAAC;aACzB;YACD,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,KAAK;YAC7B,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,SAAS,QAAQ;QAClB,IAAI,8BAA8B,EAAE;YAClC,8BAA8B,CAAC,MAAM,EAAE,CAAC;SACzC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC"}