{"version": 3, "file": "createlocaltracks.js", "sourceRoot": "", "sources": ["../lib/createlocaltracks.ts"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWb,6EAA6E;AAErE,IAAA,cAAc,GAAK,OAAO,CAAC,QAAQ,CAAC,eAAtB,CAAuB;AACvC,IAAA,KAAqC,OAAO,CAAC,UAAU,CAAC,EAAtD,YAAY,kBAAA,EAAE,gBAAgB,sBAAwB,CAAC;AAEzD,IAAA,KAIF,OAAO,CAAC,mBAAmB,CAAC,EAH9B,eAAe,qBAAA,EACf,cAAc,oBAAA,EACd,eAAe,qBACe,CAAC;AAEjC,IAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAC5B,IAAA,KAA4E,OAAO,CAAC,kBAAkB,CAAC,EAArG,iBAAiB,uBAAA,EAAE,mBAAmB,yBAAA,EAAgB,aAAa,8BAAkC,CAAC;AAC9G,IAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAEhE,2EAA2E;AAC3E,yEAAyE;AACzE,WAAW;AACX,IAAI,qBAAqB,GAAG,CAAC,CAAC;AAgB9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDG;AACH,SAAsB,iBAAiB,CAAC,OAAkC;;;;;;;oBAClE,kBAAkB,GACtB,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC;oBAErD,WAAW,cACf,KAAK,EAAE,kBAAkB,EACzB,YAAY,cAAA,EACZ,UAAU,EAAE,mBAAmB,EAC/B,QAAQ,EAAE,iBAAiB,EAC3B,eAAe,iBAAA;wBACf,cAAc,gBAAA;wBACd,eAAe,iBAAA;wBACf,gBAAgB,kBAAA;wBAChB,GAAG,KAAA,EACH,KAAK,EAAE,kBAAkB,IACtB,OAAO,CACX,CAAC;oBAEI,gBAAgB,GAAG,yBAAuB,EAAE,qBAAqB,MAAG,CAAC;oBACrE,SAAS,GAAG,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;oBACjD,GAAG,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;oBAE1F,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,KAAA,EAAE,EAAE,WAAW,CAAC,CAAC;oBAE9D,gEAAgE;oBAChE,iEAAiE;oBACjE,gCAAgC;oBAChC,oHAAoH;oBACpH,2EAA2E;oBAC3E,yEAAyE;oBACzE,OAAQ,iBAAyB,CAAC,IAAI,CAAC;oBAEvC,IAAI,WAAW,CAAC,KAAK,KAAK,KAAK,IAAI,WAAW,CAAC,KAAK,KAAK,KAAK,EAAE;wBAC9D,GAAG,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;wBAC9E,sBAAO,EAAE,EAAC;qBACX;oBAED,IAAI,WAAW,CAAC,MAAM,EAAE;wBACtB,GAAG,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;wBAC7C,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;wBAC9C,sBAAO,WAAW,CAAC,MAAM,EAAC;qBAC3B;oBAEK,sBAAsB,GAA2B;wBACrD,KAAK,EAAE,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI;4BACpE,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE;4BAClC,CAAC,CAAC,EAAE,wBAAwB,EAAE,MAAM,EAAE;wBACxC,KAAK,EAAE,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI;4BACpE,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE;4BAClC,CAAC,CAAC,EAAE;qBACP,CAAC;oBAEF,sBAAsB,CAAC,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAAC;oBACjE,sBAAsB,CAAC,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAAC;oBAIjE,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,EAAE;wBACzC,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,0BAA0B,KAAK,SAAS,EAAE;4BACrE,sBAAsB,CAAC,KAAK,CAAC,0BAA0B,GAAG,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC;yBACxG;wBAED,IAAI,0BAA0B,IAAI,WAAW,CAAC,KAAK,EAAE;4BACnD,wBAAwB,GAAG,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC;4BACtE,OAAO,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC;yBACnD;wBAED,IAAI,CAAC,CAAC,0BAA0B,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;4BACtD,sBAAsB,CAAC,KAAK,CAAC,wBAAwB,GAAG,MAAM,CAAC;yBAChE;6BAAM,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,KAAM,WAAW,CAAC,KAAsC,CAAC,wBAAwB,EAArF,CAAqF,CAAC,EAAE;4BAClI,mCAAmC;4BACnC,MAAM,aAAa,CAAC,uDAAuD,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;yBAClG;6BAAM;4BACL,sBAAsB,CAAC,KAAK,CAAC,wBAAwB,GAAG,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC;yBACpG;qBACF;oBAED,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,0BAA0B,KAAK,SAAS,EAAE;wBAC9G,sBAAsB,CAAC,KAAK,CAAC,0BAA0B,GAAG,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC;qBACxG;oBAED,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,EAAE;wBACzC,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;qBAC/B;oBACD,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,EAAE;wBACzC,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;qBAC/B;oBAEK,sBAAsB,GAAG;wBAC7B,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,KAAK,EAAE,WAAW,CAAC,KAAK;qBACzB,CAAC;oBAEI,yBAAyB,GAAG,OAAO,WAAW,CAAC,KAAK,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC;;;;oBAGjG,qBAAM,CAAC,yBAAyB;4BAClD,CAAC,CAAC,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,YAAY,EAAE,sBAAsB,CAAC;4BACzE,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,EAAA;;oBAF/C,WAAW,GAAG,SAEiC;oBAE/C,iBAAiB,0CAClB,WAAW,CAAC,cAAc,EAAE,WAC5B,WAAW,CAAC,cAAc,EAAE,EAChC,CAAC;oBAEF,GAAG,CAAC,IAAI,CAAC,8CAA8C,EAAE,iBAAiB,CAAC,CAAC;oBAErE,qBAAM,OAAO,CAAC,GAAG,CACtB,iBAAiB,CAAC,GAAG,CAAC,UAAM,gBAAgB;;;;;6CACtC,CAAA,gBAAgB,CAAC,IAAI,KAAK,OAAO,IAAI,wBAAwB,CAAA,EAA7D,wBAA6D;wCACrB,qBAAM,8CAAsB,CAAC,gBAAgB,EAAE,wBAAwB,EAAE,GAAG,CAAC,EAAA;;wCAAjH,KAAoC,SAA6E,EAA/G,UAAU,gBAAA,EAAE,iBAAiB,uBAAA;wCACrC,sBAAO,IAAI,iBAAiB,CAAC,eAAe,CAAC,UAAU,iCAClD,sBAAsB,CAAC,KAAK,GAC5B,iBAAiB,KACpB,iBAAiB,mBAAA,IACjB,EAAC;;wCACE,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,EAAE;4CAC5C,sBAAO,IAAI,iBAAiB,CAAC,eAAe,CAAC,gBAAgB,wBACxD,sBAAsB,CAAC,KAAK,GAC5B,iBAAiB,EACpB,EAAC;yCACJ;;4CACD,sBAAO,IAAI,iBAAiB,CAAC,eAAe,CAAC,gBAAgB,wBACxD,sBAAsB,CAAC,KAAK,GAC5B,iBAAiB,EACpB,EAAC;;;6BACJ,CAAC,CACH,EAAA;wBApBD,sBAAO,SAoBN,EAAC;;;oBAEF,GAAG,CAAC,IAAI,CAAC,8BAA8B,EAAE,OAAK,CAAC,CAAC;oBAChD,MAAM,OAAK,CAAC;;;;;CAEf;AApID,8CAoIC"}