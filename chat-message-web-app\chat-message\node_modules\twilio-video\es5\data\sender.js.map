{"version": 3, "file": "sender.js", "sourceRoot": "", "sources": ["../../lib/data/sender.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,oBAAoB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACtD,IAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC;AAE7C;;;;;GAKG;AACH;IAA8B,mCAAoB;IAChD;;;;;OAKG;IACH,yBAAY,iBAAiB,EAAE,cAAc,EAAE,OAAO;QAAtD,YACE,kBAAM,QAAQ,EAAE,EAAE,iBAAiB,EAAE,cAAc,EAAE,OAAO,CAAC,SAS9D;QARC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;;OAIG;IACH,mCAAS,GAAT,UAAU,KAAK;QACb,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,qCAAW,GAAX,UAAY,KAAK;QACf,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,wCAAc,GAAd,UAAe,WAAW;QACxB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAGD;;;;;OAKG;IACH,+BAAK,GAAL;QAAA,iBAQC;QAPC,IAAM,KAAK,GAAG,IAAI,eAAe,CAC/B,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtB,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAvB,CAAuB,CAAC,CAAC;QACrD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,2CAAiB,GAAjB,UAAkB,WAAW;QAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,8BAAI,GAAJ,UAAK,IAAI;QACP,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,WAAW;YACpC,IAAI;gBACF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACd,cAAc;aACf;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;YACxB,IAAI;gBACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClB;YAAC,OAAO,KAAK,EAAE;gBACd,cAAc;aACf;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8BAAI,GAAJ;QACE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,KAAK,EAAE,EAAnB,CAAmB,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,EAAE,EAAZ,CAAY,CAAC,CAAC;QAC5C,iBAAM,IAAI,WAAE,CAAC;IACf,CAAC;IACH,sBAAC;AAAD,CAAC,AAtGD,CAA8B,oBAAoB,GAsGjD;AAED,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC"}