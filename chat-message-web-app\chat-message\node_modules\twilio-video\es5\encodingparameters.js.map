{"version": 3, "file": "encodingparameters.js", "sourceRoot": "", "sources": ["../lib/encodingparameters.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;AAEpD;;;;;;;;GAQG;AACH;IAAqC,0CAAY;IAC/C;;;;OAIG;IACH,gCAAY,kBAAkB,EAAE,iBAAiB;QAAjD,YACE,iBAAO,SAoBR;QAlBC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC;YACjC,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;SACtB,EAAE,kBAAkB,CAAC,CAAC;QAEvB,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,eAAe,EAAE;gBACf,KAAK,EAAE,kBAAkB,CAAC,eAAe;gBACzC,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,kBAAkB,CAAC,eAAe;gBACzC,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,iBAAiB;aACzB;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;OAGG;IACH,uCAAM,GAAN;QACE,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,uCAAM,GAAN,UAAO,kBAAkB;QAAzB,iBAoBC;QAnBC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,EAAE,kBAAkB,CAAC,CAAC;QAEvB,IAAM,iBAAiB,GAAG;YACxB,iBAAiB;YACjB,iBAAiB;SAClB,CAAC,MAAM,CAAC,UAAC,iBAAiB,EAAE,cAAc;YACzC,IAAI,KAAI,CAAC,cAAc,CAAC,KAAK,kBAAkB,CAAC,cAAc,CAAC,EAAE;gBAC/D,KAAI,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;gBAC1D,iBAAiB,GAAG,IAAI,CAAC;aAC1B;YACD,OAAO,iBAAiB,CAAC;QAC3B,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,IAAI,iBAAiB,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACtB;IACH,CAAC;IACH,6BAAC;AAAD,CAAC,AAlED,CAAqC,YAAY,GAkEhD;AAED;;;GAGG;AAEH,MAAM,CAAC,OAAO,GAAG,sBAAsB,CAAC"}