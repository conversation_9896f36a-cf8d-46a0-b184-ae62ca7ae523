{"version": 3, "file": "localparticipant.js", "sourceRoot": "", "sources": ["../lib/localparticipant.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,gBAAgB,GAAK,OAAO,CAAC,UAAU,CAAC,iBAAxB,CAAyB;AAC3C,IAAA,KAAwD,OAAO,CAAC,QAAQ,CAAC,EAAvE,YAAY,kBAAA,EAAE,uBAAuB,6BAAA,EAAE,UAAU,gBAAsB,CAAC;AAC1E,IAAA,KAAmC,OAAO,CAAC,kBAAkB,CAAC,EAAhD,CAAC,gBAAA,EAAE,aAAa,mBAAgC,CAAC;AAC7D,IAAA,kBAAkB,GAAK,OAAO,CAAC,iBAAiB,CAAC,mBAA/B,CAAgC;AAEpD,IAAA,KAIF,OAAO,CAAC,mBAAmB,CAAC,EAH9B,eAAe,qBAAA,EACf,cAAc,oBAAA,EACd,eAAe,qBACe,CAAC;AAEjC,IAAM,0BAA0B,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACvF,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AACrF,IAAM,0BAA0B,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACvF,IAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAE7C;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH;IAA+B,oCAAW;IACxC;;;;;OAKG;IACH,0BAAY,SAAS,EAAE,WAAW,EAAE,OAAO;QAA3C,iBAwDC;QAvDC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,eAAe,iBAAA;YACf,eAAe,iBAAA;YACf,cAAc,gBAAA;YACd,gBAAgB,kBAAA;YAChB,0BAA0B,4BAAA;YAC1B,0BAA0B,4BAAA;YAC1B,yBAAyB,2BAAA;YACzB,qBAAqB,EAAE,KAAK;YAC5B,MAAM,EAAE,WAAW;SACpB,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,YAAY,GAAG,OAAO,CAAC,qBAAqB;YAChD,CAAC,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,CAAC,IAAI,KAAK,MAAM,EAA1B,CAA0B,CAAC,CAAC;YACvE,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;QAEd,QAAA,kBAAM,SAAS,EAAE,OAAO,CAAC,SAAC;QAE1B,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,cAAc,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,aAAa;aAC7B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,OAAO,CAAC,cAAc;aAC9B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAC,gBAAgB;aAChC;YACD,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;aAC1C;YACD,0BAA0B,EAAE;gBAC1B,KAAK,EAAE,OAAO,CAAC,yBAAyB;aACzC;YACD,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;aAC1C;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,YAAY;aACpB;YACD,eAAe,EAAE;gBACf,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,eAAe,CAAC;gBACnC,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,2BAA2B,EAAE,CAAC;;IACrC,CAAC;IAED;;;;;;OAMG;IACH,oCAAS,GAAT,UAAU,KAAK,EAAE,EAAE,EAAE,QAAQ;QAC3B,IAAM,UAAU,GAAG,iBAAM,SAAS,YAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC9C,IAAI,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YAC/C,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACtC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACH,yCAAc,GAAd,UAAe,KAAK,EAAE,QAAQ;;QAC5B,iDAAiD;QACjD,IAAM,MAAM,GAAG,MAAA,KAAK,CAAC,iBAAiB,0CAAE,MAAM,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3E,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAe,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,MAAG,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,MAAG,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACH,uCAAY,GAAZ,UAAa,KAAK,EAAE,EAAE;QACpB,IAAM,YAAY,GAAG,iBAAM,YAAY,YAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACnD,IAAI,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAa,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,MAAG,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,MAAG,EAAE,KAAK,CAAC,CAAC;SACvD;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,0CAAe,GAAf;QACE,OAAO,iBAAM,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC7C,CAAC,UAAU,EAAE,eAAe,CAAC;YAC7B,CAAC,SAAS,EAAE,cAAc,CAAC;YAC3B,CAAC,SAAS,EAAE,cAAc,CAAC;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,mCAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,IAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAK,IAAI,CAAC,GAAK,CAAC,CAAC,CAAC,EAAE,OAAG,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,sDAA2B,GAA3B;QAAA,iBAgFC;QA/EC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,OAAO;SACR;QAED,IAAM,kBAAkB,GAAG,UAAA,UAAU;YACnC,IAAM,cAAc,GAAG,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/E,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,GAAG,CAAC,KAAK,CAAC,kBAAgB,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,MAAG,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;aAC3E;QACH,CAAC,CAAC;QAEF,IAAM,iBAAiB,GAAG,UAAA,UAAU;YAClC,IAAM,cAAc,GAAG,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/E,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxB,GAAG,CAAC,KAAK,CAAC,iBAAe,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,MAAG,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;aAC1E;QACH,CAAC,CAAC;QAEF,IAAM,iBAAiB,GAAG,UAAA,UAAU;YAClC,0EAA0E;YAC1E,yBAAyB;YACzB,IAAM,cAAc,GAAG,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/E,IAAI,cAAc,EAAE;gBAClB,cAAc,CAAC,IAAI,EAAE,CAAC;aACvB;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC;QAEF,IAAM,YAAY,GAAG,UAAA,KAAK;YACxB,GAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC3C,IAAI,KAAK,KAAK,cAAc,EAAE;gBAC5B,GAAG,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBACjD,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBAC7D,KAAI,CAAC,cAAc,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;gBACzD,KAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;gBACvD,KAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;gBAEvD,wEAAwE;gBACxE,qDAAqD;gBACrD,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;oBACxB,IAAM,cAAc,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAChD,IAAI,cAAc,EAAE;wBAClB,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;qBAClE;gBACH,CAAC,CAAC,CAAC;gBAEH,GAAG,CAAC,IAAI,CAAC,6CAA2C,KAAI,CAAC,aAAa,CAAC,IAAI,wCAAqC,CAAC,CAAC;gBAClH,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;oBAC9B,KAAK,CAAC,IAAI,EAAE,CAAC;gBACf,CAAC,CAAC,CAAC;aACJ;iBAAM,IAAI,KAAK,KAAK,WAAW,EAAE;gBAChC,sEAAsE;gBACtE,wEAAwE;gBACxE,+BAA+B;gBAC/B,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAExB,8EAA8E;gBAC9E,6DAA6D;gBAC7D,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAxB,CAAwB,EAAE,CAAC,CAAC,CAAC;aAC/C;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;QAC7C,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAC3C,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAE3C,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAEjD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;YACxB,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAC;YAC5D,KAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;gBACvD,8BAA8B;gBAC9B,GAAG,CAAC,IAAI,CAAC,uDAAqD,KAAK,MAAG,EAAE,KAAK,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,4DAAiC,GAAjC,UAAkC,UAAU;QAC1C,IAAI,qBAAqB,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,qBAAqB,EAAE;YACzB,OAAO,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;SAC/C;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/E,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,2BAAyB,UAAU,yBAAsB,CAAC,CAAC,CAAC;SAC7F;QAED,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YACjC,SAAS,OAAO;gBACd,IAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;gBACnC,IAAI,KAAK,EAAE;oBACT,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;oBAClD,GAAG,CAAC,IAAI,CAAC,2BAAyB,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAK,KAAK,CAAC,OAAS,CAAC,CAAC;oBACpF,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;oBAC7C,UAAU,CAAC;wBACT,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC;oBACH,MAAM,CAAC,KAAK,CAAC,CAAC;oBACd,OAAO;iBACR;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;oBACpC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;oBAClD,MAAM,CAAC,IAAI,KAAK,CAAC,SAAO,UAAU,qBAAkB,CAAC,CAAC,CAAC;oBACvD,OAAO;iBACR;gBAED,IAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;gBAC/B,IAAI,CAAC,GAAG,EAAE;oBACR,OAAO;iBACR;gBAED,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAElD,IAAM,OAAO,GAAG;oBACd,GAAG,KAAA;oBACH,0BAA0B,EAAE,IAAI,CAAC,2BAA2B;oBAC5D,yBAAyB,EAAE,IAAI,CAAC,0BAA0B;oBAC1D,0BAA0B,EAAE,IAAI,CAAC,2BAA2B;iBAC7D,CAAC;gBAEF,qBAAqB,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAErE,IAAM,cAAc,GAAG,UAAA,iBAAiB;oBACtC,OAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;gBAAnE,CAAmE,CAAC;gBAEtE,IAAM,sBAAsB,GAAG;oBAC7B,OAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;gBAAxD,CAAwD,CAAC;gBAE3D,IAAM,SAAS,GAAG,UAAA,WAAW;oBAC3B,qBAAqB,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;oBACrE,qBAAqB,CAAC,cAAc,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;oBACrF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACzC,CAAC,CAAC;gBAEF,IAAI,CAAC,qBAAqB,EAAE;oBAC1B,qBAAqB,GAAG,uBAAuB,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;oBAChG,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;iBAClD;gBAED,qBAAqB,CAAC,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACpD,qBAAqB,CAAC,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;gBAE5D,IAAA,KAAK,GAAK,IAAI,CAAC,UAAU,MAApB,CAAqB;gBAClC,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,YAAY,EAAE;oBACnD,IAAI,UAAU,CAAC,uBAAuB,EAAE;wBACtC,UAAU,CAAC,uBAAuB,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,KAAK;4BAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE;gCAChC,IAAI,EAAE,KAAK,CAAC,IAAI;gCAChB,OAAO,EAAE,KAAK,CAAC,IAAI;gCACnB,KAAK,EAAE,iBAAiB;gCACxB,KAAK,EAAE,MAAM;6BACd,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;qBACJ;oBAED,oGAAoG;oBACpG,IAAI,UAAU,CAAC,cAAc,EAAE;wBAC7B,UAAU,CAAC,cAAc,EAAE,CAAC;wBAC5B,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;qBAC7C;iBACF;gBACD,IAAI,KAAK,KAAK,WAAW,EAAE;oBACzB,UAAU,CAAC;wBACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC;iBACJ;gBACD,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACjC,CAAC;YAED,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+BE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCC;IACH,uCAAY,GAAZ,UAAa,4BAA4B,EAAE,OAAO;QAChD,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,4BAA4B,CAAC,CAAC;QACxF,IAAI,gBAAgB,EAAE;YACpB,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SAC1C;QAED,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,QAAQ,EAAE,aAAa,CAAC,iBAAiB;YACzC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,cAAc,EAAE,IAAI,CAAC,eAAe;YACpC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,UAAU,CAAC;QACf,IAAI;YACF,UAAU,GAAG,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;SAClE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAM,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;QACvD,IAAM,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QAC/D,IAAI,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;YACnF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAI,iBAAiB,CAAC,MAAM,6DAA0D,CAAC,CAAC;YACtG,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;SACxC;QAED,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC9C,mCAAmC;YACnC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,mCAAmC,EAAE,cAAc,CAAC,CAAC,CAAC;SAC7F;QAED,IAAI,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC;eAC5E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,wCAAa,GAAb,UAAc,MAAM;QAClB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,mCAAmC;YACnC,MAAM,CAAC,CAAC,YAAY,CAAC,QAAQ,EAC3B,gFAAgF,CAAC,CAAC;SACrF;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,8CAAmB,GAAnB;QACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uGAAuG,CAAC,CAAC;IAC1H,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,yDAA8B,GAA9B,UAA+B,2BAA2B;QACxD,IAAI,OAAO,2BAA2B,KAAK,QAAQ;eAC9C,2BAA2B,KAAK,IAAI,EAAE;YACzC,mCAAmC;YACnC,MAAM,CAAC,CAAC,YAAY,CAAC,6BAA6B,EAAE,6BAA6B,CAAC,CAAC;SACpF;QACD,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC9B,IAAI,IAAI,IAAI,2BAA2B,IAAI,CAAC,OAAO,2BAA2B,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBAC9I,mCAAmC;gBACnC,MAAM,CAAC,CAAC,YAAY,CAAC,iCAA+B,IAAM,EAAE,QAAQ,CAAC,CAAC;aACvE;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,8BAA8B,CAAC,2BAA2B,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;OAOG;IACH,wCAAa,GAAb,UAAc,kBAAkB;QAC9B,IAAI,OAAO,kBAAkB,KAAK,WAAW;eACxC,OAAO,kBAAkB,KAAK,QAAQ,EAAE;YAC3C,mCAAmC;YACnC,MAAM,CAAC,CAAC,YAAY,CAAC,oBAAoB,EACvC,uCAAuC,CAAC,CAAC;SAC5C;QAED,IAAI,kBAAkB,EAAE;YACtB,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,iBAAiB,IAAI,kBAAkB,CAAC,eAAe,EAAE;gBAC3F,mCAAmC;gBACnC,MAAM,CAAC,CAAC,YAAY,CAAC,oBAAoB,EAAE,uFAAuF,CAAC,CAAC;aACrI;YAED,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBACjD,IAAI,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,WAAW;uBAC9C,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,QAAQ;uBAC5C,kBAAkB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtC,mCAAmC;oBACnC,MAAM,CAAC,CAAC,YAAY,CAAC,wBAAsB,IAAM,EAAE,2BAA2B,CAAC,CAAC;iBACjF;YACH,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,kBAAkB,KAAK,IAAI,EAAE;YACtC,kBAAkB,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;SACvE;QAED,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;MAUE;IACF,yCAAc,GAAd,UAAe,KAAK;QAClB,kBAAkB,CAAC,KAAK,EAAE;YACxB,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,cAAc,EAAE,IAAI,CAAC,eAAe;YACpC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;QAEH,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAED,IAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/E,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,SAAO,UAAU,qBAAkB,CAAC,CAAC,CAAC;QAE7E,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAED,IAAM,qBAAqB,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC3E,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;SACrD;QACD,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAED;;;;;;;;;OASG;IACH,0CAAe,GAAf,UAAgB,MAAM;QAAtB,iBAWC;QAVC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,mCAAmC;YACnC,MAAM,CAAC,CAAC,YAAY,CAAC,QAAQ,EAC3B,gFAAgF,CAAC,CAAC;SACrF;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,iBAAiB,EAAE,KAAK;YAC5C,IAAM,gBAAgB,GAAG,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACpD,OAAO,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAC3F,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IACH,uBAAC;AAAD,CAAC,AAllBD,CAA+B,WAAW,GAklBzC;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;;;;;;;;GAYG;AAEH;;;;;;;GAOG;AAEH;;;;GAIG;AAEH;;;;;;GAMG;AAEH;;;;;;GAMG;AAEH;;;;;GAKG;AAEH;;;;;;;;;;;;;;;;;;GAkBG;AAEH;;;;;;;GAOG;AAEH;;;;;;;GAOG;AAEH;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,iBAAiB,EAAE,KAAK;IACnD,OAAO,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,gBAAgB,IAAI,OAAA,gBAAgB,CAAC,KAAK,KAAK,KAAK;WAClG,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,KAAK,KAAK,EADiB,CACjB,CAAC,IAAI,IAAI,CAAC;AAClE,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}