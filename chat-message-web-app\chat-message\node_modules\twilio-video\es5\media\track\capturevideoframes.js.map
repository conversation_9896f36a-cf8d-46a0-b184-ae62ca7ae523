{"version": 3, "file": "capturevideoframes.js", "sourceRoot": "", "sources": ["../../../lib/media/track/capturevideoframes.js"], "names": [], "mappings": "AAAA,mFAAmF;AACnF,YAAY,CAAC;;;;;;;;;;;;;;;;;AAEL,IAAA,kBAAkB,GAAK,OAAO,CAAC,sBAAsB,CAAC,mBAApC,CAAqC;AAE/D,SAAS,6BAA6B,CAAC,OAAO,EAAE,iBAAiB;IACzD,IAAA,KAAA,OAAU,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,IAAA,EAA3C,KAAK,QAAsC,CAAC;IAC3C,IAAA,KAAmC,KAAK,CAAC,WAAW,EAAE,UAAxB,EAA9B,SAAS,mBAAG,kBAAkB,KAAA,CAAyB;IAC/D,IAAI,cAAc,CAAC;IAEnB,IAAM,QAAQ,GAAG,IAAI,cAAc,CAAC;QAClC,KAAK,YAAC,UAAU;YACd,cAAc,GAAG,WAAW,CAC1B,cAAM,OAAA,UAAU,CAAC,OAAO,EAAE,EAApB,CAAoB,EAC1B,IAAI,GAAG,SAAS,CACjB,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;IAEH,IAAM,WAAW,GAAG,IAAI,eAAe,CAAC;QACtC,SAAS;YACP,OAAO,iBAAiB,EAAE,CAAC;QAC7B,CAAC;KACF,CAAC,CAAC;IAEH,QAAQ;SACL,WAAW,CAAC,WAAW,CAAC;SACxB,MAAM,CAAC,IAAI,cAAc,EAAE,CAAC;SAC5B,IAAI,CAAC,cAAmB,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,aAAa,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,mCAAmC,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc;IAC/E,IAAA,KAAA,OAAU,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,IAAA,EAA3C,KAAK,QAAsC,CAAC;IAC3C,IAAA,QAAQ,GAAK,IAAI,yBAAyB,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,SAA7C,CAA8C;IAC9D,IAAM,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;IACnE,IAAI,UAAU,GAAG,KAAK,CAAC;IAEvB,IAAM,WAAW,GAAG,IAAI,eAAe,CAAC;QACtC,SAAS,YAAC,UAAU,EAAE,UAAU;YAC9B,IAAM,OAAO,GAAG,cAAc,KAAK,YAAY;gBAC7C,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAC/B,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;qBAClC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7B,OAAO,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,UAAU,EAAE;oBACd,UAAU,CAAC,SAAS,EAAE,CAAC;iBACxB;YACH,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;IAEH,QAAQ;SACL,WAAW,CAAC,WAAW,CAAC;SACxB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;SAC1B,IAAI,CAAC,cAAmB,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,OAAO,yBAAyB,KAAK,UAAU,IAAI,OAAO,yBAAyB,KAAK,UAAU;IACjH,CAAC,CAAC,mCAAmC;IACrC,CAAC,CAAC,6BAA6B,CAAC"}