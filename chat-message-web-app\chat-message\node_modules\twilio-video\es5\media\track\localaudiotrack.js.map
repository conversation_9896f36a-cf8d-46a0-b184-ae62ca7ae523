{"version": 3, "file": "localaudiotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localaudiotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,MAA3C,CAA4C;AACzD,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAC1D,IAAA,WAAW,GAAK,OAAO,CAAC,mBAAmB,CAAC,YAAjC,CAAkC;AACrD,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE1D,IAAM,oBAAoB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH;IAA8B,mCAAoB;IAChD;;;;OAIG;IACH,yBAAY,gBAAgB,EAAE,OAAO;QAArC,iBAkEC;QAjEC,IAAM,iBAAiB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,KAAI,IAAI,CAAC;QAC7D,QAAA,kBAAM,gBAAgB,EAAE,OAAO,CAAC,SAAC;QAEzB,IAAM,GAAG,GAAK,KAAI,KAAT,CAAU;QACnB,IAAA,KAAmC,gBAAgB,MAArB,EAAvB,kBAAkB,mBAAG,EAAE,KAAA,CAAsB;QACtD,IAAA,KAAmE,gBAAgB,CAAC,WAAW,EAAE,EAA/F,gBAA8B,EAApB,eAAe,mBAAG,EAAE,KAAA,EAAE,eAA4B,EAAnB,cAAc,mBAAG,EAAE,KAAmC,CAAC;QAExG,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,yBAAyB,EAAE;gBACzB,KAAK,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBACxF,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,CAAA,KAAK,UAAU;oBACpD,CAAC,CAAC,OAAO,CAAC,gBAAgB;oBAC1B,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB;aAC5C;YACD,yBAAyB,EAAE;gBACzB,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,iBAAiB,CAAC;uBACnC,KAAI,CAAC,6BAA6B;uBAClC,OAAO,SAAS,KAAK,QAAQ;uBAC7B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ;uBAC1C,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,KAAK,UAAU;uBAC7D,CAAC,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,CAAA,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,KAAK,UAAU,CAAC;oBACrH,CAAC,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,wBAAwB,KAAI,MAAM;oBAC7C,CAAC,CAAC,QAAQ;aACb;YACD,eAAe,EAAE;gBACf,KAAK,EAAE;oBACL,KAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,UAAA,WAAW;wBACvC,wGAAwG;wBACxG,0GAA0G;wBAC1G,2GAA2G;wBAC3G,kCAAkC;wBAClC,IAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,UAAC,EAAkB;gCAAhB,QAAQ,cAAA,EAAE,IAAI,UAAA;4BAC1D,OAAO,IAAI,KAAK,YAAY,IAAI,QAAQ,KAAK,SAAS,CAAC;wBACzD,CAAC,CAAC,CAAC;wBAEH,IAAI,iBAAiB,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;4BACxD,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,KAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;wBAC1E,CAAC,CAAC,EAAE;4BACF,GAAG,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;4BACnE,GAAG,CAAC,KAAK,CAAC,2BAAwB,KAAI,CAAC,yBAAyB,CAAC,QAAQ,gBAAS,KAAI,CAAC,yBAAyB,CAAC,KAAK,OAAG,CAAC,CAAC;4BAC3H,GAAG,CAAC,KAAK,CAAC,2BAAwB,iBAAiB,CAAC,QAAQ,gBAAS,iBAAiB,CAAC,KAAK,OAAG,CAAC,CAAC;4BACjG,KAAI,CAAC,yBAAyB,GAAG,iBAAiB,CAAC;4BACnD,KAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,GAAG,CAAC,IAAI,CAAC,wBAAsB,KAAK,CAAC,OAAS,CAAC,EAA/C,CAA+C,CAAC,CAAC;yBAC9F;oBACH,CAAC,EAAE,UAAA,KAAK;wBACN,GAAG,CAAC,IAAI,CAAC,uCAAqC,KAAK,CAAC,OAAS,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB;gBACxB,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAI,CAAC,yBAAyB,CAAC,CAAC;QACvE,KAAI,CAAC,kCAAkC,EAAE,CAAC;;IAC5C,CAAC;IAED,kCAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,EAAE,MAAG,CAAC;IAC9D,CAAC;IAED,gCAAM,GAAN,UAAO,EAAE;QACP,EAAE,GAAG,iBAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC;QAChB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,8BAAI,GAAJ;QACE,OAAO,iBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,4DAAkC,GAAlC;QAAA,iBAsCC;QArCO,IAAA,KAAgG,IAAI,EAApF,WAAW,kBAAA,EAA6B,wBAAwB,+BAAA,EAAQ,GAAG,UAAS,CAAC;QAC3G,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACrG,IAAA,QAAQ,GAAK,gBAAgB,CAAC,WAAW,EAAE,SAAnC,CAAoC;QAEpD,IAAM,4CAA4C,GAAG,UAAA,iBAAiB;YACpE,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,SAAS,CAAC;QAC3E,CAAC,CAAC;QAEF,IAAM,4BAA4B,GAAG,CAAC,SAAS,iCAAiC,CAAC,kBAAuB;YAAvB,mCAAA,EAAA,uBAAuB;YACtG,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;gBAC1C,OAAO,4CAA4C,CAAC,kBAAkB,CAAC,CAAC;aACzE;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC5C,OAAO,kBAAkB,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;aAC/E;iBAAM,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBACnC,OAAO,iCAAiC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACpE;iBAAM,IAAI,kBAAkB,CAAC,KAAK,EAAE;gBACnC,OAAO,iCAAiC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACpE;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEzB,IAAI,wBAAwB,KAAK,MAAM,IAAI,4BAA4B,EAAE;YACvE,IAAI,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBAC9C,GAAG,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;gBAC5E,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9E,IAAI,CAAC,oCAAoC,GAAG;oBAC1C,GAAG,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;oBAClG,SAAS,CAAC,YAAY,CAAC,mBAAmB,CAAC,cAAc,EAAE,KAAI,CAAC,eAAe,CAAC,CAAC;oBACjF,KAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;gBACnD,CAAC,CAAC;aACH;SACF;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,oCAAoC,EAAE;gBAC7C,IAAI,CAAC,oCAAoC,EAAE,CAAC;aAC7C;SACF;IACH,CAAC;IAED;;OAEG;IACH,yCAAe,GAAf,UAAgB,WAAW;QAA3B,iBASC;QARC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC3C,OAAO,iBAAM,eAAe,CAAC,IAAI,CAAC,KAAI,EAAE,WAAW,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,iBAAM,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,+CAAqB,GAArB;QAAA,iBAUC;QATC,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,IAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjH,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC;YAC3C,oGAAoG;YACpG,qGAAqG;YACrG,oDAAoD;YACpD,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,KAAI,CAAC,kCAAkC,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,8CAAoB,GAApB,UAAqB,gBAAgB;QAArC,iBAeC;QAdO,IAAA,KAAmC,IAAI,EAA/B,GAAG,UAAA,EAAE,iBAAiB,uBAAS,CAAC;QAC9C,IAAI,OAAO,GAAG,iBAAM,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAEtE,IAAI,WAAW,EAAE,IAAI,CAAC,CAAC,iBAAiB,EAAE;YACxC,GAAG,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;YAClF,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,cAAM,OAAA,iBAAiB,CAAC,KAAI,CAAC,QAAQ,CAAC,EAAhC,CAAgC,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ;gBAC1E,GAAG,CAAC,KAAK,CAAC,qBAAkB,QAAQ,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,YAAY,CAAE,CAAC,CAAC;gBACtF,OAAO,QAAQ,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC;oBAC7D,OAAO,iBAAM,oBAAoB,CAAC,IAAI,CAAC,KAAI,EAAE,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC9E,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,iCAAO,GAAP;QACE,OAAO,iBAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;MAIE,CAAA;;;;;;;;OAQC;IACH,gCAAM,GAAN;QACE,OAAO,iBAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,iCAAO,GAAP;QACE,OAAO,iBAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACH,8BAAI,GAAJ;QACE,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,oCAAoC,EAAE;YAC7C,IAAI,CAAC,oCAAoC,EAAE,CAAC;SAC7C;QACD,OAAO,iBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IACH,sBAAC;AAAD,CAAC,AA1QD,CAA8B,oBAAoB,GA0QjD;AAED;;;;;GAKG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;;GAMG;AAEH;;;;;;;;GAQG;AAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC"}