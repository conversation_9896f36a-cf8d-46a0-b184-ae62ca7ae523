{"version": 3, "file": "localmediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localmediatrack.js"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,cAAc,CAAC,aAA5B,CAA6B;AACzC,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,MAA3C,CAA4C;AAEnD,IAAA,KAAuD,OAAO,CAAC,YAAY,CAAC,EAA1E,UAAU,gBAAA,EAAE,KAAK,WAAA,EAAE,eAAe,qBAAA,EAAE,YAAY,kBAA0B,CAAC;AAC7D,IAAA,cAAc,GAAO,OAAO,CAAC,sBAAsB,CAAC,0BAAtC,CAAuC;AAC3E,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AACrF,IAAM,0BAA0B,GAAG,OAAO,CAAC,uCAAuC,CAAC,CAAC;AACpF,IAAM,wBAAwB,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC5E,IAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAE7C,SAAS,oBAAoB,CAAC,iBAAiB;IAC7C;;;;;;;;;OASG;IACH;QAAqC,mCAAiB;QACpD;;;;WAIG;QACH,yBAAY,gBAAgB,EAAE,OAAO;YAArC,iBAwFC;YAvFC,IAAM,0BAA0B,GAAG,KAAK,EAAE;mBACrC,OAAO,QAAQ,KAAK,QAAQ;mBAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU;mBAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC;YAElD,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBACtB,YAAY,cAAA;gBACZ,4BAA4B,EAAE,KAAK;gBACnC,0BAA0B,4BAAA;gBAC1B,wBAAwB,0BAAA;aACzB,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACxD,IAAA,IAAI,GAAK,gBAAgB,KAArB,CAAsB;YAElC,QAAA,kBAAM,gBAAgB,EAAE,OAAO,CAAC,SAAC;YAEjC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;gBAC5B,YAAY,EAAE;oBACZ,KAAK,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ;wBACtC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;wBACf,CAAC,CAAC,EAAE;oBACN,QAAQ,EAAE,IAAI;iBACf;gBACD,aAAa,EAAE;oBACb,KAAK,EAAE,OAAO,CAAC,YAAY;iBAC5B;gBACD,yBAAyB,EAAE;oBACzB,KAAK,EAAE,OAAO,CAAC,wBAAwB;iBACxC;gBACD,mBAAmB,EAAE;oBACnB,KAAK,EAAE,IAAI,GAAG,CAAC;wBACb,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAI,CAAC,EAAxB,CAAwB,CAAC;wBACzC,CAAC,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAI,CAAC,EAA1B,CAA0B,CAAC;qBAC9C,CAAC;iBACH;gBACD,2BAA2B,EAAE;oBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;iBAC1C;gBACD,kCAAkC,EAAE;oBAClC,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,IAAI;iBACf;gBACD,6BAA6B,EAAE;oBAC7B,KAAK,EAAE,OAAO,CAAC,4BAA4B;iBAC5C;gBACD,kBAAkB,EAAE;oBAClB,KAAK,EAAE,OAAO,CAAC,iBAAiB,IAAI,IAAI;iBACzC;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,gBAAgB;iBACxB;gBACD,EAAE,EAAE;oBACF,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,gBAAgB,CAAC,EAAE;iBAC3B;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,gBAAgB,CAAC,OAAO,CAAC;oBAClC,CAAC;iBACF;gBACD,OAAO,EAAE;oBACP,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,gBAAgB,CAAC,KAAK,CAAC;oBAChC,CAAC;iBACF;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,gBAAgB,CAAC,UAAU,KAAK,OAAO,CAAC;oBACjD,CAAC;iBACF;aACF,CAAC,CAAC;YAEH,qGAAqG;YACrG,6FAA6F;YAC7F,IAAI,KAAI,CAAC,2BAA2B,EAAE;gBACpC,KAAI,CAAC,kCAAkC,GAAG,+BAA+B,CAAC,KAAI,CAAC,CAAC;aACjF;YAED,KAAI,CAAC,wBAAwB,EAAE,CAAC;;QAClC,CAAC;QAED;;WAEG;QACH,8BAAI,GAAJ;YAAA,iBAQC;YAPC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,OAAO;aACR;YACD,iBAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK,IAAK,OAAA,KAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,EAAlD,CAAkD,CAAC,CAAC;YAC3G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED;;WAEG;QACH,qCAAW,GAAX;YACE,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;aAC1B;YACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACjC;YACD,iBAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;QAED;;WAEG;QACH,yCAAe,GAAf,UAAgB,WAAW;;YACnB,IAAA,KAKF,IAAI,EAJS,YAAY,mBAAA,EACA,wBAAwB,+BAAA,EAC7C,GAAG,UAAA,EACW,IAAI,2BAClB,CAAC;YAET,GAAG,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAC9C,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAEvC,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;gBACnC,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,KAAK;aACb,YAAI,GAAC,IAAI,IAAG,WAAW,MAAG,CAAC;YAE5B,IAAM,UAAU,GAAG,IAAI,CAAC,kCAAkC;gBACxD,CAAC,CAAC,wBAAwB,CAAC,GAAG,EAAE,YAAY,EAAE,cAAc,CAAC;gBAC7D,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAEjC,OAAO,UAAU,CAAC,IAAI,CAAC,UAAA,WAAW;gBAChC,OAAO,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG;QACH,kDAAwB,GAAxB;YAAA,iBAIC;YAHC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK,IAAK,OAAA,KAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAtC,CAAsC,CAAC,CAAC;YAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QAED;;WAEG;QACH,kCAAQ,GAAR,UAAS,WAAW;YAApB,iBAmBC;YAlBS,IAAM,GAAG,GAAK,IAAI,KAAT,CAAU;YAC3B,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC;YAE/C,oEAAoE;YACpE,0EAA0E;YAC1E,yEAAyE;YACzE,yBAAyB;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;gBAClD,GAAG,CAAC,KAAK,CAAC,4CAA4C,EAAE,EAAE,KAAK,OAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;gBAChF,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,mBAAmB;gBACzB,GAAG,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBAC7C,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;gBACpD,KAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;gBACnD,OAAO,KAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG;QACH,8CAAoB,GAApB,UAAqB,gBAAgB;YAArC,iBAkBC;YAjBC,+DAA+D;YAC/D,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAEzD,sEAAsE;YACtE,+CAA+C;YAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,6EAA6E;YAC7E,iEAAiE;YACjE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;gBACtD,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;gBACtE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,KAAK,OAAA,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACP,KAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,KAAI,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAhB,CAAgB,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG;QACH,+BAAK,GAAL;YACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gCAAM,GAAN,UAAO,OAAO;YACZ,OAAO,GAAG,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YACxD,IAAI,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,YAAQ,CAAC,CAAC;gBAClD,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aACnD;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iCAAO,GAAP;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,iCAAO,GAAP,UAAQ,WAAW;YAAnB,iBAmBC;YAlBS,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;YACtB,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,yBAAyB;uBACrE,WAAS,UAAU,CAAC,IAAI,CAAC,kDAA+C,CAAA;uBACxE,oBAAkB,UAAU,CAAC,IAAI,CAAC,WAAQ,CAAA,CAAC,CAAC,CAAC;aAClD;YACD,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;gBAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;aAChD;YACD,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACpC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;oBACxB,KAAI,CAAC,kCAAkC,GAAG,+BAA+B,CAAC,KAAI,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC;aACJ;YACD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,8BAAI,GAAJ;YACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;gBAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;aAChD;YACD,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QACH,sBAAC;IAAD,CAAC,AAlQM,CAA8B,iBAAiB,GAkQpD;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,+BAA+B,CAAC,eAAe;IAEpD,IAAM,GAAG,GAGP,eAAe,KAHR,EACT,IAAI,GAEF,eAAe,KAFb,EACgB,iBAAiB,GACnC,eAAe,mBADoB,CACnB;IAEpB,IAAM,aAAa,GAAG;QACpB,KAAK,EAAE,iBAAiB;QACxB,KAAK,EAAE,iBAAiB;KACzB,CAAC,IAAI,CAAC,CAAC;IAER,IAAM,yBAAyB,GAAG,cAAM,OAAA,iBAAiB;QACvD,CAAC,CAAC,iBAAiB,CAAC,WAAW;QAC/B,CAAC,CAAC,eAAe,CAAC,gBAAgB,EAFI,CAEJ,CAAC;IAE/B,IAAU,EAAE,GAAK,eAAe,SAApB,CAAqB;IACvC,IAAI,gBAAgB,GAAG,yBAAyB,EAAE,CAAC;IACnD,IAAI,qBAAqB,GAAG,IAAI,CAAC;IAEjC,SAAS,YAAY;QACnB,mEAAmE;QACnE,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,cAAM,OAAA,aAAa,CAAC,EAAE,CAAC,EAAjB,CAAiB,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ;YAC1D,IAAI,QAAQ,EAAE;gBACZ,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9B;iBAAM;gBACL,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aAClC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,GAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,iEAAiE;YACjE,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACnC,EAAE,CAAC,KAAK,EAAE,CAAC;aACZ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,oBAAoB;QAEzB,IAAA,kCAAkC,GAEhC,eAAe,mCAFiB,EAClC,SAAS,GACP,eAAe,UADR,CACS;QAEpB,IAAM,sBAAsB,GAAG,SAAS,IAAI,CAAC,CAAC,kCAAkC,CAAC;QACzE,IAAA,KAAK,GAAK,yBAAyB,EAAE,MAAhC,CAAiC;QAE9C,oDAAoD;QACpD,kCAAkC;QAClC,+CAA+C;QAC/C,0EAA0E;QAC1E,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,OAAO,QAAQ,CAAC,eAAe,KAAK,SAAS;mBACxC,CAAC,qBAAqB;mBACtB,CAAC,KAAK,IAAI,sBAAsB,IAAI,YAAY,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,YAAY;QACnB,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,YAAY,CAAC,gBAAgB,EAAE,QAAQ,CAAC;YACxC,eAAe,CAAC,EAAE,CAAC;SACpB,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,oBAAoB,EAAE,EAAtB,CAAsB,CAAC,CAAC,IAAI,CAAC,UAAA,eAAe;YACxD,IAAI,eAAe,IAAI,CAAC,qBAAqB,EAAE;gBAC7C,qBAAqB,GAAG,KAAK,EAAE,CAAC;gBAChC,eAAe,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACjC,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC9B,+BAA+B,EAAE,CAAC;oBAClC,gBAAgB,GAAG,yBAAyB,EAAE,CAAC;oBAC/C,4BAA4B,EAAE,CAAC;oBAC/B,qBAAqB,CAAC,OAAO,EAAE,CAAC;oBAChC,qBAAqB,GAAG,IAAI,CAAC;gBAC/B,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;oBACZ,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;aACJ;YAED,4EAA4E;YAC5E,2EAA2E;YAC3E,6CAA6C;YAC7C,IAAM,OAAO,GAAG,CAAC,qBAAqB,IAAI,qBAAqB,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC9F,OAAO,OAAO,CAAC,OAAO,CAAC,cAAM,OAAA,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,EAAhD,CAAgD,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,EAAE;YACT,GAAG,CAAC,KAAK,CAAC,4BAA0B,EAAE,CAAC,OAAS,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,MAAM;QACL,IAAM,GAAG,GAAW,eAAe,KAA1B,EAAE,IAAI,GAAK,eAAe,KAApB,CAAqB;QAC5C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClB,GAAG,CAAC,KAAK,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QAE/C,0EAA0E;QAC1E,2EAA2E;QAC3E,2EAA2E;QAC3E,8EAA8E;QAC9E,wDAAwD;QACxD,EAAE;QACF,sDAAsD;QACtD,EAAE;QACF,0BAA0B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,SAAS,4BAA4B;QACnC,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACzD,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAClD,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC3D;aAAM;YACL,gBAAgB,CAAC,OAAO,GAAG,YAAY,CAAC;YACxC,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;YACjC,gBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC;SAC1C;IACH,CAAC;IAED,SAAS,+BAA+B;QACtC,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;YACxC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC5D,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACrD,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC9D;aAAM;YACL,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;SAClC;IACH,CAAC;IAED,yEAAyE;IACzE,oEAAoE;IACpE,qEAAqE;IACrE,0DAA0D;IAC1D,IAAI,kBAAkB,GAAG,UAAA,SAAS;QAChC,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC,CAAC;IACF,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;IACpE,4BAA4B,EAAE,CAAC;IAE/B,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QACrE,+BAA+B,EAAE,CAAC;IACpC,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC"}