{"version": 3, "file": "localtrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localtrackpublication.js"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACjD,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,EAApD,CAAC,gBAAA,EAAE,aAAa,mBAAoC,CAAC;AAEzE;;;;;;;;;;;GAWG;AACH;IAAoC,yCAAgB;IAClD;;;;;;;;;OASG;IACH,+BAAY,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;QAAhD,YACE,kBAAM,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,SA+C1C;QA7CC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE;oBAAC,cAAO;yBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;wBAAP,yBAAO;;oBAAK,OAAA,KAAI,CAAC,IAAI,OAAT,KAAI,iBACtB,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,UAChD,IAAI;gBAFW,CAGnB;aACF;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,KAAI,CAAC,cAAc;oBACxC,CAAC,CAAC,cAAc;oBAChB,CAAC,CAAC,eAAe,CAAC,EAFP,CAEO;aACrB;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;gBAClE,CAAC;aACF;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,IAAI;aAClB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,eAAe,CAAC;gBACnC,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK;aACb;SACF,CAAC,CAAC;QAEH,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAClC,OAAA,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC;QAAtC,CAAsC,CAAC,CAAC;QAE1C,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YACzC,OAAA,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,KAAI,CAAC,qBAAqB,CAAC;QAA9C,CAA8C,CAAC,CAAC;;IACpD,CAAC;IAED,wCAAQ,GAAR;QACE,OAAO,6BAA2B,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,QAAQ,MAAG,CAAC;IAC1E,CAAC;IAED;;;;;OAKG;IACH,2CAAW,GAAX,UAAY,QAAQ;QAClB,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,yCAAS,GAAT;QAAA,iBASC;QARC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAClC,OAAA,KAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC;QAAvD,CAAuD,CAAC,CAAC;QAE3D,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YACzC,OAAA,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,qBAAqB,CAAC;QAAhE,CAAgE,CAAC,CAAC;QAEpE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IACH,4BAAC;AAAD,CAAC,AAhGD,CAAoC,gBAAgB,GAgGnD;AAED;;;;;GAKG;AAEH;;;;GAIG;AAEH,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC"}