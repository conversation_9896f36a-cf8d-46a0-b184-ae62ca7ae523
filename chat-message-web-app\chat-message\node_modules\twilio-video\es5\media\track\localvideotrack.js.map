{"version": 3, "file": "localvideotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/localvideotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,MAA3C,CAA4C;AACzD,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC1D,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C,IAAM,oBAAoB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAE9D;;;;;;;;;;;;;;;;;;;;GAoBG;AACH;IAA8B,mCAAoB;IAChD;;;;OAIG;IACH,yBAAY,gBAAgB,EAAE,OAAO;QAArC,iBA2BC;QA1BC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,0BAA0B,EAAE,KAAK,EAAE;mBAC9B,OAAO,QAAQ,KAAK,WAAW;mBAC/B,OAAO,QAAQ,CAAC,aAAa,KAAK,UAAU;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,kBAAM,gBAAgB,EAAE,OAAO,CAAC,SAAC;QAEjC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;oBACvC,CAAC,CAAC,0BAA0B;oBAC5B,CAAC,CAAC,IAAI;aACT;YACD,kCAAkC,EAAE;gBAClC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,0EAA0E;QAC1E,gFAAgF;QAChF,0CAA0C;QAC1C,IAAI,KAAI,CAAC,2BAA2B,EAAE;YACpC,KAAI,CAAC,kCAAkC,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;SAC5F;;IACH,CAAC;IAED,kCAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,EAAE,MAAG,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,kDAAwB,GAAxB;QACE,OAAO,iBAAM,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,8BAAI,GAAJ;QACE,OAAO,iBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,oDAA0B,GAA1B,UAA2B,YAAY;QAAvC,iBAUC;QATC,IAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,IAAM,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAE/E,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;aAC3D,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,IAAI,CAAC,IAAI,CAC5B,4DAA4D,EAAE,EAAE,KAAK,OAAA,EAAE,gBAAgB,kBAAA,EAAE,CAAC,EAD5E,CAC4E,CAAC;aAC5F,IAAI,CAAC;YACJ,KAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;QAClE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,sCAAY,GAAZ;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAChE,IAAM,MAAM,GAAG,iBAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kFAAkF,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzH,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAEtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,yCAAe,GAAf;QAAA,iBASC;QARC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACpE,IAAM,MAAM,GAAG,iBAAM,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;QACnG,IAAI,CAAC,0BAA0B,EAAE;aAC9B,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,+BAA+B,EAAE,EAAtC,CAAsC,CAAC,CAAC;QAEtD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,iCAAO,GAAP;QACE,IAAM,MAAM,GAAG,iBAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;SACrC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;MAKE,CAAA;;;;;;;;;OASC;IACH,gCAAM,GAAN,UAAO,OAAc;QAAd,wBAAA,EAAA,cAAc;QACnB,IAAM,MAAM,GAAG,iBAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;YAEtC,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kFAAkF,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACzH,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;aACvC;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,iCAAO,GAAP;QAAA,iBAmBC;QAlBC,IAAI,IAAI,CAAC,kCAAkC,EAAE;YAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;SAChD;QAED,IAAM,OAAO,GAAG,iBAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC;gBACX,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;YACpC,OAAO,CAAC,OAAO,CAAC;gBACd,KAAI,CAAC,kCAAkC,GAAG,KAAI,CAAC,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,8BAAI,GAAJ;QACE,IAAI,IAAI,CAAC,kCAAkC,EAAE;YAC3C,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC1C,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;SAChD;QACD,OAAO,iBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IACH,sBAAC;AAAD,CAAC,AAtPD,CAA8B,oBAAoB,GAsPjD;AAED;;;;;;;GAOG;AACH,SAAS,0BAA0B,CAAC,eAAe,EAAE,GAAG;IAC9C,IAAM,GAAG,GAAK,eAAe,KAApB,CAAqB;IAChC,IAAU,EAAE,GAAuB,eAAe,SAAtC,EAAE,gBAAgB,GAAK,eAAe,iBAApB,CAAqB;IAEzD,SAAS,QAAQ;QACf,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;YAC9B,OAAO;SACR;QACD,GAAG,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEtC,mEAAmE;QACnE,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,cAAM,OAAA,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ;YAC5D,IAAI,CAAC,QAAQ,EAAE;gBACb,GAAG,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBAC9D,OAAO;aACR;YACD,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAEzC,oEAAoE;YACpE,uEAAuE;YACvE,qCAAqC;YACrC,eAAe,CAAC,KAAK,EAAE,CAAC;YAExB,+BAA+B;YAC/B,6CAA6C;YAC7C,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,GAAG,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,0EAA0E;YAC1E,kCAAkC;YAClC,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC;YAC9B,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACvD,EAAE,CAAC,KAAK,EAAE,CAAC;aACZ;YAED,4BAA4B;YAC5B,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACzD,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;YACpD,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;gBACrC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aACvD;iBAAM;gBACL,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,0BAA0B;IAC1B,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;QACrC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KACvD;SAAM;QACL,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC;KACtC;IAED,OAAO;QACL,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;YACxC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC1D;aAAM;YACL,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;SAClC;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;;GAMG;AAEH;;;;;;;;GAQG;AAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC"}