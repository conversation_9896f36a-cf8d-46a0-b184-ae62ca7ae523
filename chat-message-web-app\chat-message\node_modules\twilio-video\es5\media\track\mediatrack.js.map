{"version": 3, "file": "mediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/mediatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,MAA3C,CAA4C;AACjD,IAAA,WAAW,GAAK,OAAO,CAAC,cAAc,CAAC,YAA5B,CAA6B;AAE1C,IAAA,KAAoC,OAAO,CAAC,YAAY,CAAC,EAAvD,YAAY,kBAAA,EAAE,eAAe,qBAA0B,CAAC;AAChE,IAAM,0BAA0B,GAAG,OAAO,CAAC,uCAAuC,CAAC,CAAC;AACpF,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5B;;;;;;;;;;;;;;;;GAgBG;AACH;IAAyB,8BAAK;IAC5B;;;;OAIG;IACH,oBAAY,qBAAqB,EAAE,OAAO;QAA1C,iBA+EC;QA9EC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,mCAAmC,EAAE,KAAK,EAAE;mBACvC,OAAO,QAAQ,KAAK,QAAQ;mBAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU;mBAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,kBAAM,qBAAqB,CAAC,EAAE,EAAE,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,SAAC;QACrE,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,WAAW,aAAA;SACZ,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,0DAA0D,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;SACnG;QAED,0BAA0B;QAC1B,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,UAAU,EAAE;gBACV,GAAG;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;gBACD,GAAG,YAAC,UAAU;oBACZ,SAAS,GAAG,UAAU,CAAC;gBACzB,CAAC;aACF;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,OAAO,CAAC,mCAAmC;aACnD;YACD,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,yBAAyB;uBACnC,OAAO,CAAC,mCAAmC;aACjD;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,OAAO,CAAC,WAAW;aAC3B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,OAAO,CAAC,mBAAmB;aACnC;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;aACF;YACD,gBAAgB,EAAE;gBAChB,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,iBAAiB,IAAI,qBAAqB,CAAC,KAAK,CAAC;gBAC/D,CAAC;aACF;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,WAAW,EAAE,CAAC;;IACrB,CAAC;IAED;;OAEG;IACH,2BAAM,GAAN;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAChC;QACD,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,gCAAW,GAAX;QACE,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;YACnE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,OAAO;gBAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAChC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,SAAS,OAAO;gBAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YACvC,CAAC,CAAC;SACH;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEhE,oFAAoF;YACpF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;OAEG;IACH,yBAAI,GAAJ;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C;iBAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;aAChC;YACD,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED,2BAAM,GAAN,UAAO,EAAE;QAAT,iBAgBC;QAfC,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC1B,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;SAC9B;aAAM,IAAI,CAAC,EAAE,EAAE;YACd,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QACxD,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9D,IAAM,uBAAuB,GAAG,IAAI,CAAC,oCAAoC;gBACvE,CAAC,CAAC,cAAM,OAAA,8BAA8B,CAAC,EAAE,EAAE,KAAI,CAAC,IAAI,CAAC,EAA7C,CAA6C;gBACrD,CAAC,CAAC,IAAI,CAAC;YACT,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,gBAAgB,CAAC,EAAE,EAAE,uBAAuB,CAAC,CAAC,CAAC;SACtE;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;;;OAOG;IACH,4BAAO,GAAP,UAAQ,EAAE,EAAE,gBAA+D;QAA/D,iCAAA,EAAA,mBAAmB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB;QACzE,IAAI,WAAW,GAAG,EAAE,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,CAAC,WAAW,YAAY,IAAI,CAAC,YAAY,CAAC,EAAE;YAC/C,WAAW,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;SACvC;QAED,IAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,KAAK,OAAO;YACjD,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,gBAAgB,CAAC;QAErB,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,UAAA,KAAK;YACpC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAEvC,2DAA2D;QAC3D,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;YAC3F,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;SAC3B;QAED,4FAA4F;QAC5F,8FAA8F;QAC9F,EAAE,CAAC,SAAS,GAAG,WAAW,CAAC;QAC3B,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC;QACnB,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAC3B;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,mCAAc,GAAd,UAAe,QAAQ;QACrB,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,IAAI,KAAK,CAAC,kCAAgC,QAAU,CAAC,CAAC;SAC7D;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,oDAA+B,GAA/B;QAAA,iBAGC;QAFC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACvE,IAAI,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAhB,CAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,mCAAc,GAAd;QACE,OAAO,OAAO,QAAQ,KAAK,WAAW;YACpC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YACnC,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED,2BAAM,GAAN,UAAO,EAAE;QACP,IAAI,GAAG,CAAC;QAER,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC1B,GAAG,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;SACjC;aAAM,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SACtC;aAAM;YACL,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;SACZ;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC;QAC5D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,oCAAe,GAAf,UAAgB,QAAQ;QACtB,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,mCAAc,GAAd,UAAe,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9B,OAAO,EAAE,CAAC;SACX;QACD,IAAM,WAAW,GAAG,EAAE,CAAC,SAAS,CAAC;QACjC,IAAI,WAAW,YAAY,IAAI,CAAC,YAAY,EAAE;YAC5C,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvE;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;YACjG,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC7D,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SAC1B;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,4CAAuB,GAAvB;QACE,IAAM,GAAG,GAAG,EAAE,CAAC;QAEf,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,EAAE;YAC1B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC;IACH,iBAAC;AAAD,CAAC,AA5SD,CAAyB,KAAK,GA4S7B;AAED;;;;;;GAMG;AACH,SAAS,8BAA8B,CAAC,EAAE,EAAE,GAAG;IAC7C,IAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACrC,GAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAExC,4EAA4E;IAC5E,8EAA8E;IAC9E,2EAA2E;IAC3E,OAAO,CAAC,IAAI,CAAC;QACX,YAAY,CAAC,QAAQ,EAAE,kBAAkB,CAAC;QAC1C,eAAe,CAAC,IAAI,CAAC;KACtB,CAAC,CAAC,IAAI,CAAC;QACN,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;YAC1C,yEAAyE;YACzE,mEAAmE;YACnE,EAAE;YACF,sDAAsD;YACtD,EAAE;YACF,0BAA0B,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;gBACpD,GAAG,CAAC,IAAI,CAAC,qCAAmC,GAAG,cAAW,CAAC,CAAC;gBAC5D,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC,IAAI,CAAC;gBACN,GAAG,CAAC,IAAI,CAAC,iDAA+C,GAAG,cAAW,CAAC,CAAC;gBACxE,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;gBACZ,GAAG,CAAC,IAAI,CAAC,iDAA+C,GAAG,eAAY,EAAE,EAAE,KAAK,OAAA,EAAE,EAAE,IAAA,EAAE,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,gBAAgB,CAAC,EAAE,EAAE,uBAA8B;IAA9B,wCAAA,EAAA,8BAA8B;IAC1D,IAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC;IAC3B,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC;IAEzB,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAEhC,EAAE,CAAC,KAAK,GAAG;QACT,mBAAmB,GAAG,IAAI,CAAC;QAC3B,OAAO,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG;QACR,mBAAmB,GAAG,KAAK,CAAC;QAC5B,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAM,OAAO,GAAG,uBAAuB,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,EAAE;YACxB,uBAAuB,EAAE,CAAC;SAC3B;IACH,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAET,IAAI,OAAO,EAAE;QACX,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACvC;IAED,OAAO;QACL,mBAAmB;YACjB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QACD,MAAM;YACJ,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;YACrB,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC;YACnB,IAAI,OAAO,EAAE;gBACX,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC1C;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC"}