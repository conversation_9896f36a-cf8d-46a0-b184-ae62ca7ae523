'use strict';
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var MediaTrackTransceiver = require('./transceiver');
/**
 * A {@link MediaTrackReceiver} represents a remote MediaStreamTrack.
 * @extends MediaTrackTransceiver
 */
var MediaTrackReceiver = /** @class */ (function (_super) {
    __extends(MediaTrackReceiver, _super);
    /**
     * Construct a {@link MediaTrackReceiver}.
     * @param {Track.ID} id - The MediaStreamTrack ID signaled through RSP/SDP
     * @param {MediaStreamTrack} mediaStreamTrack - The remote MediaStreamTrack
     */
    function MediaTrackReceiver(id, mediaStreamTrack) {
        return _super.call(this, id, mediaStreamTrack) || this;
    }
    return MediaTrackReceiver;
}(MediaTrackTransceiver));
module.exports = MediaTrackReceiver;
//# sourceMappingURL=receiver.js.map