{"version": 3, "file": "remotedatatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotedatatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACtB,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,EAApD,CAAC,gBAAA,EAAE,aAAa,mBAAoC,CAAC;AAEzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH;IAA8B,mCAAK;IACjC;;;;;OAKG;IACH,yBAAY,GAAG,EAAE,iBAAiB,EAAE,OAAO;QAA3C,YACE,kBAAM,iBAAiB,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,SAqD7C;QAnDC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,cAAc,EAAE;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;YACD,aAAa,EAAE;gBACb,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,cAAc,CAAC;gBAC7B,CAAC;aACF;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,iBAAiB;aAC3C;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,cAAc;aACxC;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,OAAO;aACjC;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACxB,CAAC;aACF;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB,CAAC,iBAAiB,KAAK,IAAI;uBAC9C,iBAAiB,CAAC,cAAc,KAAK,IAAI;aAC/C;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,GAAG;aACX;SACF,CAAC,CAAC;QAEH,iBAAiB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAA,IAAI;YAClC,KAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,KAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;;IACL,CAAC;IAED;;;;;;OAMG;IACH,qCAAW,GAAX,UAAY,QAAQ;QAClB,IAAM,cAAc,kBAAI,IAAI,UAAK,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtC,mCAAmC;YACnC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACnD;QAED,wDAAwD;QACxD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,qCAAW,GAAX;QACE,cAAc;IAChB,CAAC;IAED;;;OAGG;IACH,yCAAe,GAAf,UAAgB,aAAa;QAC3B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;YACzC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC/D;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AAnGD,CAA8B,KAAK,GAmGlC;AAED;;;;;;GAMG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC"}