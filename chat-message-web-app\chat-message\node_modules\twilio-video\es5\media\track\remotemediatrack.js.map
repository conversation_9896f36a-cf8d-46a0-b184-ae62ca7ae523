{"version": 3, "file": "remotemediatrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotemediatrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,IAAA,KAAmC,OAAO,CAAC,sBAAsB,CAAC,EAApD,CAAC,gBAAA,EAAE,aAAa,mBAAoC,CAAC;AACjE,IAAA,KAAK,GAAK,OAAO,CAAC,6BAA6B,CAAC,MAA3C,CAA4C;AACzD,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAErF,SAAS,qBAAqB,CAAC,iBAAiB;IAC9C;;;;;;;;;;;OAWG;IACH;QAAsC,oCAAiB;QACrD;;;;;;;;;;WAUG;QACH,0BAAY,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO;YAAlG,iBAiEC;YAhEC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBACtB,6GAA6G;gBAC7G,2GAA2G;gBAC3G,yCAAyC;gBACzC,yBAAyB,EAAE,KAAK,EAAE;uBAC7B,OAAO,QAAQ,KAAK,QAAQ;uBAC5B,OAAO,QAAQ,CAAC,gBAAgB,KAAK,UAAU;uBAC/C,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;aAClD,EAAE,OAAO,CAAC,CAAC;YAEZ,QAAA,kBAAM,kBAAkB,EAAE,OAAO,CAAC,SAAC;YAEnC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;gBAC5B,UAAU,EAAE;oBACV,KAAK,EAAE,SAAS;oBAChB,QAAQ,EAAE,IAAI;iBACf;gBACD,cAAc,EAAE;oBACd,KAAK,EAAE,aAAa;oBACpB,QAAQ,EAAE,IAAI;iBACf;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,WAAW;iBACnB;gBACD,cAAc,EAAE;oBACd,KAAK,EAAE,UAAA,UAAU;wBACf,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;wBACrD,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC5B,CAAC;iBACF;gBACD,SAAS,EAAE;oBACT,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,IAAI,CAAC,UAAU,CAAC;oBACzB,CAAC;iBACF;gBACD,aAAa,EAAE;oBACb,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,IAAI,CAAC,cAAc,CAAC;oBAC7B,CAAC;iBACF;gBACD,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI;oBAChB,GAAG;wBACD,OAAO,IAAI,CAAC,SAAS,CAAC;oBACxB,CAAC;iBACF;gBACD,GAAG,EAAE;oBACH,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE,GAAG;iBACX;gBACD,0BAA0B,EAAE;oBAC1B,KAAK,EAAE,OAAO,CAAC,yBAAyB;iBACzC;gBACD,iCAAiC,EAAE;oBACjC,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;;QACL,CAAC;QAED;;;;;;;WAOG;QACH,sCAAW,GAAX,UAAY,QAAQ;YAClB,IAAM,cAAc,kBAAI,IAAI,UAAK,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAC,CAAC;YAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACtC,mCAAmC;gBACnC,MAAM,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;aACnD;YACD,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;gBAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;aAC7B;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;WAGG;QACH,sCAAW,GAAX,UAAY,SAAS;YACnB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;gBACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC3D;QACH,CAAC;QAED;;;WAGG;QACH,0CAAe,GAAf,UAAgB,aAAa;YAC3B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,EAAE;gBACzC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;aAC/D;QACH,CAAC;QAED,iCAAM,GAAN,UAAO,EAAE;YACP,IAAM,MAAM,GAAG,iBAAM,MAAM,YAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,IAAI,EAAE;gBAC1C,4DAA4D;gBAC5D,6DAA6D;gBAC7D,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACrC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC;iBACpC;gBAED,4EAA4E;gBAC5E,kFAAkF;gBAClF,mEAAmE;gBACnE,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,cAAc,EAAE,CAAC;iBACvB;aACF;YACD,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACnC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,iCAAiC;uBAC1E,6BAA6B,CAAC,IAAI,CAAC,CAAC;aAC1C;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAM,GAAN,UAAO,EAAE;YACP,IAAM,MAAM,GAAG,iBAAM,MAAM,YAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;gBAChC,2DAA2D;gBAC3D,0DAA0D;gBAC1D,+DAA+D;gBAC/D,6CAA6C;gBAC7C,wCAAwC;gBACxC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;gBACtC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC;iBACrC;gBAED,IAAI,IAAI,CAAC,iCAAiC,EAAE;oBAC1C,2BAA2B;oBAC3B,IAAI,CAAC,iCAAiC,EAAE,CAAC;oBACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;iBAC/C;aACF;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACH,uBAAC;IAAD,CAAC,AAzKM,CAA+B,iBAAiB,GAyKrD;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,gBAAgB;IAC7C,IAAM,GAAG,GAAW,gBAAgB,KAA3B,EAAE,IAAI,GAAK,gBAAgB,KAArB,CAAsB;IAE7C,SAAS,mBAAmB,CAAC,SAAS;QACpC,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,EAAE;YACtC,IAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC/C,IAAM,qBAAqB,GAAG,EAAE,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/E,IAAI,qBAAqB,EAAE;gBACzB,GAAG,CAAC,IAAI,CAAC,mCAAiC,IAAI,cAAW,CAAC,CAAC;gBAC3D,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC1B,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;gBACjD,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;oBACb,GAAG,CAAC,IAAI,CAAC,+CAA6C,IAAI,cAAW,CAAC,CAAC;oBACvE,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;oBAC1B,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;oBACV,GAAG,CAAC,IAAI,CAAC,+CAA6C,IAAI,eAAY,EAAE,EAAE,GAAG,KAAA,EAAE,EAAE,IAAA,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAC;gBACzG,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yEAAyE;IACzE,0FAA0F;IAC1F,+EAA+E;IAC/E,0DAA0D;IAC1D,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACrE,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;;;;GAQG;AAEH,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC"}