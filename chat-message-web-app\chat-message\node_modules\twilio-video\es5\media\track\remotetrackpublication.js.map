{"version": 3, "file": "remotetrackpublication.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotetrackpublication.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH;IAAqC,0CAAgB;IACnD;;;;;OAKG;IACH,gCAAY,SAAS,EAAE,OAAO;QAA9B,YACE,kBAAM,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,SA4E9C;QA1EC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,CAAC;aACF;YACD,cAAc,EAAE;gBACd,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,SAAS,CAAC;gBAC7B,CAAC;aACF;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,SAAS,CAAC,IAAI;aACtB;YACD,eAAe,EAAE;gBACf,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,QAAQ,CAAC;gBAC5B,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC;gBACrB,CAAC;aACF;SACF,CAAC,CAAC;QAEH,2DAA2D;QAEzD,IAAA,KAAK,GAIH,SAAS,MAJN,EACL,SAAS,GAGP,SAAS,UAHF,EACT,aAAa,GAEX,SAAS,cAFE,EACb,QAAQ,GACN,SAAS,SADH,CACI;QAEd,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE;gBAC7B,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;gBACxB,KAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjD,OAAO;aACR;YACD,IAAI,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE;gBACrC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;gBAChC,IAAI,KAAI,CAAC,KAAK,EAAE;oBACd,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;iBAC7C;gBACD,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;aACnE;YACD,IAAI,aAAa,KAAK,SAAS,CAAC,aAAa,EAAE;gBAC7C,KAAI,CAAC,IAAI,CAAC,KAAK,CAAI,KAAI,CAAC,QAAQ,WAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAO,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;gBAClH,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;gBACxC,IAAI,KAAI,CAAC,KAAK,EAAE;oBACd,KAAI,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;oBACpD,KAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAG,KAAI,CAAC,KAAK,CAAC,CAAC;iBAChF;qBAAM,IAAI,aAAa,EAAE;oBACxB,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;iBAC/D;aACF;YACD,IAAI,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE;gBACnC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAC9B,KAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;aAC/C;QACH,CAAC,CAAC,CAAC;;IACL,CAAC;IAED,yCAAQ,GAAR;QACE,OAAO,8BAA4B,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,QAAQ,MAAG,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,4CAAW,GAAX,UAAY,KAAK;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;OAEG;IACH,6CAAY,GAAZ;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;IACH,6BAAC;AAAD,CAAC,AA/GD,CAAqC,gBAAgB,GA+GpD;AAED;;;;;;;GAOG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH,MAAM,CAAC,OAAO,GAAG,sBAAsB,CAAC"}