{"version": 3, "file": "remotevideotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/remotevideotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC5D,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,yBAAyB,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC7E,IAAA,YAAY,GAAK,OAAO,CAAC,4BAA4B,CAAC,aAA1C,CAA2C;AAC/D,IAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE9C,IAAM,qBAAqB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAChE,IAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC;;;;;;;;;;;;;;GAcG;AACH;IAA+B,oCAAqB;IAClD;;;;;;;;;;OAUG;IACH,0BAAY,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO;QAAlG,iBAgFC;QA/EC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,2BAA2B,EAAE,MAAM;YACnC,sBAAsB,EAAE,MAAM;YAC9B,+BAA+B,EAAE,IAAI;SACtC,EAAE,OAAO,CAAC,CAAC;QAEZ,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,oBAAoB,EAAE,OAAO,oBAAoB,KAAK,WAAW,IAAI,OAAO,CAAC,2BAA2B,KAAK,MAAM,CAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB;YAC1J,cAAc,EAAE,OAAO,cAAc,KAAK,WAAW,IAAI,OAAO,CAAC,sBAAsB,KAAK,MAAM,CAAC,CAAC,CAAE,YAAY,CAAC,CAAC,CAAC,cAAc;SACpI,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,kBAAM,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,SAAC;QAE9F,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,gCAAgC,EAAE;gBAChC,KAAK,EAAE,OAAO,CAAC,+BAA+B,KAAK,IAAI,IAAI,OAAO,CAAC,2BAA2B,KAAK,MAAM;aAC1G;YACD,iCAAiC,EAAE;gBACjC,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,OAAO,CAAC,2BAA2B;aAC3C;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,OAAO,CAAC,sBAAsB;aACtC;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI,OAAO,CAAC;oBACjB,KAAI,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1C,CAAC,EAAE,sBAAsB,EAAE,KAAK,CAAC;aAClC;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,OAAO,CAAC,cAAc,CAAC,UAAA,OAAO;oBACvC,8DAA8D;oBAC9D,kGAAkG;oBAClG,2EAA2E;oBAC3E,IAAM,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAA1C,CAA0C,CAAC,CAAC;oBAChG,IAAI,qBAAqB,EAAE;wBACzB,wBAAwB,CAAC,KAAI,CAAC,CAAC;qBAChC;gBACH,CAAC,CAAC;aACH;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,OAAO,CAAC,oBAAoB,CAAC,UAAA,OAAO;oBAC7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;wBACnB,IAAM,UAAU,GAAG,CAAC,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBAC9D,IAAI,UAAU,KAAK,KAAK,CAAC,cAAc,EAAE;4BACvC,IAAI,KAAK,CAAC,cAAc,EAAE;gCACxB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gCAC5D,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;6BAC9C;iCAAM;gCACL,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gCAC5D,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;6BAC3C;4BACD,mBAAmB,GAAG,IAAI,CAAC;yBAC5B;oBACH,CAAC,CAAC,CAAC;oBACH,IAAI,mBAAmB,EAAE;wBACvB,sBAAsB,CAAC,KAAI,CAAC,CAAC;wBAE7B,wFAAwF;wBACxF,0FAA0F;wBAC1F,gCAAgC;wBAChC,wBAAwB,CAAC,KAAI,CAAC,CAAC;qBAChC;gBACH,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;aACxB;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;OAEG;IACH,iCAAM,GAAN,UAAO,OAAO;QACZ,IAAM,MAAM,GAAG,iBAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,mFAAmF;QACnF,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,mCAAQ,GAAR;QACE,IAAI,IAAI,CAAC,4BAA4B,KAAK,QAAQ,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,sHAAsH,CAAC,CAAC;SACzI;QACD,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,oCAAS,GAAT;QACE,IAAI,IAAI,CAAC,4BAA4B,KAAK,QAAQ,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,uHAAuH,CAAC,CAAC;SAC1I;QACD,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,gDAAqB,GAArB,UAAsB,kBAAkB;QACtC,IAAI,IAAI,CAAC,uBAAuB,KAAK,QAAQ,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,iHAAiH,CAAC,CAAC;SACpI;QAED,IAAI,kBAAkB,CAAC,gBAAgB,EAAE;YACvC,IAAI,CAAC,cAAc,CAAC,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wCAAa,GAAb,UAAc,EAAE;QACd,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,YAAY,EAAE;YAChB,EAAE,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YACzE,EAAE,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YACzE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACnC;IACH,CAAC;IAED,sCAAW,GAAX,UAAY,EAAE;QAAd,iBAWC;QAVC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACjB,IAAM,UAAU,GAAG,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,EAA3B,CAA2B,CAAC;YACxD,IAAM,UAAU,GAAG,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,EAA3B,CAA2B,CAAC;YACxD,IAAM,WAAW,GAAG,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,EAA5B,CAA4B,CAAC;YAE1D,EAAE,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;YACzD,EAAE,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,UAAU,YAAA,EAAE,UAAU,YAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;SACzE;IACH,CAAC;IAED,sCAAW,GAAX,UAAY,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACrC,IAAA,WAAW,GAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,YAAxC,CAAyC;QAC5D,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAClD,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,sCAAW,GAAX,UAAY,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7B,IAAA,WAAW,GAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,YAAxC,CAAyC;QAC5D,IAAM,SAAS,GAAG,KAAK,CAAC,sBAAsB,CAAC;QAC/C,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACrD,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,uCAAY,GAAZ;QACE,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,iCAAM,GAAN,UAAO,EAAE;QACP,IAAM,MAAM,GAAG,iBAAM,MAAM,YAAC,EAAE,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,4BAA4B,KAAK,MAAM,EAAE;YAChD,mDAAmD;YACnD,2EAA2E;YAC3E,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,iCAAiC,IAAI,8BAA8B,CAAC,IAAI,CAAC,CAAC;SACzH;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,iCAAM,GAAN,UAAO,EAAE;QAAT,iBAoBC;QAnBC,IAAM,MAAM,GAAG,iBAAM,MAAM,YAAC,EAAE,CAAC,CAAC;QAChC,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC3D,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO;YACtB,KAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxC,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACxC,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;YAChC,IAAI,IAAI,CAAC,iCAAiC,EAAE;gBAC1C,IAAI,CAAC,iCAAiC,EAAE,CAAC;gBACzC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;aAC/C;SACF;QAED,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7B,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,uCAAY,GAAZ;QACE,OAAO,iBAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACH,0CAAe,GAAf;QACE,OAAO,iBAAM,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC;IAED,mCAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,GAAG,MAAG,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACH,sCAAW,GAAX,UAAY,QAAQ;QAClB,OAAO,iBAAM,WAAW,YAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACH,uBAAC;AAAD,CAAC,AAxTD,CAA+B,qBAAqB,GAwTnD;AAED,SAAS,uBAAuB,CAAC,gBAAgB;IAC/C,IAAI,CAAC,CAAC,0BAA0B,IAAI,UAAU,CAAC,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IAED,IAAM,SAAS,GAAG,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC;IAC7D,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,KAAK,CAAC;KACd;IAED,IAAM,MAAM,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IAEzE,OAAO,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAd,CAAc,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,sBAAsB,CAAC,gBAAgB;IAC9C,IAAI,gBAAgB,CAAC,4BAA4B,KAAK,MAAM,EAAE;QAC5D,OAAO;KACR;IAED,IAAM,eAAe,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,EAA5C,CAA4C,CAAC,CAAC;IAC9H,IAAM,UAAU,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAxC,CAAwC,CAAC,CAAC;IAErH,2EAA2E;IAC3E,IAAM,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC;QACnC,uBAAuB,CAAC,gBAAgB,CAAC;QACzC,CAAC,QAAQ,CAAC,eAAe,KAAK,SAAS,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEzE,IAAI,OAAO,KAAK,IAAI,EAAE;QACpB,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACvC,gBAAgB,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KACpD;SAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE;QAChD,mDAAmD;QACnD,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KACxC;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,gBAAgB;IAChD,IAAI,gBAAgB,CAAC,uBAAuB,KAAK,MAAM,EAAE;QACvD,OAAO;KACR;IAED,IAAM,eAAe,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,EAA5C,CAA4C,CAAC,CAAC;IAC9H,IAAM,WAAW,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,UAAA,EAAE;QACnE,IAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3D,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;IAC5H,CAAC,CAAC,CAAC;IACH,IAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC1D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,IAAA,KAAA,OAAkC,aAAa,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,GAAG;YAClE,OAAA,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,WAAW,GAAG,CAAC;QAA3E,CAA2E,CAAC,IAAA,EADvE,UAA6B,EAA3B,YAAY,kBAAA,EAAE,WAAW,iBAC4C,CAAC;QAC/E,IAAM,gBAAgB,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;QACtE,gBAAgB,CAAC,cAAc,CAAC,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAC;KACvD;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,gBAAgB;IACtD,SAAS,mBAAmB;QAC1B,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAED,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACrE,OAAO;QACL,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC;AAED;;;GAGG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}