{"version": 3, "file": "sender.js", "sourceRoot": "", "sources": ["../../../lib/media/track/sender.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,qBAAqB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAEvD;;;;GAIG;AACH;IAA+B,oCAAqB;IAClD;;;OAGG;IACH,0BAAY,gBAAgB;QAA5B,YACE,kBAAM,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,SAgC7C;QA/BC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,CAAC;oBACb,CAAC,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAnB,CAAmB,CAAC;oBACnC,CAAC,QAAQ,EAAE,cAAM,OAAA,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAArB,CAAqB,CAAC;iBACxC,CAAC;aACH;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,+BAA+B,EAAE;gBAC/B,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7B,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC3B,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,6BAA6B,EAAE,CAAC;;IACvC,CAAC;IAED;;OAEG;IACH,wDAA6B,GAA7B,UAA8B,gBAA8B;QAA9B,iCAAA,EAAA,mBAAmB,IAAI,CAAC,MAAM;QACpD,IAAA,KAA6D,IAAI,EAA1C,kBAAkB,yBAAA,EAAU,KAAK,YAAS,CAAC;QACxE,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,kBAAkB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK,IAAK,OAAA,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,EAAnD,CAAmD,CAAC,CAAC;SACvG;aAAM;YACL,kBAAkB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;gBAC1C,gBAAgB,CAAC,OAAK,KAAO,CAAC,GAAG,SAAS,CAAC;YAC7C,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,KAAK,KAAK,gBAAgB,EAAE;YAC9B,kBAAkB,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK,IAAK,OAAA,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC,EAA3C,CAA2C,CAAC,CAAC;YAC9F,IAAI,KAAK,CAAC,KAAK,KAAK,gBAAgB,CAAC,KAAK,EAAE;gBAC1C,IAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACrF,SAAS,EAAE,CAAC;aACb;SACF;IACH,CAAC;IAED;;;;OAIG;IACH,gCAAK,GAAL;QACE,IAAM,KAAK,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,sCAAW,GAAX,UAAY,KAAK;QACf,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,8CAAmB,GAAnB,UAAoB,gBAAgB;QAApC,iBAWC;QAVC,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK;YACjC,OAAO,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,MAAM;YAC1B,OAAO,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACX,KAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YACrD,KAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,oCAAS,GAAT,UAAU,MAAM,EAAE,qBAAqB;QACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,uCAAY,GAAZ,UAAa,MAAM;QACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,2CAAgB,GAAhB,UAAiB,SAAS;QACxB,yGAAyG;QACnG,IAAA,KAAA,OAA0B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,MAAM,EAAE,CAAC,IAAA,EAAlF,qBAAqB,QAA6D,CAAC;QAC1F,OAAO,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAC5G,CAAC;IAED,wCAAa,GAAb,UAAc,MAAM,EAAE,gBAAgB;QAAtC,iBAOC;QANC,OAAO,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAA,kBAAkB;YAClE,wDAAwD;YACxD,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;YAC5C,KAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IACH,uBAAC;AAAD,CAAC,AA9ID,CAA+B,qBAAqB,GA8InD;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}