{"version": 3, "file": "videoprocessoreventobserver.js", "sourceRoot": "", "sources": ["../../../lib/media/track/videoprocessoreventobserver.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,QAAQ,CAAC,aAAtB,CAAuB;AACnC,IAAA,yCAAyC,GAAK,OAAO,CAAC,sBAAsB,CAAC,0CAApC,CAAqC;AAEtF;;;;;GAKG;AACH;IAA0C,+CAAY;IAEpD;;;OAGG;IACH,qCAAY,GAAG;QAAf,YACE,iBAAO,SAkDR;QAhDC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,KAAK,EAAE,UAAA,IAAI;YACjB,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACrC,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,KAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YACjB,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YAChB,IAAM,IAAI,GAAG,KAAI,CAAC,aAAa,EAAE,CAAC;YAClC,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACf,KAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAA,OAAO;YACrB,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,SAAA,EAAE,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,eAAe,EAAE,EAAtB,CAAsB,CAAC,CAAC;;IACjD,CAAC;IAED;;OAEG;IACH,mDAAa,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,OAAO,EAAE,CAAC;SACX;QAEK,IAAA,KAQF,IAAI,CAAC,cAAc,EAPrB,SAAS,eAAA,EACT,aAAa,mBAAA,EACb,YAAY,kBAAA,EACZ,cAAc,oBAAA,EACd,kBAAkB,wBAAA,EAClB,oBAAoB,0BAAA,EACpB,4BAA4B,kCACP,CAAC;QACxB,IAAM,IAAI,GAAG,EAAE,aAAa,eAAA,EAAE,YAAY,cAAA,EAAE,cAAc,gBAAA,EAAE,kBAAkB,oBAAA,EAAE,oBAAoB,sBAAA,EAAE,4BAA4B,8BAAA,EAAE,CAAC;QACrI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,gBAAgB,CAAC;QAEhD,CAAC,YAAY,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC9H,IAAM,GAAG,GAAG,SAAS,CAAC,MAAI,IAAM,CAAC,CAAC;YAClC,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aAClB;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,qDAAe,GAAf;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxC,OAAO;SACR;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,EAAE;YACxC,OAAO;SACR;QAED,IAAM,KAAK,GAAG,EAAE,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE,CAAC;QAC7E,CAAC,mBAAmB,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAC5H,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,GAAG,GAAG,IAAI,CAAC,qBAAqB,GAAG,yCAAyC,EAAE;YAChF,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;QACjC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,UAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;gBAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpB;gBACD,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;YAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,kDAAY,GAAZ,UAAa,IAAI,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAkB,IAAM,EAAE,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;IACrC,CAAC;IACH,kCAAC;AAAD,CAAC,AArJD,CAA0C,YAAY,GAqJrD;AAED,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC"}