{"version": 3, "file": "videotrack.js", "sourceRoot": "", "sources": ["../../../lib/media/track/videotrack.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3C,IAAM,kBAAkB,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC3D,IAAM,2BAA2B,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AACrE,IAAA,YAAY,GAAK,OAAO,CAAC,mBAAmB,CAAC,aAAjC,CAAkC;AAC9C,IAAA,kBAAkB,GAAK,OAAO,CAAC,sBAAsB,CAAC,mBAApC,CAAqC;AAE/D;;;;;;;;;;;;;;;;;;;;GAoBG;AACH;IAAyB,8BAAU;IACjC;;;;OAIG;IACH,oBAAY,qBAAqB,EAAE,OAAO;QAA1C,YACE,kBAAM,qBAAqB,EAAE,OAAO,CAAC,SA+CtC;QA9CC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,cAAO,CAAC;gBACf,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI;oBACX,MAAM,EAAE,IAAI;iBACb;aACF;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,IAAI,2BAA2B,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;QAEnH,OAAO,KAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,6CAAwB,GAAxB,UAAyB,YAAoB;QAApB,6BAAA,EAAA,oBAAoB;QAC3C,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAC5B,IAAI,OAAO,GAAG,EAAE,CAAC;QACX,IAAA,KAA0B,IAAI,CAAC,gBAAgB,EAA7C,OAAO,aAAA,EAAE,UAAU,gBAA0B,CAAC;QAEtD,IAAI,CAAC,OAAO,EAAE;YACZ,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,8BAA8B,CAAC;SAC1C;QACD,IAAI,UAAU,KAAK,OAAO,EAAE;YAC1B,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,2BAA2B,CAAC;SACvC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,8BAA8B,CAAC;SAC1C;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5C,gBAAgB,GAAG,KAAK,CAAC;YACzB,OAAO,GAAG,gEAAgE,CAAC;SAC5E;QAED,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QACD,OAAO,EAAE,gBAAgB,kBAAA,EAAE,OAAO,SAAA,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,mCAAc,GAAd;QAAA,iBAgFC;QA/EC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;YAC/E,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,gBAAgB,EAAE;YACrD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACvE,OAAO;SACR;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAElC,IAAA,oBAAoB,GAAK,IAAI,CAAC,iBAAiB,qBAA3B,CAA4B;QAExD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;YACxB,IAAM,OAAO,GAAG,UAAA,UAAU;gBACxB,IAAM,WAAW,GAAG,KAAI,CAAC,wBAAwB,EAAE,CAAC;gBACpD,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;oBACjC,IAAI,UAAU,EAAE;wBACd,UAAU,CAAC,KAAK,EAAE,CAAC;qBACpB;oBACD,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,KAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC/D,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;oBACrE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBACK,IAAA,KAA4B,KAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAA7D,aAAS,EAAT,KAAK,mBAAG,CAAC,KAAA,EAAE,cAAU,EAAV,MAAM,mBAAG,CAAC,KAAwC,CAAC;gBACtE,mDAAmD;gBACnD,iCAAiC;gBACjC,IAAI,KAAI,CAAC,YAAY,IAAI,KAAI,CAAC,YAAY,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC1D,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;oBAChC,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;iBACnC;gBACD,IAAI,KAAI,CAAC,WAAW,EAAE;oBACpB,IAAI,KAAI,CAAC,WAAW,CAAC,KAAK,KAAK,KAAK,EAAE;wBACpC,KAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;wBAC/B,KAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;qBAClC;oBACD,KAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,CACzC,KAAI,CAAC,QAAQ,EACb,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,CACP,CAAC;iBACH;gBACD,IAAM,KAAK,GAAG,UAAU,IAAI,CAC1B,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBACpD,CAAC,CAAC,KAAI,CAAC,QAAQ;oBACf,CAAC,CAAC,KAAI,CAAC,WAAW,CACrB,CAAC;gBACF,IAAI,MAAM,GAAG,IAAI,CAAC;gBAElB,IAAI;oBACF,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,EAAE,KAAI,CAAC,YAAY,CAAC,CAAC;iBAChE;gBAAC,OAAO,EAAE,EAAE;oBACX,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE,EAAE,CAAC,CAAC;iBACvE;gBACD,OAAO,CAAC,CAAC,MAAM,YAAY,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;qBACpE,IAAI,CAAC;oBACJ,IAAI,KAAI,CAAC,YAAY,EAAE;wBACrB,IAAI,OAAO,KAAI,CAAC,cAAc,CAAC,YAAY,KAAK,UAAU,EAAE;4BAC1D,KAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;yBACpC;wBACD,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC5C;gBACH,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YACF,KAAI,CAAC,YAAY,GAAG,kBAAkB,CACpC,KAAI,CAAC,QAAQ,EACb,OAAO,EACP,oBAAoB,CACrB,CAAC;QACJ,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,IAAI,CAAC,KAAK,CAC/B,gCAAgC,EAChC,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,KAAI,EAAE,CACvB,EAHiB,CAGjB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gCAAW,GAAX;QAAA,iBAoBC;QAnBC,iBAAM,WAAW,WAAE,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG;gBAC/B,IAAI,iBAAiB,CAAC,KAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1C,KAAI,CAAC,UAAU,CAAC,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACjD,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;iBACpD;YACH,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;gBACvB,IAAI,iBAAiB,CAAC,KAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1C,KAAI,CAAC,UAAU,CAAC,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACjD,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;oBACnD,IAAI,KAAI,CAAC,SAAS,EAAE;wBAClB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAI,CAAC,UAAU,CAAC,CAAC;wBACxD,KAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,KAAI,CAAC,CAAC;qBAChD;iBACF;YACH,CAAC,CAAC;SACH;IACH,CAAC;IAED;;OAEG;IACH,sCAAiB,GAAjB;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,SAAS,EAAE;YACb,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;SAChD;IACH,CAAC;IAED;;OAEG;IACH,2BAAM,GAAN,UAAO,OAAO;QACZ,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC/C,OAAO,iBAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,iCAAY,GAAZ,UAAa,SAAS,EAAE,OAAO;QAA/B,iBAgGC;QA/FC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,UAAU,EAAE;YAC9D,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,SAAS,CAAC,CAAC;QAEtE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG;gBACpB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC5C,oGAAoG;gBACpG,4FAA4F;gBAC5F,wFAAwF;gBACxF,IAAI,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE;oBAC7B,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC;oBAClG,KAAI,CAAC,iBAAiB,EAAE,CAAC;iBAC1B;YACH,CAAC,CAAC;YACF,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE;gBAC1C,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aACvE;iBAAM;gBACL,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjE;SACF;QAED,IAAI,CAAC,iBAAiB,GAAG,OAAO,IAAI,EAAE,CAAC;QACnC,IAAA,KAAyD,IAAI,CAAC,iBAAiB,EAA7E,oBAAoB,0BAAA,EAAE,4BAA4B,kCAA2B,CAAC;QACpF,IAAI,OAAO,eAAe,KAAK,WAAW,IAAI,oBAAoB,KAAK,iBAAiB,EAAE;YACxF,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;QACD,IAAI,oBAAoB;eACnB,oBAAoB,KAAK,YAAY;eACrC,oBAAoB,KAAK,OAAO;eAChC,oBAAoB,KAAK,QAAQ;eACjC,oBAAoB,KAAK,iBAAiB,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,qCAAmC,oBAAsB,CAAC,CAAC;SAC5E;QACD,IAAI,CAAC,oBAAoB,EAAE;YACzB,oBAAoB,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC;SAC9F;QAEK,IAAA,KAA4D,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAA7F,aAAS,EAAT,KAAK,mBAAG,CAAC,KAAA,EAAE,cAAU,EAAV,MAAM,mBAAG,CAAC,KAAA,EAAE,iBAA8B,EAA9B,SAAS,mBAAG,kBAAkB,KAAwC,CAAC;QACtG,IAAI,oBAAoB,KAAK,iBAAiB,EAAE;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SACvD;QACD,IAAI,oBAAoB,KAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;SAClC;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,wFAAwF;QACxF,6FAA6F;QAC7F,uFAAuF;QACvF,4BAA4B,GAAG,4BAA4B,IAAI,IAAI,CAAC;QAEpE,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,8CAA4C,4BAA4B,MAAG,CAAC,CAAC;SAC9F;QAED,sGAAsG;QACtG,kHAAkH;QAClH,8DAA8D;QAC9D,IAAM,SAAS,GAAG,OAAO,6BAA6B,KAAK,WAAW,IAAI,6BAA6B,CAAC,SAAS;YAC/G,2BAA2B;YAC3B,OAAO,6BAA6B,CAAC,SAAS,CAAC,YAAY,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE7F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QAC5D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,EAAE;YACvC,SAAS,WAAA;YACT,aAAa,EAAE,MAAM;YACrB,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,SAAS;YACzB,kBAAkB,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAChE,oBAAoB,sBAAA;YACpB,4BAA4B,8BAAA;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;MAcE,CAAA;;;;;;;;;;;;;;;;;;;;;;MAsBA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;OAyBC;IACH,2BAAM,GAAN;QACE,IAAM,MAAM,GAAG,iBAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;MAME,CAAA;;;;;;;;MAQA,CAAA;;;;;;;;OAQC;IACH,2BAAM,GAAN;QACE,OAAO,iBAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,oCAAe,GAAf,UAAgB,SAAS;QACvB,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAC7E;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6CAA6C,EAAE,SAAS,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,cAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzE,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IACH,iBAAC;AAAD,CAAC,AAveD,CAAyB,UAAU,GAuelC;AAED,UAAU,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;AAEpD,SAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI;IACpC,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU;WAC5C,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC;AACpD,CAAC;AAED;;;;;;;GAOG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC"}