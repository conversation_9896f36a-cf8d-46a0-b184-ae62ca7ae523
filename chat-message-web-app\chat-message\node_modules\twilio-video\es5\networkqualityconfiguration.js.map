{"version": 3, "file": "networkqualityconfiguration.js", "sourceRoot": "", "sources": ["../lib/networkqualityconfiguration.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,QAAQ,CAAC,aAAtB,CAAuB;AAErC,IAAA,KAIF,OAAO,CAAC,kBAAkB,CAAC,EAH7B,sBAAsB,4BAAA,EACtB,uBAAuB,6BAAA,EACvB,YAAY,kBACiB,CAAC;AAExB,IAAA,OAAO,GAAK,OAAO,CAAC,QAAQ,CAAC,QAAtB,CAAuB;AAEtC;;;;;;;GAOG;AACH;IAA8C,mDAAY;IACxD;;;OAGG;IACH,yCAAY,2BAA2B;QAAvC,YACE,iBAAO,SAqBR;QAnBC,2BAA2B,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1C,KAAK,EAAE,sBAAsB;YAC7B,MAAM,EAAE,uBAAuB;SAChC,EAAE,2BAA2B,CAAC,CAAC;QAEhC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,KAAK,EAAE;gBACL,KAAK,EAAE,OAAO,CAAC,2BAA2B,CAAC,KAAK,EAAE,sBAAsB,EAAE,YAAY,CAAC;oBACrF,CAAC,CAAC,2BAA2B,CAAC,KAAK;oBACnC,CAAC,CAAC,sBAAsB;gBAC1B,QAAQ,EAAE,IAAI;aACf;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,OAAO,CAAC,2BAA2B,CAAC,MAAM,EAAE,uBAAuB,EAAE,YAAY,CAAC;oBACvF,CAAC,CAAC,2BAA2B,CAAC,MAAM;oBACpC,CAAC,CAAC,uBAAuB;gBAC3B,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;;;OAKG;IACH,gDAAM,GAAN,UAAO,2BAA2B;QAAlC,iBAeC;QAdC,2BAA2B,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1C,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,EAAE,2BAA2B,CAAC,CAAC;QAEhC;YACE,CAAC,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACpC,CAAC,QAAQ,EAAE,uBAAuB,EAAE,CAAC,CAAC;SACvC,CAAC,OAAO,CAAC,UAAC,EAAyB;gBAAzB,KAAA,aAAyB,EAAxB,aAAa,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAA;YACjC,KAAI,CAAC,aAAa,CAAC,GAAG,OAAO,2BAA2B,CAAC,aAAa,CAAC,KAAK,QAAQ;mBAC/E,OAAO,CAAC,2BAA2B,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChE,CAAC,CAAC,2BAA2B,CAAC,aAAa,CAAC;gBAC5C,CAAC,CAAC,GAAG,CAAC;QACV,CAAC,CAAC,CAAC;IACL,CAAC;IACH,sCAAC;AAAD,CAAC,AAnDD,CAA8C,YAAY,GAmDzD;AAED,MAAM,CAAC,OAAO,GAAG,+BAA+B,CAAC"}