{"version": 3, "file": "noisecancellationadapter.js", "sourceRoot": "", "sources": ["../lib/noisecancellationadapter.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKb,IAAM,aAAa,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACtD,IAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAElC,IAAM,aAAa,GAAG;IACpB,KAAK,EAAE;QACL,gBAAgB,EAAE,OAAO;QACzB,UAAU,EAAE,cAAc;KAC3B;IACD,OAAO,EAAE;QACP,gBAAgB,EAAE,OAAO;QACzB,UAAU,EAAE,iBAAiB;KAC9B;CACF,CAAC;AAkBF,IAAM,sBAAsB,GAAG,UAAC,EAA+G;QAA7G,gBAAgB,sBAAA,EAAE,MAAM,YAAA,EAAE,GAAG,SAAA;IAC7D,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,8FAA8F,CAAC,CAAC;KACjH;IAED,IAAM,aAAa,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;IAC1C,GAAG,CAAC,KAAK,CAAC,sBAAoB,aAAe,CAAC,CAAC;IAC/C,IAAM,iBAAiB,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAC;IACtF,IAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAC;IAChF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;QACjE,MAAM,IAAI,KAAK,CAAC,wCAAsC,gBAAgB,UAAK,aAAe,CAAC,CAAC;KAC7F;IAED,IAAI,iBAAiB,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,CAAC,CAAC,EAAE;QAC9C,MAAM,IAAI,KAAK,CAAC,6CAA2C,aAAa,+BAA0B,gBAAgB,MAAG,CAAC,CAAC;KACxH;IAED,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,6CAA2C,aAAa,+BAA0B,gBAAgB,MAAG,CAAC,CAAC;KACxH;IAED,IAAM,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC;IACvC,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACpD,WAAW,CAAC,KAAK,EAAE,CAAC;IACpB,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;KAC/E;AACH,CAAC,CAAC;AAEF,IAAI,eAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;AACxD,SAAsB,qCAAqC,CACzD,wBAAkD,EAClD,GAAe;;;;;;oBAEX,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;yBACtE,CAAC,cAAc,EAAf,wBAAe;oBACb,YAAY,GAAG,aAAa,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;oBAClE,IAAI,CAAC,YAAY,EAAE;wBACjB,MAAM,IAAI,KAAK,CAAC,kDAAgD,wBAAwB,CAAC,MAAQ,CAAC,CAAC;qBACpG;oBAEO,gBAAgB,GAAkB,YAAY,iBAA9B,EAAE,UAAU,GAAM,YAAY,WAAlB,CAAmB;oBAChD,OAAO,GAAG,wBAAwB,CAAC,aAAa,CAAC;oBACjD,WAAW,GAAM,OAAO,SAAI,UAAY,CAAC;;;;oBAG9C,GAAG,CAAC,KAAK,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC;oBACrC,qBAAM,aAAa,CAAC,WAAW,CAAC,EAAA;;oBAAhD,aAAa,GAAG,SAAgC;oBACtD,GAAG,CAAC,KAAK,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;oBAErD,WAAS,aAAa,CAAC,OAAkC,CAAC;oBAChE,sBAAsB,CAAC;wBACrB,gBAAgB,kBAAA;wBAChB,MAAM,UAAA;wBACN,GAAG,KAAA;qBACJ,CAAC,CAAC;yBAEC,CAAC,QAAM,CAAC,aAAa,EAAE,EAAvB,wBAAuB;oBACzB,GAAG,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC;oBAC5D,qBAAM,QAAM,CAAC,IAAI,CAAC,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;;oBAA9B,SAA8B,CAAC;oBAC/B,GAAG,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;;oBAGnD,cAAc,GAAG;wBACf,MAAM,EAAE,wBAAwB,CAAC,MAAM;wBACvC,aAAa,EAAE,cAAM,OAAA,QAAM,CAAC,aAAa,EAAE,EAAtB,CAAsB;wBAC3C,WAAW,EAAE,cAAM,OAAA,QAAM,CAAC,WAAW,EAAE,EAApB,CAAoB;wBACvC,SAAS,EAAE,cAAM,OAAA,QAAM,CAAC,SAAS,EAAE,EAAlB,CAAkB;wBACnC,UAAU,EAAE,cAAM,OAAA,QAAM,CAAC,UAAU,EAAE,EAAnB,CAAmB;wBACrC,MAAM,EAAE,cAAM,OAAA,QAAM,CAAC,MAAM,EAAE,EAAf,CAAe;wBAC7B,OAAO,EAAE,cAAM,OAAA,QAAM,CAAC,OAAO,EAAE,EAAhB,CAAgB;wBAC/B,OAAO,EAAE,cAAM,OAAA,QAAM,CAAC,OAAO,EAAE,EAAhB,CAAgB;wBAC/B,UAAU,EAAE,UAAC,MAAe,IAAK,OAAA,QAAM,CAAC,UAAU,CAAC,MAAM,CAAC,EAAzB,CAAyB;wBAC1D,OAAO,EAAE,UAAC,WAA6B;4BACrC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;4BACvC,IAAI,QAAM,CAAC,WAAW,EAAE,EAAE;gCACxB,QAAM,CAAC,UAAU,EAAE,CAAC;6BACrB;4BAED,IAAM,WAAW,GAAG,QAAM,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;4BACnE,IAAI,CAAC,WAAW,EAAE;gCAChB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;6BACjE;4BACD,IAAM,UAAU,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;4BACnD,IAAI,CAAC,UAAU,EAAE;gCACf,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;6BAC1E;4BACD,QAAM,CAAC,MAAM,EAAE,CAAC;4BAChB,OAAO,UAAU,CAAC;wBACpB,CAAC;qBACF,CAAC;oBACF,eAAe,CAAC,GAAG,CAAC,wBAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;;;;oBAGrE,GAAG,CAAC,KAAK,CAAC,0CAAwC,WAAa,EAAE,IAAE,CAAC,CAAC;oBACrE,MAAM,IAAE,CAAC;wBAGb,sBAAO,cAAc,EAAC;;;;CACvB;AArED,sFAqEC"}