{"version": 3, "file": "participant.js", "sourceRoot": "", "sources": ["../lib/participant.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC/C,IAAM,gBAAgB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACnE,IAAM,2BAA2B,GAAG,OAAO,CAAC,2CAA2C,CAAC,CAAC;AACzF,IAAM,eAAe,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AACjE,IAAM,0BAA0B,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACvF,IAAM,gBAAgB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACnE,IAAM,2BAA2B,GAAG,OAAO,CAAC,2CAA2C,CAAC,CAAC;AACzF,IAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AAE/B,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;GAIG;AAEH;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH;IAA0B,+BAAY;IACpC;;;;OAIG;IACH,qBAAY,SAAS,EAAE,OAAO;QAA9B,YACE,iBAAO,SA4IR;QA1IC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,gBAAgB,kBAAA;YAChB,2BAA2B,6BAAA;YAC3B,eAAe,iBAAA;YACf,0BAA0B,4BAAA;YAC1B,gBAAgB,kBAAA;YAChB,2BAA2B,6BAAA;YAC3B,MAAM,EAAE,EAAE;SACX,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAChD,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAI,CAAC,CAAC;QACnD,IAAM,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACjD,IAAM,UAAU,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvC,IAAM,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAEjD,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAC,gBAAgB;aAChC;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,OAAO,CAAC,2BAA2B;aAC3C;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,2BAA2B,EAAE;gBAC3B,KAAK,EAAE,OAAO,CAAC,0BAA0B;aAC1C;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAC,gBAAgB;aAChC;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,OAAO,CAAC,2BAA2B;aAC3C;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,WAAW;aACnB;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,UAAU;aAClB;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE,UAAU;aACpB;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,OAAO,CAAC,2BAA2B;aAC3C;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,OAAO,CAAC,sBAAsB;aACtC;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,MAAM;aACd;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,gCAAgC,EAAE;gBAChC,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,WAAW;aACnB;YACD,WAAW,EAAE;gBACX,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,QAAQ,CAAC;gBAC5B,CAAC;aACF;YACD,mBAAmB,EAAE;gBACnB,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,mBAAmB,CAAC;gBACvC,CAAC;aACF;YACD,mBAAmB,EAAE;gBACnB,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,mBAAmB,CAAC;gBACvC,CAAC;aACF;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,GAAG,CAAC;gBACvB,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,SAAS,CAAC,KAAK,CAAC;gBACzB,CAAC;aACF;YACD,MAAM,EAAE;gBACN,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,WAAW,EAAE;gBACX,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,OAAO,CAAC,WAAW;aAC3B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,OAAO,CAAC,mBAAmB;aACnC;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,CAAC,CAAC;QACzD,SAAS,CAAC,EAAE,CAAC,4BAA4B,EAAE;YACzC,OAAA,KAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAI,CAAC,mBAAmB,EAC9D,KAAI,CAAC,mBAAmB;gBACxB,CAAC,KAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAChE,CAAC,CAAC,KAAI,CAAC,mBAAmB;gBAC1B,CAAC,CAAC,IAAI,CAAC;QAJX,CAIW,CAAC,CAAC;QACf,iCAAiC,CAAC,KAAI,EAAE,SAAS,CAAC,CAAC;QACnD,GAAG,CAAC,IAAI,CAAC,+BAA4B,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAK,KAAI,CAAC,QAAU,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,CAAC;;IACpF,CAAC;IAED;;;;OAIG;IACH,qCAAe,GAAf;QACE,OAAO;YACL,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;YAC/C,CAAC,SAAS,EAAE,cAAc,CAAC;YAC3B,CAAC,SAAS,EAAE,cAAc,CAAC;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gDAA0B,GAA1B;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,8BAAQ,GAAR;QACE,OAAO,mBAAiB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,GAAG,MAAG,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACH,+BAAS,GAAT,UAAU,KAAK,EAAE,EAAE;QACjB,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAE5B,IAAM,YAAY,GAAG;YACnB,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,IAAI,EAAE,IAAI,CAAC,WAAW;SACvB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACd,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5B,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QAEnC,GAAG,CAAC,IAAI,CAAC,iBAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAG,EAAE,EAAE,CAAC,CAAC;QACvD,GAAG,CAAC,KAAK,CAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAG,EAAE,KAAK,CAAC,CAAC;QAE/C,OAAO,KAAK,CAAC;IACf,CAAC;IAGD;;;;OAIG;IACH,0CAAoB,GAApB,UAAqB,WAAW;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;YACzC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAEnD,IAAM,uBAAuB,GAAG;YAC9B,KAAK,EAAE,IAAI,CAAC,WAAW;YACvB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,KAAK,EAAE,IAAI,CAAC,WAAW;SACxB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,uBAAuB,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC/D,4BAA4B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEhD,GAAG,CAAC,IAAI,CAAC,iBAAe,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC1F,GAAG,CAAC,KAAK,CAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAG,EAAE,WAAW,CAAC,CAAC;QACtE,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,iDAA2B,GAA3B;QACQ,IAAA,KAAqO,IAAI,EAAjO,GAAG,UAAA,EAAgC,2BAA2B,kCAAA,EAA2B,sBAAsB,6BAAA,EAAgB,WAAW,kBAAA,EAAoB,eAAe,sBAAA,EAAwB,mBAAmB,0BAAS,CAAC;QAChP,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,OAAO;SACR;QAED,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAChD,IAAM,2BAA2B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACtE,IAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAChD,IAAM,2BAA2B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACtE,IAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,IAAM,0BAA0B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACpE,IAAM,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC;QAE7C,SAAS,mBAAmB,CAAC,SAAS;YACpC,IAAM,sBAAsB,GAAG;gBAC7B,KAAK,EAAE,2BAA2B;gBAClC,IAAI,EAAE,0BAA0B;gBAChC,KAAK,EAAE,2BAA2B;aACnC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAElB,IAAM,WAAW,GAAG,IAAI,sBAAsB,CAAC,SAAS,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAEvC,IAAI,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YAC1C,IAAI,YAAY,EAAE;gBAChB,wBAAwB,CAAC,SAAS,CAAC,CAAC;aACrC;YAED,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE;gBAC3D,IAAI,YAAY,KAAK,SAAS,CAAC,YAAY,EAAE;oBAC3C,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;oBACtC,IAAI,YAAY,EAAE;wBAChB,wBAAwB,CAAC,SAAS,CAAC,CAAC;wBACpC,OAAO;qBACR;oBACD,0BAA0B,CAAC,SAAS,CAAC,CAAC;iBACvC;YACH,CAAC,CAAC,CAAC;YACH,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QACxF,CAAC;QAED,SAAS,qBAAqB,CAAC,SAAS;YACtC,IAAI,SAAS,CAAC,YAAY,EAAE;gBAC1B,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;aACrC;YACD,IAAM,OAAO,GAAG,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC7E,IAAI,OAAO,EAAE;gBACX,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC7C,IAAI,CAAC,oCAAoC,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACjE;YACD,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACnD,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;aAC3C;QACH,CAAC;QAED,SAAS,wBAAwB,CAAC,SAAS;YACjC,IAAA,SAAS,GAAuD,SAAS,UAAhE,EAAE,IAAI,GAAiD,SAAS,KAA1D,EAAE,IAAI,GAA2C,SAAS,KAApD,EAAE,GAAG,GAAsC,SAAS,IAA/C,EAAE,gBAAgB,GAAoB,SAAS,iBAA7B,EAAE,aAAa,GAAK,SAAS,cAAd,CAAe;YAClF,IAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,gBAAgB;gBACvB,KAAK,EAAE,gBAAgB;gBACvB,IAAI,EAAE,eAAe;aACtB,CAAC,IAAI,CAAC,CAAC;YAER,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEzC,0EAA0E;YAC1E,yEAAyE;YACzE,2BAA2B;YAC3B,IAAI,CAAC,WAAW,IAAI,IAAI,KAAK,gBAAgB,CAAC,IAAI,EAAE;gBAClD,OAAO;aACR;YAED,IAAM,OAAO,GAAG,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,2BAA2B,6BAAA,EAAE,sBAAsB,wBAAA,EAAE,eAAe,iBAAA,EAAE,mBAAmB,qBAAA,EAAE,CAAC;YACzH,IAAI,WAAW,EAAE;gBACf,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;aACnC;YACD,IAAM,WAAW,GAAG,UAAA,WAAW,IAAI,OAAA,oBAAoB,CAAC,6BAA6B,CAAC,GAAG,EAAE,WAAW,CAAC,EAApE,CAAoE,CAAC;YACxG,IAAM,aAAa,GAAG,UAAA,UAAU;gBAC9B,IAAI,SAAS,CAAC,YAAY,EAAE;oBAC1B,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;iBAC7D;YACH,CAAC,CAAC;YACF,IAAM,KAAK,GAAG,IAAI,KAAK,MAAM;gBAC3B,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,EAAE,gBAAgB,EAAE,OAAO,CAAC;gBACjD,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,EAAE,gBAAgB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAE1G,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,0BAA0B,CAAC,SAAS;YACrC,IAAA,KAAA,OAAc,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,UAAC,EAAS;oBAAT,KAAA,aAAS,EAAN,KAAK,QAAA;gBAAM,OAAA,KAAK,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG;YAA3B,CAA2B,CAAC,IAAA,EAAhG,EAAE,QAAA,EAAE,KAAK,QAAuF,CAAC;YACxG,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACnD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;aAC3C;QACH,CAAC;QAED,oBAAoB,CAAC,EAAE,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC3D,oBAAoB,CAAC,EAAE,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;QAE/D,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEzD,oBAAoB,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;YACjE,IAAI,KAAK,KAAK,cAAc,EAAE;gBAC5B,GAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBACtC,oBAAoB,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBAClE,oBAAoB,CAAC,cAAc,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;gBACvE,oBAAoB,CAAC,cAAc,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;aAC5E;iBAAM,IAAI,KAAK,KAAK,WAAW,EAAE;gBAChC,sEAAsE;gBACtE,wEAAwE;gBACxE,+BAA+B;gBAC/B,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAExB,8EAA8E;gBAC9E,6DAA6D;gBAC7D,UAAU,CAAC,cAAM,OAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAxB,CAAwB,EAAE,CAAC,CAAC,CAAC;aAE/C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,kCAAY,GAAZ,UAAa,KAAK,EAAE,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAExB,IAAM,YAAY,GAAG;YACnB,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,IAAI,EAAE,IAAI,CAAC,WAAW;SACvB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACd,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAExB,IAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QACnE,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;YAClC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,IAAI,CAAC,eAAa,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAG,EAAE,EAAE,CAAC,CAAC;QACrD,GAAG,CAAC,KAAK,CAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAG,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,6CAAuB,GAAvB,UAAwB,WAAW;QACjC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAM,uBAAuB,GAAG;YAC9B,KAAK,EAAE,IAAI,CAAC,WAAW;YACvB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,KAAK,EAAE,IAAI,CAAC,WAAW;SACxB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,uBAAuB,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAErD,IAAM,UAAU,GAAG,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QAChG,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;YAClC,WAAW,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,IAAI,CAAC,eAAa,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxF,GAAG,CAAC,KAAK,CAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAG,EAAE,WAAW,CAAC,CAAC;QACtE,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IACH,kBAAC;AAAD,CAAC,AAlaD,CAA0B,YAAY,GAkarC;AAED;;;;;GAKG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;;;GAOG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;;;;;;;GAWG;AAEH;;;;;GAKG;AACH,SAAS,eAAe,CAAC,MAAM;IAC7B,IAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAC7D,IAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAA5B,CAA4B,CAAC,CAAC;IAC1F,IAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAA5B,CAA4B,CAAC,CAAC;IAC1F,IAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EAA3B,CAA2B,CAAC,CAAC;IAExF,OAAO;QACL,WAAW,EAAE,kBAAkB;QAC/B,UAAU,EAAE,iBAAiB;QAC7B,MAAM,EAAE,aAAa;QACrB,WAAW,EAAE,kBAAkB;KAChC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,iCAAiC,CAAC,WAAW,EAAE,SAAS;IAC/D,IAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC;IAE7B,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,EAAE;QACxC,OAAO;KACR;IAED,gEAAgE;IAChE,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;QACtD,GAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC3C,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACrC,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,GAAG,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAC7C,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAEvD,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC/B,IAAM,UAAU,GAAG,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACnE,IAAI,KAAK,IAAI,UAAU,EAAE;oBACvB,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;wBAClC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,oFAAoF;YACpF,uCAAuC;YACvC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,cAAc;gBACrC,IAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;gBACzD,IAAM,UAAU,GAAG,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;gBAC5E,IAAI,KAAK,IAAI,UAAU,EAAE;oBACvB,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;wBAClC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;YAEH,WAAW,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YAE1C,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,WAAW;gBACpC,WAAW,CAAC,gCAAgC,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC;qBACnE,OAAO,CAAC,UAAC,SAAS,EAAE,KAAK;oBACxB,WAAW,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YACH,WAAW,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;SACtD;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;IAC/C,IAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;IAEvC,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,EAAE;QACxC,OAAO;KACR;IAED,WAAW,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,UAAA,SAAS;QAC7C,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAChC,IAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtC,oBAAoB,CAAC,GAAG,CAAC,UAAU,EAAE;YACnC,IAAM,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACjE,OAAO,WAAW,CAAC,IAAI,OAAhB,WAAW,2BAAS,IAAI,IAAE;QACnC,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,oBAAoB,CAAC,CAAC;AAClE,CAAC;AAED;;;;;GAKG;AACH,SAAS,4BAA4B,CAAC,WAAW,EAAE,WAAW;IAC5D,IAAM,0BAA0B,GAAG,IAAI,GAAG,EAAE,CAAC;IAE7C,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,EAAE;QACxC,OAAO;KACR;IAED,WAAW,CAAC,0BAA0B,EAAE,CAAC,OAAO,CAAC,UAAC,EAAoC;YAApC,KAAA,aAAoC,EAAnC,gBAAgB,QAAA,EAAE,gBAAgB,QAAA;QACnF,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,EAAE;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YACvD,WAAW,CAAC,IAAI,OAAhB,WAAW,+BAAM,gBAAgB,UAAK,IAAI,KAAE,WAAW,IAAE;QAC3D,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,WAAW,CAAC,gCAAgC,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,0BAA0B,CAAC,CAAC;AACrG,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC"}