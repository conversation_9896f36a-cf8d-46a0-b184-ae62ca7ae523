{"version": 3, "file": "getCombinedConnectionStats.js", "sourceRoot": "", "sources": ["../../lib/preflight/getCombinedConnectionStats.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAS,aAAa,CAAC,MAAsB,EAAE,QAAgB,EAAE,IAAc,EAAE,WAAqB;IACpG,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,MAAM,CAAC,OAAO,CAAC,UAAA,IAAI;QACjB,IACE,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YACpC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9B;IACH,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAsB,0BAA0B,CAAC,EAA0F;QAAxF,SAAS,eAAA,EAAE,UAAU,gBAAA;;;;;wBAC5B,qBAAM,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,QAAQ,EAAE,EAAb,CAAa,CAAC,CAAC,EAAA;;oBAAvG,KAAA,sBAAoC,SAAmE,KAAA,EAAtG,cAAc,QAAA,EAAE,eAAe,QAAA;oBAEhC,UAAU,GAAG,aAAa,CAAC,eAAe,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;oBACrF,SAAS,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAKtD,MAAM,GAAG,aAAa,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAc,EAAE,CAAC,CAAC,CAAC;oBAKlH,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,iBAAiB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,CAAC;oBAC5H,WAAW,GAAG,aAAa,CAAC,eAAe,EAAE,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,CAAC;oBAI5H,kBAAkB,GAAG,aAAa,CAAC,cAAc,EAAE,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAc,EAAE,CAAC,CAAC,CAAC;oBAGpJ,oBAAoB,GAAG,aAAa,CAAC,eAAe,EAAE,sBAAsB,EAAE,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAd,CAAc,EAAE,CAAC,CAAC,CAAC;oBAC1I,aAAa,GAAG,CAAC,oBAAoB,IAAI,kBAAkB,CAAC,GAAG,IAAI,CAAC;oBAEpE,SAAS,GAAG,aAAa,CAAC,cAAc,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,CAAC;oBAC1G,aAAa,GAAG,aAAa,CAAC,eAAe,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,CAAC;oBAEnH,6BAA6B,GAAG,kCAAkC,CAAC,eAAe,CAAC,CAAC;oBAEpF,iBAAiB,GAA2B,EAAE,CAAC;oBACrD,eAAe,CAAC,OAAO,CAAC,UAAA,IAAI;wBAC1B,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;4BACvE,iBAAiB,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;yBAC1D;oBACH,CAAC,CAAC,CAAC;oBACH,sBAAO,EAAE,SAAS,WAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,WAAW,aAAA,EAAE,aAAa,eAAA,EAAE,SAAS,WAAA,EAAE,aAAa,eAAA,EAAE,6BAA6B,+BAAA,EAAE,iBAAiB,mBAAA,EAAE,EAAC;;;;CAC/I;AArCD,gEAqCC;AAGD,SAAS,0BAA0B,CAAC,KAAU;IAC5C,IAAM,8BAA8B,GAAG;QACrC,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE;QACtC,EAAE,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE;QACxC,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;QACxD,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;QAChE,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE;QACnC,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC3D,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;QAC9B,EAAE,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE;KACzC,CAAC;IAEF,OAAO,8BAA8B,CAAC,MAAM,CAAC,UAAS,MAAW,EAAE,OAAO;QACxE,IAAI,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACvD;QACD,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,KAAK,EAAZ,CAAY,CAAC,CAAC;QAClD,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE;YAC7C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;SAClC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAAS,kCAAkC,CAAC,KAAqB;IAC/D,IAAI,uBAAuB,GAAe,IAAI,CAAC;IAC/C,IAAM,cAAc,GAA+B,EAAE,CAAC;IACtD,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC7D,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;SACxD;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;YACzC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3B;IACH,CAAC,CAAC,CAAC;IAEH,IAAM,6BAA6B,GAAG,cAAc,CAAC,IAAI,CAAC,UAAA,IAAI;QAC5D,UAAU;QACV,OAAA,IAAI,CAAC,QAAQ;YACb,qBAAqB;YACrB,CAAC,uBAAuB,IAAI,IAAI,CAAC,EAAE,KAAK,uBAAuB,CAAC;IAFhE,CAEgE,CACjE,CAAC;IAEF,IAAI,CAAC,6BAA6B,EAAE;QAClC,OAAO,IAAI,CAAC;KACb;IAED,IAAM,wBAAwB,GAAG,6BAAyD,CAAC;IAC3F,IAAM,yBAAyB,GAAG,KAAK,CAAC,GAAG,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IACvF,IAAM,0BAA0B,GAAG,KAAK,CAAC,GAAG,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IACzF,IAAI,CAAC,yBAAyB,IAAI,CAAC,0BAA0B,EAAE;QAC7D,OAAO,IAAI,CAAC;KACb;IAED,OAAO;QACL,cAAc,EAAE,0BAA0B,CAAC,yBAAyB,CAAC;QACrE,eAAe,EAAE,0BAA0B,CAAC,0BAA0B,CAAC;KACxE,CAAC;AACJ,CAAC"}