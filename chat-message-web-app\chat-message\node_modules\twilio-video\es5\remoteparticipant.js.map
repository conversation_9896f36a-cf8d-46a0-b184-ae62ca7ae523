{"version": 3, "file": "remoteparticipant.js", "sourceRoot": "", "sources": ["../lib/remoteparticipant.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAE7C;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH;IAAgC,qCAAW;IACzC;;;;OAIG;IACH,2BAAY,SAAS,EAAE,OAAO;QAA9B,YACE,kBAAM,SAAS,EAAE,OAAO,CAAC,SAG1B;QAFC,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,KAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;;IAChE,CAAC;IAED,oCAAQ,GAAR;QACE,OAAO,yBAAuB,IAAI,CAAC,WAAW,IAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAK,IAAI,CAAC,GAAK,CAAC,CAAC,CAAC,EAAE,OAAG,CAAC;IACtF,CAAC;IAED;;;;;;OAMG;IACH,qCAAS,GAAT,UAAU,WAAW,EAAE,WAAW,EAAE,EAAE;QACpC,IAAI,CAAC,iBAAM,SAAS,YAAC,WAAW,EAAE,EAAE,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC;SACb;QACD,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACvD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,gDAAoB,GAApB,UAAqB,WAAW;QAC9B,IAAM,gBAAgB,GAAG,iBAAM,oBAAoB,YAAC,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;QAC9C,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IACD;;OAEG;IACH,sDAA0B,GAA1B;QACE,8CACK,iBAAM,0BAA0B,WAAE;YACrC,CAAC,oBAAoB,EAAE,yBAAyB,CAAC;YACjD,CAAC,eAAe,EAAE,eAAe,CAAC;YAClC,CAAC,cAAc,EAAE,cAAc,CAAC;YAChC,CAAC,wBAAwB,EAAE,6BAA6B,CAAC;YACzD,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;YACxC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;WACtC;IACJ,CAAC;IAED;;OAEG;IACH,8CAAkB,GAAlB;QAAA,iBAQC;QAPC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,WAAW;YAC7B,IAAI,WAAW,CAAC,YAAY,EAAE;gBAC5B,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;gBAChC,WAAW,CAAC,YAAY,EAAE,CAAC;gBAC3B,KAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;aACpD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,wCAAY,GAAZ,UAAa,WAAW,EAAE,WAAW,EAAE,EAAE;QACvC,IAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,iBAAiB,EAAE;YACtB,OAAO,IAAI,CAAC;SACb;QAED,iBAAM,YAAY,YAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QAC1C,WAAW,CAAC,YAAY,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAC/D,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,mDAAuB,GAAvB,UAAwB,WAAW;QACjC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAM,kBAAkB,GAAG,iBAAM,uBAAuB,YAAC,WAAW,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;QAClD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACH,wBAAC;AAAD,CAAC,AA1GD,CAAgC,WAAW,GA0G1C;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;;;GAOG;AAEH;;;;;;;;;;;;;;;;;;;;GAoBG;AAEH;;;;GAIG;AAEH;;;;;;GAMG;AAEH;;;;;;;;GAQG;AAEH;;;;;;;;GAQG;AAEH;;;;;;GAMG;AAEH;;;;;;GAMG;AAEH;;;;;GAKG;AAEH;;;;;;GAMG;AAEH,MAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC"}