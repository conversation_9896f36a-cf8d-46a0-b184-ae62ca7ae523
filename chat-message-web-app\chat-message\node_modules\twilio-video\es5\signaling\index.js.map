{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/signaling/index.js"], "names": [], "mappings": "AAAA,gCAAgC;AAChC,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,oBAAoB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACtD,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;EAoBE;AAEF,IAAM,MAAM,GAAG;IACb,MAAM,EAAE;QACN,SAAS;KACV;IACD,OAAO,EAAE;QACP,QAAQ;QACR,MAAM;KACP;IACD,IAAI,EAAE;QACJ,QAAQ;QACR,SAAS;KACV;IACD,OAAO,EAAE;QACP,QAAQ;QACR,MAAM;KACP;CACF,CAAC;AAEF;;;GAGG;AACH;IAAwB,6BAAY;IAClC;;OAEG;IACH;eACE,kBAAM,QAAQ,EAAE,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,uEAAuE;IACvE,0BAAM,GAAN,UAAO,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,uEAAuE;IACvE,4BAAQ,GAAR,UACE,gBAAgB,EAChB,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,OAAO;QAEP,gBAAgB,CAAC,OAAO,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC;QACvE,IAAM,GAAG,GAAG,oCAAoC,CAAC;QACjD,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,gBAAgB,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACnF,OAAO,CAAC,MAAM,GAAG,SAAS,MAAM,KAAI,CAAC,CAAC;QACtC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,uEAAuE;IACvE,yBAAK,GAAL,UAAM,GAAG;QACP,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,yBAAK,GAAL;QAAA,iBAWC;QAVC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,GAAG;YAC9B,QAAQ,KAAI,CAAC,KAAK,EAAE;gBAClB,KAAK,QAAQ;oBACX,OAAO,KAAI,CAAC;gBACd,KAAK,MAAM;oBACT,OAAO,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC1B;oBACE,MAAM,IAAI,KAAK,CAAC,kCAA+B,KAAI,CAAC,KAAK,OAAG,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,2BAAO,GAAP,UACE,gBAAgB,EAChB,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,OAAO;QAEP,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,UAAU,CAAC,GAAG;YACpD,QAAQ,IAAI,CAAC,KAAK,EAAE;gBAClB,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC1D,KAAK,MAAM;oBACT,uEAAuE;oBACvE,qDAAqD;oBACrD,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;oBAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,KAAK,EAAE,kBAAkB,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;gBAC9F;oBACE,MAAM,IAAI,KAAK,CAAC,kCAA+B,IAAI,CAAC,KAAK,OAAG,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,mDAA+B,GAA/B;QACE,OAAO,IAAI,oBAAoB,EAAE,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,wBAAI,GAAJ;QAAA,iBAWC;QAVC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAA,GAAG;YAC7B,QAAQ,KAAI,CAAC,KAAK,EAAE;gBAClB,KAAK,QAAQ;oBACX,OAAO,KAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzB,KAAK,MAAM;oBACT,OAAO,KAAI,CAAC;gBACd;oBACE,MAAM,IAAI,KAAK,CAAC,kCAA+B,KAAI,CAAC,KAAK,OAAG,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACH,gBAAC;AAAD,CAAC,AAvHD,CAAwB,YAAY,GAuHnC;AAED,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC"}