{"version": 3, "file": "participant.js", "sourceRoot": "", "sources": ["../../lib/signaling/participant.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAChD,IAAM,mBAAmB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAEpE;;;;;;;;;;;;;;;EAeE;AAEF,IAAM,MAAM,GAAG;IACb,UAAU,EAAE;QACV,WAAW;KACZ;IACD,SAAS,EAAE;QACT,cAAc;QACd,cAAc;KACf;IACD,YAAY,EAAE;QACZ,WAAW;QACX,cAAc;KACf;IACD,YAAY,EAAE,EAAE;CACjB,CAAC;AAEF;;;;;;;;;;GAUG;AACH;IAAmC,wCAAY;IAC7C;;OAEG;IACH;QAAA,YACE,kBAAM,YAAY,EAAE,MAAM,CAAC,SAoC5B;QAlCC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACxB,CAAC;aACF;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,IAAI,CAAC;gBACnB,CAAC;aACF;YACD,MAAM,EAAE;gBACN,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;SACF,CAAC,CAAC;;IACL,CAAC;IAMD,sBAAI,qDAAmB;QAJvB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC,CAAC;;;OAAA;IAMD,sBAAI,qDAAmB;QAJvB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC,CAAC;;;OAAA;IAED;;;;;;OAMG;IACH,uCAAQ,GAAR,UAAS,KAAK;QACZ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,yCAAU,GAAV;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACH,0CAAW,GAAX,UAAY,KAAK;QACf,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,SAAS,IAAI,IAAI,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,qDAAsB,GAAtB,UAAuB,mBAAmB,EAAE,oBAAoB;QAC9D,IAAI,IAAI,CAAC,oBAAoB,KAAK,mBAAmB,EAAE;YACrD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;YAChD,IAAI,CAAC,oBAAoB,GAAG,oBAAoB;mBAC7C,CAAC,oBAAoB,CAAC,KAAK,IAAI,oBAAoB,CAAC,KAAK,CAAC;gBAC3D,CAAC,CAAC,IAAI,mBAAmB,CAAC,oBAAoB,CAAC;gBAC/C,CAAC,CAAC,IAAI,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;;;;OAKG;IACH,sCAAO,GAAP,UAAQ,GAAG,EAAE,QAAQ;QACnB,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YAChE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;aACjB;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;aAC3B;YACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC1B,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,2CAAY,GAAZ;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;YAC7D,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACH,2BAAC;AAAD,CAAC,AAnJD,CAAmC,YAAY,GAmJ9C;AAED;;GAEG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC"}