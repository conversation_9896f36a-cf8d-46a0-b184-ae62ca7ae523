{"version": 3, "file": "room.js", "sourceRoot": "", "sources": ["../../lib/signaling/room.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,yBAAyB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AACzD,IAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAChD,IAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC1C,IAAA,cAAc,GAAK,OAAO,CAAC,SAAS,CAAC,eAAvB,CAAwB;AACtC,IAAA,iBAAiB,GAAK,OAAO,CAAC,mBAAmB,CAAC,kBAAjC,CAAkC;AAC3D,IAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAE7B,IAAA,KAIF,OAAO,CAAC,6BAA6B,CAAC,EAHxC,oBAAoB,0BAAA,EACpB,6BAA6B,mCAAA,EAC7B,oCAAoC,0CACI,CAAC;AAE3C,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;;;;;;;;;;;;;;EAiBE;AAEF,IAAM,MAAM,GAAG;IACb,SAAS,EAAE;QACT,cAAc;QACd,cAAc;KACf;IACD,YAAY,EAAE;QACZ,WAAW;QACX,cAAc;KACf;IACD,YAAY,EAAE,EAAE;CACjB,CAAC;AAEF;;;;;;;;;;;;;;;;;;GAkBG;AACH;IAA4B,iCAAY;IACtC;;;;;;OAMG;IACH,uBAAY,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO;QAAhD,iBAkFC;QAjFC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,iBAAiB;YAC3B,kBAAkB,EAAE,yBAAyB;YAC7C,OAAO,EAAE,cAAc;SACxB,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEnD,QAAA,kBAAM,WAAW,EAAE,MAAM,CAAC,SAAC;QAE3B,IAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAEtD,IAAM,cAAc,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC;YACzC,KAAI,CAAC,WAAW,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5C,CAAC,EAAE,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAElC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,WAAW,EAAE;gBACX,KAAK,EAAE,UAAU,EAAE;aACpB;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,OAAO,CAAC,GAAG;oBAChB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAI,CAAC;oBACxC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC;aAC5D;YACD,8BAA8B,EAAE;gBAC9B,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,OAAO;aACf;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,kBAAkB,EAAE;gBAClB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,gBAAgB,EAAE;gBAChB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,gBAAgB;aACxB;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,SAAS,EAAE;gBACT,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,kBAAkB,EAAE;aAChC;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,GAAG;aACX;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,wBAAwB,EAAE;YAChC,IAAI,KAAI,CAAC,eAAe,KAAK,QAAQ;mBAChC,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAI,CAAC,kBAAkB,CAAC,EAAE;gBAClE,KAAI,CAAC,WAAW,CAAC,IAAI,6BAA6B,EAAE,CAAC,CAAC;aACvD;QACH,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,2BAA2B,EAAE,cAAM,OAAA,gBAAgB,CAAC,KAAI,CAAC,EAAtB,CAAsB,CAAC,CAAC;QACnE,KAAI,CAAC,EAAE,CAAC,iCAAiC,EAAE,cAAM,OAAA,gBAAgB,CAAC,KAAI,CAAC,EAAtB,CAAsB,CAAC,CAAC;QAEzE,2EAA2E;QAC3E,0EAA0E;QAC1E,0FAA0F;QAC1F,kBAAkB;QAClB,UAAU,CAAC,cAAM,OAAA,gBAAgB,CAAC,KAAI,CAAC,EAAtB,CAAsB,CAAC,CAAC;;IAC3C,CAAC;IAED;;;;;OAKG;IACH,mCAAW,GAAX,UAAY,KAAK;QACf,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gCAAQ,GAAR;QACE,OAAO,qBAAmB,IAAI,CAAC,WAAW,WAAK,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,OAAG,CAAC;IAC/G,CAAC;IAED;;;;OAIG;IACH,0CAAkB,GAAlB,UAAmB,WAAW;QAC5B,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,WAAW,CAAC,KAAK,KAAK,cAAc,EAAE;YACxC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAEpD,WAAW,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;YACxD,IAAI,KAAK,KAAK,cAAc,EAAE;gBAC5B,WAAW,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBACzD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;aACnD;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,0CAAkB,GAAlB,UAAmB,kBAAkB;QACnC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACtC,CAAC;IACH,oBAAC;AAAD,CAAC,AA9JD,CAA4B,YAAY,GA8JvC;AAED;;GAEG;AAEH;;GAEG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;GAEG;AAEH;;GAEG;AAEH;;;GAGG;AACH,SAAS,gBAAgB,CAAC,aAAa;IACrC,IAAI,aAAa,CAAC,KAAK,KAAK,cAAc,IAAI,aAAa,CAAC,wBAAwB,KAAK,cAAc,EAAE;QACvG,aAAa,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACtC,OAAO;KACR;IAED,IAAI,QAAQ,CAAC;IAEb,IAAI,aAAa,CAAC,wBAAwB,KAAK,cAAc,EAAE;QAC7D,QAAQ,GAAG,aAAa,CAAC,wBAAwB,CAAC;KACnD;SAAM,IAAI,aAAa,CAAC,kBAAkB,KAAK,QAAQ,EAAE;QACxD,aAAa,CAAC,8BAA8B,GAAG,IAAI,CAAC;QACpD,QAAQ,GAAG,cAAc,CAAC;KAC3B;SAAM,IAAI,aAAa,CAAC,kBAAkB,KAAK,KAAK,IAAI,aAAa,CAAC,kBAAkB,KAAK,UAAU,EAAE;QACxG,QAAQ,GAAG,aAAa,CAAC,8BAA8B,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC;KACxF;SAAM;QACL,aAAa,CAAC,8BAA8B,GAAG,KAAK,CAAC;QACrD,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACxC,aAAa,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACtC,QAAQ,GAAG,WAAW,CAAC;KACxB;IAED,IAAI,QAAQ,KAAK,aAAa,CAAC,KAAK,EAAE;QACpC,OAAO;KACR;IAED,IAAI,QAAQ,KAAK,cAAc,EAAE;QAC/B,aAAa,CAAC,kBAAkB,GAAG,aAAa,CAAC,wBAAwB,KAAK,cAAc;YAC1F,CAAC,CAAC,IAAI,oCAAoC,EAAE;YAC5C,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;QAC/B,aAAa,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACtC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC;KAC3E;SAAM;QACL,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;KACjC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC"}