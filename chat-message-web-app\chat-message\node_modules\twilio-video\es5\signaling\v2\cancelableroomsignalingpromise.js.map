{"version": 3, "file": "cancelableroomsignalingpromise.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/cancelableroomsignalingpromise.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,IAAM,iBAAiB,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;AAClE,IAAM,4BAA4B,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACxE,IAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAE1D,IAAA,KAGF,OAAO,CAAC,gCAAgC,CAAC,EAF3C,oCAAoC,0CAAA,EACpC,oCAAoC,0CACO,CAAC;AAExC,IAAA,KAA6C,OAAO,CAAC,YAAY,CAAC,EAAhE,OAAO,aAAA,EAAE,6BAA6B,mCAA0B,CAAC;AAEzE,SAAS,oCAAoC,CAAC,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,eAAe,EAAE,OAAO;IAC3H,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;QACtB,qBAAqB,EAAE,4BAA4B;QACnD,MAAM,EAAE,aAAa;QACrB,SAAS,EAAE,gBAAgB;KAC5B,EAAE,OAAO,CAAC,CAAC;IAEZ,IAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,IAAK,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,KAAK,IAAI,CAAC;IACnG,IAAA,qBAAqB,GAAyC,OAAO,sBAAhD,EAAE,MAAM,GAAiC,OAAO,OAAxC,EAAE,SAAS,GAAsB,OAAO,UAA7B,EAAE,UAAU,GAAU,OAAO,WAAjB,EAAE,GAAG,GAAK,OAAO,IAAZ,CAAa;IAC9E,IAAM,qBAAqB,GAAG,IAAI,qBAAqB,CAAC,kBAAkB,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IACtG,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAA,OAAO,IAAI,OAAA,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAA1B,CAA0B,CAAC,CAAC;IAC7F,qBAAqB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IAEpD,IAAM,iBAAiB,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;IAEhD,IAAI,SAAS,CAAC;IAEd,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,UAAC,OAAO,EAAE,MAAM,EAAE,UAAU;QAC1E,IAAM,MAAM,GAAG,UAAA,UAAU;YACvB,IAAI,UAAU,EAAE,EAAE;gBAChB,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;aAC1C;YACD,GAAG,CAAC,KAAK,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;YAC1C,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YAChC,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEhD,OAAO,qBAAqB,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC;gBACjD,IAAI,UAAU,EAAE,EAAE;oBAChB,MAAM,CAAC,iBAAiB,CAAC,CAAC;oBAC1B,MAAM,iBAAiB,CAAC;iBACzB;gBACD,GAAG,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACzC,sEAAsE;gBACtE,oEAAoE;gBACpE,qEAAqE;gBACrE,yCAAyC;gBACzC,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;gBACZ,GAAG,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,KAAK,CAAC,CAAC;gBACd,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAGA,IAAA,qBAAqB,GAcnB,OAAO,sBAdY,EACrB,gBAAgB,GAad,OAAO,iBAbO,EAChB,eAAe,GAYb,OAAO,gBAZM,EACf,qBAAqB,GAWnB,OAAO,sBAXY,EACrB,WAAW,GAUT,OAAO,YAVE,EACX,aAAa,GASX,OAAO,cATI,EACb,UAAU,GAQR,OAAO,WARC,EACV,QAAQ,GAON,OAAO,SAPD,EACR,IAAI,GAMF,OAAO,KANL,EACJ,cAAc,GAKZ,OAAO,eALK,EACd,cAAc,GAIZ,OAAO,eAJK,EACd,cAAc,GAGZ,OAAO,eAHK,EACd,KAAK,GAEH,OAAO,MAFJ,EACL,YAAY,GACV,OAAO,aADG,CACF;QAEZ,uCAAuC;QACvC,kCAAkC;QAClC,IAAM,aAAa,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACzC,IAAM,cAAc,GAAG,CAAC,CAAC,gBAAgB,CAAC;QAC1C,IAAM,WAAW,GAAG,CAAC,CAAC,gBAAgB;YACpC,CAAC,OAAO,CAAC,2BAA2B,KAAK,UAAU,IAAI,OAAO,CAAC,sBAAsB,KAAK,UAAU,CAAC,CAAC;QAExG,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;YACrC,iBAAiB,mBAAA;YACjB,qBAAqB,uBAAA;YACrB,eAAe,iBAAA;YACf,qBAAqB,uBAAA;YACrB,WAAW,aAAA;YACX,aAAa,eAAA;YACb,UAAU,YAAA;YACV,QAAQ,UAAA;YACR,cAAc,gBAAA;YACd,cAAc,gBAAA;YACd,cAAc,gBAAA;YACd,UAAU,YAAA;YACV,MAAM,QAAA;YACN,KAAK,OAAA;YACL,WAAW,aAAA;YACX,YAAY,cAAA;YACZ,aAAa,eAAA;YACb,cAAc,gBAAA;SACf,EAAE,gBAAgB,CAAC,CAAC,CAAC;YACpB,gBAAgB,kBAAA;SACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAER,SAAS,GAAG,IAAI,SAAS,CACvB,IAAI,EACJ,KAAK,EACL,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,EACR,gBAAgB,CAAC,CAAC;QAEpB,IAAM,mBAAmB,GAAG,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACnE,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAEjD,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,UAAA,YAAY;YACtC,GAAG,CAAC,KAAK,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;YAChD,IAAI,UAAU,EAAE,EAAE;gBAChB,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO;aACR;YACO,IAAa,qBAAqB,GAAK,YAAY,YAAjB,CAAkB;YAC5D,IAAI,CAAC,qBAAqB,EAAE;gBAC1B,MAAM,CAAC,IAAI,oCAAoC,EAAE,CAAC,CAAC;gBACnD,OAAO;aACR;YACD,OAAO,CAAC,IAAI,MAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,UAAC,KAAK,EAAE,KAAK;YAC1C,IAAI,KAAK,KAAK,cAAc,EAAE;gBAC5B,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM,CAAC,KAAK,IAAI,IAAI,oCAAoC,EAAE,CAAC,CAAC;aAC7D;iBAAM;gBACL,GAAG,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;aAC9C;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE;QACD,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,CAAC;SAClB;IACH,CAAC,CAAC,CAAC;IAEH,iBAAiB,CAAC,KAAK,CAAC;QACtB,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,CAAC;SAClB;QACD,qBAAqB,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,oCAAoC,CAAC"}