{"version": 3, "file": "iceconnectionmonitor.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/iceconnectionmonitor.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEP,IAAA,KAAgE,OAAO,CAAC,sBAAsB,CAAC,EAA7F,4BAA4B,kCAAA,EAAE,2BAA2B,iCAAoC,CAAC;AAEtG;;;GAGG;AACH;IACE;;;;OAIG;IACH,8BAAY,cAAc,EAAE,OAAO;QACjC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,qBAAqB,EAAE,4BAA4B;YACnD,qBAAqB,EAAE,2BAA2B;SACnD,EAAE,OAAO,CAAC,CAAC;QAEZ,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAED,iDAAkB,GAAlB,UAAmB,KAAK;QACtB,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9C,IAAM,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,SAAS,EAAhD,CAAgD,CAAC,CAAC;QAClG,8FAA8F;QAC9F,qGAAqG;QACrG,OAAO,eAAe,IAAI;YACxB,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,qDAAsB,GAAtB;QAAA,iBAIC;QAHC,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAA9B,CAA8B,CAAC,CAAC,KAAK,CAAC;YACzF,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,0DAA2B,GAA3B,UAA4B,QAAQ;QAApC,iBAuBC;QAtBC,IAAI,QAAQ,IAAI,IAAI,CAAC,4BAA4B,KAAK,IAAI,EAAE;YAC1D,oBAAoB;YACpB,IAAI,CAAC,4BAA4B,GAAG;gBAClC,IAAI,KAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,cAAc,EAAE;oBAC9D,2CAA2C;oBAC3C,QAAQ,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;YACF,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE;gBACzC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;aACtG;iBAAM;gBACL,IAAI,CAAC,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC,4BAA4B,CAAC;aACrF;SACF;aAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACzD,sBAAsB;YACtB,IAAI,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;gBAC5C,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,0BAA0B,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;aACzG;iBAAM;gBACL,IAAI,CAAC,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC;aACxD;YACD,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;SAC1C;IACH,CAAC;IAED;;;;;OAKG;IACH,oCAAK,GAAL,UAAM,uBAAuB;QAA7B,iBA4BC;QA3BC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;YACxB,KAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,UAAA,QAAQ;gBACzC,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;iBACR;gBAED,kHAAkH;gBAClH,0GAA0G;gBAC1G,gFAAgF;gBAChF,IAAI,CAAC,KAAI,CAAC,aAAa,IAAI,KAAI,CAAC,aAAa,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa,EAAE;oBACtF,KAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;oBAC9B,uDAAuD;oBACvD,KAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;iBACxC;gBAED,IAAI,QAAQ,CAAC,SAAS,GAAG,KAAI,CAAC,aAAa,CAAC,SAAS,IAAI,KAAI,CAAC,sBAAsB,EAAE;oBACpF,uBAAuB;oBACvB,IAAI,KAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,cAAc,EAAE;wBAC9D,uBAAuB,EAAE,CAAC;qBAC3B;yBAAM,IAAI,KAAI,CAAC,4BAA4B,KAAK,IAAI,EAAE;wBACrD,KAAI,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,CAAC;qBAC3D;iBACF;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,mCAAI,GAAJ;QACE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;IACH,CAAC;IACH,2BAAC;AAAD,CAAC,AAtID,IAsIC;AAED,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC"}