{"version": 3, "file": "localparticipant.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/localparticipant.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,yBAAyB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACjE,IAAM,uBAAuB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC3D,IAAA,iBAAiB,GAAK,OAAO,CAAC,sBAAsB,CAAC,kBAApC,CAAqC;AAC9D,IAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAChC,IAAA,KAAkC,OAAO,CAAC,YAAY,CAAC,EAArD,cAAc,oBAAA,EAAE,WAAW,iBAA0B,CAAC;AAE9D;;;;;;GAMG;AACH;IAAiC,sCAAyB;IACxD;;;;;OAKG;IACH,4BAAY,kBAAkB,EAAE,2BAA2B,EAAE,OAAO;QAApE,iBA8EC;QA7EC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,iBAAiB;YAC3B,uBAAuB,yBAAA;SACxB,EAAE,OAAO,CAAC,CAAC;QAEZ,QAAA,iBAAO,SAAC;QAER,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEnD,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,yBAAyB,EAAE;gBACzB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,kBAAkB;aAC1B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,OAAO,CAAC,uBAAuB;aACvC;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,OAAO,CAAC,GAAG;oBAChB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAI,CAAC;oBACxC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC;aAC5D;YACD,kBAAkB,EAAE;gBAClB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;aACT;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;aACT;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACf;YACD,gBAAgB,EAAE;gBAChB,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;gBAChC,CAAC;aACF;YACD,wBAAwB,EAAE;gBACxB,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,yBAAyB,CAAC;gBACxC,CAAC;aACF;YACD,2BAA2B,EAAE;gBAC3B,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,2BAA2B;aACnC;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,SAAS,CAAC;gBACxB,CAAC;aACF;YACD,eAAe,EAAE;gBACf,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;gBAC/B,CAAC;aACF;SACF,CAAC,CAAC;;IACL,CAAC;IAED,qCAAQ,GAAR;QACE,OAAO,iCAA+B,IAAI,CAAC,GAAG,MAAG,CAAC;IACpD,CAAC;IAED;;;OAGG;IACH,+CAAkB,GAAlB,UAAmB,eAAe;QAChC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;SACzC;IACH,CAAC;IAED;;;OAGG;IACH,gDAAmB,GAAnB,UAAoB,gBAAgB;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,EAAE;YAC1D,8DAA8D;YAC9D,iEAAiE;YACjE,kCAAkC;YAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;IACH,CAAC;IAED;;;OAGG;IACH,+CAAkB,GAAlB,UAAmB,eAAe;QAChC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,0CAAa,GAAb;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACH,0CAAa,GAAb,UAAc,kBAAkB;QAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,mCAAM,GAAN,UAAO,SAAS;QACd,IAAI,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,QAAQ,CAAC;QAE7C,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,UAAS,gBAAgB;YAChD,IAAM,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACrE,IAAI,uBAAuB,EAAE;gBAC3B,uBAAuB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;aAClD;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8CAAiB,GAAjB,UAAkB,WAAW;QAC3B,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;QAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,WAAW;YAClD,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACnD,IAAI,MAAM,EAAE;gBACV,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;aACvC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;OAOG;IACH,kEAAqC,GAArC,UAAsC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,uBAAuB;QACxF,OAAO,IAAI,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,uBAAuB,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACrH,CAAC;IAED;;;;;;;OAOG;IACH,qCAAQ,GAAR,UAAS,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,uBAAuB;QAA7D,iBA6BC;QA5BC,iBAAM,QAAQ,YAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,uBAAuB,CAAC,CAAC;QACrE,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAGnD,IAAA,SAAS,GAEP,WAAW,UAFJ,EACT,eAAe,GACb,WAAW,gBADE,CACD;QAEhB,IAAM,OAAO,GAAG;YACd,wEAAwE;YACxE,kEAAkE;YAClE,4FAA4F;YAC5F,4CAA4C;YAC5C,IAAI,SAAS,KAAK,WAAW,CAAC,SAAS,IAAI,eAAe,KAAK,WAAW,CAAC,eAAe,EAAE;gBAC1F,KAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;gBAClC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;aAC/C;QACH,CAAC,CAAC;QAEF,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEnC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,cAAM,OAAA,WAAW,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,EAA9C,CAA8C,CAAC,CAAC;QAE7F,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,4CAAe,GAAf,UAAgB,WAAW;QACzB,IAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9D,IAAI,cAAc,EAAE;YAClB,cAAc,EAAE,CAAC;SAClB;IACH,CAAC;IAED;;;OAGG;IACH,qCAAQ,GAAR;QACE,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,QAAQ,EAAE,EAAhB,CAAgB,CAAC;SACxE,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,sCAAS,GAAT;QACE,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,wCAAW,GAAX,UAAY,WAAW;QACrB,IAAM,WAAW,GAAG,iBAAM,WAAW,YAAC,WAAW,CAAC,CAAC;QACnD,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACtD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,2DAA8B,GAA9B,UAA+B,2BAA2B;QACxD,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;OAMG;IACH,6CAAgB,GAAhB,UAAiB,QAAQ,EAAE,SAAS;QAClC,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,GAAG,KAAK,QAAQ,EAAzB,CAAyB,CAAC,CAAC;QACpG,IAAI,CAAC,cAAc,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAS,QAAQ,eAAY,CAAC,CAAC;YAC9C,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SACzC;QACD,OAAO,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IACrE,CAAC;IACH,yBAAC;AAAD,CAAC,AA9SD,CAAiC,yBAAyB,GA8SzD;AAGD;;;;GAIG;AAEH;;GAEG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;GAIG;AAEH;;GAEG;AAEH,MAAM,CAAC,OAAO,GAAG,kBAAkB,CAAC"}