{"version": 3, "file": "networkqualitymonitor.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/networkqualitymonitor.js"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AAEvC,IAAM,2BAA2B,GAAG,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAEvF;;GAEG;AACH;IAAoC,yCAAY;IAC9C;;;;OAIG;IACH,+BAAY,OAAO,EAAE,SAAS;QAA9B,YACE,iBAAO,SAaR;QAZC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,UAAU,EAAE;gBACV,KAAK,EAAE,IAAI,OAAO,EAAE;aACrB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,OAAO;aACf;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;SACF,CAAC,CAAC;QACH,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAApB,CAAoB,CAAC,CAAC;;IACtD,CAAC;IAMD,sBAAI,wCAAK;QAJT;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAC/B,CAAC;;;OAAA;IAMD,sBAAI,yCAAM;QAJV;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAChC,CAAC;;;OAAA;IAMD,sBAAI,+CAAY;QAJhB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACtC,CAAC;;;OAAA;IAED;;;OAGG;IACH,qCAAK,GAAL;QAAA,iBAkBC;QAjBC,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAM,OAAO,GAAG,UAAU,CAAC;YACzB,IAAI,KAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC7B,OAAO;aACR;YACD,IAAI,CAAC,KAAI,CAAC,CAAC,IAAI,CAAC,UAAA,OAAO;gBACrB,IAAI,KAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;oBAC7B,OAAO;iBACR;gBACD,IAAI,OAAO,CAAC,MAAM,EAAE;oBACZ,IAAA,KAAA,OAAW,OAAO,IAAA,EAAjB,MAAM,QAAW,CAAC;oBACzB,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC7B;gBACD,KAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,oCAAI,GAAJ;QACE,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IACH,4BAAC;AAAD,CAAC,AA9ED,CAAoC,YAAY,GA8E/C;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,OAAO;IACnB,IAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,gBAAgB;QAC7C,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QACxD,CAAC,CAAC,EAAE,CAAC;IAEP,IAAM,GAAG,GAAG,KAAK;SACd,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,eAAe,EAApB,CAAoB,CAAC;SACjC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,cAAc,KAAK,QAAQ,EAA9B,CAA8B,CAAC,CAAC;IAEhD,IAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,UAAA,EAAE;QAC1B,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACnC;QACD,IAAM,OAAO,GAAG,IAAI,2BAA2B,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC,CAAC;IAEH,IAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC,EAAhC,CAAgC,CAAC,CAAC;IAEzF,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,UAAA,aAAa,IAAI,OAAA,aAAa;SAC1E,MAAM,CAAC,UAAA,YAAY,IAAI,OAAA,YAAY,EAAZ,CAAY,CAAC;SACpC,GAAG,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,SAAS,EAAE,EAAlB,CAAkB,CAAC,EAF4B,CAE5B,CAAC,CAAC;AACxC,CAAC;AAED;;;GAGG;AAEH,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC"}