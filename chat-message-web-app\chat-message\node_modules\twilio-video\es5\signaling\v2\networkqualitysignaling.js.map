{"version": 3, "file": "networkqualitysignaling.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/networkqualitysignaling.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACnD,IAAM,QAAQ,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAChD,IAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE9C,IAAM,gCAAgC,GAAG,IAAI,CAAC;AAE9C;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;;GAKG;AAEH;;;;;;GAMG;AAEH;;;;;GAKG;AAEH;;GAEG;AAEH;;;;;;;;;GASG;AACH;IAAsC,2CAAc;IAClD;;;;OAIG;IACH,iCAAY,WAAW,EAAE,2BAA2B,EAAE,OAAO;QAA7D,YACE,kBAAM,WAAW,EAAE,iBAAiB,EAAE,OAAO,CAAC,SAiD/C;QA/CC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,QAAQ,EAAE;aACtB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,OAAO,CAAC;oBACjB,8CAA8C;oBAC9C,KAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;oBAC1D,KAAI,CAAC,yBAAyB,EAAE,CAAC;gBACnC,CAAC,EAAE,gCAAgC,EAAE,KAAK,CAAC;aAC5C;YACD,2BAA2B,EAAE;gBAC3B,GAAG;oBACD,OAAO;wBACL,WAAW,EAAE,2BAA2B,CAAC,KAAK;wBAC9C,iBAAiB,EAAE,2BAA2B,CAAC,MAAM;qBACtD,CAAC;gBACJ,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,SAAS;YACxB,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,UAAA,OAAO;gBAC7B,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBACvC,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,iBAAiB;wBACpB,KAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;wBAC3C,MAAM;oBACR;wBACE,MAAM;iBACT;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,yBAAyB,EAAE,CAAC;;IACnC,CAAC;IAMD,sBAAI,0CAAK;QAJT;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAMD,sBAAI,2CAAM;QAJV;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAMD,sBAAI,iDAAY;QAJhB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAED;;;;;;OAMG;IACH,8DAA4B,GAA5B,UAA6B,OAAO;QAApC,iBAgDC;QA/CC,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,2DAA2D;YAC3D,KAAK,GAAG,KAAK,CAAC;YACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE;YAC7C,0EAA0E;YAC1E,wEAAwE;YACxE,yBAAyB;YACzB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;gBACrC,CAAC,CAAC,KAAK,CAAC,KAAK;gBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CACR,KAAK,CAAC,KAAK,CAAC,IAAI,EAChB,KAAK,CAAC,KAAK,CAAC,IAAI,EAChB,KAAK,CAAC,KAAK,CAAC,IAAI,EAChB,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACvB;QACD,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YAC1C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,OAAO,GAAG,IAAI,CAAC;SAChB;QACD,IAAI,CAAC,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO;YAC7C,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,GAAG;gBACnC,IAAM,MAAM,GAAG,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACrD,IAAI,MAAM,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,EAAE;oBAC9B,OAAO,GAAG,IAAI,CAAC;iBAChB;gBACD,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAClC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC;YACb,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAEvB,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACtB;QAGD,4DAA4D;QAC5D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC;QAE7D,qDAAqD;QACrD,2DAA2D;QAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YAC3B,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,yBAAyB,EAAE,EAAhC,CAAgC,EAAE,IAAI,CAAC,CAAC;SAC1D;IACH,CAAC;IAED;;;;OAIG;IACH,2DAAyB,GAAzB;QAAA,iBAUC;QATC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAA,oBAAoB;YAChE,IAAI,KAAI,CAAC,UAAU,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,OAAO,CACrB,iCAAiC,CAAC,oBAAoB,EAAE,KAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;aAC9F;QACH,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,qCAAG,GAAH,UAAI,oBAAoB;QACtB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACvD,CAAC;IACH,8BAAC;AAAD,CAAC,AArKD,CAAsC,cAAc,GAqKnD;AAED;;;GAGG;AAEH;;;;GAIG;AAEH;;;;GAIG;AACH,SAAS,iCAAiC,CAAC,oBAAoB,EAAE,0BAA0B;IACzF,OAAO,MAAM,CAAC,MAAM,CAClB,EAAE,IAAI,EAAE,iBAAiB,EAAE,EAC3B,oBAAoB,EACpB,0BAA0B,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC"}