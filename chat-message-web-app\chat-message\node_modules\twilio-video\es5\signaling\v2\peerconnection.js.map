{"version": 3, "file": "peerconnection.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/peerconnection.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC/C,IAAA,KAKF,OAAO,CAAC,cAAc,CAAC,EAJR,sBAAsB,qBAAA,EACpB,wBAAwB,uBAAA,EACpB,4BAA4B,2BAAA,EACzC,aAAa,cACE,CAAC;AAE5B,IAAM,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAEpC,IAAA,KAKF,OAAO,CAAC,sBAAsB,CAAC,EAJjC,gCAAgC,sCAAA,EAChC,iBAAiB,uBAAA,EACjB,2BAA2B,iCAAA,EAC3B,uBAAuB,6BACU,CAAC;AAE9B,IAAA,KAYF,OAAO,CAAC,gBAAgB,CAAC,EAX3B,uBAAuB,6BAAA,EACvB,oBAAoB,0BAAA,EACpB,6BAA6B,mCAAA,EAC7B,UAAU,gBAAA,EACV,gBAAgB,sBAAA,EAChB,iBAAiB,uBAAA,EACjB,gBAAgB,sBAAA,EAChB,oBAAoB,0BAAA,EACpB,eAAe,qBAAA,EACf,mBAAmB,yBAAA,EACnB,YAAY,kBACe,CAAC;AAE9B,IAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE/C,IAAA,KAGF,OAAO,CAAC,gCAAgC,CAAC,EAF3C,+BAA+B,qCAAA,EAC/B,gCAAgC,sCACW,CAAC;AAExC,IAAA,KAMF,OAAO,CAAC,YAAY,CAAC,EALvB,cAAc,oBAAA,EACd,WAAW,iBAAA,EACX,wBAAwB,8BAAA,EACxB,WAAW,iBAAA,EACX,KAAK,WACkB,CAAC;AAE1B,IAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AACnC,IAAM,2BAA2B,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACzE,IAAM,iBAAiB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACzD,IAAM,kBAAkB,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACjE,IAAM,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACnD,IAAM,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACtC,IAAM,YAAY,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAC5D,IAAM,mBAAmB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEhE,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAClC,IAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AAC/B,IAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,IAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ,CAAC;AACpC,IAAM,SAAS,GAAG,KAAK,KAAK,SAAS,CAAC;AACtC,IAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ,CAAC;AAEpC,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;;;;;;;;;;;;;;;;EAmBE;AAEF,IAAM,MAAM,GAAG;IACb,IAAI,EAAE;QACJ,QAAQ;QACR,UAAU;KACX;IACD,QAAQ,EAAE;QACR,QAAQ;QACR,MAAM;KACP;IACD,MAAM,EAAE,EAAE;CACX,CAAC;AAEF;;;;;;;GAOG;AACH;IAA+B,oCAAY;IACzC;;;;;;OAMG;IACH,0BAAY,EAAE,EAAE,kBAAkB,EAAE,eAAe,EAAE,OAAO;QAA5D,YACE,kBAAM,MAAM,EAAE,MAAM,CAAC,SAqRtB;QApRC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,UAAU,EAAE,KAAK;YACjB,0BAA0B,EAAE,IAAI;YAChC,wBAAwB,0BAAA;YACxB,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,iBAAiB;YAC3B,YAAY,EAAE,EAAE;YAChB,eAAe,iBAAA;YACf,cAAc,EAAE,2BAA2B,GAAG,IAAI;YAClD,mBAAmB,qBAAA;YACnB,YAAY,cAAA;YACZ,OAAO,EAAE,cAAc;YACvB,oBAAoB,EAAE,2BAA2B;YACjD,eAAe,EAAE,sBAAsB;YACvC,iBAAiB,EAAE,wBAAwB;YAC3C,qBAAqB,EAAE,4BAA4B;YACnD,OAAO,EAAE,cAAc;SACxB,EAAE,OAAO,CAAC,CAAC;QAEZ,wFAAwF;QACxF,6HAA6H;QAC7H,2FAA2F;QAC3F,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAC7F,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAEpD,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;YAC/B,OAAO,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,IAAI,EAAE,CAAC;YAC5E,OAAO,CAAC,yBAAyB,CAAC,QAAQ,GAAG,OAAO,CAAC,yBAAyB,CAAC,QAAQ,IAAI,EAAE,CAAC;YAC9F,OAAO,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;SACrE;QAED,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACzH,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAE/F,IAAI,OAAO,CAAC,0BAA0B,EAAE;YACtC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,oBAAoB,EAAE;gBACpB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;aACT;YACD,2BAA2B,EAAE;gBAC3B,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO,CAAC,UAAU;aAC1B;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,kBAAkB;aAC1B;YACD,yBAAyB,EAAE;gBACzB,KAAK,EAAE,OAAO,CAAC,wBAAwB;aACxC;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,CACxB,cAAM,OAAA,KAAI,CAAC,0BAA0B,EAAE,EAAjC,CAAiC,EACvC,gCAAgC,EAChC,KAAK,CAAC;aACT;YACD,kBAAkB,EAAE;gBAClB,mCAAmC;gBACnC,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC;aACpD;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE,UAAU;aACpB;YACD,wBAAwB,EAAE;gBACxB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,UAAU,EAAE;gBACV,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,8BAA8B,EAAE;gBAC9B,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,gBAAgB,EAAE;gBAChB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,uBAAuB,EAAE;gBACvB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,8BAA8B,EAAE;gBAC9B,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;aACT;YACD,gBAAgB,EAAE;gBAChB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,EAAE;aACV;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,wBAAwB,EAAE;gBACxB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;aACT;YACD,iCAAiC,EAAE;gBACjC,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,WAAW,EAAE;gBACX,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,aAAa;aAC7B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC;aACxD;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,YAAY,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,gBAAgB,EAAE;gBAChB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,aAAa,EAAE;gBACb,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,OAAO,CAAC,YAAY;aAC5B;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,WAAW,CAAC;oBACjB,IAAI,CAAC,KAAI,CAAC,YAAY,EAAE;wBACtB,wBAAwB,CAAC,KAAI,CAAC,CAAC;qBAChC;gBACH,CAAC,CAAC;aACH;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,eAAe,CAAC,KAAK;aAC7B;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,eAAe,CAAC,KAAK;aAC7B;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,UAAC,EAAS;wBAAP,KAAK,WAAA;oBAAO,OAAA,KAAK,KAAK,MAAM;gBAAhB,CAAgB,CAAC;uBAC9D,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,UAAC,EAAc;4BAAZ,KAAK,WAAA,EAAE,GAAG,SAAA;wBAAO,OAAA,KAAK,KAAK,MAAM,IAAI,GAAG;oBAAvB,CAAuB,CAAC;aAC7E;YACD,kBAAkB,EAAE;gBAClB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC;oBACzB,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;oBACrC,KAAI,CAAC,KAAK,EAAE,CAAC;gBACf,CAAC,EAAE,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;aAClC;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,EAAE;iBACV;aACF;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI,MAAM,EAAE;aACpB;YACD,oBAAoB,EAAE;gBACpB,+EAA+E;gBAC/E,qFAAqF;gBACrF,mFAAmF;gBACnF,mDAAmD;gBACnD,KAAK,EAAE,SAAS,IAAI,SAAS,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM;oBAClH,CAAC,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,EAAH,CAAG;oBACZ,CAAC,CAAC,OAAO,CAAC,mBAAmB;aAChC;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,OAAO,CAAC,YAAY;aAC5B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,OAAO,CAAC,iBAAiB;aACjC;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,YAAY,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,aAAa,EAAE;gBACb,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,iCAAiC,EAAE;gBACjC,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,EAAE,EAAE;gBACF,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,EAAE;aACV;SACF,CAAC,CAAC;QAEH,kBAAkB,CAAC,EAAE,CAAC,SAAS,EAAE,KAAI,CAAC,4BAA4B,CAAC,CAAC;QAEpE,cAAc,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,KAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QACvG,cAAc,CAAC,gBAAgB,CAAC,aAAa,EAAE,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QACxF,cAAc,CAAC,gBAAgB,CAAC,cAAc,EAAE,KAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QAC1F,cAAc,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,KAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QAC7G,cAAc,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,KAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QAC3G,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,KAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QACrG,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;QAE5E,IAAM,IAAI,GAAG,KAAI,CAAC;QAClB,KAAI,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;YACjD,IAAI,KAAK,KAAK,QAAQ,EAAE;gBACtB,OAAO;aACR;YACD,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,WAAW,EAAE,eAAe;gBACtD,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;;IACL,CAAC;IAED,mCAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,EAAE,MAAG,CAAC;IAC/D,CAAC;IAED,wDAA6B,GAA7B,UAA8B,0BAA0B;QACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,0BAA0B,CAAC,CAAC;QACvF,iEAAiE;QACjE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAA,EAAE;YACnC,IAAI,mBAAmB,IAAI,EAAE,EAAE;gBAC7B,EAAE,CAAC,iBAAiB,GAAG,0BAA0B,CAAC;aACnD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sBAAI,mDAAqB;aAAzB;YACE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAC1B,OAAO,KAAK,CAAC;aACd;YAED,4GAA4G;YAC5G,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAA,EAAE;gBAClD,OAAO,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,EAAE,CAAC,SAAS,IAAI,EAAE,CAAC,iBAAiB,KAAK,KAAK,CAAC;YAC5F,CAAC,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAOD,sBAAI,6CAAe;QALnB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,kBAAkB,KAAK,QAAQ;gBACzC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnF,CAAC;;;OAAA;IAOD,sBAAI,gDAAkB;QALtB;;;;WAIG;aACH;YACE,OAAO,CAAC,CAAC,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,cAAc,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC;gBAChI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QACzD,CAAC;;;OAAA;IAOD,sBAAI,4DAA8B;QALlC;;;;WAIG;aACH;YACE,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;gBACpD,4EAA4E;gBAC5E,OAAO,IAAI,CAAC,eAAe,CAAC,gBAAgB;oBAC1C,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC;oBACvF,CAAC,CAAC,KAAK,CAAC;aACX;YACD,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAMD,sBAAI,yDAA2B;QAJ/B;;;WAGG;aACH;YACE,IAAM,sBAAsB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,mBAAmB,IAAI,EAAE,EAAzB,CAAyB,CAAC,CAAC;YAChG,OAAO,sBAAsB,IAAI,sBAAsB,CAAC,iBAAiB,KAAK,IAAI,CAAC;QACrF,CAAC;;;OAAA;IAED;;;;;OAKG;IACH,gDAAqB,GAArB,UAAsB,KAAK,EAAE,SAAS,EAAE,aAAqB;QAArB,8BAAA,EAAA,qBAAqB;QAC3D,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,EAAE;YAC1D,OAAO,KAAK,CAAC;SACd;QACD,oHAAoH;QACpH,gFAAgF;QAChF,8EAA8E;QACxE,IAAA,KAAoB,KAAK,CAAC,WAAW,EAAE,EAArC,MAAM,YAAA,EAAE,KAAK,WAAwB,CAAC;QAC9C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3D,OAAO,KAAK,CAAC;SACd;QACD,4DAA4D;QAC5D,mDAAmD;QACnD,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACpC,IAAI,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,2BAA2B,CAAC,EAAE;YACtF,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;OAQG;IACH,2CAAgB,GAAhB,UAAiB,KAAK,EAAE,SAAS,EAAE,aAAa;QAC9C,IAAI,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE;YACzC,IAAM,8BAA4B,GAAG;gBACnC,EAAE,qBAAqB,EAAE,CAAC,EAAE;gBAC5B,EAAE,qBAAqB,EAAE,CAAC,EAAE;aAC7B,CAAC;YACF,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,CAAC;gBAC5B,IAAM,iBAAiB,GAAG,8BAA4B,CAAC,CAAC,CAAC,CAAC;gBAC1D,IAAI,iBAAiB,EAAE;oBACrB,QAAQ,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,qBAAqB,CAAC;oBACzE,IAAI,aAAa,EAAE;wBACjB,OAAO,QAAQ,CAAC,MAAM,CAAC;qBACxB;iBACF;qBAAM;oBACL,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;oBACxB,OAAO,QAAQ,CAAC,qBAAqB,CAAC;iBACvC;YACH,CAAC,CAAC,CAAC;SACJ;aAAM;YACC,IAAA,KAAqB,KAAK,CAAC,WAAW,EAAE,EAAtC,KAAK,WAAA,EAAE,MAAM,YAAyB,CAAC;YAC/C,iDAAiD;YACjD,+CAA+C;YAC/C,IAAM,uBAAuB,GAAG;gBAC9B,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,eAAe,EAAE,CAAC,EAAE;gBACzC,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,eAAe,EAAE,CAAC,EAAE;gBACzC,EAAE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aAClC,CAAC;YAEF,IAAM,aAAW,GAAI,KAAK,GAAG,MAAM,CAAC;YACpC,IAAM,gBAAgB,GAAG,uBAAuB,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,aAAW,IAAI,KAAK,CAAC,MAAM,EAA3B,CAA2B,CAAC,CAAC;YAC5F,IAAM,cAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAClF,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,CAAC;gBAC5B,IAAM,OAAO,GAAI,CAAC,GAAG,cAAY,CAAC;gBAClC,IAAI,OAAO,EAAE;oBACX,QAAQ,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,cAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7D,IAAI,aAAa,EAAE;wBACjB,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;qBACxB;iBACF;qBAAM;oBACL,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;oBACxB,OAAO,QAAQ,CAAC,qBAAqB,CAAC;iBACvC;YACH,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,SAAS,CAAC,GAAG,CAAC,UAAC,EAAiC,EAAE,CAAC;gBAAlC,MAAM,YAAA,EAAE,qBAAqB,2BAAA;YAAU,OAAA,MAAI,CAAC,UAAK,MAAM,WAAK,qBAAqB,IAAI,CAAC,OAAG;QAAlD,CAAkD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/J,CAAC;IAED;;;;;OAKG;IACH,2CAAgB,GAAhB,UAAiB,SAAS;QAA1B,iBAgBC;QAfC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,SAAS,GAAG,IAAI,KAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACjD,OAAO,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,yEAAyE;YACzE,yEAAyE;YACzE,6EAA6E;YAC7E,kFAAkF;YAClF,0EAA0E;YAC1E,EAAE;YACF,2EAA2E;YAC3E,EAAE;YACF,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oCAAiC,SAAS,CAAC,CAAC,CAAC,OAAI,SAAS,CAAC,SAAS,OAAG,CAAC,CAAC,CAAC,MAAM,QAAI;kBAC/F,KAAK,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,4CAAiB,GAAjB,UAAkB,UAAU;QAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,cAAO,CAAC,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG;IACH,kDAAuB,GAAvB,UAAwB,KAAK;QAA7B,iBAqBC;QApBC,IAAM,WAAW,GAAG,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE;YACrC,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACjF,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,0BAAwB,WAAW,CAAC,GAAG,UAAK,UAAU,YAAO,KAAK,CAAC,EAAI,CAAC,CAAC;aACzF;YACD,sEAAsE;YACtE,mGAAmG;YACnG,gEAAgE;YAChE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;gBACtF,WAAW,CAAC,SAAS,GAAG,UAAU,CAAC;YACrC,CAAC,EAAE;gBACD,cAAc;YAChB,CAAC,CAAC,CAAC,OAAO,CAAC;gBACT,KAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC;YACJ,OAAO,WAAW,CAAC;SACpB;QACD,wFAAwF;QACxF,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACH,uCAAY,GAAZ,UAAa,WAAW;QACtB,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACH,kCAAO,GAAP,UAAQ,KAAK;QAAb,iBAgEC;QA/DC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;gBAC1B,KAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;aACpC;YACD,OAAO,KAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,KAAK,CAAC;YACP,MAAM,IAAI,gCAAgC,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;YACZ,IAAI,SAAS,EAAE;gBACb,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,MAAM,GAAG,IAAI,KAAI,CAAC,sBAAsB,CAAC;oBACvC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC;oBAC3B,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;aACtC;YAED,kFAAkF;YAClF,2FAA2F;YAC3F,6HAA6H;YAC7H,4GAA4G;YAC5G,wCAAwC;YACxC,IAAI,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAExE,IAAI,KAAI,CAAC,qBAAqB,EAAE;gBAC9B,IAAI,mBAAmB,GAAG,UAAU,CAAC;gBACrC,UAAU,GAAG,KAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,KAAI,CAAC,qBAAqB,CAAC,CAAC;gBACjF,gEAAgE;gBAChE,kEAAkE;gBAClE,4EAA4E;gBAC5E,UAAU,GAAG,KAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,mBAAmB,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;aAChF;YAED,sDAAsD;YACtD,gEAAgE;YAChE,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAErD,OAAO,KAAI,CAAC,oBAAoB,CAAC;gBAC/B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,GAAG,EAAE,UAAU;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC,kBAAkB;mBACzB,KAAI,CAAC,kBAAkB,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,IAAM,YAAY,GAAG,KAAK,YAAY,gCAAgC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,+BAA+B,EAAE,CAAC;YACvH,KAAI,CAAC,oBAAoB,CAAC;gBACxB,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,KAAK,OAAA;aACN,CAAC,CAAC;YACH,MAAM,YAAY,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,iCAAM,GAAN;QACE,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvB,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACtF,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,uDAA4B,GAA5B;QACE,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACH,kDAAuB,GAAvB,UAAwB,KAAK;QAA7B,iBAUC;QATC,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;QAClC,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEhD,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE;YACpC,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACH,uCAAY,GAAZ,UAAa,KAAK;QAAlB,iBAgBC;QAfC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;YACvG,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,KAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,KAAI,CAAC,4BAA4B,CAAC,CAAC;YACxE,OAAO,KAAI,CAAC,oBAAoB,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,OAAO,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,UAAU;YAChB,OAAO,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,MAAM,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+CAAoB,GAApB,UAAqB,EAA6B;YAA3B,OAAO,aAAA,EAAE,IAAI,UAAA,EAAE,KAAK,WAAA,EAAE,GAAG,SAAA;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC5F,OAAO,SAAA;gBACP,IAAI,MAAA;gBACJ,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,GAAG,KAAA,EAAE,CAAC;aACvD,EAAE,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACH,mDAAwB,GAAxB,UAAyB,KAAK;QAC5B,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAClD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;YACxC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SAC7C;QACD,IAAM,mBAAmB,GAAG;YAC1B,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;gBAChE,KAAK,EAAE,IAAI,CAAC,WAAW;aACxB;YACD,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACpB,mBAAmB,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;SACzC;QACD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE;YACzC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;SAC9C;IACH,CAAC;IAED;;;;OAIG;IACH,0DAA+B,GAA/B;QAAA,iBA0CC;QAzCS,IAAA,kBAAkB,GAAK,IAAI,CAAC,eAAe,mBAAzB,CAA0B;QACpD,IAAM,wBAAwB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACzF,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,GAAG,CAAC,KAAK,CAAC,+BAA4B,kBAAkB,OAAG,CAAC,CAAC;QAC7D,IAAI,wBAAwB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,uBAAuB,KAAK,QAAQ,IAAI,kBAAkB,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACrI,kCAAkC;YAClC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACvB,IAAI,CAAC,0BAA0B,EAAE,CAAC;SACnC;aAAM,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,wBAAwB,EAAE;YACxG,sDAAsD;YACtD,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;SAC9B;QAED,8GAA8G;QAC9G,IAAI,kBAAkB,KAAK,WAAW,EAAE;YACtC,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;YACtC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBAC/B,4EAA4E;gBAC5E,gHAAgH;gBAChH,KAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;gBAClC,IAAI,CAAC,KAAI,CAAC,iBAAiB,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;oBACrD,GAAG,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;oBACvD,KAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;oBACrC,KAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,KAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBACvC,KAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;iBACrC;YACH,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,EAAE,uDAAuD;YAC/H,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;SACvC;QAED,IAAI,CAAC,uBAAuB,GAAG,kBAAkB,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,qDAA0B,GAA1B;QACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC5D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,yDAA8B,GAA9B;QACU,IAAA,iBAAiB,GAAK,IAAI,CAAC,eAAe,kBAAzB,CAA0B;QACnD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,KAAK,CAAC,8BAA2B,iBAAiB,OAAG,CAAC,CAAC;QAE3D,kFAAkF;QAClF,qFAAqF;QACrF,uCAAuC;QACjC,IAAA,KAAmB,IAAI,CAAC,oBAAoB,EAA1C,KAAK,WAAA,EAAE,KAAK,WAA8B,CAAC;QACnD,IAAI,iBAAiB,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,EAAE;YACpF,GAAG,CAAC,KAAK,CAAC,qCAAmC,KAAO,CAAC,CAAC;YACtD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SACnC;IACH,CAAC;IAED;;;;OAIG;IACH,sDAA2B,GAA3B;QACE,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACzE;IACH,CAAC;IAED;;;;;OAKG;IACH,4CAAiB,GAAjB,UAAkB,KAAK;QAAvB,iBA8BC;QA7BC,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAChD,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG;YAC5C,CAAC,CAAC,IAAI,CAAC;QAET,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,YAAY,EAAE,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE/B,IAAM,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC;QACrC,IAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC;QAC/E,IAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;QAErF,6EAA6E;QAC7E,6EAA6E;QAC7E,oFAAoF;QACpF,6EAA6E;QAC7E,4CAA4C;QAC5C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAA,aAAa;YAC7C,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,KAAK,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE;gBAC1D,KAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;aACjD;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClD,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAApD,CAAoD,CAAC,CAAC;SACxG;aAAM;YACL,gBAAgB,CAAC,OAAO,GAAG,cAAM,OAAA,KAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAApD,CAAoD,CAAC;SACvF;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACH,8CAAmB,GAAnB;QACE,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACtC,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAExB,IAAA,KAAmB,IAAI,CAAC,oBAAoB,EAA1C,KAAK,WAAA,EAAE,KAAK,WAA8B,CAAC;QACnD,IAAI,CAAC,KAAK,EAAE;YACV,GAAG,CAAC,KAAK,CAAC,qCAAmC,KAAO,CAAC,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,UAAA,EAAE;YACnB,GAAG,CAAC,KAAK,CAAC,+CAA6C,EAAE,CAAC,OAAS,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,qDAA0B,GAA1B;QAAA,iBAOC;QANC,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAAC,8BAA8B,EAAE;YAC3F,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,mBAAmB,EAAE,EAA1B,CAA0B,CAAC,CAAC;IACpE,CAAC;IAED;;;;;OAKG;IACH,wCAAa,GAAb,UAAc,gBAAgB;QAC5B,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC;QAEtC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,EAAE;YAC5C,8EAA8E;YAC9E,0EAA0E;YAC1E,yEAAyE;YACzE,iBAAiB;YACjB,IAAM,SAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,KAAK,EAAZ,CAAY,CAAC,CAAC;YACjF,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,UAAC,WAAW,EAAE,IAAI;gBAC1D,IAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;gBAC1F,IAAM,aAAa,GAAG,SAAO,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACtE,OAAO,WAAW,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;YACtE,CAAC,EAAE,aAAa,CAAC,CAAC;YAElB,2EAA2E;YAC3E,iCAAiC;YACjC,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;YACjD,IAAM,0BAA0B,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACpG,IAAM,4BAA4B,GAAG,YAAY,IAAI,CAAC,0BAA0B,CAAC;YACjF,aAAa,GAAG,aAAa,IAAI,4BAA4B,CAAC;SAC/D;QAED,IAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAClE,OAAO,OAAO,CAAC,IAAI,CAAC,cAAM,OAAA,aAAa,EAAb,CAAa,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,iCAAM,GAAN;QAAA,iBAiEC;QAhEC,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;SAChC;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC;YAC3D,OAAO,KAAI,CAAC,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,IAAM,YAAY,GAAG,IAAI,+BAA+B,EAAE,CAAC;YAC3D,KAAI,CAAC,oBAAoB,CAAC;gBACxB,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,KAAK,OAAA;aACN,CAAC,CAAC;YACH,MAAM,YAAY,CAAC;QACrB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK;YACX,IAAI,SAAS,EAAE;gBACb,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,KAAK,GAAG,IAAI,KAAI,CAAC,sBAAsB,CAAC;oBACtC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;oBAC1B,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAC;aACJ;iBAAM;gBACL,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;aACpC;YAED,kFAAkF;YAClF,2FAA2F;YAC3F,6HAA6H;YAC7H,uHAAuH;YACvH,wCAAwC;YACxC,IAAI,GAAG,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,GAAG,GAAG,KAAI,CAAC,eAAe,CAAC,iBAAiB;gBAC1C,CAAC,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBACpE,CAAC,CAAC,GAAG,CAAC;YAER,IAAI,UAAU,GAAG,KAAI,CAAC,oBAAoB,CACxC,GAAG,EACH,KAAI,CAAC,qBAAqB,EAC1B,KAAI,CAAC,qBAAqB,CAAC,CAAC;YAE9B,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;gBAC1B,KAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;aACnC;YAED,IAAI,KAAI,CAAC,qBAAqB,EAAE;gBAC9B,KAAI,CAAC,iCAAiC,GAAG;oBACvC,IAAI,EAAE,OAAO;oBACb,GAAG,EAAE,UAAU;iBAChB,CAAC;gBACF,UAAU,GAAG,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,KAAI,CAAC,qBAAqB,CAAC,CAAC;aACzE;YAED,OAAO,KAAI,CAAC,oBAAoB,CAAC;gBAC/B,IAAI,EAAE,OAAO;gBACb,GAAG,EAAE,UAAU;aAChB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,iDAAsB,GAAtB,UAAuB,OAAO;QAC5B,IAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAC,EAAiB;gBAAN,EAAE,cAAA;YAAS,OAAA,EAAE,KAAK,OAAO;QAAd,CAAc,CAAC,CAAC;QACzG,OAAO,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IAC1D,CAAC;IAED;;;;;OAKG;IACH,qDAA0B,GAA1B,UAA2B,WAAW;QAAtC,iBAyBC;QAxBC,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;QAC5D,IAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAAC,UAAC,EAAmB;gBAAjB,MAAM,YAAA,EAAE,OAAO,aAAA;YAAO,OAAA,CAAC,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK;QAAlC,CAAkC,CAAC,CAAC;QAE5G,uFAAuF;QACvF,sFAAsF;QACtF,sFAAsF;QACtF,oCAAoC;QACpC,IAAM,oBAAoB,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAC,EAAO;gBAAL,GAAG,SAAA;YAAO,OAAA,GAAG;QAAH,CAAG,CAAC,CAAC;QACzE,IAAM,cAAc,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAC,EAAe;gBAAb,GAAG,SAAA,EAAE,MAAM,YAAA;YAAO,OAAA,CAAC,GAAG,EAAE,KAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAAnD,CAAmD,CAAC,CAAC,CAAC;QACnI,IAAM,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAEnE,uFAAuF;QACvF,6FAA6F;QAC7F,IAAM,sBAAsB,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAC,EAAO;gBAAL,GAAG,SAAA;YAAO,OAAA,CAAC,GAAG;QAAJ,CAAI,CAAC,CAAC;QAC5E,IAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA;YAC/D,IAAI;YACJ,sBAAsB,CAAC,MAAM,CAAC,UAAC,EAAU;oBAAR,MAAM,YAAA;gBAAO,OAAA,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI;YAA1B,CAA0B,CAAC,CAAC,GAAG,CAAC,UAAC,EAAU;oBAAR,MAAM,YAAA;gBAAO,OAAA,KAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAA5C,CAA4C,CAAC;SAC5I,EAHgE,CAGhE,CAAC,CAAC,CAAC;QACJ,IAAM,IAAI,GAAG,uBAAuB,CAAC,IAAI,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAE9E,OAAO,IAAI,IAAI,CAAC,sBAAsB,CAAC;YACrC,GAAG,EAAE,IAAI;YACT,IAAI,EAAE,WAAW,CAAC,IAAI;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,iDAAsB,GAAtB,UAAuB,KAAK;QAA5B,iBAEC;QADC,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAhC,CAAgC,CAAC,CAAC;IACtG,CAAC;IAED;;;;;OAKG;IACH,+CAAoB,GAApB,UAAqB,WAAW;QAAhC,iBAmDC;QAlDC,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;YAC3D,WAAW,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC;gBAC5C,GAAG,EAAE,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC;gBACtC,IAAI,EAAE,WAAW,CAAC,IAAI;aACvB,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACtE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,yEAAsE,WAAW,CAAC,IAAI,mCAA4B,KAAK,CAAC,OAAO,QAAI,EAAE,KAAK,CAAC,CAAC;YAE3J,IAAM,YAAY,GAAG,IAAI,+BAA+B,EAAE,CAAC;YAC3D,IAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,yEAAsE,WAAW,CAAC,IAAI,cAAU;gBACzG,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,KAAK,OAAA;aACN,CAAC;YAEF,IAAI,WAAW,CAAC,GAAG,EAAE;gBACnB,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAe,WAAW,CAAC,GAAK,CAAC,CAAC;gBACjD,cAAc,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC;aACtC;YACD,KAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAC1C,MAAM,YAAY,CAAC;QACrB,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;gBACnC,KAAI,CAAC,iBAAiB,GAAG,KAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;gBAEtE,mFAAmF;gBACnF,oFAAoF;gBACpF,mFAAmF;gBACnF,sFAAsF;gBACtF,qEAAqE;gBACrE,IAAI,KAAI,CAAC,eAAe,EAAE;oBACxB,KAAI,CAAC,iBAAiB,GAAG,IAAI,KAAI,CAAC,sBAAsB,CAAC;wBACvD,GAAG,EAAE,gBAAgB,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC;wBACrD,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC,IAAI;qBAClC,CAAC,CAAC;iBACJ;gBAED,KAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAC3B,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;oBAChC,KAAI,CAAC,oBAAoB,EAAE,CAAC;iBAC7B;qBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACxC,KAAI,CAAC,8BAA8B,GAAG,KAAI,CAAC,oBAAoB,CAAC;oBAChE,oBAAoB,CAAC,KAAI,CAAC,CAAC;iBAC5B;gBACD,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACzC,KAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;aAC3C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,gDAAqB,GAArB,UAAsB,WAAW;QAAjC,iBAgEC;QA/DC,IAAI,WAAW,CAAC,GAAG,EAAE;YACnB,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CACzC,WAAW,CAAC,GAAG,EACf,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxB,WAAW,CAAC,GAAG,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aACrD;iBAAM;gBACL,uEAAuE;gBACvE,yBAAyB;gBACzB,WAAW,CAAC,GAAG,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;aACzD;YAED,IAAI,SAAS,EAAE;gBACb,yEAAyE;gBACzE,yEAAyE;gBACzE,wEAAwE;gBACxE,kCAAkC;gBAClC,WAAW,CAAC,GAAG,GAAG,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAC5D;YACD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;gBAC3C,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aACtD;SACF;QACD,WAAW,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAC3D,6CAA6C;QAC7C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,gEAAgE;YAChE,kEAAkE;YAClE,4EAA4E;YAC5E,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAI,CAAC,iCAAiC,EAAE;gBAC3E,4FAA4F;gBAC5F,iCAAiC;gBACjC,IAAM,sBAAsB,GAAG,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,mBAAmB,IAAI,EAAE,EAAzB,CAAyB,CAAC,CAAC;gBAChG,IAAM,YAAY,GAAG,CAAC,CAAC,sBAAsB,IAAI,sBAAsB,CAAC,iBAAiB,KAAK,KAAK,CAAC;gBACpG,IAAM,yCAAyC,GAAG,KAAI,CAAC,gBAAgB,CACrE,KAAI,CAAC,iBAAiB,CAAC,GAAG,EAC1B,KAAI,CAAC,iCAAiC,CAAC,GAAG,EAC1C,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;gBACjC,KAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;gBAC9C,IAAI,yCAAyC,KAAK,KAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE;oBAC5E,OAAO,KAAI,CAAC,sBAAsB,CAAC;wBACjC,IAAI,EAAE,KAAI,CAAC,iBAAiB,CAAC,IAAI;wBACjC,GAAG,EAAE,yCAAyC;qBAC/C,CAAC,CAAC;iBACJ;aACF;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAtD,CAAsD,CAAC,CAAC,IAAI,CAAC;YACzE,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,IAAI,KAAI,CAAC,gBAAgB,EAAE;oBACzB,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;oBACvE,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;iBAC/B;gBACD,oBAAoB,CAAC,KAAI,CAAC,CAAC;aAC5B;QACH,CAAC,EAAE,UAAA,KAAK;YACN,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,0EAAuE,WAAW,CAAC,IAAI,mCAA4B,KAAK,CAAC,OAAO,QAAI,EAAE,KAAK,CAAC,CAAC;YAC5J,IAAI,WAAW,CAAC,GAAG,EAAE;gBACnB,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAe,WAAW,CAAC,GAAK,CAAC,CAAC;aAClD;YACD,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,6CAAkB,GAAlB,UAAmB,WAAW;QAA9B,iBAwEC;QAvEC,QAAQ,WAAW,CAAC,IAAI,EAAE;YACxB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,oBAAoB;uBACjD,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,kBAAkB,EAAE;oBAC/D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBACD,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;gBACjD,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,cAAc;gBACjB,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,8BAA8B,EAAE;oBAC/D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;qBAAM,IAAI,IAAI,CAAC,YAAY,EAAE;oBAC5B,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;oBACtC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBACD,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;gBACjD,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,OAAO;gBACV,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,8BAA8B;uBAC1D,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;oBACrD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBACD,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,kBAAkB,EAAE;oBAC9D,2CAA2C;oBAC3C,0DAA0D;oBAC1D,kHAAkH;oBAClH,iHAAiH;oBACjH,mKAAmK;oBACnK,wIAAwI;oBACxI,4GAA4G;oBAC5G,6FAA6F;oBAC7F,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,8BAA8B,KAAK,CAAC,EAAE;wBAClE,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;wBACtC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;qBAC1B;oBACD,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;oBACjD,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;gBACjD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,cAAO,CAAC,CAAC,CAAC;YAClD,QAAQ;YACN,cAAc;SACjB;QAED,6BAA6B;QAC7B,IAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACtC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC5B,OAAO,KAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACZ,IAAM,YAAY,GAAG,IAAI,gCAAgC,EAAE,CAAC;YAC5D,KAAI,CAAC,oBAAoB,CAAC;gBACxB,OAAO,EAAE,0EAAuE,WAAW,CAAC,IAAI,cAAU;gBAC1G,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,KAAK,OAAA;gBACL,GAAG,EAAE,WAAW,CAAC,GAAG;aACrB,CAAC,CAAC;YACH,MAAM,YAAY,CAAC;QACrB,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,8BAA8B,GAAG,QAAQ,CAAC;YAC/C,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,OAAO,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC,kBAAkB;mBACzB,KAAI,CAAC,kBAAkB,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,cAAO,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,qCAAU,GAAV,UAAW,QAAQ;QACjB,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,6CAAkB,GAAlB,UAAmB,eAAe;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;YAC3C,OAAO;SACR;QACD,IAAI;YACF,IAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,eAAe,CAAC,OAAO;aACjC,CAAC;YACF,IAAI,eAAe,CAAC,iBAAiB,KAAK,IAAI,EAAE;gBAC9C,eAAe,CAAC,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC;aACvE;YACD,IAAI,eAAe,CAAC,cAAc,KAAK,IAAI,EAAE;gBAC3C,eAAe,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;aACjE;YACD,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAChG,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;SACtD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sDAAmD,eAAe,CAAC,EAAE,YAAM,KAAK,CAAC,OAAS,CAAC,CAAC;SAC5G;IACH,CAAC;IAED,sDAA2B,GAA3B;QAAA,iBASC;QARC,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,UAAC,EAAuB,EAAE,gBAAgB;oBAAvC,QAAQ,cAAA,EAAE,SAAS,eAAA;gBACnE,KAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;gBAChE,KAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,SAAS,CAAC;qBAChD,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAxB,CAAwB,CAAC;qBACxC,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAtB,CAAsB,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;;;;OAMG;IACH,4CAAiB,GAAjB,UAAkB,gBAAgB,EAAE,SAAS;QAA7C,iBAqDC;QApDC,IAAI,SAAS,EAAE;YACb,OAAO,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;YAChE,4DAA4D;YAC5D,IAAM,UAAU,GAAG,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAChF,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC/C,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACjE;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YACpF,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;YACrF,OAAO,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,0DAA0D;YAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mDAAmD,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAC1G,IAAM,QAAQ,GAAG,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;YACtF,OAAO,QAAQ,CAAC,OAAO,CAAC;SACzB;QAED,IAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,SAAS,CAAC,OAAO,CAAC,UAAC,EAAoC;oBAAlC,OAAO,aAAA,EAAe,UAAU,iBAAA;gBACnD,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,EAAE;oBAC5C,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAS,UAAU,iBAAY,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,YAAO,OAAS,CAAC,CAAC;oBACxG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC;iBACnD;qBAAM;oBACL,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAiB,UAAU,iBAAY,OAAS,CAAC,CAAC;iBAClE;YACH,CAAC,CAAC,CAAC;SACJ;QAED,yFAAyF;QACzF,+EAA+E;QAC/E,mFAAmF;QACnF,8FAA8F;QAC9F,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEvG,OAAO,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YAClE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,sBAAsB,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,8CAAmB,GAAnB,UAAoB,gBAAgB;QAApC,iBASC;QARC,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;YAC9F,OAAO;SACR;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACjE,IAAA,MAAM,GAAK,WAAW,OAAhB,CAAiB;QAC/B,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE,UAAA,SAAS,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,SAAS,CAAC,EAAnD,CAAmD,CAAC,CAAC;QACrG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACH,gCAAK,GAAL;QACE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC3C;IACH,CAAC;IAED;;;;OAIG;IACH,4CAAiB,GAAjB;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;OAGG;IACH,mCAAQ,GAAR;QACE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACb;QAED,qGAAqG;QACrG,iHAAiH;QACjH,2GAA2G;QAC3G,qDAAqD;QACrD,IAAM,wBAAwB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;QAC5I,IAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YACjC,QAAQ,EAAE,wBAAwB;SACnC,CAAC;QACF,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE;YAC9B,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;SACnD;QACD,OAAO;YACL,WAAW,EAAE,gBAAgB;YAC7B,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,gCAAK,GAAL;QAAA,iBAgBC;QAfC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAA,GAAG;YACjC,KAAI,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACjC,IAAM,OAAO,GAAG,KAAI,CAAC,YAAY,IAAI,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,MAAM,EAAE,CAAC;YAC/F,OAAO,OAAO,CAAC,IAAI,CAAC;gBAClB,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAClC,CAAC,EAAE,UAAA,KAAK;gBACN,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,gDAAqB,GAArB,UAAsB,eAAe;QACnC,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC5D,IAAI,WAAW,EAAE;YACf,eAAe,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAC3C,WAAW,CAAC,KAAK,EAAE,CAAC;SACrB;IACH,CAAC;IAED;;;;OAIG;IACH,iDAAsB,GAAtB,UAAuB,gBAAgB;QACrC,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC1C;QACD,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACtC,8EAA8E;QAC9E,IAAI,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;YAChE,IAAM,UAAU,GAAG,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAChF,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,2CAAgB,GAAhB,UAAiB,aAAa;QAC5B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,UAAU,EAAE;YAC/D,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC;SACxE;IACH,CAAC;IAED;;;;OAIG;IACH,iDAAsB,GAAtB,UAAuB,MAAM;QAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAA0C,EACxD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,iCAAM,GAAN,UAAO,mBAAmB;QAA1B,iBAyBC;QAxBC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAA,GAAG;YACjC,IAAI,KAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAC3B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;aAC1B;YAED,KAAI,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAEjC,IAAM,OAAO,GAAG,EAAE,CAAC;YAEnB,IAAI,mBAAmB,CAAC,GAAG,EAAE;gBAC3B,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;aACxD;YAED,IAAI,mBAAmB,CAAC,WAAW,EAAE;gBACnC,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;aACxE;YAED,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;gBAC/B,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAClC,CAAC,EAAE,UAAA,KAAK;gBACN,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,mCAAQ,GAAR;QAAA,iBAEC;QADC,OAAO,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,eAAe,CAAC,KAAI,EAAE,QAAQ,CAAC,EAA/B,CAA+B,CAAC,CAAC;IACnH,CAAC;IACH,uBAAC;AAAD,CAAC,AAngDD,CAA+B,YAAY,GAmgD1C;AAED,SAAS,mBAAmB,CAAC,IAAI,EAAE,KAAK;IACtC,IAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3D,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,cAAc,CAAC,IAAI,EAAE,KAAK;IACjC,IAAM,QAAQ,GAAG,yBAAI,IAAI,CAAC,oBAAoB,GAC3C,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAnC,CAAmC,CAAC,CAAC;IACzD,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,eAAe,CAAC,IAAI,EAAE,QAAQ;IACrC,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;QAC7B,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,EAA3B,CAA2B,CAAC;QAC/F,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,EAA3B,CAA2B,CAAC;QAC/F,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAhC,CAAgC,CAAC;QAClG,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAhC,CAAgC,CAAC;KACnG,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AAEH;;GAEG;AAEH;;;GAGG;AAEH;;GAEG;AAEH;;;GAGG;AAEH,SAAS,QAAQ,CAAC,WAAW;IAC3B,IAAI,WAAW,CAAC,GAAG,EAAE;QACnB,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtE,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;SACjB;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,gBAAgB,CAAC,aAAkB;IAAlB,8BAAA,EAAA,kBAAkB;IAC1C,OAAO,MAAM,CAAC,MAAM,CAAC;QACnB,YAAY,EAAE,YAAY;QAC1B,aAAa,EAAE,SAAS;KACzB,EAAE,aAAa,CAAC,CAAC;AACpB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,cAAc,CAAC,IAAI,EAAE,MAAM;IAClC,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAC3B,OAAO,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC;AACtE,CAAC;AAED;;;;;GAKG;AAEH,SAAS,uBAAuB,CAAC,GAAG;IAClC,OAAO,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;AACpD,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,WAAW,EAAE,IAAI;IACjD,OAAO,CAAC,WAAW,CAAC,OAAO;WACtB,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,WAAW,CAAC;WAC5C,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAChE,CAAC;AAED;;;;;GAKG;AACH,SAAS,uBAAuB,CAAC,IAAI,EAAE,IAAI;IACzC,IAAM,eAAe,GAAG;QACtB,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAC,EAAS;gBAAP,KAAK,WAAA;YAAO,OAAA,KAAK,CAAC,WAAW,EAAE;QAAnB,CAAmB,CAAC;QACzE,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAC,EAAS;gBAAP,KAAK,WAAA;YAAO,OAAA,KAAK,CAAC,WAAW,EAAE;QAAnB,CAAmB,CAAC;KAC1E,CAAC,IAAI,CAAC,CAAC;IAER,IAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC9D,IAAM,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAA5B,CAA4B,CAAC,CAAC;IAC/E,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,oBAAoB,CAAC,KAAK,EAAE,CAAC;KACrC;IAED,IAAM,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAAC,UAAA,WAAW;QACvD,IAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAClE,OAAO,cAAc,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;IAEH,IAAI,WAAW,EAAE;QACf,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;KAC3E;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,IAAI;IAC7B,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;IAC1D,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;QACpC,OAAO;KACR;IACD,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,OAAO;QAC/C,IAAM,QAAQ,GAAG,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACxD,QAAQ,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAA5B,CAA4B,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,qBAAqB,CAAC,IAAI;IACjC,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;IAC3D,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;QACpC,OAAO;KACR;IACD,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,OAAO;QAC/C,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAC3B,OAAO;SACR;QACD,IAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACvB,IAAM,QAAQ,GAAG,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,0BAA0B,CAAC,IAAI;IACtC,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,EAAE,CAAC;IACtC,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,EAAE,CAAC;IACtC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,UAAA,WAAW;QACxD,IAAI,wBAAwB,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;YAC/C,IAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;YACzC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1D;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,IAAI;IAChC,0BAA0B,CAAC,IAAI,CAAC,CAAC;IACjC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACxB,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC5B,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAClC,2DAA2D;QAC3D,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,IAAI;IAC9B,IAAA,KAAuC,IAAI,CAAC,mBAAmB,EAA7D,eAAe,qBAAA,EAAE,eAAe,qBAA6B,CAAC;IAEtE,IAAM,WAAW,GAAG,IAAI,GAAG,CAAC;QAC1B,CAAC,OAAO,EAAE,eAAe,CAAC;QAC1B,CAAC,OAAO,EAAE,eAAe,CAAC;KAC3B,CAAC,CAAC;IAEH,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,KAAK,EAAZ,CAAY,CAAC,CAAC,OAAO,CAAC,UAAA,MAAM;QAC7E,IAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtD,IAAM,MAAM,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAEtC,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,CAAC,EAAE;YAC3C,gBAAgB,CAAC,MAAM,CAAC,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvD,wIAAwI;YACxI,0HAA0H;YAC1H,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gCAA8B,MAAM,CAAC,KAAK,CAAC,IAAI,eAAU,MAAM,CAAC,KAAK,CAAC,EAAE,sDAAiD,MAAM,CAAC,KAAK,CAAC,KAAO,CAAC,CAAC;SAC/J;aAAM;YACL,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7C,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBACjC,gEAAgE;gBAChE,wEAAwE;gBACxE,sEAAsE;gBACtE,wEAAwE;gBACxE,4EAA4E;gBAC5E,2BAA2B;gBAC3B,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,MAAM,CAAC;aACvC;iBAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACvD,2EAA2E;gBAC3E,iBAAiB;gBACjB,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;aACzC;YACD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,uEAAuE;gBACvE,wEAAwE;gBACxE,sEAAsE;gBACtE,wEAAwE;gBACxE,kDAAkD;gBAClD,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,MAAM,CAAC;aAC9C;SACF;QAED,sEAAsE;QACtE,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK;YACtD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kDAAgD,MAAM,CAAC,KAAK,CAAC,IAAI,eAAU,MAAM,CAAC,KAAK,CAAC,EAAE,WAAK,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAE,CAAC,CAAC;QAC/I,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,MAAM;IAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;QACnC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,IAAI,OAAA,OAAO,QAAQ,CAAC,UAAU,EAA1B,CAA0B,CAAC,CAAC;KAClE;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CAAC,MAAM,EAAE,UAAU;IACvC,IAAI,SAAS,EAAE;QACb,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,UAAU,YAAA,EAAE,CAAC,CAAC;KACrC;SAAM;QACL,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;YAC/B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,CAAC,CAAC,CAAC;KAEJ;AACH,CAAC;AACD,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}