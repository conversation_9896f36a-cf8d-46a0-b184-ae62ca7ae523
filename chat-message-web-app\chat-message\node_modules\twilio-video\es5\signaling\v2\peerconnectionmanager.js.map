{"version": 3, "file": "peerconnectionmanager.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/peerconnectionmanager.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,mBAAmB,CAAC,aAAjC,CAAkC;AACtD,IAAM,gBAAgB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACrD,IAAM,gBAAgB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC7D,IAAM,oBAAoB,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACnE,IAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAC3B,IAAA,oBAAoB,GAAK,OAAO,CAAC,gCAAgC,CAAC,qBAA9C,CAA+C;AAE3E,IAAM,SAAS,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC;AAE/C;;;;;;;;GAQG;AACH;IAAoC,yCAAoB;IACtD;;;;;OAKG;IACH,+BAAY,kBAAkB,EAAE,eAAe,EAAE,OAAO;QAAxD,YACE,iBAAO,SAmFR;QAjFC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,mBAAmB,EAAE,SAAS;gBAC5B,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC;gBACxC,CAAC,CAAC,IAAI;YACR,gBAAgB,kBAAA;SACjB,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB;YAC9C,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,KAAI,CAAC;YAC/C,CAAC,CAAC,IAAI,CAAC;QAET,2EAA2E;QAC3E,4CAA4C;QAC5C,IAAM,YAAY,GAAG,YAAY;YAC/B,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,EAAE;YAC/B,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC;QAE7D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,oBAAoB,EAAE;gBACpB,KAAK,EAAE,OAAO,CAAC,mBAAmB;aACnC;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,cAAc,EAAE;gBACd,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,sBAAsB,EAAE;gBACtB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;aACpB;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,YAAY;oBACjB,CAAC,CAAC,IAAI,gBAAgB,CAAC,gCAAgC,CAAC,YAAY,CAAC,CAAC;oBACtE,CAAC,CAAC,IAAI;aACT;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,kBAAkB;aAC1B;YACD,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,uBAAuB,EAAE;gBACvB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,kBAAkB,EAAE;gBAClB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,YAAY;aACpB;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,eAAe;aACvB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAC,gBAAgB;aAChC;SACF,CAAC,CAAC;;IACL,CAAC;IAED,6DAA6B,GAA7B,UAA8B,0BAA0B;QACtD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,6BAA6B,CAAC,0BAA0B,CAAC,EAA5D,CAA4D,CAAC,CAAC;QAClG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,EAAE;YACpC,IAAI,mBAAmB,IAAI,EAAE,EAAE;gBAC7B,EAAE,CAAC,iBAAiB,GAAG,0BAA0B,CAAC;aACnD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAOD,sBAAI,kDAAe;QALnB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;;;OAAA;IAOD,sBAAI,qDAAkB;QALtB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,CAAC;;;OAAA;IAED;;;;OAIG;IACH,2DAA2B,GAA3B,UAA4B,oBAAoB;QAC9C,IAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAA,mBAAmB,IAAI,OAAA,mBAAmB,CAAC,EAAE,EAAtB,CAAsB,CAAC,CAAC,CAAC;QAC3G,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE;gBAC7C,cAAc,CAAC,MAAM,EAAE,CAAC;aACzB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,iDAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACH,4CAAY,GAAZ,UAAa,EAAE,EAAE,aAAa;QAA9B,iBA6CC;QA5CC,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,EAAE;YACnB,IAAM,kBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAEhD,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC5B,0BAA0B,EAAE,IAAI,CAAC,sBAAsB;oBACrD,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK;oBACnC,CAAC,CAAC,IAAI;gBACR,YAAY,EAAE,IAAI,CAAC,aAAa;aACjC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;gBACxB,cAAc,EAAE,IAAI,CAAC,eAAe;aACrC,CAAC,CAAC,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAEvB,IAAI;gBACF,cAAc,GAAG,IAAI,kBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;aACrG;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,oBAAoB,EAAE,CAAC;aAClC;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAC7D,cAAc,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;YACrE,cAAc,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;YACvE,cAAc,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;YACrE,cAAc,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;gBAC3D,IAAI,KAAK,KAAK,QAAQ,EAAE;oBACtB,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;oBAC5D,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAA5C,CAA4C,CAAC,CAAC;oBACvF,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAA7C,CAA6C,CAAC,CAAC;oBACzF,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;oBAChD,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;oBACrD,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBAC5B,wBAAwB,CAAC,IAAI,CAAC,CAAC;iBAChC;YACH,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,EAAE,CAAC,wBAAwB,EAAE,cAAM,OAAA,qBAAqB,CAAC,KAAI,CAAC,EAA3B,CAA2B,CAAC,CAAC;YAC/E,cAAc,CAAC,EAAE,CAAC,2BAA2B,EAAE,cAAM,OAAA,wBAAwB,CAAC,KAAI,CAAC,EAA9B,CAA8B,CAAC,CAAC;YAErF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YAClF,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;YAEpF,wBAAwB,CAAC,IAAI,CAAC,CAAC;SAChC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,qCAAK,GAAL;QACE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;YAC1C,cAAc,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACzC;QACD,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,8CAAc,GAAd;QAAA,iBAaC;QAZC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,UAAA,aAAa;YAChD,IAAI,EAAE,CAAC;YACP,GAAG;gBACD,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;aACtB,QAAQ,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAExC,OAAO,KAAI,CAAC,YAAY,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,cAAc;YACpB,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,iDAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAA,cAAc,IAAI,OAAA,cAAc,CAAC,iBAAiB,EAAE,EAAlC,CAAkC,CAAC,CAAC;IACnG,CAAC;IAED;;;OAGG;IACH,yCAAS,GAAT;QACE,IAAM,oBAAoB,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;YAC1C,IAAM,mBAAmB,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;YACtD,IAAI,mBAAmB,EAAE;gBACvB,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAChD;QACH,CAAC,CAAC,CAAC;QACH,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,gDAAgB,GAAhB,UAAiB,aAAa;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;gBAC1C,cAAc,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,sDAAsB,GAAtB,UAAuB,MAAM;QAC3B,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;gBAC1C,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,+CAAe,GAAf,UAAgB,YAAY;QAC1B,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,IAAI,KAAK,MAAM,EAA3B,CAA2B,CAAC,CAAC,CAAC;QAElG,IAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,YAAY;aAC3C,MAAM,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,OAAO,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,CAAC,EAA7E,CAA6E,CAAC,CAAC,CAAC;QAEzG,IAAM,OAAO,GAAG,qBAAqB,CAAC,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QACjF,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,sCAAM,GAAN,UAAO,oBAAoB,EAAE,MAAc;QAA3C,iBAeC;QAf4B,uBAAA,EAAA,cAAc;QACzC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,UAAA,aAAa;YAChD,OAAO,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAA,mBAAmB;gBAC7D,IAAI,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE;oBAC7D,OAAO,IAAI,CAAC;iBACb;gBACD,IAAM,cAAc,GAAG,KAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;gBAChF,OAAO,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC;YACN,OAAO,KAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,wCAAQ,GAAR;QACE,IAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QACnE,OAAO,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,UAAA,cAAc,IAAI,OAAA,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA;YAClG,cAAc,CAAC,EAAE;YACjB,QAAQ;SACT,EAHmG,CAGnG,CAAC,EAHuD,CAGvD,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,SAAS,IAAI,OAAA,IAAI,GAAG,CAAC,SAAS,CAAC,EAAlB,CAAkB,CAAC,CAAC;IAC7C,CAAC;IACH,4BAAC;AAAD,CAAC,AA1VD,CAAoC,oBAAoB,GA0VvD;AAED;;;;;GAKG;AACH,SAAS,gCAAgC,CAAC,YAAY;IACpD,IAAM,sBAAsB,GAAG,YAAY,CAAC,4BAA4B,EAAE,CAAC;IAC3E,OAAO,sBAAsB,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED;;;GAGG;AAEH;;GAEG;AAEH;;;GAGG;AAEH;;GAEG;AAEH;;;GAGG;AAEH;;;;;GAKG;AACH,SAAS,uBAAuB,CAAC,qBAAqB,EAAE,OAAO;IAC7D,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;WACpB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;WACxB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;WACtB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;QAC9B,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,cAAc;YAC3D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;YAClF,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;YACpF,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YAC5E,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;YAC9E,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;mBACrB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;mBACzB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,8BAA8B,CAAC,EAAE;gBAC9E,cAAc,CAAC,KAAK,EAAE,CAAC;aACxB;QACH,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;GAIG;AAEH;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,qBAAqB,EAAE,gBAAgB;IACxE,IAAM,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;IACzG,IAAM,wBAAwB,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAC5G,OAAO;QACL,GAAG,EAAE,qBAAqB;QAC1B,MAAM,EAAE,wBAAwB;KACjC,CAAC;AACJ,CAAC;AAED;;;;GAIG;AAEH;;;;;;GAMG;AACH,SAAS,qBAAqB,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,iBAAiB;IACvF,OAAO;QACL,IAAI,EAAE,yBAAyB,CAAC,qBAAqB,EAAE,gBAAgB,CAAC;QACxE,KAAK,EAAE,0BAA0B,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;KAC5E,CAAC;AACJ,CAAC;AAED;;;;GAIG;AAEH;;;;;GAKG;AACH,SAAS,0BAA0B,CAAC,qBAAqB,EAAE,iBAAiB;IAC1E,IAAM,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;IAC5G,IAAM,yBAAyB,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IAC/G,OAAO;QACL,GAAG,EAAE,sBAAsB;QAC3B,MAAM,EAAE,yBAAyB;KAClC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,IAAM,MAAM,GAAG;IACb,GAAG,EAAE,CAAC;IACN,QAAQ,EAAE,CAAC;IACX,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,CAAC;IACZ,SAAS,EAAE,CAAC;IACZ,YAAY,EAAE,CAAC,CAAC;IAChB,MAAM,EAAE,CAAC,CAAC;IACV,MAAM,EAAE,CAAC,CAAC;CACX,CAAC;AAEF;;GAEG;AACH,IAAI,QAAQ,CAAC;AAEb;;;;;GAKG;AACH,SAAS,cAAc;IACrB,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ,EAAE,KAAK;;QAChD,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,YAAI,GAAC,MAAM,CAAC,KAAK,CAAC,IAAG,KAAK,MAAG,CAAC;IAC7D,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED;;;;GAIG;AACH,SAAS,kCAAkC,CAAC,MAAM;IAChD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IACD,QAAQ,GAAG,QAAQ,IAAI,cAAc,EAAE,CAAC;IACxC,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,MAAM;QAClC,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,wBAAwB,CAAC,GAAG;IACnC,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,kBAAkB,CAAC;IACrD,GAAG,CAAC,mBAAmB,GAAG,kCAAkC,CAC1D,yBAAI,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAE,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,kBAAkB,EAAvB,CAAuB,CAAC,CAAC,CAAC;IAC3E,IAAI,GAAG,CAAC,kBAAkB,KAAK,GAAG,CAAC,uBAAuB,EAAE;QAC1D,GAAG,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;KACvC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,qBAAqB,CAAC,GAAG;IAChC,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,eAAe,CAAC;IAC/C,GAAG,CAAC,gBAAgB,GAAG,kCAAkC,CACvD,yBAAI,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAE,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,eAAe,EAApB,CAAoB,CAAC,CAAC,CAAC;IACxE,IAAI,GAAG,CAAC,eAAe,KAAK,GAAG,CAAC,oBAAoB,EAAE;QACpD,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;KACpC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC"}