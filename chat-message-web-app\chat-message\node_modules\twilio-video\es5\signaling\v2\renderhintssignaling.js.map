{"version": 3, "file": "renderhintssignaling.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/renderhintssignaling.js"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACnD,IAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtC,IAAA,WAAW,GAAK,OAAO,CAAC,YAAY,CAAC,YAA1B,CAA2B;AAC9C,IAAM,4BAA4B,GAAG,IAAI,CAAC,CAAC,iEAAiE;AAE5G,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB;IAAmC,wCAAc;IAC/C;;OAEG;IACH,8BAAY,WAAW,EAAE,OAAO;QAAhC,YACE,kBAAM,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,SA+B5C;QA9BC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,OAAO,CAAC;oBACjB,KAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,qDAAqD;oBACrD,KAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9D,CAAC,EAAE,4BAA4B,EAAE,KAAK,CAAC;aACxC;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,SAAS;YACxB,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,UAAA,OAAO;gBAC7B,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBACvC,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,cAAc;wBACjB,KAAI,CAAC,mBAAmB,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;wBAC5F,MAAM;oBACR;wBACE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;wBACvD,MAAM;iBACT;YACH,CAAC,CAAC,CAAC;YAEH,mFAAmF;YACnF,2BAA2B;YAC3B,KAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;;IACL,CAAC;IAED,4CAAa,GAAb;QAAA,iBAaC;QAZC,8DAA8D;QAC9D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,QAAQ;YAC9D,IAAM,UAAU,GAAG,KAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC/B,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;aACpC;YAED,IAAI,SAAS,IAAI,UAAU,EAAE;gBAC3B,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,kDAAmB,GAAnB,UAAoB,WAAW;QAA/B,iBASC;QARC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;QAC3D,WAAW,CAAC,OAAO,CAAC,UAAA,UAAU;YAC5B,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE;gBAC9B,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,UAAU,CAAC,CAAC;aAC9D;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,yCAAU,GAAV;QAAA,iBAqCC;QApCC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YACjD,OAAO;SACR;QAED,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,QAAQ;YAC9D,IAAM,UAAU,GAAG,KAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC5D,IAAM,OAAO,GAAG;oBACd,OAAO,EAAE,QAAQ;iBAClB,CAAC;gBACF,IAAI,UAAU,CAAC,cAAc,EAAE;oBAC7B,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;oBACrC,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;iBACnC;gBACD,IAAI,UAAU,CAAC,gBAAgB,EAAE;oBAC/B,qCAAqC;oBACrC,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC;oBACxD,UAAU,CAAC,gBAAgB,GAAG,KAAK,CAAC;iBACrC;gBACD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACrB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,IAAM,OAAO,GAAG;gBACd,IAAI,EAAE,cAAc;gBACpB,UAAU,EAAE;oBACV,EAAE,EAAE,SAAS,EAAE;oBACf,KAAK,OAAA;iBACN;aACF,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;SAC7B;IACH,CAAC;IAED;;;OAGG;IACH,2CAAY,GAAZ,UAAa,QAAQ,EAAE,UAAU;QAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC;QACpH,IAAI,SAAS,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE;YACxE,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1C,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;SAClC;QAED,IAAI,UAAU,CAAC,gBAAgB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,CAAC,EAAE;YACzG,qCAAqC;YACrC,UAAU,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAC1D,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACpC;QACD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,6CAAc,GAAd,UAAe,QAAQ;QACrB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IACH,2BAAC;AAAD,CAAC,AAlID,CAAmC,cAAc,GAkIhD;AAGD,MAAM,CAAC,OAAO,GAAG,oBAAoB,CAAC"}