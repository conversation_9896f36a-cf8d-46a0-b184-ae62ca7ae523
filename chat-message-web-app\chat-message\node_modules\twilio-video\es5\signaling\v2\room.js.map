{"version": 3, "file": "room.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/room.js"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,wBAAwB,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;AACvE,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACnE,IAAM,qBAAqB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACjE,IAAM,uBAAuB,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACrE,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAC3C,IAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACzC,IAAM,mBAAmB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC3D,IAAM,WAAW,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACvD,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACnE,IAAM,uBAAuB,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACrE,IAAM,oBAAoB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC/D,IAAM,uBAAuB,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAGjE,IAAA,KAQF,OAAO,CAAC,YAAY,CAAC,EAPV,2BAA2B,2CAAA,EACxC,6BAA6B,mCAAA,EAC7B,KAAK,WAAA,EACL,UAAU,gBAAA,EACV,YAAY,kBAAA,EACZ,OAAO,aAAA,EACP,WAAW,iBACY,CAAC;AAE1B,IAAM,kBAAkB,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC5D,IAAA,iBAAiB,GAAK,OAAO,CAAC,gCAAgC,CAAC,kBAA9C,CAA+C;AAExE,IAAM,yBAAyB,GAAG,KAAK,CAAC;AAExC;;GAEG;AACH;IAAqB,0BAAa;IAChC,gBAAY,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAE,qBAAqB,EAAE,OAAO;QAArF,iBA4IC;QA3IC,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACnC,eAAe,EAAE,2BAA2B;SAC7C,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzB,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,wBAAwB,0BAAA;YACxB,sBAAsB,wBAAA;YACtB,qBAAqB,uBAAA;YACrB,uBAAuB,yBAAA;YACvB,kBAAkB,EAAE,WAAW;YAC/B,mBAAmB,qBAAA;YACnB,sBAAsB,wBAAA;YACtB,uBAAuB,yBAAA;YACvB,gBAAgB,EAAE,IAAI;YACtB,cAAc,EAAE,YAAY,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI;YAC3D,sBAAsB,EAAE,yBAAyB;SAClD,EAAE,OAAO,CAAC,CAAC;QAEZ,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEvD,IAAA,KAA2F,YAAY,QAAjB,EAAzD,eAAe,sBAAA,EAAE,wBAAsC,EAApB,eAAe,mBAAG,EAAE,KAAE,CAAkB;QAChH,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAGrD,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrC,6FAA6F;YAC7F,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACjC;QAED,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAErD,qBAAqB,CAAC,sBAAsB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAErE,QAAA,kBAAM,gBAAgB,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,SAAC;QAEtE,IAAM,gBAAgB,GAAG,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAA1B,CAA0B,CAAC;QAC1D,IAAM,GAAG,GAAG,KAAI,CAAC,IAAI,CAAC;QAEtB,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,iCAAiC,EAAE;gBACjC,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,6BAA6B,EAAE;gBAC7B,KAAK,EAAE,gBAAgB,CAAC,wBAAwB;gBAChD,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,gBAAgB,CAAC,2BAA2B;aACpD;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,qBAAqB;aAC7B;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,OAAO,CAAC,mBAAmB;aACnC;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,yBAAyB,EAAE;gBACzB,KAAK,EAAE,IAAI,OAAO,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aACvE;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aACrE;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,OAAO,CAAC,uBAAuB,CACxC,gBAAgB,EAChB,gBAAgB,CAAC,2BAA2B,EAC5C,EAAE,GAAG,KAAA,EAAE,CACR;aACF;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aAC3D;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,uBAAuB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aAC9D;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aACrE;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC;aACtE;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,uBAAuB,EAAE;gBACvB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,WAAW,EAAE;gBACX,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI;aACjD;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,KAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,KAAI,CAAC,mCAAmC,EAAE,CAAC;QAC3C,KAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,4BAA4B,CAAC,KAAI,EAAE,gBAAgB,CAAC,CAAC;QACrD,0BAA0B,CAAC,KAAI,EAAE,qBAAqB,CAAC,CAAC;QACxD,qBAAqB,CAAC,KAAI,EAAE,SAAS,CAAC,CAAC;QACvC,wBAAwB,CAAC,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAE1E,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE3B,sFAAsF;QACtF,0FAA0F;QAC1F,KAAI,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,KAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;;IACnG,CAAC;IAMD,sBAAI,mCAAe;QAJnB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC;QACrD,CAAC;;;OAAA;IAMD,sBAAI,4CAAwB;QAJ5B;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,SAAS;gBACxC,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAC5B,CAAC;;;OAAA;IAMD,sBAAI,sCAAkB;QAJtB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC;QACxD,CAAC;;;OAAA;IAED;;OAEG;IACH,6CAA4B,GAA5B,UAA6B,EAAE;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,kDAAiC,GAAjC,UAAkC,EAAE;QAClC,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC;QACjE,IAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,CAAC;QAEvE,gFAAgF;QAChF,8EAA8E;QAC9E,0EAA0E;QAC1E,IAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,UAAA,aAAa,IAAI,OAAA,aAAa,CAAC,EAAE,KAAK,EAAE,IAAI,aAAa,CAAC,UAAU,KAAK,OAAO,EAA/D,CAA+D,CAAC,CAAC;QAE5H,IAAI,aAAa,EAAE;YACjB,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACjC;aAAM;YACL,mEAAmE;YACnE,sEAAsE;YACtE,0EAA0E;YAC1E,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SAChD;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,kCAAiB,GAAjB,UAAkB,aAAa;QAC7B,IAAM,QAAQ,GAAG,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAC1E,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,4BAAW,GAAX,UAAY,KAAK;QACf,IAAM,aAAa,GAAG,iBAAM,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAI,aAAa,EAAE;YACjB,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;YACxC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,kCAAiB,GAAjB,UAAkB,EAAE;QAApB,iBAKC;QAJC,OAAO,IAAI,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,aAAa;YAC1E,KAAI,CAAC,4BAA4B,CAAC,EAAE,CAAC,CAAC;YACtC,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,+CAA8B,GAA9B,UAA+B,QAAQ;QACrC,IAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;QACjF,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAI,QAAQ,mCAAgC,CAAC,CAAC;SAC9D;QACD,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IAGD;;OAEG;IACH,+CAA8B,GAA9B;QACE,IAAM,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,UAAA,WAAW,IAAI,OAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAA9B,CAA8B,CAAC,CAAC;QAC7G,OAAO,IAAI,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,8CAA6B,GAA7B,UAA8B,gBAAgB;QAA9C,iBAsBC;QArBC,IAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACtD,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,WAAW,EAAE;YAChB,WAAW,GAAG,IAAI,mBAAmB,CACnC,gBAAgB,EAChB,UAAA,QAAQ,IAAI,OAAA,KAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,EAA7C,CAA6C,EACzD,UAAC,QAAQ,EAAE,QAAQ,IAAK,OAAA,KAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,EAArF,CAAqF,EAC7G,UAAC,QAAQ,EAAE,IAAI,IAAK,OAAA,KAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAvD,CAAuD,EAC3E,UAAA,QAAQ,IAAI,OAAA,KAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAnD,CAAmD,CAChE,CAAC;YACF,WAAW,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;gBACxD,IAAI,KAAK,KAAK,cAAc,EAAE;oBAC5B,WAAW,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;oBACzD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;oBAC1C,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;iBACnF;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;SACtC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,0BAAS,GAAT;QACE,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,0CAAyB,GAAzB,UAA0B,MAAM;QACxB,IAAA,KAAiD,IAAI,CAAC,gBAAgB,EAApE,gBAAgB,sBAAA,EAAE,wBAAwB,8BAA0B,CAAC;QAC7E,IAAI,gBAAgB,IAAI,IAAI,CAAC,6BAA6B,GAAG,wBAAwB,EAAE;YACrF,IAAI,CAAC,6BAA6B,GAAG,wBAAwB,CAAC;YAC9D,OAAO,MAAM,CAAC,MAAM,CAAC;gBACnB,iBAAiB,EAAE,6BAA6B,CAAC,gBAAgB,CAAC;aACnE,EAAE,MAAM,CAAC,CAAC;SACZ;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD;;OAEG;IACH,iDAAgC,GAAhC;QACE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,4CAA2B,GAA3B,UAA4B,mBAAmB;QAC7C,wBAAwB;QACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;YACpC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC;SACxC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,wBAAO,GAAP,UAAQ,SAAS;QAAjB,iBAgHC;QA/GC,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE;YACpF,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzD,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,UAAU;gBAC5C,IAAI,UAAU,CAAC,EAAE,EAAE;oBACjB,KAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAClD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;iBACrD;qBAAM,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,KAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBAC9E,KAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;iBAClE;YACH,CAAC,CAAC,CAAC;YAEH,IAAM,qBAAmB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM;iBAC5D,MAAM,CAAC,UAAA,UAAU,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,EAAE,EAAf,CAAe,CAAC;iBACrC,GAAG,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,CAAC,GAAG,EAAd,CAAc,CAAC,CAAC,CAAC;YAEtC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,QAAQ;gBACzC,IAAI,CAAC,qBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBACtC,KAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBACnC;YACH,CAAC,CAAC,CAAC;SACJ;QAED,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;QAErC,+CAA+C;QAC/C,sDAAsD;QACtD,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,gBAAgB;YACrD,IAAI,gBAAgB,CAAC,GAAG,KAAK,KAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACtD,OAAO;aACR;YAED,4FAA4F;YAC5F,4FAA4F;YAC5F,wFAAwF;YACxF,4DAA4D;YAC5D,IAAM,+BAA+B,GAAG,KAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACzG,IAAI,+BAA+B,IAAI,gBAAgB,CAAC,QAAQ,IAAI,+BAA+B,EAAE;gBACnG,OAAO;aACR;YAED,IAAI,+BAA+B,EAAE;gBACnC,KAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;aACrE;YACD,IAAM,WAAW,GAAG,KAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YACzE,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACrC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW;gBACnC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;oBACxC,WAAW,CAAC,UAAU,EAAE,CAAC;iBAC1B;YACH,CAAC,CAAC,CAAC;SACJ;QAED,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE1B,+CAA+C;QAC/C,sDAAsD;QACtD,wBAAwB;QACxB,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;SAC7F;QAED,IAAI,SAAS,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAC5C;QAED,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE;YACjF,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;YACvD,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gBACtC,IAAI,KAAK,CAAC,GAAG,EAAE;oBACb,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;iBAC1C;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SACnD;QAED,IAAI,SAAS,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAC3B,SAAS,CAAC,WAAW,CAAC,GAAG,EACzB,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED;YACE,IAAI,CAAC,yBAAyB;YAC9B,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,wBAAwB;SAC9B,CAAC,OAAO,CAAC,UAAA,cAAc;YACtB,IAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,OAAO;mBACtB,SAAS,CAAC,eAAe;mBACzB,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC;mBAClC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS;mBAC5C,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE;gBACzE,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC1E;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM;YAClD,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE;YAC/D,IAAI,CAAC,4BAA4B,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,4CAA2B,GAA3B;QAAA,iBAsBC;QArBC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK,EAAE,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI;gBACxB,OAAO,KAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;oBACnF,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,QAAA,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,aAAa;gBACpB,KAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAA,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAM,cAAc,GAAG,UAAA,KAAK;YAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC1B,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAE;oBACpC,KAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC;QAEF,4DAA4D;QAC5D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAC;QAC1F,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAC;IACzE,CAAC;IAED,6CAA4B,GAA5B;QAAA,iBA4BC;QA3BC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,SAAS,EAAE,QAAQ;YAC9D,IAAI;gBACF,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;gBACnE,IAAM,cAAY,GAAG,IAAI,GAAG,EAAE,CAAC;gBAC/B,QAAQ,CAAC,OAAO,CAAC,UAAA,QAAQ,IAAI,OAAA,cAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAhC,CAAgC,CAAC,CAAC;gBAC/D,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;oBACxB,IAAI,cAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;wBAC9B,qEAAqE;wBACrE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAI,QAAQ,uDAAoD,CAAC,CAAC;qBACjF;oBACD,cAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBACH,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW;oBACnC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;wBAC9B,IAAM,IAAI,GAAG,cAAY,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACzC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;4BAC/B,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;4BAC5B,cAAY,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;yBAChC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,4FAA4F;gBAC5F,cAAY,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,QAAQ,IAAK,OAAA,KAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAjD,CAAiD,CAAC,CAAC;aAC7F;YAAC,OAAO,EAAE,EAAE;gBACX,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,CAAC,CAAC;aAC3D;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8CAA6B,GAA7B;QAAA,iBAEC;QADC,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,kBAAkB,CAAC,KAAI,CAAC,yBAAyB,CAAC,qBAAqB,CAAC,EAA7E,CAA6E,CAAC,CAAC;IACpI,CAAC;IAED,4CAA2B,GAA3B;QAAA,iBAIC;QAHC,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,eAAe,EAAE,UAAA,IAAI;YACnD,KAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oDAAmC,GAAnC;QAAA,iBAqBC;QApBC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,OAAO,EAAE;YACxC,IAAM,qBAAqB,GAAG,IAAI,KAAI,CAAC,sBAAsB,CAAC,KAAI,CAAC,sBAAsB,EAAE,KAAI,CAAC,wBAAwB,CAAC,CAAC;YAC1H,KAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;YACpD,qBAAqB,CAAC,EAAE,CAAC,SAAS,EAAE;gBAClC,IAAI,KAAI,CAAC,kBAAkB,KAAK,QAAQ,EAAE;oBACxC,OAAO;iBACR;gBACD,KAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAC1C,qBAAqB,CAAC,KAAK,EAC3B,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAChC,KAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW;oBACnC,IAAM,MAAM,GAAG,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;oBACvE,IAAI,MAAM,EAAE;wBACV,WAAW,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;qBAC1D;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,qBAAqB,CAAC,KAAK,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,UAAU,EAAE,cAAM,OAAA,KAAI,CAAC,8BAA8B,EAAE,EAArC,CAAqC,CAAC,CAAC;IAC5F,CAAC;IAED,+CAA8B,GAA9B;QACE,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;SACpC;IACH,CAAC;IAED;;;OAGG;IACH,yBAAQ,GAAR;QAAA,iBAWC;QAVC,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,SAAS;YAC1D,OAAA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,EAAc;oBAAd,KAAA,aAAc,EAAb,EAAE,QAAA,EAAE,QAAQ,QAAA;gBAC9C,OAAA,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE;wBAC/B,oBAAoB,EAAE,0BAA0B,CAAC,KAAI,EAAE,QAAQ,CAAC,oBAAoB,CAAC;wBACrF,oBAAoB,EAAE,0BAA0B,CAAC,KAAI,EAAE,QAAQ,CAAC,oBAAoB,CAAC;wBACrF,qBAAqB,EAAE,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,qBAAqB,CAAC;wBACxF,qBAAqB,EAAE,2BAA2B,CAAC,KAAI,EAAE,QAAQ,CAAC,qBAAqB,CAAC;qBACzF,CAAC,CAAC;YALH,CAKG,CACJ,CAAC;QAPF,CAOE,CACH,CAAC;IACJ,CAAC;IACH,aAAC;AAAD,CAAC,AAviBD,CAAqB,aAAa,GAuiBjC;AAED;;;;;;GAMG;AACH,SAAS,qBAAqB,CAAC,OAAO,EAAE,UAAU;IAChD,OAAO,UAAU,CAAC,MAAM,CAAC,UAAC,UAAU,EAAE,SAAS;QAC7C,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,QAAQ;YACb,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;YACjE,CAAC,CAAC,UAAU,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED;;;;;;GAMG;AACH,SAAS,0BAA0B,CAAC,MAAM,EAAE,eAAe;IACzD,OAAO,qBAAqB,CAAC,MAAM,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AACnE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,2BAA2B,CAAC,MAAM,EAAE,gBAAgB;IAC3D,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,EAAS;YAAT,KAAA,aAAS,EAAR,GAAG,QAAA,EAAE,EAAE,QAAA;QAAM,OAAA,CAAC,EAAE,EAAE,GAAG,CAAC;IAAT,CAAS,CAAC,CAAC,CAAC;IAChG,OAAO,qBAAqB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;GAQG;AAEH,SAAS,4BAA4B,CAAC,MAAM,EAAE,gBAAgB;IAC5D,IAAM,uBAAuB,GAAG,WAAW,CAAC;QAC1C,MAAM,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAM,WAAW,GAAG,WAAW,CAAC;QAC9B,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,gBAAgB,EAAxB,CAAwB,CAAC,CAAC;QAC3F,MAAM,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAC/C,gBAAgB,CAAC,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IACjD,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;IAExD,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;QACnD,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,gBAAgB,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;YAC3D,gBAAgB,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAC7D,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;YACpE,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YACpD,gBAAgB,CAAC,UAAU,EAAE,CAAC;SAC/B;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,EAAE,CAAC,iCAAiC,EAAE;QACnC,IAAA,gBAAgB,GAA+B,MAAM,iBAArC,EAAE,wBAAwB,GAAK,MAAM,yBAAX,CAAY;QACtD,IAAA,QAAQ,GAAU,gBAAgB,SAA1B,EAAE,GAAG,GAAK,gBAAgB,IAArB,CAAsB;QAC3C,QAAQ,wBAAwB,EAAE;YAChC,KAAK,WAAW;gBACd,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,cAAc;gBACjB,gBAAgB,CAAC,YAAY,EAAE,CAAC;gBAChC,MAAM;SACT;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,0BAA0B,CAAC,MAAM,EAAE,qBAAqB;IAC/D,qBAAqB,CAAC,EAAE,CAAC,aAAa,EAAE,SAAS,aAAa,CAAC,WAAW;QACxE,MAAM,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IACH,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAE7C,qBAAqB,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,YAAY,CAAC,UAAU;QACrE,MAAM,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IACH,qBAAqB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAE5C,qBAAqB,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9E,qBAAqB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC5C,qBAAqB,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAEpF,qBAAqB,CAAC,EAAE,CAAC,wBAAwB,EAAE;QACjD,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,qBAAqB,CAAC,EAAE,CAAC,2BAA2B,EAAE;QACpD,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,kBAAkB,KAAK,QAAQ,EAAE;YAC1C,IAAI,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,IAAI,EAAE;gBACxD,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;aACnD;YACD,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW;gBACrC,IAAI,WAAW,CAAC,mBAAmB,KAAK,IAAI,EAAE;oBAC5C,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;iBACvC;YACH,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAM,EAAE,SAAS;IAC9C,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrD,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE,KAAK;QAC7D,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,IAAI,MAAM,CAAC,KAAK,KAAK,cAAc,EAAE;gBACnC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC3B;YACD,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SACxD;QACD,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU;IAC7D,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;IACtC,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAM,QAAQ,GAAG,WAAW,CAAC;QAC3B,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,KAAK;YAC1B,eAAe,GAAG,CAAC,eAAe,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,EAAE;gBACzB,sEAAsE;gBACtE,oEAAoE;gBACpE,8DAA8D;gBAC9D,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAE5E,kFAAkF;gBAClF,4CAA4C;gBAC5C,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;oBACxD,eAAe,EAAE,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAC,SAAS,EAAE,CAAC;wBAC7D,OAAA,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;oBAArG,CAAqG,CAAC;oBACxG,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAC,SAAS,EAAE,CAAC;wBACjE,OAAA,iCAAiC,CAAC,SAAS,EAAE,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;oBAAnG,CAAmG,CAAC;oBACtG,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAC,SAAS,EAAE,CAAC;wBACjE,OAAA,iCAAiC,CAAC,SAAS,EAAE,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;oBAAnG,CAAmG,CAAC;oBACtG,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;oBACzC,eAAe,EAAE,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAC,SAAS,EAAE,CAAC;wBAC7D,OAAA,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;oBAArG,CAAqG,CAAC;iBACzG,CAAC,CAAC;gBAEH,+FAA+F;gBAC/F,IAAM,IAAI,GAAG,OAAO,CAAC;oBACnB,sBAAsB;oBACtB,sBAAsB;oBACtB,uBAAuB;oBACvB,uBAAuB;iBACxB,EAAE,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAC,EAAkB;wBAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;oBAAO,OAAG,QAAQ,SAAI,IAAM;gBAArB,CAAqB,CAAC,EAA/D,CAA+D,CAAC,CAAC;gBAC5E,IAAM,iCAAiC,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;gBACnG,iCAAiC,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAC;gBAElF,IAAI,eAAe,EAAE;oBACnB,uEAAuE;oBACvE,iEAAiE;oBACjE,uBAAuB;oBACvB,IAAM,sBAAsB,GAAG,wBAAwB,CACrD,QAAQ,CAAC,sBAAsB,EAC/B,MAAM,CAAC,gBAAgB,CAAC,CAAC;oBAE3B,SAAS,CAAC,YAAY,CACpB,SAAS,EACT,2BAA2B,EAC3B,MAAM,EACN,sBAAsB,CAAC,CAAC;iBAC3B;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE;YACD,cAAc;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,UAAU,CAAC,CAAC;IAEf,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,cAAc,CAAC,KAAK;QACrD,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;SACvD;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAI;IAC/B,IAAM,0BAA0B,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAEzE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,QAAQ;QACjD,IAAM,cAAc,GAAG,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5C,cAAc,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SAC9E;IACH,CAAC,CAAC,CAAC;IAEH,0BAA0B,CAAC,OAAO,CAAC,UAAA,cAAc;QAC/C,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,gBAAgB,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE;YAC/F,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,aAAa,IAAI,OAAA,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAjD,CAAiD,CAAC,CAAC;SAC1G;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,iCAAiC,CAAC,UAAU,EAAE,aAAa,EAAE,mBAAmB;IAErF,IAAA,aAAa,GAIX,aAAa,cAJF,EACb,WAAW,GAGT,aAAa,YAHJ,EACX,eAAe,GAEb,aAAa,gBAFA,EACf,oBAAoB,GAClB,aAAa,qBADK,CACJ;IAClB,IAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IAC1D,IAAM,GAAG,GAAM,UAAU,CAAC,QAAQ,SAAI,UAAU,CAAC,IAAM,CAAC;IACxD,IAAM,wBAAwB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IAE3E,IAAI,OAAO,eAAe,KAAK,QAAQ,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QAC5E,IAAM,qCAAqC,GAAG,wBAAwB,CAAC,GAAG,CAAC,gBAAgB,CAAC;eACvF,IAAI,kBAAkB,EAAE,CAAC;QAC9B,qCAAqC,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC;QACvF,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,GAAG,EAAE,CAAC,CAAC;QAC7F,wBAAwB,CAAC,GAAG,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,CAAC;KACvF;IACD,IAAI,OAAO,oBAAoB,KAAK,QAAQ,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QAC/E,IAAM,yCAAyC,GAAG,wBAAwB,CAAC,GAAG,CAAC,oBAAoB,CAAC;eAC/F,IAAI,kBAAkB,EAAE,CAAC;QAC9B,yCAAyC,CAAC,SAAS,CAAC,oBAAoB,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;QAC9F,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,yCAAyC,CAAC,GAAG,EAAE,CAAC,CAAC;QACrG,wBAAwB,CAAC,GAAG,CAAC,oBAAoB,EAAE,yCAAyC,CAAC,CAAC;KAC/F;IACD,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;IACvD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED;;;;GAIG;AACH,SAAS,kCAAkC,CAAC,UAAU,EAAE,aAAa,EAAE,mBAAmB;IAEtF,IAAA,yBAAyB,GAKvB,aAAa,0BALU,EACzB,aAAa,GAIX,aAAa,cAJF,EACb,iBAAiB,GAGf,aAAa,kBAHE,EACjB,wBAAwB,GAEtB,aAAa,yBAFS,EACxB,eAAe,GACb,aAAa,gBADA,CACC;IAClB,IAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IAC1D,IAAM,GAAG,GAAM,UAAU,CAAC,QAAQ,SAAI,UAAU,CAAC,IAAM,CAAC;IACxD,IAAM,wBAAwB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IAE3E,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE;QACjD,mBAAmB,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;KAC3E;IACD,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QAC5E,IAAM,qCAAqC,GAAG,wBAAwB,CAAC,GAAG,CAAC,gBAAgB,CAAC;eACvF,IAAI,kBAAkB,EAAE,CAAC;QAC9B,qCAAqC,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC;QACvF,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,GAAG,EAAE,CAAC,CAAC;QAC7F,wBAAwB,CAAC,GAAG,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,CAAC;KACvF;IACD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;QACzF,IAAM,2CAA2C,GAAG,wBAAwB,CAAC,GAAG,CAAC,sBAAsB,CAAC;eACnG,IAAI,kBAAkB,EAAE,CAAC;QAC9B,2CAA2C,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI,EAAE,wBAAwB,CAAC,CAAC;QAC1G,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,GAAG,EAAE,CAAC,CAAC;QACzG,wBAAwB,CAAC,GAAG,CAAC,sBAAsB,EAAE,2CAA2C,CAAC,CAAC;KACnG;IACD,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;IACvD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,SAAS,wBAAwB,CAAC,sBAAsB,EAAE,gBAAgB;IACxE,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC;QACrC,wBAAwB,EAAE,CAAC;QAC3B,wBAAwB,EAAE,CAAC;QAC3B,aAAa,EAAE,CAAC;QAChB,SAAS,EAAE,CAAC;QACZ,mBAAmB,EAAE,CAAC;QACtB,oBAAoB,EAAE,CAAC;QACvB,2BAA2B,EAAE,CAAC;QAC9B,uBAAuB,EAAE,CAAC;QAC1B,SAAS,EAAE,KAAK;QAChB,gBAAgB,EAAE,gBAAgB;QAClC,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,KAAK;QACf,gBAAgB,EAAE,CAAC;QACnB,YAAY,EAAE,CAAC;QACf,iBAAiB,EAAE,CAAC;QACpB,aAAa,EAAE,CAAC;QAChB,uBAAuB,EAAE,CAAC;QAC1B,mBAAmB,EAAE,CAAC;QACtB,KAAK,EAAE,QAAQ;QACf,kBAAkB,EAAE,CAAC;QACrB,WAAW,EAAE,EAAE;QACf,QAAQ,EAAE,KAAK;KAChB,EAAE,YAAY,CAAC,sBAAsB,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAErD,sBAAsB,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;QACpD,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,KAAK;QACd,EAAE,EAAE,EAAE;QACN,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,KAAK;QACf,GAAG,EAAE,EAAE;KACR,EAAE,YAAY,CAAC,sBAAsB,CAAC,cAAc,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAEpE,sBAAsB,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC;QACrD,aAAa,EAAE,MAAM;QACrB,EAAE,EAAE,EAAE;QACN,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,KAAK;QACf,GAAG,EAAE,EAAE;KACR,EAAE,YAAY,CAAC,sBAAsB,CAAC,eAAe,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAErE,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC"}