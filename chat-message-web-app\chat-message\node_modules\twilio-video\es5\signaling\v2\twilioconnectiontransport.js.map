{"version": 3, "file": "twilioconnectiontransport.js", "sourceRoot": "", "sources": ["../../../lib/signaling/v2/twilioconnectiontransport.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACnD,IAAM,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC3D,IAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC7C,IAAA,sBAAsB,GAAK,OAAO,CAAC,sBAAsB,CAAC,uBAApC,CAAqC;AACnE,IAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACxC,IAAA,KAAwC,OAAO,CAAC,sBAAsB,CAAC,EAArE,QAAQ,cAAA,EAAE,WAAW,iBAAA,EAAE,UAAU,gBAAoC,CAAC;AAExE,IAAA,KAOF,OAAO,CAAC,YAAY,CAAC,EANvB,6BAA6B,mCAAA,EAC7B,2BAA2B,iCAAA,EAC3B,0BAA0B,gCAAA,EAC1B,sBAAsB,4BAAA,EACtB,YAAY,kBAAA,EACZ,gBAAgB,sBACO,CAAC;AAEpB,IAAA,KAKF,OAAO,CAAC,gCAAgC,CAAC,EAJ3C,iBAAiB,uBAAA,EACjB,kBAAkB,wBAAA,EAClB,wBAAwB,8BAAA,EACxB,wBAAwB,8BACmB,CAAC;AAE9C,IAAM,WAAW,GAAG,CAAC,CAAC;AACtB,IAAM,WAAW,GAAG,CAAC,CAAC;AAEtB;;;;;;;;;;;;;;;;;;;;;;EAsBE;AAEF,IAAM,MAAM,GAAG;IACb,UAAU,EAAE;QACV,WAAW;QACX,cAAc;KACf;IACD,SAAS,EAAE;QACT,cAAc;QACd,SAAS;KACV;IACD,OAAO,EAAE;QACP,WAAW;QACX,cAAc;KACf;IACD,YAAY,EAAE,EAAE;CACjB,CAAC;AAEF;;;;;;GAMG;AACH;IAAwC,6CAAY;IAClD;;;;;;;;OAQG;IACH,mCAAY,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,QAAQ,EAAE,OAAO;QAAzF,iBAsGC;QArGC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,OAAO,EAAE,cAAc;YACvB,gBAAgB,kBAAA;YAChB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,cAAc,EAAE,IAAI;YACpB,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,YAAY,EAAE;SAC1B,EAAE,OAAO,CAAC,CAAC;QACZ,QAAA,kBAAM,YAAY,EAAE,MAAM,CAAC,SAAC;QAE5B,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,YAAY,EAAE;gBACZ,KAAK,EAAE,WAAW;aACnB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,OAAO,CAAC,iBAAiB;aACjC;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,OAAO,CAAC,gBAAgB;aAChC;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,OAAO,CAAC,eAAe;aAC/B;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,qBAAqB;aACrC;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,aAAa;gBAC5B,QAAQ,EAAE,KAAK;aAChB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,OAAO,CAAC,WAAW;aAC3B;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;oBACtC,CAAC,CAAC,UAAU;oBACZ,CAAC,CAAC,SAAS;aACd;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,gBAAgB;aACxB;YACD,KAAK,EAAE;gBACL,KAAK,EAAE,IAAI;aACZ;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,gBAAgB,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,cAAc;aAC1E;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,OAAO,CAAC,cAAc;aAC9B;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,OAAO;aACf;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,qBAAqB;aAC7B;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC;aACnD;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,aAAa;aAC7B;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,OAAO,CAAC,cAAc;aAC9B;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,EAAE;aACV;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,EAAE;aACV;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO,CAAC,SAAS;aACzB;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,QAAQ;aAChB;SACF,CAAC,CAAC;QAGH,cAAc,CAAC,KAAI,CAAC,CAAC;;IACvB,CAAC;IAED;;;;OAIG;IACH,2EAAuC,GAAvC;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,WAAW;aACrB,CAAC;SACH;QAED,IAAM,IAAI,GAAG;YACX,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,MAAM;SAChB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEd,IAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YAC9C,gBAAgB,EAAE,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE;YACzD,IAAI,MAAA;YACJ,OAAO,EAAE,WAAW;SACrB,CAAC;QAEF,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAE7C,OAAO,CAAC,SAAS,GAAG;gBAClB,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,WAAW;gBACxB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;YAEF,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,CAAC,iBAAiB,GAAG,6BAA6B,CACvD,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC3B;YAED,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxB,OAAO,CAAC,WAAW,CAAC,cAAc,GAAG,0BAA0B,CAC7D,IAAI,CAAC,eAAe,CAAC,CAAC;aACzB;YAED,OAAO,CAAC,eAAe,GAAG,2BAA2B,CACnD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAE/B,OAAO,CAAC,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACxE,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;YAC5B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;SACnC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAChC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;SACnC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;YACpC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SACjC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,qDAAiB,GAAjB;QACE,OAAO;YACL,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,yEAAqC,GAArC;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,uCAAuC,EAAE,CAAC;QAC/D,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC7C;IACH,CAAC;IAED;;;;;OAKG;IACH,8CAAU,GAAV,UAAW,KAAK;QACd,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,qCAAqC,EAAE,CAAC;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,2CAAO,GAAP,UAAQ,MAAM;QACZ,QAAQ,IAAI,CAAC,KAAK,EAAE;YAClB,KAAK,WAAW;gBACd,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;oBAC/C,OAAO,EAAE,IAAI,CAAC,QAAQ;oBACtB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,WAAW;iBACrB,EAAE,MAAM,CAAC,CAAC,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,KAAK,YAAY,CAAC;YAClB,KAAK,SAAS;gBACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,KAAK,cAAc,CAAC;YACpB;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;IAED;;;;;;;OAOG;IACH,gDAAY,GAAZ,UAAa,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO;QACtC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACH,wCAAI,GAAJ;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,CAAC,qCAAqC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,+CAAW,GAAX,UAAY,OAAO,EAAE,cAAc;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,cAAc,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,sDAAkB,GAAlB;QAAA,iBAwBC;QAvBC,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAChC,6CAA6C;YAC7C,4CAA4C;YAC5C,8CAA8C;YAC9C,OAAO,IAAI,CAAC;SACb;QAED,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC;gBAC/B,0CAA0C;gBAC1C,gCAAgC;gBAChC,IAAI,KAAI,CAAC,aAAa,EAAE;oBACtB,4CAA4C;oBAC5C,KAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;iBAC5B;YACH,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5B;QAED,sDAAsD;QACtD,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO;YACxB,KAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,wDAAoB,GAApB;QACE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;IACH,CAAC;IACH,gCAAC;AAAD,CAAC,AA5UD,CAAwC,YAAY,GA4UnD;AAED;;;GAGG;AAEH;;;GAGG;AAEH,SAAS,qBAAqB,CAAC,eAAe;IAC5C,OAAO,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAC,mBAAmB,EAAE,MAAM;QACnE,IAAM,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC;QAE7D,sDAAsD;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE;YAC9C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;SAC1C;aAAM,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE;YACpD,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE;gBAC9D,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;aAC1C;SACF;QAED,6CAA6C;QAC7C,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE;YAC9B,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;SAC1B;aAAM,IAAI,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE;YACpC,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC9C,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;aAC1B;SACF;QAED,2BAA2B;QAC3B,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7C,OAAO,mBAAmB,CAAC;IAC7B,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,aAAa,CAAC,OAAO;IAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,OAAO,EAAE,MAAM;QACpC,sDAAsD;QACtD,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE;YAC9C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;SAC1C;aAAM,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE;YACpD,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE;gBAC9D,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;aAC1C;SACF;QAED,0DAA0D;QAC1D,wBAAwB;QACxB,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,EAAE;YACxD,OAAO,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SAC3E;aAAM,IAAI,OAAO,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,EAAE;YAC9D,OAAO,CAAC,gBAAgB,GAAG,qBAAqB,CAC9C,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAAS,cAAc,CAAC,SAAS;IAC/B,SAAS,6BAA6B;QACpC,IAAI,SAAS,CAAC,KAAK,KAAK,cAAc,EAAE;YACtC,OAAO;SACR;QACD,IAAI,SAAS,CAAC,iBAAiB,EAAE;YAC/B,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;SACtE;QACO,IAAA,iBAAiB,GAAiC,SAAS,kBAA1C,EAAE,QAAQ,GAAuB,SAAS,SAAhC,EAAE,SAAS,GAAY,SAAS,UAArB,EAAE,KAAK,GAAK,SAAS,MAAd,CAAe;QAC5D,IAAA,gBAAgB,GAAK,QAAQ,iBAAb,CAAc;QAEtC,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC;YACrE,SAAS,EAAE,KAAK,KAAK,YAAY,IAAI,iBAAiB,KAAK,SAAS;gBAClE,CAAC,CAAC,SAAS,CAAC,iBAAiB,EAAE;gBAC/B,CAAC,CAAC,SAAS,CAAC,uCAAuC,EAAE;SACxD,EAAE,QAAQ,CAAC,CAAC,CAAC;QAEd,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAA,MAAM;YACnC,IAAI,MAAM,KAAK,gBAAgB,CAAC,WAAW,CAAC,KAAK,EAAE;gBACjD,UAAU,EAAE,CAAC;aACd;iBAAM;gBACL,UAAU,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC,CAAC;QAEH,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC9C,SAAS,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IACjD,CAAC;IAED,SAAS,UAAU,CAAC,KAAK;QACvB,IAAI,SAAS,CAAC,KAAK,KAAK,cAAc,EAAE;YACtC,OAAO;SACR;QACD,IAAI,CAAC,KAAK,EAAE;YACV,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO;SACR;QAED,IAAM,cAAc,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE;YACnB,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,KAAK,gBAAgB,CAAC,WAAW,CAAC,IAAI;gBACrE,CAAC,CAAC,IAAI,wBAAwB,EAAE;gBAChC,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC;YACnC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAClC,OAAO;SACR;QAED,IAAI,SAAS,CAAC,KAAK,KAAK,WAAW,EAAE;YACnC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC9B;QAED,cAAc,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACrD,CAAC;IAED,SAAS,aAAa,CAAC,OAAO;QAC5B,IAAI,SAAS,CAAC,KAAK,KAAK,cAAc,EAAE;YACtC,OAAO;SACR;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE;YAC5B,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACvE,OAAO;SACR;QACD,QAAQ,SAAS,CAAC,KAAK,EAAE;YACvB,KAAK,WAAW;gBACd,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,WAAW,CAAC;oBACjB,KAAK,QAAQ,CAAC;oBACd,KAAK,QAAQ,CAAC;oBACd,KAAK,SAAS;wBACZ,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;wBACnC,OAAO;oBACT,KAAK,cAAc;wBACjB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW;4BACjD,CAAC,CAAC,IAAI,kBAAkB,EAAE;4BAC1B,CAAC,CAAC,IAAI,CAAC,CAAC;wBACV,OAAO;oBACT;wBACE,cAAc;wBACd,OAAO;iBACV;YACH,KAAK,YAAY;gBACf,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,MAAM;wBACT,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;4BAClD,SAAS,CAAC,qCAAqC,EAAE,CAAC;wBACpD,CAAC,CAAC,CAAC;wBACH,OAAO;oBACT,KAAK,WAAW;wBACd,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;wBACxE,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;wBACrC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBAC/B,OAAO;oBACT,KAAK,QAAQ,CAAC;oBACd,KAAK,QAAQ;wBACX,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACzC,OAAO;oBACT,KAAK,cAAc;wBACjB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW;4BACjD,CAAC,CAAC,IAAI,kBAAkB,EAAE;4BAC1B,CAAC,CAAC,IAAI,CAAC,CAAC;wBACV,OAAO;oBACT;wBACE,cAAc;wBACd,OAAO;iBACV;YACH,KAAK,SAAS;gBACZ,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,WAAW,CAAC;oBACjB,KAAK,QAAQ;wBACX,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACzC,OAAO;oBACT,KAAK,QAAQ;wBACX,SAAS,CAAC,oBAAoB,EAAE,CAAC;wBACjC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;wBACnC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBAC/B,OAAO;oBACT,KAAK,cAAc;wBACjB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW;4BACjD,CAAC,CAAC,IAAI,kBAAkB,EAAE;4BAC1B,CAAC,CAAC,IAAI,CAAC,CAAC;wBACV,OAAO;oBACT;wBACE,cAAc;wBACd,OAAO;iBACV;YACH;gBACE,aAAa;gBACb,OAAO;SACV;IACH,CAAC;IAED,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,YAAY,CAAC,KAAK;QACtD,QAAQ,KAAK,EAAE;YACb,KAAK,WAAW,CAAC,CAAC;gBAChB,IAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACnD,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC3C;gBACD,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,MAAM,IAAI,OAAA,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAAjC,CAAiC,CAAC,CAAC;gBAC1F,OAAO;aACR;YACD,KAAK,cAAc;gBACjB,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBACrE,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBACvD,OAAO;YACT,KAAK,SAAS;gBACZ,cAAc;gBACd,OAAO;YACT;gBACE,aAAa;gBACb,OAAO;SACV;IACH,CAAC,CAAC,CAAC;IAEK,IAAA,QAAQ,GAAwB,SAAS,SAAjC,EAAE,iBAAiB,GAAK,SAAS,kBAAd,CAAe;IAC1C,IAAA,UAAU,GAAa,QAAQ,WAArB,EAAE,MAAM,GAAK,QAAQ,OAAb,CAAc;IAExC,IAAI,iBAAiB,KAAK,UAAU,EAAE;QACpC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;KACxD;SAAM;QACL,6BAA6B,EAAE,CAAC;KACjC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,yBAAyB,CAAC"}