{"version": 3, "file": "statemachine.js", "sourceRoot": "", "sources": ["../lib/statemachine.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;AACpD,IAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AAE/B;;;;;;;;;;GAUG;AACH;IAA2B,gCAAY;IACrC;;;;OAIG;IACH,sBAAY,YAAY,EAAE,MAAM;QAAhC,YACE,iBAAO,SAiDR;QAhDC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,YAAY,CAAC;QACzB,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,KAAK,EAAE;gBACL,GAAG;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,GAAG,YAAC,KAAK;oBACP,IAAI,GAAG,KAAK,CAAC;gBACf,CAAC;aACF;YACD,gBAAgB,EAAE;gBAChB,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC;aACzB;YACD,MAAM,EAAE;gBACN,GAAG;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,GAAG,YAAC,MAAM;oBACR,KAAK,GAAG,MAAM,CAAC;gBACjB,CAAC;aACF;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,MAAM;aACd;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,IAAI,KAAK,IAAI,CAAC;gBACvB,CAAC;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,EAAE,CAAC,cAAc,EAAE,UAAA,KAAK;YAC3B,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,QAAQ;gBAClC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;;IACL,CAAC;IAED;;;;;OAKG;IACH,mCAAY,GAAZ,UAAa,IAAI;QAAjB,iBAiBC;QAhBC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;YAC9B,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;SACxE;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9B,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAElC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,OAAO;YAClC,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,OAAO,OAAO,CAAC;QACjB,CAAC,EAAE,UAAA,KAAK;YACN,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,iEAAiE;IACjE,yFAAyF;IACzF,8BAAO,GAAP,UAAQ,IAAI,EAAE,kBAAkB;QAC9B,IAAI,GAAG,CAAC;QACR,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,SAAS,WAAW,CAAC,KAAK;YACxB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;aACjC;YACD,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;QACH,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI;YAClD,GAAG,GAAG,IAAI,CAAC;YACX,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM;YAC7B,WAAW,EAAE,CAAC;YACd,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,WAAW,CAAC,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,8BAAO,GAAP,UAAQ,GAAG;QACT,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;IAC5B,CAAC;IAED;;;;;;;;OAQG;IACH,8BAAO,GAAP,UAAQ,QAAQ,EAAE,IAAI,EAAE,OAAO;QAC7B,wCAAwC;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,8BAA2B,IAAI,CAAC,KAAK,gBAAS,QAAQ,OAAG,CAAC,CAAC;SAC5E;QAED,mCAAmC;QACnC,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;QAED,kCAAkC;QAClC,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,IAAI,IAAI,EAAE;YACR,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SAC/B;QAED,sEAAsE;QACtE,6CAA6C;QAC7C,IAAM,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAEnE,iBAAiB;QACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,IAAI,aAAa,EAAE,OAAO,CAAC,CAAC;QAEzD,6CAA6C;QAC7C,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,mDAAmD;QACnD,IAAI,aAAa,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;SACjC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACH,kCAAW,GAAX,UAAY,GAAG;QACb,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAkC,GAAG,CAAC,IAAI,4CAAyC,CAAC,CAAC;SACtG;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,oCAAkC,GAAG,CAAC,IAAI,iBAAY,IAAI,CAAC,KAAK,CAAC,IAAI,kBAAe,CAAC,CAAC;SACvG;QACD,IAAI,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,GAAG,CAAC,OAAO,EAAE,CAAC;SACf;aAAM;YACL,GAAG,CAAC,KAAK,EAAE,CAAC;SACb;IACH,CAAC;IAED;;;;;;OAMG;IACH,4CAAqB,GAArB,UAAsB,GAAG;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAkC,GAAG,CAAC,IAAI,4CAAyC,CAAC,CAAC;SACtG;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,oCAAkC,GAAG,CAAC,IAAI,iBAAY,IAAI,CAAC,KAAK,CAAC,IAAI,kBAAe,CAAC,CAAC;SACvG;QACD,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,GAAG,CAAC,OAAO,EAAE,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACH,+BAAQ,GAAR,UAAS,SAAS;QAAlB,iBAgBC;QAfC,iBAAiB;QACjB,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,IAAM,KAAG,GAAG,SAAS,CAAC;YACtB,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO;gBACxB,OAAO,CAAC,KAAI,CAAC,YAAY,CAAC,KAAG,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;SACJ;QAED,WAAW;QACX,IAAM,IAAI,GAAG,SAAS,CAAC;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1C;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;;;OAQG;IACH,mCAAY,GAAZ,UAAa,SAAS;QACpB,IAAM,GAAG,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QAC7D,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QAExC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,iCAA+B,IAAI,8BAAyB,IAAI,CAAC,KAAK,CAAC,IAAI,sBAAmB,CAAC,CAAC;SACjH;QAED,iBAAiB;QACjB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,KAAK,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC;SACZ;QAED,WAAW;QACX,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG;IACH,iCAAU,GAAV,UAAW,QAAQ,EAAE,GAAG,EAAE,OAAO;QAC/B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,wCAAwC;QACxC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,GAAG,EAAE;gBACR,MAAM,IAAI,KAAK,CAAC,uCAAuC;oBACrD,YAAY,CAAC,CAAC;aACjB;iBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,4CAA0C,GAAG,CAAC,IAAI,iBAAY,IAAI,CAAC,KAAK,CAAC,IAAI,kBAAe,CAAC,CAAC;aAC/G;SACF;aAAM,IAAI,GAAG,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,sBAAoB,GAAG,CAAC,IAAI,uEAAoE,CAAC,CAAC;SACnH;QAED,wCAAwC;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,8BAA2B,IAAI,CAAC,KAAK,gBAAS,QAAQ,OAAG,CAAC,CAAC;SAC5E;QAED,yCAAyC;QACzC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,IAAI,OAAT,IAAI,2BAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAE;IAC3D,CAAC;IAED;;;;;;;OAOG;IACH,oCAAa,GAAb,UAAc,QAAQ,EAAE,GAAG,EAAE,OAAO;QAClC,IAAI;YACF,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,2BAAI,GAAJ,UAAK,KAAK;QAAV,iBAaC;QAZC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9B;aAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;YACvE,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SAClE;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,UAAC,QAAQ,EAAE,OAAO,EAAE,MAAM;YACjD,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACtB,OAAO,CAAC,KAAI,CAAC,CAAC;aACf;iBAAM,IAAI,CAAC,iBAAiB,CAAC,KAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;gBACrE,MAAM,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;aACjD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACH,mBAAC;AAAD,CAAC,AArVD,CAA2B,YAAY,GAqVtC;AAED;;;GAGG;AAEH;;;;;;;GAOG;AACH,SAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IACxC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AAEH,SAAS,QAAQ,CAAC,IAAI;IACpB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,SAAS,CAAC,KAAK;IACtB,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ,EAAE,IAAI,IAAK,OAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAA9C,CAA8C,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACxH,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IACpC,EAAE,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;IACrB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI;QAC1B,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACb,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;SACpD;IACH,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,eAAe,CAAC,MAAM;IAC7B,IAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5B,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;QACxB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC1C;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,IAAI,EAAE,KAAK;IACzC,OAAO,IAAI,KAAK,CAAC,OAAI,KAAK,oCAA6B,IAAI,OAAG,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC"}