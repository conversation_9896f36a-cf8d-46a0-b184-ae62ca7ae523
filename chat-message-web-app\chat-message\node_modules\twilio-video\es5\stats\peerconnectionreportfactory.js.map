{"version": 3, "file": "peerconnectionreportfactory.js", "sourceRoot": "", "sources": ["../../lib/stats/peerconnectionreportfactory.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEL,IAAA,YAAY,GAAK,OAAO,CAAC,gBAAgB,CAAC,aAA9B,CAA+B;AAEnD,IAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACvD,IAAM,oBAAoB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC/D,IAAM,qBAAqB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACjE,IAAM,mBAAmB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAE7D;;GAEG;AAEH;;GAEG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;;;GAMG;AACH;IACE;;;OAGG;IACH,qCAAY,EAAE;QACZ,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,EAAE,EAAE;gBACF,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,EAAE;aACV;YACD,GAAG,EAAE;gBACH,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,gBAAgB,EAAE;aAC9B;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,GAAG,EAAE;oBACf,IAAI,EAAE,IAAI,GAAG,EAAE;iBAChB;aACF;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,GAAG,EAAE;oBACf,IAAI,EAAE,IAAI,GAAG,EAAE;iBAChB;aACF;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,0CAAI,GAAJ;QAAA,iBA2BC;QA1BC,IAAM,aAAa,GAAG,YAAY,EAAE,KAAK,SAAS;YAChD,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;YACrB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEvB,OAAO,aAAa,CAAC,IAAI,CAAC;YACxB,IAAM,0BAA0B,4BAAO,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;YACjE,IAAM,0BAA0B,4BAAO,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;YACjE,IAAM,4BAA4B,4BAAO,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;YACnE,IAAM,4BAA4B,4BAAO,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;YAEnE,IAAM,MAAM,GAAG,IAAI,oBAAoB,CACrC,KAAI,CAAC,GAAG,CAAC,UAAU,EACnB;gBACE,IAAI,EAAE,0BAA0B,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,UAAU,EAAlB,CAAkB,CAAC,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,EAAN,CAAM,CAAC;gBAC5F,IAAI,EAAE,4BAA4B,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,UAAU,EAAlB,CAAkB,CAAC,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,EAAN,CAAM,CAAC;aAC/F,EACD;gBACE,IAAI,EAAE,0BAA0B,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,UAAU,EAAlB,CAAkB,CAAC,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,EAAN,CAAM,CAAC;gBAC5F,IAAI,EAAE,4BAA4B,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,UAAU,EAAlB,CAAkB,CAAC,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,EAAN,CAAM,CAAC;aAC/F,CACF,CAAC;YAEF,KAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YAEzB,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IACH,kCAAC;AAAD,CAAC,AArED,IAqEC;AAED;;;;;GAKG;AACH,SAAS,0BAA0B,CAAC,kBAAkB;IACpD,OAAO,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAA,gBAAgB;QACxD,IAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1C,OAAO,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM;;;gBAC5C,0DAA0D;gBAC1D,EAAE;gBACF,yDAAyD;gBACzD,EAAE;gBACF,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;wBAChC,KAAK,CAAC,EAAE,GAAM,OAAO,SAAI,KAAK,CAAC,EAAI,CAAC;qBACrC;iBACF;;;;;;;;;YACD,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,IAAI,GAAG,CAAC,KAAK,CAAC,EAAd,CAAc,CAAC,CAAC;AACpC,CAAC;AAED;;;;;;;GAOG,CAAA;;;;;;;EAOA;AACH,SAAS,wCAAwC,CAAC,6BAA6B,EAAE,6BAA6B,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IACpI,IAAM,kBAAkB,GAAG,6BAA6B,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC1E,IAAI,CAAC,OAAO,EAAE;QACZ,IAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,UAAU,EAAE;YACd,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC;SACtC;KACF;IACD,IAAI,kBAAkB,IAAI,OAAO,EAAE;QACjC,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;YACpC,OAAO,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SACzC;QACD,IAAM,uBAAuB,GAAG,IAAI,6BAA6B,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAClF,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,uBAAuB,CAAC,CAAC;KAC3D;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAS,mCAAmC,CAAC,OAAO;IAClD,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,SAAS,qCAAqC,CAAC,OAAO;IACpD,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAClE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,8BAA8B,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IACrE,OAAO,wCAAwC,CAAC,mBAAmB,EAAE,mCAAmC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7I,CAAC;AAED;;;;;;GAMG;AACH,SAAS,gCAAgC,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IACvE,OAAO,wCAAwC,CAAC,qBAAqB,EAAE,qCAAqC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACjJ,CAAC;AAED;;;GAGG;AACH,SAAS,oCAAoC,CAAC,OAAO;IACnD,OAAO;QACL,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACzC,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KAC1C,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,sCAAsC,CAAC,OAAO;IACrD,OAAO;QACL,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACzC,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KAC1C,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAS,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,yCAAyC,EAAE,OAAO;;;QAC9F,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;YAAhC,IAAM,KAAK,WAAA;YACd,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACpD,IAAI,YAAY,EAAE,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;oBAClD,SAAS;iBACV;gBACD,IAAM,8BAA8B,GAAG,yCAAyC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAClG,IAAI,8BAA8B,EAAE;oBAClC,8BAA8B,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;iBACjD;gBACD,IAAM,mBAAmB,GAAG,8BAA8B,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5F,IAAI,mBAAmB,EAAE;oBACvB,IAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACtD,mBAAmB,CAAC,IAAI,CAAC,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;iBAC7F;aACF;SACF;;;;;;;;;AACH,CAAC;AAED;;;;;;GAMG;AACH,SAAS,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,2CAA2C,EAAE,OAAO;;;QAClG,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;YAAhC,IAAM,KAAK,WAAA;YACd,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACnD,IAAM,gCAAgC,GAAG,2CAA2C,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACtG,IAAI,gCAAgC,EAAE;oBACpC,gCAAgC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;iBACnD;gBACD,IAAM,qBAAqB,GAAG,gCAAgC,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAChG,IAAI,qBAAqB,EAAE;oBACzB,qBAAqB,CAAC,IAAI,CAAC,OAAO,IAAI,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC7E;aACF;SACF;;;;;;;;;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,qCAAqC,CAAC,0CAA0C,EAAE,2CAA2C;4BACzH,SAAS;QAClB,IAAM,+BAA+B,GAAG,0CAA0C,CAAC,SAAS,CAAC,CAAC;QAC9F,IAAM,gCAAgC,GAAG,2CAA2C,CAAC,SAAS,CAAC,CAAC;QAChG,gCAAgC,CAAC,OAAO,CAAC,UAAA,+BAA+B,IAAI,OAAA,+BAA+B,CAAC,MAAM,CAAC,+BAA+B,CAAC,EAAvE,CAAuE,CAAC,CAAC;;IAHvJ,KAAK,IAAM,SAAS,IAAI,2CAA2C;gBAAxD,SAAS;KAInB;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,GAAG,EAAE,MAAM;;IAClC,IAAI,qBAAqB,CAAC;;QAC1B,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;YAAhC,IAAM,KAAK,WAAA;YACd,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;gBAC9B,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;aACnE;SACF;;;;;;;;;IACD,IAAI,qBAAqB,EAAE;QACzB,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAChC,OAAO;KACR;;QACD,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;YAAhC,IAAM,KAAK,WAAA;YACd,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB;mBAC9B,KAAK,CAAC,SAAS;mBACf,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjB;SACF;;;;;;;;;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,OAAO;IAC5B,IAAM,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE;SACzC,MAAM,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,EAAtG,CAAsG,CAAC;SAC7H,GAAG,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,MAAM,EAAlB,CAAkB,CAAC,CAAC;IAE1C,IAAM,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE;SAC3C,MAAM,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,EAA1E,CAA0E,CAAC;SACjG,GAAG,CAAC,UAAA,WAAW,IAAI,OAAA,WAAW,CAAC,QAAQ,EAApB,CAAoB,CAAC,CAAC;IAE5C,OAAO,OAAO,CAAC,GAAG,CAAC;QACjB,0BAA0B,CAAC,OAAO,CAAC;QACnC,0BAA0B,CAAC,SAAS,CAAC;QACrC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE;KACtB,CAAC,CAAC,IAAI,CAAC,UAAC,EAA0C;YAA1C,KAAA,aAA0C,EAAzC,aAAa,QAAA,EAAE,eAAe,QAAA,EAAE,QAAQ,QAAA;QAChD,IAAM,gCAAgC,GAAG,mCAAmC,CAAC,OAAO,CAAC,CAAC;QACtF,IAAM,yCAAyC,GAAG,oCAAoC,CAAC,OAAO,CAAC,CAAC;QAChG,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,OAAO,IAAK,OAAA,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,yCAAyC,EAAE,OAAO,CAAC,EAAxF,CAAwF,CAAC,CAAC;QACrI,qCAAqC,CAAC,gCAAgC,EAAE,yCAAyC,CAAC,CAAC;QAEnH,IAAM,kCAAkC,GAAG,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1F,IAAM,2CAA2C,GAAG,sCAAsC,CAAC,OAAO,CAAC,CAAC;QACpG,eAAe,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,OAAO,IAAK,OAAA,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,2CAA2C,EAAE,OAAO,CAAC,EAA5F,CAA4F,CAAC,CAAC;QAC3I,qCAAqC,CAAC,kCAAkC,EAAE,2CAA2C,CAAC,CAAC;QAEvH,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,OAAO;IAC3B,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM;QACtC,IAAM,gCAAgC,GAAG,mCAAmC,CAAC,OAAO,CAAC,CAAC;QACtF,IAAM,yCAAyC,GAAG,oCAAoC,CAAC,OAAO,CAAC,CAAC;QAChG,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,yCAAyC,CAAC,CAAC;QAChF,qCAAqC,CAAC,gCAAgC,EAAE,yCAAyC,CAAC,CAAC;QAEnH,IAAM,kCAAkC,GAAG,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1F,IAAM,2CAA2C,GAAG,sCAAsC,CAAC,OAAO,CAAC,CAAC;QACpG,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,2CAA2C,CAAC,CAAC;QACpF,qCAAqC,CAAC,kCAAkC,EAAE,2CAA2C,CAAC,CAAC;QAEvH,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC"}