{"version": 3, "file": "receiverreport.js", "sourceRoot": "", "sources": ["../../lib/stats/receiverreport.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AACrC,IAAM,sBAAsB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACnE,IAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE7B;;;;;GAKG;AAEH;;;;;;;GAOG;AACH;IAA6B,kCAAsB;IACjD;;;;;;;;OAQG;IACH,wBAAY,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,YAAY,EAAE,MAAM;QAA9F,YACE,kBAAM,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,SA0B5B;QAzBC,IAAM,iBAAiB,GAAG,oBAAoB,GAAG,CAAC;YAChD,CAAC,CAAC,gBAAgB,GAAG,oBAAoB;YACzC,CAAC,CAAC,CAAC,CAAC;QACN,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,gBAAgB,EAAE;gBAChB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,gBAAgB;aACxB;YACD,oBAAoB,EAAE;gBACpB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,oBAAoB;aAC5B;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,YAAY;aACpB;YACD,MAAM,EAAE;gBACN,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,MAAM;aACd;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,iBAAiB;aACzB;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;;;;OAMG;IACI,iBAAE,GAAT,UAAU,OAAO,EAAE,UAAU,EAAE,UAAU;QACvC,IAAI,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QACD,IAAM,cAAc,GAAG,CAAC,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QAC5E,IAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAC/E,IAAM,OAAO,GAAG,cAAc,GAAG,CAAC;YAChC,CAAC,CAAC,CAAC,kBAAkB,GAAG,cAAc,CAAC,GAAG,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC;QACN,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QACtF,IAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAC7E,IAAA,YAAY,GAAa,UAAU,aAAvB,EAAE,MAAM,GAAK,UAAU,OAAf,CAAgB;QAC5C,OAAO,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;IAC3H,CAAC;IAED;;;;OAIG;IACI,wBAAS,GAAhB,UAAiB,OAAO;QACtB,IAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,SAAS,EAAE,EAAlB,CAAkB,CAAC,CAAC;QAC5D,IAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,OAAO,EAAf,CAAe,CAAC,CAAC,CAAC;QAC/D,IAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,YAAY,EAApB,CAAoB,CAAC,CAAC,CAAC;QAC7E,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,MAAM,EAAd,CAAc,CAAC,CAAC,CAAC;QACjE,OAAO;YACL,OAAO,SAAA;YACP,YAAY,cAAA;YACZ,MAAM,QAAA;SACP,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,kCAAS,GAAT;QACE,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EAAE,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB;YAChG,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;IACJ,CAAC;IACH,qBAAC;AAAD,CAAC,AAzFD,CAA6B,sBAAsB,GAyFlD;AAED,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC"}