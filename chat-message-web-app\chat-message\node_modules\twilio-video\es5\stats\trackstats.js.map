{"version": 3, "file": "trackstats.js", "sourceRoot": "", "sources": ["../../lib/stats/trackstats.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb;;;;;;;;;;;;GAYG;AACH;IACE;;;OAGG;IACH,oBAAY,OAAO,EAAE,WAAW;QAC9B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAC9C;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO;gBACd,UAAU,EAAE,IAAI;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,WAAW,CAAC,QAAQ;gBAC3B,UAAU,EAAE,IAAI;aACjB;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,WAAW,CAAC,SAAS;gBAC5B,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,WAAW,CAAC,IAAI;gBACvB,UAAU,EAAE,IAAI;aACjB;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO,WAAW,CAAC,WAAW,KAAK,QAAQ;oBAChD,CAAC,CAAC,WAAW,CAAC,WAAW;oBACzB,CAAC,CAAC,IAAI;gBACR,UAAU,EAAE,IAAI;aACjB;YACD,KAAK,EAAE;gBACL,KAAK,EAAE,OAAO,WAAW,CAAC,SAAS,KAAK,QAAQ;oBAC9C,CAAC,CAAC,WAAW,CAAC,SAAS;oBACvB,CAAC,CAAC,IAAI;gBACR,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IACH,iBAAC;AAAD,CAAC,AAzCD,IAyCC;AAED,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC"}