{"version": 3, "file": "transceiver.js", "sourceRoot": "", "sources": ["../lib/transceiver.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;AAEb,IAAM,oBAAoB,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAE/D;;;;;;GAMG;AACH;IAA+B,oCAAoB;IACjD;;;;OAIG;IACH,0BAAY,EAAE,EAAE,IAAI;QAApB,YACE,iBAAO,SAWR;QAVC,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,EAAE,EAAE;gBACF,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,EAAE;aACV;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI;aACZ;SACF,CAAC,CAAC;;IACL,CAAC;IAED;;;;OAIG;IACH,+BAAI,GAAJ;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IACH,uBAAC;AAAD,CAAC,AA5BD,CAA+B,oBAAoB,GA4BlD;AAED;;;GAGG;AAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}