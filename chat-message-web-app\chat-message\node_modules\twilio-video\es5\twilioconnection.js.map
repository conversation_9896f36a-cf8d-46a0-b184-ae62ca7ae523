{"version": 3, "file": "twilioconnection.js", "sourceRoot": "", "sources": ["../lib/twilioconnection.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACzC,IAAA,KAA+B,OAAO,CAAC,QAAQ,CAAC,EAA9C,cAAc,oBAAA,EAAE,QAAQ,cAAsB,CAAC;AACvD,IAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAClC,IAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACxD,IAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAE1C,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB;;;;;;;;;;;;;;;;;;GAkBG;AAEH,IAAM,MAAM,GAAG;IACb,MAAM,EAAE,EAAE;IACV,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;IACzC,KAAK,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;IAC/B,IAAI,EAAE,CAAC,QAAQ,CAAC;IAChB,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC;CACnD,CAAC;AAEF,IAAM,MAAM,GAAG;IACb,MAAM,EAAE,OAAO;IACf,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,SAAS;CACnB,CAAC;AAEF,IAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,IAAM,yCAAyC,GAAG,CAAC,CAAC;AACpD,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAChD,IAAM,uCAAuC,GAAG,IAAI,CAAC;AACrD,IAAM,oBAAoB,GAAG,KAAK,CAAC;AACnC,IAAM,uBAAuB,GAAG,IAAI,CAAC;AACrC,IAAM,yBAAyB,GAAG,GAAG,CAAC;AAEtC,IAAM,eAAe,GAAG,IAAI,CAAC;AAC7B,IAAM,wBAAwB,GAAG,IAAI,CAAC;AACtC,IAAM,0BAA0B,GAAG,IAAI,CAAC;AACxC,IAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,IAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,IAAM,wBAAwB,GAAG,IAAI,CAAC;AACtC,IAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,IAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,IAAM,qBAAqB,GAAG,IAAI,CAAC;AAEnC,mEAAmE;AACnE,iEAAiE;AACjE,mBAAmB;AAEnB,IAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1E,IAAM,WAAW,GAAG;IAClB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;IAChB,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;CACnB,CAAC;AAEF,IAAM,0BAA0B,GAAG,IAAI,GAAG,CAAC;IACzC,CAAC,wBAAwB,EAAE,WAAW,CAAC,OAAO,CAAC;IAC/C,CAAC,0BAA0B,EAAE,WAAW,CAAC,OAAO,CAAC;IACjD,CAAC,qBAAqB,EAAE,WAAW,CAAC,MAAM,CAAC;IAC3C,CAAC,oBAAoB,EAAE,WAAW,CAAC,MAAM,CAAC;IAC1C,CAAC,wBAAwB,EAAE,WAAW,CAAC,OAAO,CAAC;IAC/C,CAAC,oBAAoB,EAAE,WAAW,CAAC,IAAI,CAAC;IACxC,CAAC,qBAAqB,EAAE,WAAW,CAAC,OAAO,CAAC;CAC7C,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH;IAA+B,oCAAY;IACzC;;;;OAIG;IACH,0BAAY,SAAS,EAAE,OAAO;QAA9B,YACE,kBAAM,OAAO,EAAE,MAAM,CAAC,SA0GvB;QAxGC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,SAAS,EAAE,IAAI;YACf,0BAA0B,EAAE,qCAAqC;YACjE,8BAA8B,EAAE,yCAAyC;YACzE,yBAAyB,EAAE,uCAAuC;YAClE,WAAW,EAAE,oBAAoB;YACjC,cAAc,EAAE,uBAAuB;YACvC,GAAG,KAAA;YACH,SAAS,WAAA;SACV,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAM,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAE5E,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC;YACzD,IAAA,IAAI,GAAK,cAAc,KAAnB,CAAoB;YAChC,IAAM,MAAM,GAAG,qBAAkB,IAAI,CAAC,CAAC,CAAC,SAAO,IAAM,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC;YAC7D,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClB,KAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEV,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,gBAAgB,EAAE;gBAChB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,OAAO,CAAC,aAAa;aAC7B;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO,CAAC,0BAA0B;gBACzC,QAAQ,EAAE,IAAI;aACf;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE,UAAU;aACpB;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,GAAG;aACX;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,EAAE;aACV;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,OAAO;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,IAAM,cAAc,GAAG;YACrB,UAAU,EAAE,MAAM;YAClB,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,MAAM;SACf,CAAC;QAEF,KAAI,CAAC,EAAE,CAAC,cAAc,EAAE,UAAC,KAAK;YAAE,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,6BAAO;;YACrC,IAAI,KAAK,IAAI,MAAM,EAAE;gBACnB,KAAI,CAAC,IAAI,OAAT,KAAI,iBAAM,MAAM,CAAC,KAAK,CAAC,UAAK,IAAI,IAAE;aACnC;YACD,IAAM,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACrF,IAAI,KAAK,KAAK,QAAQ,EAAE;gBAChB,IAAA,KAAA,OAAW,IAAI,IAAA,EAAd,MAAM,QAAQ,CAAC;gBACtB,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,QAAA,EAAE,CAAC;gBAC3B,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;aAC/D;YACD,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/G,KAAI,CAAC,QAAQ,EAAE,CAAC;;IAClB,CAAC;IAED,mCAAQ,GAAR;QACE,OAAO,wBAAsB,IAAI,CAAC,WAAW,UAAK,IAAI,CAAC,GAAG,CAAC,GAAG,MAAG,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACH,iCAAM,GAAN,UAAO,EAAgB;YAAd,IAAI,UAAA,EAAE,MAAM,YAAA;QACnB,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC3B,OAAO;SACR;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;SAChC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,KAAK,kBAAkB,EAAE;YACxD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,IAAI,IAAI,KAAK,eAAe,EAAE;YAC5B,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;SACtD;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,aAAW,IAAI,WAAM,MAAQ,CAAC,CAAC;YACxC,IAAI,IAAI,KAAK,kBAAkB,EAAE;gBAC/B,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE;oBAC9B,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM;iBAC3D,CAAC,CAAC;aACJ;SACF;QACO,IAAA,UAAU,GAAK,IAAI,CAAC,GAAG,WAAb,CAAc;QACxB,IAAA,SAAS,GAAK,IAAI,CAAC,QAAQ,UAAlB,CAAmB;QAEpC,IAAI,UAAU,KAAK,SAAS,CAAC,OAAO,IAAI,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE;YACvE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC9B;IACH,CAAC;IAED;;;OAGG;IACH,mCAAQ,GAAR;QAAA,iBAkEC;QAjEC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,6BAAyB;kBAC7D,eAAe,CAAC,CAAC;YACrB,OAAO;SACR;QACD,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,GAAG,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;QAC1C,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAC;QAElD,IAAA,WAAW,GAAK,IAAI,CAAC,QAAQ,YAAlB,CAAmB;QACtC,gJAAgJ;QAChJ,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC;YAC9B,IAAM,MAAM,GAAG,uBAAqB,WAAW,QAAK,CAAC;YACrD,KAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;QACvD,CAAC,EAAE,WAAW,CAAC,CAAC;QAEhB,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAC1B,GAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;YACnC,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAC1B,KAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,KAAI,CAAC,eAAe,EAAE;gBACxB,KAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;aAC9B;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAA,OAAO;YACpC,GAAG,CAAC,KAAK,CAAC,eAAa,OAAO,CAAC,IAAM,CAAC,CAAC;YACvC,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aACpC;YAAC,OAAO,KAAK,EAAE;gBACd,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,OAAO;aACR;YAED,QAAQ,OAAO,CAAC,IAAI,EAAE;gBACpB,KAAK,KAAK;oBACR,KAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBACzB,MAAM;gBACR,KAAK,MAAM;oBACT,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC1B,MAAM;gBACR,KAAK,KAAK;oBACR,cAAc;oBACd,MAAM;gBACR,KAAK,KAAK;oBACR,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC7B,4EAA4E;gBAC5E,+DAA+D;gBAC/D,0CAA0C;gBAC5C,KAAK,WAAW;oBACd,KAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS;oBACZ,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAC7B,MAAM;gBACR;oBACE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAAyB,OAAO,CAAC,IAAM,CAAC,CAAC;oBACzD,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,2BAAyB,OAAO,CAAC,IAAM,CAAC,CAAC,CAAC;oBACvE,MAAM;aACT;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,qCAAU,GAAV,UAAW,EAAU;YAAR,MAAM,YAAA;QACjB,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAChD,GAAG,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,sCAAgC;kBACpE,wBAAwB,CAAC,CAAC;YAC9B,OAAO;SACR;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,cAAY,qBAAqB,WAAM,MAAQ,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;YACrD,OAAO;SACR;QACD,GAAG,CAAC,KAAK,CAAC,YAAU,MAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,sCAAW,GAAX,UAAY,EAAiC;QAA7C,iBAoCC;YApCa,MAAM,YAAA,EAAE,SAAS,eAAA,EAAE,UAAU,gBAAA;QACzC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACnD,GAAG,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,uCAAiC;kBACrE,wBAAwB,CAAC,CAAC;YAC9B,OAAO;SACR;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QACD,IAAM,MAAM,GAAG,UAAU,GAAG,CAAC;YAC3B,CAAC,CAAC,kCAAkC;YACpC,CAAC,CAAC,+CAA2C,UAAU,QAAK,CAAC;QAE/D,IAAI,UAAU,GAAG,CAAC,EAAE;YAClB,GAAG,CAAC,IAAI,CAAC,cAAY,oBAAoB,WAAM,MAAQ,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;YACpD,OAAO;SACR;QACO,IAAA,0BAA0B,GAAK,IAAI,CAAC,QAAQ,2BAAlB,CAAmB;QACrD,IAAI,CAAC,WAAW,GAAG,0BAA0B,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC;QAE9B,IAAI,SAAS,EAAE;YACb,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,eAAe,EAAE,EAAtB,CAAsB,EAAE,UAAU,CAAC,CAAC;SAC/E;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,cAAY,kBAAkB,WAAM,MAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,QAAQ,EAAE,EAAf,CAAe,EAAE,UAAU,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACH,2CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,oCAA8B;kBACxE,gCAAgC,CAAC,CAAC;YACtC,OAAO;SACR;QACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,kDAAuB,GAAvB;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QACd,IAAA,8BAA8B,GAAK,IAAI,CAAC,QAAQ,+BAAlB,CAAmB;QAEzD,GAAG,CAAC,KAAK,CAAC,oCAAkC,8BAAgC,CAAC,CAAC;QAC9E,IAAM,MAAM,GAAG,YAAU,8BAA8B,4BAAuB,CAAC;QAC/E,GAAG,CAAC,IAAI,CAAC,cAAY,0BAA0B,WAAM,MAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,yCAAc,GAAd,UAAe,EAAQ;YAAN,IAAI,UAAA;QACnB,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,sCAAgC;kBAC1E,wBAAwB,CAAC,CAAC;YAC9B,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,yCAAc,GAAd,UAAe,EAAqB;QAApC,iBAuBC;YAvBgB,iBAAiB,uBAAA;QAChC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACnD,GAAG,CAAC,IAAI,CAAC,wBAAqB,IAAI,CAAC,KAAK,kCAA4B;kBAChE,gCAAgC,CAAC,CAAC;YACtC,OAAO;SACR;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,GAAG,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACtE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QAEO,IAAA,8BAA8B,GAAK,IAAI,CAAC,QAAQ,+BAAlB,CAAmB;QACzD,IAAM,gBAAgB,GAAG,iBAAiB,GAAG,8BAA8B,CAAC;QAC5E,IAAM,wBAAwB,GAAG,iBAAiB,GAAG,yBAAyB,CAAC;QAE/E,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,uBAAuB,EAAE,EAA9B,CAA8B,EAAE,gBAAgB,CAAC,CAAC;QAC7F,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAnB,CAAmB,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,GAAG,IAAI,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,cAAc,EAAE,EAArB,CAAqB,EAAE,wBAAwB,CAAC,CAAC;QAChG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,gDAAqB,GAArB;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE;YAC/B,OAAO;SACR;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE;YACzB,IAAM,MAAM,GAAG,+BAA+B,CAAC;YAC/C,GAAG,CAAC,IAAI,CAAC,cAAY,wBAAwB,WAAM,MAAQ,CAAC,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;YACxD,OAAO;SACR;QAEO,IAAA,0BAA0B,GAAK,IAAI,CAAC,QAAQ,2BAAlB,CAAmB;QACrD,GAAG,CAAC,IAAI,CAAC,wBAAqB,0BAA0B,GAAG,IAAI,CAAC,WAAW,aAAS,CAAC,CAAC;QACtF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,gCAAK,GAAL,UAAM,OAAO;QACH,IAAA,UAAU,GAAK,IAAI,CAAC,GAAG,WAAb,CAAc;QACxB,IAAA,SAAS,GAAK,IAAI,CAAC,QAAQ,UAAlB,CAAmB;QACpC,IAAI,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;YACjC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAa,IAAM,CAAC,CAAC;YACrC,IAAI;gBACF,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpB,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC9B,mEAAmE;oBACnE,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;iBACpC;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAM,MAAM,GAAG,wBAAwB,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAY,oBAAoB,WAAM,MAAQ,CAAC,CAAC;gBAC/D,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;aACrD;SACF;IACH,CAAC;IAED;;;OAGG;IACH,yCAAc,GAAd;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC3B,OAAO;SACR;QACD,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,qCAAU,GAAV;QACQ,IAAA,KAAoD,IAAI,CAAC,QAAQ,EAA/D,SAAS,eAAA,EAA6B,OAAO,+BAAkB,CAAC;QACxE,IAAM,KAAK,GAAG;YACZ,EAAE,EAAE,QAAQ,EAAE;YACd,OAAO,SAAA;YACP,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,YAAY;SACtB,CAAC;QACF,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;SAC7B;QACD,IAAI,SAAS,EAAE;YACb,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;SACxB;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,yCAAc,GAAd,UAAe,OAAO;QAAtB,iBASC;QARC,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC3B,OAAO;SACR;QACD,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM;YACzC,CAAC,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAnB,CAAmB;YAChC,CAAC,CAAC,UAAA,OAAO,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAhC,CAAgC,CAAC;QAEhD,aAAa,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,0CAAe,GAAf;QAAA,iBAWC;QAVC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,EAAE,CAAC;QACV,IAAA,cAAc,GAAK,IAAI,CAAC,QAAQ,eAAlB,CAAmB;QACzC,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,qBAAqB,EAAE,EAA5B,CAA4B,EAAE,cAAc,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACH,gCAAK,GAAL;QACE,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC3B,OAAO;SACR;QACD,IAAI,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,sCAAW,GAAX,UAAY,IAAI;QACd,IAAI,CAAC,cAAc,CAAC,EAAE,IAAI,MAAA,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAC7C,CAAC;IACH,uBAAC;AAAD,CAAC,AA9fD,CAA+B,YAAY,GA8f1C;AAED;;;GAGG;AACH,gBAAgB,CAAC,WAAW,GAAG,WAAW,CAAC;AAE3C;;;;GAIG;AAEH;;;;GAIG;AAEH;;;;GAIG;AAEH;;;GAGG;AAEH;;;;;GAKG;AAEH;;;;;;;;;;GAUG;AAEH,MAAM,CAAC,OAAO,GAAG,gBAAgB,CAAC"}