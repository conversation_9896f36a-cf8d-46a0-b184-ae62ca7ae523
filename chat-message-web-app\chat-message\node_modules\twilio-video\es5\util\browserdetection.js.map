{"version": 3, "file": "browserdetection.js", "sourceRoot": "", "sources": ["../../lib/util/browserdetection.js"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,YAAY,CAAC;;;;;;;;;;;;;;;;;AAEb;;;GAGG;AACH,SAAS,SAAS;IAChB,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC7C,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc;IACrB,OAAO,CAAC,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AACnF,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM;IACb,OAAO,cAAc,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;WAC3F,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;WAChC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ;IACf,OAAO,cAAc,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;WAC3F,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;WAClC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK;IACZ,OAAO,MAAM,EAAE,IAAI,QAAQ,EAAE,CAAC;AAChC,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ;IACf,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC1C,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,OAAO;IAChC,OAAO,OAAO,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CACjE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,WAAW,CACvE,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,sBAAsB,CAAC,OAAO;IACrC,iFAAiF;IACjF,IAAI,OAAO,KAAK,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;KACb;IAED,oEAAoE;IACpE,IAAI,OAAO,IAAI,SAAS,EAAE;QACxB,OAAO,OAAO,CAAC;KAChB;IAED,2EAA2E;IAC3E,8BAA8B;IAC9B,IAAM,uBAAuB,GAAG,0BAA0B,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAChF,IAAM,eAAe,GAAG,uBAAuB,CAAC,MAAM,CACpD,UAAC,SAAS,EAAE,SAAS,IAAK,OAAA,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAhC,CAAgC,EAC1D,SAAS,CAAC,SAAS,CACpB,CAAC;IAEF,+EAA+E;IAC/E,kCAAkC;IAClC,IAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACjD,IAAA,KAAA,OAAgD,OAAO,CAAC,GAAG,CAAC,UAAA,cAAc;QAC9E,OAAO,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACpD,CAAC,CAAC,CAAA,EAFoC,YAAY,cAEhD,CAAC;IAEH,8EAA8E;IAC9E,mFAAmF;IACnF,yCAAyC;IACzC,OAAO,YAAY,CAAC,IAAI,CAAC,UAAA,IAAI;QAC3B,OAAO,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC,IAAI,IAAI,CAAC;AACb,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,OAAO;IAClC,IAAI,OAAO,KAAK,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC;KACb;IACD,IAAI,OAAO,IAAI,SAAS,EAAE;QACxB,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;QAC9B,OAAO,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC,CAAC,IAAI,IAAI,CAAC;AACb,CAAC;AAED;;;;;;GAMG;AACH,SAAS,0BAA0B,CAAC,MAAM;IACxC,IAAM,wBAAwB,GAAG,EAAE,CAAC;IACpC,IAAM,UAAU,GAAG,EAAE,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACrB,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAClC;aAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE;YACnE,IAAM,uBAAuB,GAAG,wBAAwB,CAAC,GAAG,EAAE,CAAC;YAC/D,IAAI,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACnE;SACF;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,MAAM,CAAC,OAAO,GAAG;IACf,SAAS,WAAA;IACT,KAAK,OAAA;IACL,MAAM,QAAA;IACN,QAAQ,UAAA;IACR,QAAQ,UAAA;IACR,iBAAiB,mBAAA;IACjB,mBAAmB,qBAAA;IACnB,sBAAsB,wBAAA;CACvB,CAAC"}