{"version": 3, "file": "cancelablepromise.js", "sourceRoot": "", "sources": ["../../lib/util/cancelablepromise.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;AAEb;;;EAGE;AACF;IACE;;;;MAIE,CAAA;;;;;;MAMA,CAAA;;;OAGC;IACH,2BAAY,QAAQ,EAAE,QAAQ;QAA9B,iBA2BC;QA1BC,0BAA0B;QAC1B,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,aAAa,EAAE;gBACb,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,WAAW,EAAE;gBACX,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,KAAK;aACb;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,QAAQ;aAChB;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;YACtC,KAAK,EAAE,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBACjC,QAAQ,CAAC,UAAA,KAAK;oBACZ,KAAI,CAAC,aAAa,GAAG,KAAK,CAAC;oBAC3B,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,EAAE,UAAA,MAAM;oBACP,KAAI,CAAC,aAAa,GAAG,KAAK,CAAC;oBAC3B,MAAM,CAAC,MAAM,CAAC,CAAC;gBACjB,CAAC,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,EAAhB,CAAgB,CAAC,CAAC;YAC7B,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,wBAAM,GAAb,UAAc,MAAM;QAClB,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM;YAC5D,MAAM,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,EAAE,SAAS,QAAQ;YAClB,cAAc;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,yBAAO,GAAd,UAAe,MAAM;QACnB,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO;YACpD,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,EAAE,SAAS,QAAQ;YAClB,cAAc;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,kCAAM,GAAN;QACE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,iCAAK,GAAL;QACE,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM;YAC5D,OAAO,CAAC,KAAK,OAAb,OAAO,2BAAU,IAAI,IAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC/C,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,gCAAI,GAAJ;QACE,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM;YAC5D,OAAO,CAAC,IAAI,OAAZ,OAAO,2BAAS,IAAI,IAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACrB,CAAC;IAED;;;KAGC;IACD,mCAAO,GAAP;QACE,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,OAAO,IAAI,iBAAiB,CAAC,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM;YAC5D,OAAO,CAAC,OAAO,OAAf,OAAO,2BAAY,IAAI,IAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACrB,CAAC;IACH,wBAAC;AAAD,CAAC,AAtHD,IAsHC;AAED,MAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC"}