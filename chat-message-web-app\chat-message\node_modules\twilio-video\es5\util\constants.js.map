{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../lib/util/constants.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AACb,8BAA8B;AAC9B,IAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClD,MAAM,CAAC,OAAO,CAAC,QAAQ,GAAM,WAAW,CAAC,IAAI,QAAK,CAAC;AACnD,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;AACjD,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,SAAS,CAAC;AACtC,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG;IAC3C,oBAAoB,EAAE,OAAO;IAC7B,aAAa,EAAE,MAAM;IACrB,YAAY,EAAE,QAAQ;IACtB,YAAY,EAAE,KAAK;CACpB,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,6BAA6B,GAAG;IAC7C,oBAAoB,EAAE,OAAO;IAC7B,aAAa,EAAE,QAAQ;IACvB,YAAY,EAAE,QAAQ;IACtB,YAAY,EAAE,KAAK;CACpB,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,mBAAmB,GAAG,MAAM,CAAC;AAC5C,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;AACrC,MAAM,CAAC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;AACtC,MAAM,CAAC,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC;AAC1C,MAAM,CAAC,OAAO,CAAC,mBAAmB,GAAG,cAAc,CAAC;AACpD,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,UAAC,WAAW,EAAE,MAAM;IAC7C,MAAM,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAClE,OAAO,WAAW,KAAK,MAAM;QAC3B,CAAC,CAAC,WAAS,MAAM,8BAA2B;QAC5C,CAAC,CAAC,WAAS,MAAM,aAAQ,WAAW,0BAAuB,CAAC;AAChE,CAAC,CAAC;AACF,MAAM,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC;AACxC,MAAM,CAAC,OAAO,CAAC,sBAAsB,GAAG,EAAE,CAAC;AAC3C,MAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,CAAC;AAEvC;;;;GAIG;AACH,SAAS,OAAO,CAAC,IAAI;IACnB,qEAAqE;IACrE,0EAA0E;IAC1E,wDAAwD;IACxD,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AAChF,CAAC;AAED,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG;IAC1B,cAAc,YAAC,IAAI,EAAE,OAAO;QAC1B,OAAO,IAAI,SAAS,CAAC,qBAAmB,IAAI,UAAK,OAAS,CAAC,CAAC;IAC9D,CAAC;IACD,YAAY,YAAC,IAAI,EAAE,IAAI;QACrB,OAAO,IAAI,SAAS,CAAI,IAAI,iBAAY,OAAO,CAAC,IAAI,CAAC,SAAI,IAAM,CAAC,CAAC;IACnE,CAAC;IACD,aAAa,YAAC,IAAI,EAAE,MAAM;QACxB,OAAO,IAAI,UAAU,CAAI,IAAI,wBAAmB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;IACvE,CAAC;IACD,iBAAiB,YAAC,IAAI;QACpB,OAAO,IAAI,SAAS,CAAI,IAAI,uBAAoB,CAAC,CAAC;IACpD,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,CAAC;AACvC,MAAM,CAAC,OAAO,CAAC,yCAAyC,GAAG,KAAK,CAAC;AAEjE,MAAM,CAAC,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;AACxD,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG,EAAE,CAAC;AAEhD,MAAM,CAAC,OAAO,CAAC,sBAAsB,GAAG,CAAC,CAAC;AAC1C,MAAM,CAAC,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC;AAC3C,MAAM,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;AAEhC,MAAM,CAAC,OAAO,CAAC,4BAA4B,GAAG,IAAI,CAAC;AACnD,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC;AAElD,MAAM,CAAC,OAAO,CAAC,uBAAuB,GAAG;IACvC,MAAM,EAAE,GAAG;IACX,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG,IAAI;IACtD,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,sBAAsB,GAAG;IACtC,MAAM,EAAE,GAAG;IACX,GAAG,EAAE,EAAE;IACP,MAAM,EAAE,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG;IAChC,kBAAkB,EAAE,eAAe;IACnC,SAAS,EAAE,MAAM;IACjB,iBAAiB,EAAE,cAAc;CAClC,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,kBAAkB,GAAG;IAClC,aAAa,EAAE,UAAU;IACzB,aAAa,EAAE,UAAU;IACzB,cAAc,EAAE,WAAW;CAC5B,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG;IAC7B,aAAa,EAAE,MAAM;IACrB,YAAY,EAAE,KAAK;IACnB,iBAAiB,EAAE,UAAU;CAC9B,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG;IAC3C,SAAS,EAAE,MAAM;IACjB,WAAW,EAAE,QAAQ;CACtB,CAAC;AAEF,MAAM,CAAC,OAAO,CAAC,2BAA2B,GAAG;IAC3C,SAAS,EAAE,MAAM;IACjB,WAAW,EAAE,QAAQ;CACtB,CAAC"}