{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/util/insightspublisher/index.js"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;AAE5C,IAAA,YAAY,GAAK,OAAO,CAAC,IAAI,CAAC,aAAlB,CAAmB;AAEvC,IAAM,sBAAsB,GAAG,CAAC,CAAC;AACjC,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,IAAM,eAAe,GAAG,IAAI,CAAC;AAE7B,IAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpE,IAAA,KAAiE,OAAO,CAAC,cAAc,CAAC,EAAtF,2BAA2B,iCAAA,EAAE,6BAA6B,mCAA4B,CAAC;AAC/F,IAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AACnC,IAAM,gBAAgB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAExD;;;;;;GAMG;AACH;IAAgC,qCAAY;IAC1C;;;;;;;OAOG;IACH,2BAAY,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO;QAAnE,YACE,iBAAO,SAiDR;QA/CC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,OAAO,EAAK,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,oBAAiB;YAC9D,oBAAoB,EAAE,sBAAsB;YAC5C,mBAAmB,EAAE,qBAAqB;YAC1C,SAAS,EAAE,YAAY,EAAE;YACzB,SAAS,WAAA;SACV,EAAE,OAAO,CAAC,CAAC;QAEZ,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,iBAAiB,EAAE;gBACjB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACf;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE;aACV;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;aACpB;YACD,sBAAsB,EAAE;gBACtB,KAAK,EAAE,OAAO,CAAC,oBAAoB;gBACnC,QAAQ,EAAE,IAAI;aACf;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO,CAAC,SAAS;aACzB;SACF,CAAC,CAAC;QAEH,KAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,UAAC,EAA2B;gBAAzB,OAAO,aAAA,EAAE,cAAc,oBAAA;YAC1D,IAAM,IAAI,GAAG,KAAI,CAAC;YAClB,KAAI,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,cAAc,CAAC,KAAK;gBACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,KAAK,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE;oBAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC1B,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;oBAC9E,OAAO;iBACR;gBACD,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC,KAAK,CAAC;YACP,6BAA6B;QAC/B,CAAC,CAAC,CAAC;;IACL,CAAC;IAED;;;;;OAKG;IACH,mCAAO,GAAP,UAAQ,OAAO,EAAE,cAAc;QAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,OAAO,SAAA,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,oCAAQ,GAAR,UAAS,KAAK;QACZ,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,sCAAU,GAAV;QACE,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI;eAChB,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO;eAC/C,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACnD,OAAO,KAAK,CAAC;SACd;QAED,IAAI;YACF,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACd,cAAc;SACf;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,mCAAO,GAAP,UAAQ,SAAS,EAAE,SAAS,EAAE,OAAO;QACnC,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI;eAChB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO;mBAC9C,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YACtD,OAAO,KAAK,CAAC;SACd;QAED,IAAM,gBAAgB,GAAG,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ;YACxD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1B,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjD,gBAAgB,CAAC;YACf,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,SAAS;YACf,OAAO,SAAA;YACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IACH,wBAAC;AAAD,CAAC,AAnID,CAAgC,YAAY,GAmI3C;AAED;;;;;;;;;;;GAWG;AACH,SAAS,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO;IACtF,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACzC,SAAS,CAAC,sBAAsB,EAAE,CAAC;IACnC,SAAS,CAAC,GAAG,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvD,IAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC;IAEzB,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAA,KAAK;QAChC,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE;YAClC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/B,OAAO;SACR;QACD,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,KAAK,CAAC,qBAAmB,KAAK,CAAC,IAAI,UAAK,KAAK,CAAC,MAAQ,CAAC,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAA,OAAO;QACpC,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE;QAC1B,IAAM,cAAc,GAAG;YACrB,IAAI,EAAE,SAAS;YACf,KAAK,OAAA;YACL,OAAO,EAAE,CAAC;SACX,CAAC;QAEF,cAAc,CAAC,SAAS,GAAG;YACzB,IAAI,EAAE,OAAO;YACb,UAAU,YAAA;YACV,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,cAAc,EAAE,cAAc;YAC9B,OAAO,EAAE,OAAO;SACjB,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,EAAE,EAAE;YAC7B,cAAc,CAAC,SAAS,yBAAQ,cAAc,CAAC,SAAS,GAAK,2BAA2B,CAAE,CAAC;SAC5F;aAAM,IAAI,gBAAgB,CAAC,QAAQ,EAAE,EAAE;YACtC,cAAc,CAAC,SAAS,yBAAQ,cAAc,CAAC,SAAS,GAAK,6BAA6B,CAAE,CAAC;SAC9F;QAED,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CAAC,WAAW,EAAE,KAAK;IACvC,OAAO,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,iBAAe,KAAK,gBAAa;QAC/D,CAAC,CAAC,iBAAe,WAAW,SAAI,KAAK,gBAAa,CAAC;AACvD,CAAC;AAED;;;;;GAKG;AACH,SAAS,qBAAqB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO;IACzD,QAAQ,QAAQ,CAAC,IAAI,EAAE;QACrB,KAAK,WAAW;YACd,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC;YACtC,SAAS,CAAC,sBAAsB,GAAG,OAAO,CAAC,oBAAoB,CAAC;YAChE,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACvE,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,MAAM;QACR,KAAK,OAAO;YACV,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACtB,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5D,MAAM;KACT;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAS,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO;IACxF,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,iBAAiB,CAAC;IACjE,IAAM,UAAU,GAAG,OAAO,CAAC,mBAAmB,GAAG,eAAe,CAAC;IAEjE,IAAI,UAAU,GAAG,CAAC,EAAE;QAClB,UAAU,CAAC;YACT,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC,EAAE,UAAU,CAAC,CAAC;QACf,OAAO;KACR;IAED,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;AACnF,CAAC;AAED;;;GAGG;AAEH;;;;GAIG;AAEH;;;GAGG;AAEH;;;;;;GAMG;AAEH,MAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC"}