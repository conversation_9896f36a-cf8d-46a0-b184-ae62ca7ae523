{"version": 3, "file": "movingaveragedelta.js", "sourceRoot": "", "sources": ["../../lib/util/movingaveragedelta.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb;;;;;;GAMG;AACH;IACE;;OAEG;IACH;QACE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,QAAQ,EAAE;gBACR,KAAK,EAAE;oBACL,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;oBAChC,EAAE,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;iBACjC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,gCAAG,GAAH;QACU,IAAU,OAAO,GAAK,IAAI,SAAT,CAAU;QACnC,IAAM,gBAAgB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;QACvF,IAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACnE,OAAO,cAAc,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,sCAAS,GAAT,UAAU,SAAS,EAAE,WAAW;QACtB,IAAU,OAAO,GAAK,IAAI,SAAT,CAAU;QACnC,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,EAAE,WAAW,aAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;IAC3C,CAAC;IACH,yBAAC;AAAD,CAAC,AApCD,IAoCC;AAED,MAAM,CAAC,OAAO,GAAG,kBAAkB,CAAC"}