{"version": 3, "file": "networkmonitor.js", "sourceRoot": "", "sources": ["../../lib/util/networkmonitor.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb;;GAEG;AACH;IACE;;;;OAIG;IACH,wBAAY,gBAAgB,EAAE,OAAO;QAArC,iBAyDC;QAxDC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,SAAS,WAAA;YACT,MAAM,QAAA;SACP,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;QAC9B,IAAM,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACpD,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAErB,IAAA,KAAkC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACxD,OAAO,EAAE;gBACP,KAAK,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;aAChC;YACD,SAAS,EAAE;gBACT,KAAK,EAAE;oBACL,IAAM,cAAc,GAAG,IAAI,KAAK,KAAI,CAAC,IAAI,IAAI,KAAI,CAAC,QAAQ,CAAC;oBAC3D,IAAI,GAAG,KAAI,CAAC,IAAI,CAAC;oBACjB,IAAI,cAAc,EAAE;wBAClB,gBAAgB,EAAE,CAAC;qBACpB;gBACH,CAAC;aACF;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,UAAU;aAClB;SACF,CAAC,CAAC,CAAC;YACF,OAAO,EAAE;gBACP,KAAK,EAAE,CAAC,QAAQ,CAAC;aAClB;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,gBAAgB;aACxB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO,CAAC,MAAM;aACtB;SACF,EA1BO,OAAO,aAAA,EAAE,SAAS,eAAA,EAAE,OAAO,aA0BlC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,OAAO,GAAG,CAAC,MAAM,KAAK,SAAS;wBACpC,CAAC,CAAC,GAAG,CAAC,MAAM;wBACZ,CAAC,CAAC,IAAI,CAAC;gBACX,CAAC;aACF;YACD,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,GAAG;oBACD,OAAO,UAAU,CAAC,IAAI,IAAI,IAAI,CAAC;gBACjC,CAAC;aACF;YACD,SAAS,WAAA;YACT,OAAO,SAAA;YACP,OAAO,SAAA;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,8BAAK,GAAL;QAAA,iBAIC;QAHC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;YACxB,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAI,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,6BAAI,GAAJ;QAAA,iBAIC;QAHC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,KAAK;YACxB,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IACH,qBAAC;AAAD,CAAC,AAlFD,IAkFC;AAED,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC"}