{"version": 3, "file": "issue8329.js", "sourceRoot": "", "sources": ["../../../lib/util/sdp/issue8329.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEL,IAAA,qBAAqB,GAAK,OAAO,CAAC,cAAc,CAAC,sBAA5B,CAA6B;AAEpD,IAAA,KAA4C,OAAO,CAAC,IAAI,CAAC,EAAvD,mBAAmB,yBAAA,EAAE,gBAAgB,sBAAkB,CAAC;AAEhE;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;;GAIG;AACH,SAAS,UAAU,CAAC,WAAW;IAC7B,IAAM,eAAe,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC;IACnD,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;QACnC,eAAe,CAAC,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACtD;IACD,OAAO,IAAI,qBAAqB,CAAC,eAAe,CAAC,CAAC;AACpD,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,GAAG;IACxB,IAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,OAAO,CAAC,OAAO,CAAC;SACb,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;SACjD,IAAI,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,SAAS,sBAAsB,CAAC,YAAY;IAC1C,IAAM,aAAa,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC;IACxD,YAAY,GAAG,qBAAqB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IAClE,IAAM,cAAc,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAC;IAC3D,IAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IAEtD,IAAM,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;IAChC,IAAM,mBAAmB,GAAG,yBAAyB,CACnD,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACtD,IAAM,mBAAmB,GAAG,yBAAyB,CACnD,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAEtC,IAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAErD,6CAA6C;IAC7C,IAAM,eAAe,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/C,IAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,UAAC,eAAe,EAAE,SAAS;QACxE,IAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QACvD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAC,eAAe,EAAE,EAAE,IAAK,OAAA,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC;YAChF,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAF4B,CAE5B,EAAE,eAAe,CAAC,CAAC;IAChD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAEd,eAAe,CAAC,OAAO,CAAC,UAAA,EAAE;QACxB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YAC7B,IAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC;YACzC,YAAY,GAAG,4BAA4B,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACjE,YAAY,GAAG,wBAAwB,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;SAClE;IACH,CAAC,CAAC,CAAC;IAEH,kBAAkB,CAAC,OAAO,CAAC,UAAA,KAAK;QAC9B,YAAY,GAAG,4BAA4B,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACjE,YAAY,GAAG,8BAA8B,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;GAIG;AACH,SAAS,qBAAqB,CAAC,YAAY,EAAE,aAAa;IACxD,qEAAqE;IACrE,uEAAuE;IACvE,mEAAmE;IACnE,+BAA+B;IAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,OAAO,EAAE,EAAE;QACzD,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,eAAa,EAAE,YAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,OAAO,EAAE,MAAM;YAC9G,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,SAAO,MAAQ,CAAC,CAAC;YAChD,IAAM,YAAY,GAAG,IAAI,MAAM,CAAC,gBAAc,EAAE,gBAAa,CAAC,CAAC;YAC/D,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACpE,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,EAAE,YAAY,CAAC,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,SAAS,oBAAoB,CAAC,aAAa;IACzC,IAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;IACjC,aAAa,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,EAAE;QAClC,IAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QACvD,OAAO,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IACH,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,yBAAyB,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa;IACnF,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAC,mBAAmB,EAAE,KAAK;QAC1D,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,YAAU,KAAK,gBAAa,CAAC,CAAC;QAC7D,IAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,EAAE;YACZ,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO,mBAAmB,CAAC;SAC5B;QAED,IAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAC1B,sBAAsB;YACtB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO,mBAAmB,CAAC;SAC5B;QAED,IAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,SAAS,KAAK,KAAK,EAAE;YACvB,UAAU;YACV,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO,mBAAmB,CAAC;SAC5B;QAED,OAAO,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC5C,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,mBAAmB,EAAE,aAAa;IACnE,2DAA2D;IAC3D,IAAM,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,UAAC,oBAAoB,EAAE,IAAI;QAC7F,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,IAAM,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QACzD,OAAO,oBAAoB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IAEd,6EAA6E;IAC7E,wDAAwD;IACxD,OAAO,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,UAAC,mBAAmB,EAAE,IAAI;QACvE,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;gBAClB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,OAAO,mBAAmB,CAAC;SAC5B;QACD,OAAO,mBAAmB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,4BAA4B,CAAC,YAAY,EAAE,KAAK;IACvD,IAAM,OAAO,GAAG,IAAI,MAAM,CAAC,YAAU,KAAK,WAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,OAAO,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED;;;;GAIG;AACH,SAAS,8BAA8B,CAAC,YAAY,EAAE,KAAK;IACzD,IAAM,OAAO,GAAG,IAAI,MAAM,CAAC,cAAY,KAAK,WAAQ,EAAE,IAAI,CAAC,CAAC;IAC5D,OAAO,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;GAKG;AACH,SAAS,wBAAwB,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;IACvD,OAAO,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;QAClC,CAAC,CAAI,YAAY,eAAU,KAAK,aAAQ,EAAE,SAAM;QAChD,CAAC,CAAI,YAAY,mBAAc,KAAK,aAAQ,EAAI,CAAC;AACrD,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC"}