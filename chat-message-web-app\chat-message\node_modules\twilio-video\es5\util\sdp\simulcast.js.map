{"version": 3, "file": "simulcast.js", "sourceRoot": "", "sources": ["../../../lib/util/sdp/simulcast.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;AAEP,IAAA,KAA0B,OAAO,CAAC,KAAK,CAAC,EAAtC,UAAU,gBAAA,EAAE,OAAO,aAAmB,CAAC;AAE/C;;;GAGG;AACH,SAAS,UAAU;IACjB,IAAM,OAAO,GAAG,UAAU,CAAC;IAC3B,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;GAOG;AACH;IACE;;;;;OAKG;IACH,yBAAY,OAAO,EAAE,QAAQ,EAAE,KAAK;QAClC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK;aACb;YACD,kBAAkB,EAAE;gBAClB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,QAAQ;aAChB;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,2CAAiB,GAAjB;QACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO;SACR;QACD,IAAM,cAAc,GAAG,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACpD,cAAc,CAAC,OAAO,CAAC,UAAS,IAAI;YAClC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACtB,cAAc,CAAC,OAAO,CAAC,UAAS,IAAI;gBAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC;YACxC,CAAC,EAAE,IAAI,CAAC,CAAC;SACV;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,iCAAO,GAAP,UAAQ,IAAI,EAAE,WAAW,EAAE,SAAS;QAClC,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACtC;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACH,oCAAU,GAAV,UAAW,UAAU;QAArB,iBAkBC;QAjBC,IAAM,QAAQ,GAAG,UAAU;YACzB,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,OAAO,EAAE,EAAjB,CAAiB,CAAC,CAAC;QAE1E,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE7D,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,EAAE,UAAA,IAAI,IAAI,OAAA;YACvC,YAAU,IAAI,eAAU,KAAI,CAAC,KAAO;YACpC,YAAU,IAAI,cAAS,KAAI,CAAC,QAAQ,SAAI,KAAI,CAAC,OAAS;SACvD,EAHwC,CAGxC,CAAC,CAAC;QACH,IAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,sBAAoB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAG,EAAvC,CAAuC,CAAC,CAAC;QACtF,IAAM,aAAa,GAAG;YACpB,sBAAoB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAG;SACzC,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IACH,sBAAC;AAAD,CAAC,AApGD,IAoGC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAAC,OAAO,EAAE,OAAO;IAClC,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/D,OAAO,OAAO,CAAC,GAAG,CAAC,UAAA,KAAK;QACtB,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;QACvD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,OAAO;IAChC,IAAM,eAAe,GAAG,+CAA+C,CAAC;IACxE,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS;IAChD,IAAM,OAAO,GAAG,YAAU,IAAI,SAAI,SAAS,UAAO,CAAC;IACnD,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,OAAO;IAC9B,IAAM,cAAc,GAAG,sCAAsC,CAAC;IAC9D,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,OAAO,EAAE,EAAd,CAAc,CAAC,CAAC,CAAC;AAClF,CAAC;AAED;;;;GAIG;AACH,SAAS,yBAAyB,CAAC,OAAO;IAClC,IAAA,KAAA,OAAsB,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,IAAA,EAAvE,QAAQ,QAAA,EAAE,OAAO,QAAsD,CAAC;IAC/E,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACrE,OAAO,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAzB,CAAyB,CAAC,CAAC;AACtD,CAAC;AAED;;;;GAIG;AACH,SAAS,0BAA0B,CAAC,OAAO;IACzC,IAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC5C,IAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;IAC1C,IAAM,cAAc,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAE1D,OAAO,cAAc,CAAC,MAAM,CAAC,UAAC,eAAe,EAAE,KAAK;QAClD,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzB,IAAM,eAAe,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,eAAe,CACzE,OAAO,EACP,QAAQ,EACR,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAE5C,IAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QAC/C,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,OAAO,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACvD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,0BAA0B,CAAC,OAAO,EAAE,oBAAoB;IAC/D,IAAM,uBAAuB,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;IACpE,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/D,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC;IACvD,IAAM,aAAa,GAAG,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IACxD,IAAM,gBAAgB,GAAG,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAE3D,6DAA6D;IAC7D,wBAAwB;IACxB,IAAM,oBAAoB,GAAG,OAAO,CAAC,aAAa,EAAE,UAAA,OAAO,IAAI,OAAA,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC,EAApC,CAAoC,CAAC,CAAC;IACrG,oBAAoB,CAAC,OAAO,CAAC,UAAA,eAAe;QAC1C,eAAe,CAAC,iBAAiB,EAAE,CAAC;QACpC,oBAAoB,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,8DAA8D;IAC9D,0BAA0B;IAC1B,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,IAAM,gBAAgB,GAAG,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IAChE,IAAM,uBAAuB,GAAG,OAAO,CAAC,gBAAgB,EAAE,UAAA,OAAO,IAAI,OAAA,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAjC,CAAiC,CAAC,CAAC;IACxG,IAAM,UAAU,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACzD,IAAM,gBAAgB,GAAG,OAAO,CAAC,uBAAuB,EAAE,UAAA,eAAe,IAAI,OAAA,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC,EAAtC,CAAsC,CAAC,CAAC;IAErH,yEAAyE;IACzE,2EAA2E;IAC3E,kCAAkC;IAClC,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEtF,IAAM,qBAAqB,GAAG,4BAA4B,CAAC;IAC3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;QACzC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;KAC1C;IAED,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC"}