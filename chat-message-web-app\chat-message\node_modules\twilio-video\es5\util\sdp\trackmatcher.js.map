{"version": 3, "file": "trackmatcher.js", "sourceRoot": "", "sources": ["../../../lib/util/sdp/trackmatcher.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEL,IAAA,gBAAgB,GAAK,OAAO,CAAC,IAAI,CAAC,iBAAlB,CAAmB;AAE3C;;;GAGG;AACH;IACE;;OAEG;IACH;QACE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,eAAe,EAAE;gBACf,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,4BAAK,GAAL,UAAM,KAAK;QACT,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACjE,CAAC;IAED;;;OAGG;IACH,6BAAM,GAAN,UAAO,GAAG;QACR,IAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,cAAc,EAAE,OAAO;YAC7D,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YACxD,IAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;YAChE,IAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QAC5E,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3B,CAAC;IACH,mBAAC;AAAD,CAAC,AApCD,IAoCC;AAED,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC"}