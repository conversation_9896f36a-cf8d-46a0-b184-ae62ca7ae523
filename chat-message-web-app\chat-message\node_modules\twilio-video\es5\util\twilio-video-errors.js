// NOTE: Do not edit this file. This code is auto-generated. Contact the
// Twilio SDK Team for more information.
'use strict';
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var TwilioError = require('./twilioerror');
var TwilioErrorByCode = {};
/**
 * Create a {@link TwilioError} for a given code and message.
 * @private
 * @param {number} [code] - Error code
 * @param {string} [message] - Error message
 * @returns {TwilioError}
 */
exports.createTwilioError = function createTwilioError(code, message) {
    code = typeof code === 'number' ? code : 0;
    message = typeof message === 'string' && message ? message : 'Unknown error';
    return TwilioErrorByCode[code] ? new TwilioErrorByCode[code]() : new TwilioError(code, message);
};
/**
 * @class AccessTokenInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room is invalid.
 * @extends TwilioError
 * @property {number} code - 20101
 * @property {string} message - 'Invalid Access Token'
 */
var AccessTokenInvalidError = /** @class */ (function (_super) {
    __extends(AccessTokenInvalidError, _super);
    function AccessTokenInvalidError() {
        var _this = _super.call(this, 20101, 'Invalid Access Token') || this;
        Object.setPrototypeOf(_this, AccessTokenInvalidError.prototype);
        return _this;
    }
    return AccessTokenInvalidError;
}(TwilioError));
exports.AccessTokenInvalidError = AccessTokenInvalidError;
Object.defineProperty(TwilioErrorByCode, 20101, { value: AccessTokenInvalidError });
/**
 * @class AccessTokenHeaderInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has an invalid header.
 * @extends TwilioError
 * @property {number} code - 20102
 * @property {string} message - 'Invalid Access Token header'
 */
var AccessTokenHeaderInvalidError = /** @class */ (function (_super) {
    __extends(AccessTokenHeaderInvalidError, _super);
    function AccessTokenHeaderInvalidError() {
        var _this = _super.call(this, 20102, 'Invalid Access Token header') || this;
        Object.setPrototypeOf(_this, AccessTokenHeaderInvalidError.prototype);
        return _this;
    }
    return AccessTokenHeaderInvalidError;
}(TwilioError));
exports.AccessTokenHeaderInvalidError = AccessTokenHeaderInvalidError;
Object.defineProperty(TwilioErrorByCode, 20102, { value: AccessTokenHeaderInvalidError });
/**
 * @class AccessTokenIssuerInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room contains an invalid issuer or subject.
 * @extends TwilioError
 * @property {number} code - 20103
 * @property {string} message - 'Invalid Access Token issuer/subject'
 */
var AccessTokenIssuerInvalidError = /** @class */ (function (_super) {
    __extends(AccessTokenIssuerInvalidError, _super);
    function AccessTokenIssuerInvalidError() {
        var _this = _super.call(this, 20103, 'Invalid Access Token issuer/subject') || this;
        Object.setPrototypeOf(_this, AccessTokenIssuerInvalidError.prototype);
        return _this;
    }
    return AccessTokenIssuerInvalidError;
}(TwilioError));
exports.AccessTokenIssuerInvalidError = AccessTokenIssuerInvalidError;
Object.defineProperty(TwilioErrorByCode, 20103, { value: AccessTokenIssuerInvalidError });
/**
 * @class AccessTokenExpiredError
 * @classdesc Raised whenever the AccessToken used for connecting, or reconnecting to a Room has expired.
 * @extends TwilioError
 * @property {number} code - 20104
 * @property {string} message - 'Access Token expired or expiration date invalid'
 */
var AccessTokenExpiredError = /** @class */ (function (_super) {
    __extends(AccessTokenExpiredError, _super);
    function AccessTokenExpiredError() {
        var _this = _super.call(this, 20104, 'Access Token expired or expiration date invalid') || this;
        Object.setPrototypeOf(_this, AccessTokenExpiredError.prototype);
        return _this;
    }
    return AccessTokenExpiredError;
}(TwilioError));
exports.AccessTokenExpiredError = AccessTokenExpiredError;
Object.defineProperty(TwilioErrorByCode, 20104, { value: AccessTokenExpiredError });
/**
 * @class AccessTokenNotYetValidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room is not yet valid.
 * @extends TwilioError
 * @property {number} code - 20105
 * @property {string} message - 'Access Token not yet valid'
 */
var AccessTokenNotYetValidError = /** @class */ (function (_super) {
    __extends(AccessTokenNotYetValidError, _super);
    function AccessTokenNotYetValidError() {
        var _this = _super.call(this, 20105, 'Access Token not yet valid') || this;
        Object.setPrototypeOf(_this, AccessTokenNotYetValidError.prototype);
        return _this;
    }
    return AccessTokenNotYetValidError;
}(TwilioError));
exports.AccessTokenNotYetValidError = AccessTokenNotYetValidError;
Object.defineProperty(TwilioErrorByCode, 20105, { value: AccessTokenNotYetValidError });
/**
 * @class AccessTokenGrantsInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has invalid grants.
 * @extends TwilioError
 * @property {number} code - 20106
 * @property {string} message - 'Invalid Access Token grants'
 */
var AccessTokenGrantsInvalidError = /** @class */ (function (_super) {
    __extends(AccessTokenGrantsInvalidError, _super);
    function AccessTokenGrantsInvalidError() {
        var _this = _super.call(this, 20106, 'Invalid Access Token grants') || this;
        Object.setPrototypeOf(_this, AccessTokenGrantsInvalidError.prototype);
        return _this;
    }
    return AccessTokenGrantsInvalidError;
}(TwilioError));
exports.AccessTokenGrantsInvalidError = AccessTokenGrantsInvalidError;
Object.defineProperty(TwilioErrorByCode, 20106, { value: AccessTokenGrantsInvalidError });
/**
 * @class AccessTokenSignatureInvalidError
 * @classdesc Raised whenever the AccessToken used for connecting to a Room has an invalid signature.
 * @extends TwilioError
 * @property {number} code - 20107
 * @property {string} message - 'Invalid Access Token signature'
 */
var AccessTokenSignatureInvalidError = /** @class */ (function (_super) {
    __extends(AccessTokenSignatureInvalidError, _super);
    function AccessTokenSignatureInvalidError() {
        var _this = _super.call(this, 20107, 'Invalid Access Token signature') || this;
        Object.setPrototypeOf(_this, AccessTokenSignatureInvalidError.prototype);
        return _this;
    }
    return AccessTokenSignatureInvalidError;
}(TwilioError));
exports.AccessTokenSignatureInvalidError = AccessTokenSignatureInvalidError;
Object.defineProperty(TwilioErrorByCode, 20107, { value: AccessTokenSignatureInvalidError });
/**
 * @class SignalingConnectionError
 * @classdesc Raised whenever a signaling connection error occurs that is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53000
 * @property {string} message - 'Signaling connection error'
 */
var SignalingConnectionError = /** @class */ (function (_super) {
    __extends(SignalingConnectionError, _super);
    function SignalingConnectionError() {
        var _this = _super.call(this, 53000, 'Signaling connection error') || this;
        Object.setPrototypeOf(_this, SignalingConnectionError.prototype);
        return _this;
    }
    return SignalingConnectionError;
}(TwilioError));
exports.SignalingConnectionError = SignalingConnectionError;
Object.defineProperty(TwilioErrorByCode, 53000, { value: SignalingConnectionError });
/**
 * @class SignalingConnectionDisconnectedError
 * @classdesc Raised whenever the signaling connection is unexpectedly disconnected.
 * @extends TwilioError
 * @property {number} code - 53001
 * @property {string} message - 'Signaling connection disconnected'
 */
var SignalingConnectionDisconnectedError = /** @class */ (function (_super) {
    __extends(SignalingConnectionDisconnectedError, _super);
    function SignalingConnectionDisconnectedError() {
        var _this = _super.call(this, 53001, 'Signaling connection disconnected') || this;
        Object.setPrototypeOf(_this, SignalingConnectionDisconnectedError.prototype);
        return _this;
    }
    return SignalingConnectionDisconnectedError;
}(TwilioError));
exports.SignalingConnectionDisconnectedError = SignalingConnectionDisconnectedError;
Object.defineProperty(TwilioErrorByCode, 53001, { value: SignalingConnectionDisconnectedError });
/**
 * @class SignalingConnectionTimeoutError
 * @classdesc Raised when connection liveliness checks fail, or when the signaling session expires.
 * @extends TwilioError
 * @property {number} code - 53002
 * @property {string} message - 'Signaling connection timed out'
 */
var SignalingConnectionTimeoutError = /** @class */ (function (_super) {
    __extends(SignalingConnectionTimeoutError, _super);
    function SignalingConnectionTimeoutError() {
        var _this = _super.call(this, 53002, 'Signaling connection timed out') || this;
        Object.setPrototypeOf(_this, SignalingConnectionTimeoutError.prototype);
        return _this;
    }
    return SignalingConnectionTimeoutError;
}(TwilioError));
exports.SignalingConnectionTimeoutError = SignalingConnectionTimeoutError;
Object.defineProperty(TwilioErrorByCode, 53002, { value: SignalingConnectionTimeoutError });
/**
 * @class SignalingIncomingMessageInvalidError
 * @classdesc Raised whenever the Client receives a message from the Server that the Client cannot handle.
 * @extends TwilioError
 * @property {number} code - 53003
 * @property {string} message - 'Client received an invalid signaling message'
 */
var SignalingIncomingMessageInvalidError = /** @class */ (function (_super) {
    __extends(SignalingIncomingMessageInvalidError, _super);
    function SignalingIncomingMessageInvalidError() {
        var _this = _super.call(this, 53003, 'Client received an invalid signaling message') || this;
        Object.setPrototypeOf(_this, SignalingIncomingMessageInvalidError.prototype);
        return _this;
    }
    return SignalingIncomingMessageInvalidError;
}(TwilioError));
exports.SignalingIncomingMessageInvalidError = SignalingIncomingMessageInvalidError;
Object.defineProperty(TwilioErrorByCode, 53003, { value: SignalingIncomingMessageInvalidError });
/**
 * @class SignalingOutgoingMessageInvalidError
 * @classdesc Raised whenever the Client sends a message to the Server that the Server cannot handle.
 * @extends TwilioError
 * @property {number} code - 53004
 * @property {string} message - 'Client sent an invalid signaling message'
 */
var SignalingOutgoingMessageInvalidError = /** @class */ (function (_super) {
    __extends(SignalingOutgoingMessageInvalidError, _super);
    function SignalingOutgoingMessageInvalidError() {
        var _this = _super.call(this, 53004, 'Client sent an invalid signaling message') || this;
        Object.setPrototypeOf(_this, SignalingOutgoingMessageInvalidError.prototype);
        return _this;
    }
    return SignalingOutgoingMessageInvalidError;
}(TwilioError));
exports.SignalingOutgoingMessageInvalidError = SignalingOutgoingMessageInvalidError;
Object.defineProperty(TwilioErrorByCode, 53004, { value: SignalingOutgoingMessageInvalidError });
/**
 * @class SignalingServerBusyError
 * @classdesc Raised when the server is too busy to accept new clients.
 * @extends TwilioError
 * @property {number} code - 53006
 * @property {string} message - 'Video server is busy'
 */
var SignalingServerBusyError = /** @class */ (function (_super) {
    __extends(SignalingServerBusyError, _super);
    function SignalingServerBusyError() {
        var _this = _super.call(this, 53006, 'Video server is busy') || this;
        Object.setPrototypeOf(_this, SignalingServerBusyError.prototype);
        return _this;
    }
    return SignalingServerBusyError;
}(TwilioError));
exports.SignalingServerBusyError = SignalingServerBusyError;
Object.defineProperty(TwilioErrorByCode, 53006, { value: SignalingServerBusyError });
/**
 * @class RoomNameInvalidError
 * @classdesc Raised whenever a Room name is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53100
 * @property {string} message - 'Room name is invalid'
 */
var RoomNameInvalidError = /** @class */ (function (_super) {
    __extends(RoomNameInvalidError, _super);
    function RoomNameInvalidError() {
        var _this = _super.call(this, 53100, 'Room name is invalid') || this;
        Object.setPrototypeOf(_this, RoomNameInvalidError.prototype);
        return _this;
    }
    return RoomNameInvalidError;
}(TwilioError));
exports.RoomNameInvalidError = RoomNameInvalidError;
Object.defineProperty(TwilioErrorByCode, 53100, { value: RoomNameInvalidError });
/**
 * @class RoomNameTooLongError
 * @classdesc Raised whenever a Room name is too long.
 * @extends TwilioError
 * @property {number} code - 53101
 * @property {string} message - 'Room name is too long'
 */
var RoomNameTooLongError = /** @class */ (function (_super) {
    __extends(RoomNameTooLongError, _super);
    function RoomNameTooLongError() {
        var _this = _super.call(this, 53101, 'Room name is too long') || this;
        Object.setPrototypeOf(_this, RoomNameTooLongError.prototype);
        return _this;
    }
    return RoomNameTooLongError;
}(TwilioError));
exports.RoomNameTooLongError = RoomNameTooLongError;
Object.defineProperty(TwilioErrorByCode, 53101, { value: RoomNameTooLongError });
/**
 * @class RoomNameCharsInvalidError
 * @classdesc Raised whenever a Room name contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53102
 * @property {string} message - 'Room name contains invalid characters'
 */
var RoomNameCharsInvalidError = /** @class */ (function (_super) {
    __extends(RoomNameCharsInvalidError, _super);
    function RoomNameCharsInvalidError() {
        var _this = _super.call(this, 53102, 'Room name contains invalid characters') || this;
        Object.setPrototypeOf(_this, RoomNameCharsInvalidError.prototype);
        return _this;
    }
    return RoomNameCharsInvalidError;
}(TwilioError));
exports.RoomNameCharsInvalidError = RoomNameCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53102, { value: RoomNameCharsInvalidError });
/**
 * @class RoomCreateFailedError
 * @classdesc Raised whenever the Server is unable to create a Room.
 * @extends TwilioError
 * @property {number} code - 53103
 * @property {string} message - 'Unable to create Room'
 */
var RoomCreateFailedError = /** @class */ (function (_super) {
    __extends(RoomCreateFailedError, _super);
    function RoomCreateFailedError() {
        var _this = _super.call(this, 53103, 'Unable to create Room') || this;
        Object.setPrototypeOf(_this, RoomCreateFailedError.prototype);
        return _this;
    }
    return RoomCreateFailedError;
}(TwilioError));
exports.RoomCreateFailedError = RoomCreateFailedError;
Object.defineProperty(TwilioErrorByCode, 53103, { value: RoomCreateFailedError });
/**
 * @class RoomConnectFailedError
 * @classdesc Raised whenever a Client is unable to connect to a Room, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53104
 * @property {string} message - 'Unable to connect to Room'
 */
var RoomConnectFailedError = /** @class */ (function (_super) {
    __extends(RoomConnectFailedError, _super);
    function RoomConnectFailedError() {
        var _this = _super.call(this, 53104, 'Unable to connect to Room') || this;
        Object.setPrototypeOf(_this, RoomConnectFailedError.prototype);
        return _this;
    }
    return RoomConnectFailedError;
}(TwilioError));
exports.RoomConnectFailedError = RoomConnectFailedError;
Object.defineProperty(TwilioErrorByCode, 53104, { value: RoomConnectFailedError });
/**
 * @class RoomMaxParticipantsExceededError
 * @classdesc Raised whenever a Client is unable to connect to a Room because the Room contains too many Participants.
 * @extends TwilioError
 * @property {number} code - 53105
 * @property {string} message - 'Room contains too many Participants'
 */
var RoomMaxParticipantsExceededError = /** @class */ (function (_super) {
    __extends(RoomMaxParticipantsExceededError, _super);
    function RoomMaxParticipantsExceededError() {
        var _this = _super.call(this, 53105, 'Room contains too many Participants') || this;
        Object.setPrototypeOf(_this, RoomMaxParticipantsExceededError.prototype);
        return _this;
    }
    return RoomMaxParticipantsExceededError;
}(TwilioError));
exports.RoomMaxParticipantsExceededError = RoomMaxParticipantsExceededError;
Object.defineProperty(TwilioErrorByCode, 53105, { value: RoomMaxParticipantsExceededError });
/**
 * @class RoomNotFoundError
 * @classdesc Raised whenever attempting operation on a non-existent Room.
 * @extends TwilioError
 * @property {number} code - 53106
 * @property {string} message - 'Room not found'
 */
var RoomNotFoundError = /** @class */ (function (_super) {
    __extends(RoomNotFoundError, _super);
    function RoomNotFoundError() {
        var _this = _super.call(this, 53106, 'Room not found') || this;
        Object.setPrototypeOf(_this, RoomNotFoundError.prototype);
        return _this;
    }
    return RoomNotFoundError;
}(TwilioError));
exports.RoomNotFoundError = RoomNotFoundError;
Object.defineProperty(TwilioErrorByCode, 53106, { value: RoomNotFoundError });
/**
 * @class RoomMaxParticipantsOutOfRangeError
 * @classdesc Raised in the REST API when MaxParticipants is set out of range.
 * @extends TwilioError
 * @property {number} code - 53107
 * @property {string} message - 'MaxParticipants is out of range'
 */
var RoomMaxParticipantsOutOfRangeError = /** @class */ (function (_super) {
    __extends(RoomMaxParticipantsOutOfRangeError, _super);
    function RoomMaxParticipantsOutOfRangeError() {
        var _this = _super.call(this, 53107, 'MaxParticipants is out of range') || this;
        Object.setPrototypeOf(_this, RoomMaxParticipantsOutOfRangeError.prototype);
        return _this;
    }
    return RoomMaxParticipantsOutOfRangeError;
}(TwilioError));
exports.RoomMaxParticipantsOutOfRangeError = RoomMaxParticipantsOutOfRangeError;
Object.defineProperty(TwilioErrorByCode, 53107, { value: RoomMaxParticipantsOutOfRangeError });
/**
 * @class RoomTypeInvalidError
 * @classdesc Raised in the REST API when the user attempts to create a Room with an invalid RoomType
 * @extends TwilioError
 * @property {number} code - 53108
 * @property {string} message - 'RoomType is not valid'
 */
var RoomTypeInvalidError = /** @class */ (function (_super) {
    __extends(RoomTypeInvalidError, _super);
    function RoomTypeInvalidError() {
        var _this = _super.call(this, 53108, 'RoomType is not valid') || this;
        Object.setPrototypeOf(_this, RoomTypeInvalidError.prototype);
        return _this;
    }
    return RoomTypeInvalidError;
}(TwilioError));
exports.RoomTypeInvalidError = RoomTypeInvalidError;
Object.defineProperty(TwilioErrorByCode, 53108, { value: RoomTypeInvalidError });
/**
 * @class RoomTimeoutOutOfRangeError
 * @classdesc Raised in the REST API when Timeout is set out of range.
 * @extends TwilioError
 * @property {number} code - 53109
 * @property {string} message - 'Timeout is out of range'
 */
var RoomTimeoutOutOfRangeError = /** @class */ (function (_super) {
    __extends(RoomTimeoutOutOfRangeError, _super);
    function RoomTimeoutOutOfRangeError() {
        var _this = _super.call(this, 53109, 'Timeout is out of range') || this;
        Object.setPrototypeOf(_this, RoomTimeoutOutOfRangeError.prototype);
        return _this;
    }
    return RoomTimeoutOutOfRangeError;
}(TwilioError));
exports.RoomTimeoutOutOfRangeError = RoomTimeoutOutOfRangeError;
Object.defineProperty(TwilioErrorByCode, 53109, { value: RoomTimeoutOutOfRangeError });
/**
 * @class RoomStatusCallbackMethodInvalidError
 * @classdesc Raised in the REST API when StatusCallbackMethod is set to an invalid value.
 * @extends TwilioError
 * @property {number} code - 53110
 * @property {string} message - 'StatusCallbackMethod is invalid'
 */
var RoomStatusCallbackMethodInvalidError = /** @class */ (function (_super) {
    __extends(RoomStatusCallbackMethodInvalidError, _super);
    function RoomStatusCallbackMethodInvalidError() {
        var _this = _super.call(this, 53110, 'StatusCallbackMethod is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusCallbackMethodInvalidError.prototype);
        return _this;
    }
    return RoomStatusCallbackMethodInvalidError;
}(TwilioError));
exports.RoomStatusCallbackMethodInvalidError = RoomStatusCallbackMethodInvalidError;
Object.defineProperty(TwilioErrorByCode, 53110, { value: RoomStatusCallbackMethodInvalidError });
/**
 * @class RoomStatusCallbackInvalidError
 * @classdesc Raised in the REST API when StatusCallback is not a valid URL or the url is too long.
 * @extends TwilioError
 * @property {number} code - 53111
 * @property {string} message - 'StatusCallback is invalid'
 */
var RoomStatusCallbackInvalidError = /** @class */ (function (_super) {
    __extends(RoomStatusCallbackInvalidError, _super);
    function RoomStatusCallbackInvalidError() {
        var _this = _super.call(this, 53111, 'StatusCallback is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusCallbackInvalidError.prototype);
        return _this;
    }
    return RoomStatusCallbackInvalidError;
}(TwilioError));
exports.RoomStatusCallbackInvalidError = RoomStatusCallbackInvalidError;
Object.defineProperty(TwilioErrorByCode, 53111, { value: RoomStatusCallbackInvalidError });
/**
 * @class RoomStatusInvalidError
 * @classdesc Raised in the REST API when Status is not valid or the Room is not in-progress.
 * @extends TwilioError
 * @property {number} code - 53112
 * @property {string} message - 'Status is invalid'
 */
var RoomStatusInvalidError = /** @class */ (function (_super) {
    __extends(RoomStatusInvalidError, _super);
    function RoomStatusInvalidError() {
        var _this = _super.call(this, 53112, 'Status is invalid') || this;
        Object.setPrototypeOf(_this, RoomStatusInvalidError.prototype);
        return _this;
    }
    return RoomStatusInvalidError;
}(TwilioError));
exports.RoomStatusInvalidError = RoomStatusInvalidError;
Object.defineProperty(TwilioErrorByCode, 53112, { value: RoomStatusInvalidError });
/**
 * @class RoomRoomExistsError
 * @classdesc Raised in the REST API when the Room creation fails because a Room exists with the same name.
 * @extends TwilioError
 * @property {number} code - 53113
 * @property {string} message - 'Room exists'
 */
var RoomRoomExistsError = /** @class */ (function (_super) {
    __extends(RoomRoomExistsError, _super);
    function RoomRoomExistsError() {
        var _this = _super.call(this, 53113, 'Room exists') || this;
        Object.setPrototypeOf(_this, RoomRoomExistsError.prototype);
        return _this;
    }
    return RoomRoomExistsError;
}(TwilioError));
exports.RoomRoomExistsError = RoomRoomExistsError;
Object.defineProperty(TwilioErrorByCode, 53113, { value: RoomRoomExistsError });
/**
 * @class RoomInvalidParametersError
 * @classdesc Raised in the REST API when one or more Room creation parameter is incompatible with the Room type.
 * @extends TwilioError
 * @property {number} code - 53114
 * @property {string} message - 'Room creation parameter(s) incompatible with the Room type'
 */
var RoomInvalidParametersError = /** @class */ (function (_super) {
    __extends(RoomInvalidParametersError, _super);
    function RoomInvalidParametersError() {
        var _this = _super.call(this, 53114, 'Room creation parameter(s) incompatible with the Room type') || this;
        Object.setPrototypeOf(_this, RoomInvalidParametersError.prototype);
        return _this;
    }
    return RoomInvalidParametersError;
}(TwilioError));
exports.RoomInvalidParametersError = RoomInvalidParametersError;
Object.defineProperty(TwilioErrorByCode, 53114, { value: RoomInvalidParametersError });
/**
 * @class RoomMediaRegionInvalidError
 * @classdesc Raised in the REST API when MediaRegion is set to an invalid value.
 * @extends TwilioError
 * @property {number} code - 53115
 * @property {string} message - 'MediaRegion is invalid'
 */
var RoomMediaRegionInvalidError = /** @class */ (function (_super) {
    __extends(RoomMediaRegionInvalidError, _super);
    function RoomMediaRegionInvalidError() {
        var _this = _super.call(this, 53115, 'MediaRegion is invalid') || this;
        Object.setPrototypeOf(_this, RoomMediaRegionInvalidError.prototype);
        return _this;
    }
    return RoomMediaRegionInvalidError;
}(TwilioError));
exports.RoomMediaRegionInvalidError = RoomMediaRegionInvalidError;
Object.defineProperty(TwilioErrorByCode, 53115, { value: RoomMediaRegionInvalidError });
/**
 * @class RoomMediaRegionUnavailableError
 * @classdesc Raised in the REST API when MediaRegion is set to a valid value but no media servers are available.
 * @extends TwilioError
 * @property {number} code - 53116
 * @property {string} message - 'There are no media servers available in the MediaRegion'
 */
var RoomMediaRegionUnavailableError = /** @class */ (function (_super) {
    __extends(RoomMediaRegionUnavailableError, _super);
    function RoomMediaRegionUnavailableError() {
        var _this = _super.call(this, 53116, 'There are no media servers available in the MediaRegion') || this;
        Object.setPrototypeOf(_this, RoomMediaRegionUnavailableError.prototype);
        return _this;
    }
    return RoomMediaRegionUnavailableError;
}(TwilioError));
exports.RoomMediaRegionUnavailableError = RoomMediaRegionUnavailableError;
Object.defineProperty(TwilioErrorByCode, 53116, { value: RoomMediaRegionUnavailableError });
/**
 * @class RoomSubscriptionOperationNotSupportedError
 * @classdesc Raised whenever the subscription operation requested is not supported for the Room type.
 * @extends TwilioError
 * @property {number} code - 53117
 * @property {string} message - 'The subscription operation requested is not supported for the Room type'
 */
var RoomSubscriptionOperationNotSupportedError = /** @class */ (function (_super) {
    __extends(RoomSubscriptionOperationNotSupportedError, _super);
    function RoomSubscriptionOperationNotSupportedError() {
        var _this = _super.call(this, 53117, 'The subscription operation requested is not supported for the Room type') || this;
        Object.setPrototypeOf(_this, RoomSubscriptionOperationNotSupportedError.prototype);
        return _this;
    }
    return RoomSubscriptionOperationNotSupportedError;
}(TwilioError));
exports.RoomSubscriptionOperationNotSupportedError = RoomSubscriptionOperationNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53117, { value: RoomSubscriptionOperationNotSupportedError });
/**
 * @class RoomCompletedError
 * @classdesc Raised whenever a Room is completed via the REST API.
 * @extends TwilioError
 * @property {number} code - 53118
 * @property {string} message - 'Room completed'
 */
var RoomCompletedError = /** @class */ (function (_super) {
    __extends(RoomCompletedError, _super);
    function RoomCompletedError() {
        var _this = _super.call(this, 53118, 'Room completed') || this;
        Object.setPrototypeOf(_this, RoomCompletedError.prototype);
        return _this;
    }
    return RoomCompletedError;
}(TwilioError));
exports.RoomCompletedError = RoomCompletedError;
Object.defineProperty(TwilioErrorByCode, 53118, { value: RoomCompletedError });
/**
 * @class RoomAudioOnlyFlagNotSupportedError
 * @classdesc Raised whenever a participant tries to set the AudioOnly flag for a Room type other than Group Rooms.
 * @extends TwilioError
 * @property {number} code - 53124
 * @property {string} message - 'The AudioOnly flag is not supported for the Room type'
 */
var RoomAudioOnlyFlagNotSupportedError = /** @class */ (function (_super) {
    __extends(RoomAudioOnlyFlagNotSupportedError, _super);
    function RoomAudioOnlyFlagNotSupportedError() {
        var _this = _super.call(this, 53124, 'The AudioOnly flag is not supported for the Room type') || this;
        Object.setPrototypeOf(_this, RoomAudioOnlyFlagNotSupportedError.prototype);
        return _this;
    }
    return RoomAudioOnlyFlagNotSupportedError;
}(TwilioError));
exports.RoomAudioOnlyFlagNotSupportedError = RoomAudioOnlyFlagNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53124, { value: RoomAudioOnlyFlagNotSupportedError });
/**
 * @class RoomTrackKindNotSupportedError
 * @classdesc Raised whenever a participant tries to publish a track or connects with a track that is not supported by the group room.
 * @extends TwilioError
 * @property {number} code - 53125
 * @property {string} message - 'The track kind is not supported by the Room'
 */
var RoomTrackKindNotSupportedError = /** @class */ (function (_super) {
    __extends(RoomTrackKindNotSupportedError, _super);
    function RoomTrackKindNotSupportedError() {
        var _this = _super.call(this, 53125, 'The track kind is not supported by the Room') || this;
        Object.setPrototypeOf(_this, RoomTrackKindNotSupportedError.prototype);
        return _this;
    }
    return RoomTrackKindNotSupportedError;
}(TwilioError));
exports.RoomTrackKindNotSupportedError = RoomTrackKindNotSupportedError;
Object.defineProperty(TwilioErrorByCode, 53125, { value: RoomTrackKindNotSupportedError });
/**
 * @class ParticipantIdentityInvalidError
 * @classdesc Raised whenever a Participant identity is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53200
 * @property {string} message - 'Participant identity is invalid'
 */
var ParticipantIdentityInvalidError = /** @class */ (function (_super) {
    __extends(ParticipantIdentityInvalidError, _super);
    function ParticipantIdentityInvalidError() {
        var _this = _super.call(this, 53200, 'Participant identity is invalid') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityInvalidError.prototype);
        return _this;
    }
    return ParticipantIdentityInvalidError;
}(TwilioError));
exports.ParticipantIdentityInvalidError = ParticipantIdentityInvalidError;
Object.defineProperty(TwilioErrorByCode, 53200, { value: ParticipantIdentityInvalidError });
/**
 * @class ParticipantIdentityTooLongError
 * @classdesc Raised whenever a Participant identity is too long.
 * @extends TwilioError
 * @property {number} code - 53201
 * @property {string} message - 'Participant identity is too long'
 */
var ParticipantIdentityTooLongError = /** @class */ (function (_super) {
    __extends(ParticipantIdentityTooLongError, _super);
    function ParticipantIdentityTooLongError() {
        var _this = _super.call(this, 53201, 'Participant identity is too long') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityTooLongError.prototype);
        return _this;
    }
    return ParticipantIdentityTooLongError;
}(TwilioError));
exports.ParticipantIdentityTooLongError = ParticipantIdentityTooLongError;
Object.defineProperty(TwilioErrorByCode, 53201, { value: ParticipantIdentityTooLongError });
/**
 * @class ParticipantIdentityCharsInvalidError
 * @classdesc Raised whenever a Participant identity contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53202
 * @property {string} message - 'Participant identity contains invalid characters'
 */
var ParticipantIdentityCharsInvalidError = /** @class */ (function (_super) {
    __extends(ParticipantIdentityCharsInvalidError, _super);
    function ParticipantIdentityCharsInvalidError() {
        var _this = _super.call(this, 53202, 'Participant identity contains invalid characters') || this;
        Object.setPrototypeOf(_this, ParticipantIdentityCharsInvalidError.prototype);
        return _this;
    }
    return ParticipantIdentityCharsInvalidError;
}(TwilioError));
exports.ParticipantIdentityCharsInvalidError = ParticipantIdentityCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53202, { value: ParticipantIdentityCharsInvalidError });
/**
 * @class ParticipantMaxTracksExceededError
 * @classdesc Raised whenever a Participant tries to publish a Track and the maximum number of published tracks allowed in the Room at the same time has been reached
 * @extends TwilioError
 * @property {number} code - 53203
 * @property {string} message - 'The maximum number of published tracks allowed in the Room at the same time has been reached'
 */
var ParticipantMaxTracksExceededError = /** @class */ (function (_super) {
    __extends(ParticipantMaxTracksExceededError, _super);
    function ParticipantMaxTracksExceededError() {
        var _this = _super.call(this, 53203, 'The maximum number of published tracks allowed in the Room at the same time has been reached') || this;
        Object.setPrototypeOf(_this, ParticipantMaxTracksExceededError.prototype);
        return _this;
    }
    return ParticipantMaxTracksExceededError;
}(TwilioError));
exports.ParticipantMaxTracksExceededError = ParticipantMaxTracksExceededError;
Object.defineProperty(TwilioErrorByCode, 53203, { value: ParticipantMaxTracksExceededError });
/**
 * @class ParticipantNotFoundError
 * @classdesc Raised whenever attempting an operation on a non-existent Participant.
 * @extends TwilioError
 * @property {number} code - 53204
 * @property {string} message - 'Participant not found'
 */
var ParticipantNotFoundError = /** @class */ (function (_super) {
    __extends(ParticipantNotFoundError, _super);
    function ParticipantNotFoundError() {
        var _this = _super.call(this, 53204, 'Participant not found') || this;
        Object.setPrototypeOf(_this, ParticipantNotFoundError.prototype);
        return _this;
    }
    return ParticipantNotFoundError;
}(TwilioError));
exports.ParticipantNotFoundError = ParticipantNotFoundError;
Object.defineProperty(TwilioErrorByCode, 53204, { value: ParticipantNotFoundError });
/**
 * @class ParticipantDuplicateIdentityError
 * @classdesc Raised by the server to the existing Participant when a new Participant joins a Room with the same identity as the existing Participant.
 * @extends TwilioError
 * @property {number} code - 53205
 * @property {string} message - 'Participant disconnected because of duplicate identity'
 */
var ParticipantDuplicateIdentityError = /** @class */ (function (_super) {
    __extends(ParticipantDuplicateIdentityError, _super);
    function ParticipantDuplicateIdentityError() {
        var _this = _super.call(this, 53205, 'Participant disconnected because of duplicate identity') || this;
        Object.setPrototypeOf(_this, ParticipantDuplicateIdentityError.prototype);
        return _this;
    }
    return ParticipantDuplicateIdentityError;
}(TwilioError));
exports.ParticipantDuplicateIdentityError = ParticipantDuplicateIdentityError;
Object.defineProperty(TwilioErrorByCode, 53205, { value: ParticipantDuplicateIdentityError });
/**
 * @class TrackInvalidError
 * @classdesc Raised whenever a Track is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53300
 * @property {string} message - 'Track is invalid'
 */
var TrackInvalidError = /** @class */ (function (_super) {
    __extends(TrackInvalidError, _super);
    function TrackInvalidError() {
        var _this = _super.call(this, 53300, 'Track is invalid') || this;
        Object.setPrototypeOf(_this, TrackInvalidError.prototype);
        return _this;
    }
    return TrackInvalidError;
}(TwilioError));
exports.TrackInvalidError = TrackInvalidError;
Object.defineProperty(TwilioErrorByCode, 53300, { value: TrackInvalidError });
/**
 * @class TrackNameInvalidError
 * @classdesc Raised whenever a Track name is invalid, and the scenario is not covered by a more specific error code.
 * @extends TwilioError
 * @property {number} code - 53301
 * @property {string} message - 'Track name is invalid'
 */
var TrackNameInvalidError = /** @class */ (function (_super) {
    __extends(TrackNameInvalidError, _super);
    function TrackNameInvalidError() {
        var _this = _super.call(this, 53301, 'Track name is invalid') || this;
        Object.setPrototypeOf(_this, TrackNameInvalidError.prototype);
        return _this;
    }
    return TrackNameInvalidError;
}(TwilioError));
exports.TrackNameInvalidError = TrackNameInvalidError;
Object.defineProperty(TwilioErrorByCode, 53301, { value: TrackNameInvalidError });
/**
 * @class TrackNameTooLongError
 * @classdesc Raised whenever a Track name is too long.
 * @extends TwilioError
 * @property {number} code - 53302
 * @property {string} message - 'Track name is too long'
 */
var TrackNameTooLongError = /** @class */ (function (_super) {
    __extends(TrackNameTooLongError, _super);
    function TrackNameTooLongError() {
        var _this = _super.call(this, 53302, 'Track name is too long') || this;
        Object.setPrototypeOf(_this, TrackNameTooLongError.prototype);
        return _this;
    }
    return TrackNameTooLongError;
}(TwilioError));
exports.TrackNameTooLongError = TrackNameTooLongError;
Object.defineProperty(TwilioErrorByCode, 53302, { value: TrackNameTooLongError });
/**
 * @class TrackNameCharsInvalidError
 * @classdesc Raised whenever a Track name contains invalid characters.
 * @extends TwilioError
 * @property {number} code - 53303
 * @property {string} message - 'Track name contains invalid characters'
 */
var TrackNameCharsInvalidError = /** @class */ (function (_super) {
    __extends(TrackNameCharsInvalidError, _super);
    function TrackNameCharsInvalidError() {
        var _this = _super.call(this, 53303, 'Track name contains invalid characters') || this;
        Object.setPrototypeOf(_this, TrackNameCharsInvalidError.prototype);
        return _this;
    }
    return TrackNameCharsInvalidError;
}(TwilioError));
exports.TrackNameCharsInvalidError = TrackNameCharsInvalidError;
Object.defineProperty(TwilioErrorByCode, 53303, { value: TrackNameCharsInvalidError });
/**
 * @class TrackNameIsDuplicatedError
 * @classdesc Raised whenever a Participant is currently publishing a Track with the same name.
 * @extends TwilioError
 * @property {number} code - 53304
 * @property {string} message - 'Track name is duplicated'
 */
var TrackNameIsDuplicatedError = /** @class */ (function (_super) {
    __extends(TrackNameIsDuplicatedError, _super);
    function TrackNameIsDuplicatedError() {
        var _this = _super.call(this, 53304, 'Track name is duplicated') || this;
        Object.setPrototypeOf(_this, TrackNameIsDuplicatedError.prototype);
        return _this;
    }
    return TrackNameIsDuplicatedError;
}(TwilioError));
exports.TrackNameIsDuplicatedError = TrackNameIsDuplicatedError;
Object.defineProperty(TwilioErrorByCode, 53304, { value: TrackNameIsDuplicatedError });
/**
 * @class TrackServerTrackCapacityReachedError
 * @classdesc The server does not have enough resources available to create a new Track.
 * @extends TwilioError
 * @property {number} code - 53305
 * @property {string} message - 'The server has reached capacity and cannot fulfill this request'
 */
var TrackServerTrackCapacityReachedError = /** @class */ (function (_super) {
    __extends(TrackServerTrackCapacityReachedError, _super);
    function TrackServerTrackCapacityReachedError() {
        var _this = _super.call(this, 53305, 'The server has reached capacity and cannot fulfill this request') || this;
        Object.setPrototypeOf(_this, TrackServerTrackCapacityReachedError.prototype);
        return _this;
    }
    return TrackServerTrackCapacityReachedError;
}(TwilioError));
exports.TrackServerTrackCapacityReachedError = TrackServerTrackCapacityReachedError;
Object.defineProperty(TwilioErrorByCode, 53305, { value: TrackServerTrackCapacityReachedError });
/**
 * @class MediaClientLocalDescFailedError
 * @classdesc Raised whenever a Client is unable to create or apply a local media description.
 * @extends TwilioError
 * @property {number} code - 53400
 * @property {string} message - 'Client is unable to create or apply a local media description'
 */
var MediaClientLocalDescFailedError = /** @class */ (function (_super) {
    __extends(MediaClientLocalDescFailedError, _super);
    function MediaClientLocalDescFailedError() {
        var _this = _super.call(this, 53400, 'Client is unable to create or apply a local media description') || this;
        Object.setPrototypeOf(_this, MediaClientLocalDescFailedError.prototype);
        return _this;
    }
    return MediaClientLocalDescFailedError;
}(TwilioError));
exports.MediaClientLocalDescFailedError = MediaClientLocalDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53400, { value: MediaClientLocalDescFailedError });
/**
 * @class MediaServerLocalDescFailedError
 * @classdesc Raised whenever the Server is unable to create or apply a local media description.
 * @extends TwilioError
 * @property {number} code - 53401
 * @property {string} message - 'Server is unable to create or apply a local media description'
 */
var MediaServerLocalDescFailedError = /** @class */ (function (_super) {
    __extends(MediaServerLocalDescFailedError, _super);
    function MediaServerLocalDescFailedError() {
        var _this = _super.call(this, 53401, 'Server is unable to create or apply a local media description') || this;
        Object.setPrototypeOf(_this, MediaServerLocalDescFailedError.prototype);
        return _this;
    }
    return MediaServerLocalDescFailedError;
}(TwilioError));
exports.MediaServerLocalDescFailedError = MediaServerLocalDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53401, { value: MediaServerLocalDescFailedError });
/**
 * @class MediaClientRemoteDescFailedError
 * @classdesc Raised whenever the Client receives a remote media description but is unable to apply it.
 * @extends TwilioError
 * @property {number} code - 53402
 * @property {string} message - 'Client is unable to apply a remote media description'
 */
var MediaClientRemoteDescFailedError = /** @class */ (function (_super) {
    __extends(MediaClientRemoteDescFailedError, _super);
    function MediaClientRemoteDescFailedError() {
        var _this = _super.call(this, 53402, 'Client is unable to apply a remote media description') || this;
        Object.setPrototypeOf(_this, MediaClientRemoteDescFailedError.prototype);
        return _this;
    }
    return MediaClientRemoteDescFailedError;
}(TwilioError));
exports.MediaClientRemoteDescFailedError = MediaClientRemoteDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53402, { value: MediaClientRemoteDescFailedError });
/**
 * @class MediaServerRemoteDescFailedError
 * @classdesc Raised whenever the Server receives a remote media description but is unable to apply it.
 * @extends TwilioError
 * @property {number} code - 53403
 * @property {string} message - 'Server is unable to apply a remote media description'
 */
var MediaServerRemoteDescFailedError = /** @class */ (function (_super) {
    __extends(MediaServerRemoteDescFailedError, _super);
    function MediaServerRemoteDescFailedError() {
        var _this = _super.call(this, 53403, 'Server is unable to apply a remote media description') || this;
        Object.setPrototypeOf(_this, MediaServerRemoteDescFailedError.prototype);
        return _this;
    }
    return MediaServerRemoteDescFailedError;
}(TwilioError));
exports.MediaServerRemoteDescFailedError = MediaServerRemoteDescFailedError;
Object.defineProperty(TwilioErrorByCode, 53403, { value: MediaServerRemoteDescFailedError });
/**
 * @class MediaNoSupportedCodecError
 * @classdesc Raised whenever the intersection of codecs supported by the Client and the Server (or, in peer-to-peer, the Client and another Participant) is empty.
 * @extends TwilioError
 * @property {number} code - 53404
 * @property {string} message - 'No supported codec'
 */
var MediaNoSupportedCodecError = /** @class */ (function (_super) {
    __extends(MediaNoSupportedCodecError, _super);
    function MediaNoSupportedCodecError() {
        var _this = _super.call(this, 53404, 'No supported codec') || this;
        Object.setPrototypeOf(_this, MediaNoSupportedCodecError.prototype);
        return _this;
    }
    return MediaNoSupportedCodecError;
}(TwilioError));
exports.MediaNoSupportedCodecError = MediaNoSupportedCodecError;
Object.defineProperty(TwilioErrorByCode, 53404, { value: MediaNoSupportedCodecError });
/**
 * @class MediaConnectionError
 * @classdesc Raised by the Client or Server whenever a media connection fails or raised by the Client whenever it detects that media has stopped flowing.
 * @extends TwilioError
 * @property {number} code - 53405
 * @property {string} message - 'Media connection failed or Media activity ceased'
 */
var MediaConnectionError = /** @class */ (function (_super) {
    __extends(MediaConnectionError, _super);
    function MediaConnectionError() {
        var _this = _super.call(this, 53405, 'Media connection failed or Media activity ceased') || this;
        Object.setPrototypeOf(_this, MediaConnectionError.prototype);
        return _this;
    }
    return MediaConnectionError;
}(TwilioError));
exports.MediaConnectionError = MediaConnectionError;
Object.defineProperty(TwilioErrorByCode, 53405, { value: MediaConnectionError });
/**
 * @class MediaDTLSTransportFailedError
 * @classdesc There was a problem while negotiating with the remote DTLS peer. Therefore the Participant will not be able to publish or subscribe to Tracks.
 * @extends TwilioError
 * @property {number} code - 53407
 * @property {string} message - 'Media connection failed due to DTLS handshake failure'
 */
var MediaDTLSTransportFailedError = /** @class */ (function (_super) {
    __extends(MediaDTLSTransportFailedError, _super);
    function MediaDTLSTransportFailedError() {
        var _this = _super.call(this, 53407, 'Media connection failed due to DTLS handshake failure') || this;
        Object.setPrototypeOf(_this, MediaDTLSTransportFailedError.prototype);
        return _this;
    }
    return MediaDTLSTransportFailedError;
}(TwilioError));
exports.MediaDTLSTransportFailedError = MediaDTLSTransportFailedError;
Object.defineProperty(TwilioErrorByCode, 53407, { value: MediaDTLSTransportFailedError });
/**
 * @class ConfigurationAcquireFailedError
 * @classdesc Raised whenever the Client is unable to acquire configuration information from the Server.
 * @extends TwilioError
 * @property {number} code - 53500
 * @property {string} message - 'Unable to acquire configuration'
 */
var ConfigurationAcquireFailedError = /** @class */ (function (_super) {
    __extends(ConfigurationAcquireFailedError, _super);
    function ConfigurationAcquireFailedError() {
        var _this = _super.call(this, 53500, 'Unable to acquire configuration') || this;
        Object.setPrototypeOf(_this, ConfigurationAcquireFailedError.prototype);
        return _this;
    }
    return ConfigurationAcquireFailedError;
}(TwilioError));
exports.ConfigurationAcquireFailedError = ConfigurationAcquireFailedError;
Object.defineProperty(TwilioErrorByCode, 53500, { value: ConfigurationAcquireFailedError });
/**
 * @class ConfigurationAcquireTurnFailedError
 * @classdesc Raised whenever the Server is unable to return TURN credentials to the Client
 * @extends TwilioError
 * @property {number} code - 53501
 * @property {string} message - 'Unable to acquire TURN credentials'
 */
var ConfigurationAcquireTurnFailedError = /** @class */ (function (_super) {
    __extends(ConfigurationAcquireTurnFailedError, _super);
    function ConfigurationAcquireTurnFailedError() {
        var _this = _super.call(this, 53501, 'Unable to acquire TURN credentials') || this;
        Object.setPrototypeOf(_this, ConfigurationAcquireTurnFailedError.prototype);
        return _this;
    }
    return ConfigurationAcquireTurnFailedError;
}(TwilioError));
exports.ConfigurationAcquireTurnFailedError = ConfigurationAcquireTurnFailedError;
Object.defineProperty(TwilioErrorByCode, 53501, { value: ConfigurationAcquireTurnFailedError });
//# sourceMappingURL=twilio-video-errors.js.map