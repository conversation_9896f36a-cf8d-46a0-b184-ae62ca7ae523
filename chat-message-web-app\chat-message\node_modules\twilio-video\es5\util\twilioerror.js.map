{"version": 3, "file": "twilioerror.js", "sourceRoot": "", "sources": ["../../lib/util/twilioerror.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb;;;GAGG;AACH;IAA0B,+BAAK;IAC7B;;;;;;OAMG;IACH,qBAAY,IAAI;QAAhB,iBAmBC;QAlBC,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACzC,oDAAS,IAAI,YAAE;QACf,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;QAEnD,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAI,EAAE,IAAI,CAAC,CAAC;QACtC,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;QAE3B,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,MAAM,EAAE;YAClC,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAS,IAAI;YACrD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;gBAChC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;gBAClB,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;QACL,CAAC,EAAE,KAAI,CAAC,CAAC;;IACX,CAAC;IAED;;;OAGG;IACH,8BAAQ,GAAR;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAK,IAAI,CAAC,OAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,OAAU,IAAI,CAAC,IAAI,SAAI,IAAI,CAAC,IAAI,GAAG,OAAS,CAAC;IAC/C,CAAC;IACH,kBAAC;AAAD,CAAC,AArCD,CAA0B,KAAK,GAqC9B;AAED,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC"}