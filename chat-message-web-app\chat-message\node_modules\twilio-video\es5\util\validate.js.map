{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../lib/util/validate.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEL,IAAA,gBAAgB,GAAK,OAAO,CAAC,IAAI,CAAC,iBAAlB,CAAmB;AACrC,IAAA,KAAmI,OAAO,CAAC,aAAa,CAAC,EAA3I,CAAC,gBAAA,EAAE,2BAA2B,iCAAA,EAAE,2BAA2B,iCAAA,EAAE,gBAAgB,sBAAA,EAAE,aAAa,mBAAA,EAAE,kBAAkB,wBAA2B,CAAC;AAEhK;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,gBAAgB;IAChD,IAAI,KAAK,GAAG,cAAc,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;IACzE,IAAI,CAAC,gBAAgB,IAAI,KAAK,EAAE;QAC9B,OAAO,KAAK,CAAC;KACd;IACD,KAAK,GAAG,cAAc,CAAC,gBAAgB,CAAC,KAAK,EAAE,gCAAgC,EAAE;QAC/E,EAAE,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,EAAE;QACtF,EAAE,IAAI,EAAE,yBAAyB,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;QACzE,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,QAAQ,EAAE;QAClD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;QACrC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE;QACzD,EAAE,IAAI,EAAE,6BAA6B,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,EAAE;QAC3F,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE;KAC1E,CAAC,CAAC;IAEH,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC;KACd;IAED,IAAI,gBAAgB,CAAC,KAAK,EAAE;QAE1B,wDAAwD;QACxD,wCAAwC;QACxC,IAAI,WAAW,IAAI,gBAAgB,CAAC,KAAK,IAAI,6BAA6B,IAAI,gBAAgB,CAAC,KAAK,EAAE;YACpG,OAAO,IAAI,SAAS,CAAC,iIAAiI,CAAC,CAAC;SACzJ;QAED,0DAA0D;QAC1D,wCAAwC;QACxC,IAAI,kBAAkB,IAAI,gBAAgB,CAAC,KAAK,IAAI,wBAAwB,IAAI,gBAAgB,CAAC,KAAK,EAAE;YACtG,OAAO,IAAI,SAAS,CAAC,mIAAmI,CAAC,CAAC;SAC3J;QAED,OAAO,wBAAwB,CAAC,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;KAC1E;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;GAKG;AACH,SAAS,kBAAkB,CAAC,KAAK,EAAE,OAAO;IACxC,IAAI,CAAC,CAAC,KAAK,YAAY,OAAO,CAAC,eAAe;WACzC,KAAK,YAAY,OAAO,CAAC,cAAc;WACvC,KAAK,YAAY,OAAO,CAAC,eAAe;WACxC,KAAK,YAAY,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC/C,sBAAsB;QACtB,MAAM,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,uEAAuE,CAAC,CAAC;KACxG;AACH,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,UAAe;IAAf,2BAAA,EAAA,eAAe;IACnD,gFAAgF;IAChF,+EAA+E;IAC/E,oBAAoB;IACpB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,OAAO,IAAI,CAAC;KACb;IACD,6EAA6E;IAC7E,iDAAiD;IACjD,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;QAChD,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACvC;IACD,+EAA+E;IAC/E,4EAA4E;IAC5E,OAAO,UAAU,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,EAAsB;YAApB,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,MAAM,YAAA;QACnD,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QACD,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,IAAI,EAAE;YACjC,OAAO,CAAC,CAAC,YAAY,CAAI,IAAI,SAAI,IAAM,EAAE,IAAI,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YACrC,OAAO,CAAC,CAAC,YAAY,CAAI,IAAI,SAAI,IAAM,EAAE,IAAI,CAAC,CAAC;SAChD;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACpD,OAAO,CAAC,CAAC,aAAa,CAAI,IAAI,SAAI,IAAM,EAAE,MAAM,CAAC,CAAC;SACnD;QACD,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,IAAI,CAAC,CAAC;AACX,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,gBAAgB;IAChD,IAAM,IAAI,GAAG,iDAAiD,CAAC;IAC/D,IAAI,KAAK,GAAG,cAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACnD,OAAO,gBAAgB,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,IAAI;QACjF,OAAO,KAAK,IAAI,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAK,IAAI,SAAI,IAAM,EAAE;YACxE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;YAClC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;SAClC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACnB,CAAC;AAED,OAAO,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;AAC5D,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAChD,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC"}