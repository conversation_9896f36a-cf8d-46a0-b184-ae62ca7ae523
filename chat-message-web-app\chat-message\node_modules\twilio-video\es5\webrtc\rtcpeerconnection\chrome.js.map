{"version": 3, "file": "chrome.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/chrome.js"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,2BAA2B,GAAG,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC/E,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACvC,IAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC9C,IAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC9C,IAAA,KAAiF,OAAO,CAAC,aAAa,CAAC,EAArG,YAAY,kBAAA,EAAE,0BAA0B,gCAAA,EAAE,gCAAgC,sCAA2B,CAAC;AACxG,IAAA,KAAmF,OAAO,CAAC,SAAS,CAAC,EAAnG,eAAe,qBAAA,EAAE,cAAc,oBAAA,EAAE,WAAW,iBAAA,EAAE,aAAa,mBAAA,EAAE,eAAe,qBAAuB,CAAC;AAE5G,IAAM,aAAa,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC;AAEnD,8EAA8E;AAC9E,6EAA6E;AAC7E,eAAe;AACf,EAAE;AACF,oDAAoD;AACpD,mEAAmE;AACnE,EAAE;AACF,+DAA+D;AAC/D,EAAE;AACF,+BAA+B;AAC/B,EAAE;AACF;IAAsC,2CAAW;IAC/C,iCAAY,aAAkB,EAAE,WAAW;QAA/B,8BAAA,EAAA,kBAAkB;QAA9B,YACE,iBAAO,SAwER;QAtEC,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,kBAAkB;YACrE,CAAC,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC,kBAAkB,EAAE;YACrD,CAAC,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAEvB,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,CAAC;QACpC,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAC7C,IAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAE5E,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,WAAW,EAAE;aACzB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,UAAU,EAAE;gBACV,KAAK,EAAE,SAAS;aACjB;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,KAAK,EAAE;aACnB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,aAAa,EAAE,UAAA,KAAK;YAClD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/B,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YAC9D,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC9C,KAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,KAAI,CAAC,kBAAkB,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;gBACzD,KAAI,CAAC,aAAa,OAAlB,KAAI,2BAAkB,IAAI,IAAE;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,OAAO,GAAG;YACvB,2EAA2E;YAC3E,iDAAiD;QACnD,CAAC,CAAC;QAEF,IAAI,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU,EAAE;YACjD,cAAc,CAAC,SAAS,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;SAC7C;QACD,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IACrE,CAAC;IAED,sBAAI,qDAAgB;aAApB;YACE,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QACnG,CAAC;;;OAAA;IAED,sBAAI,sDAAiB;aAArB;YACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QACtG,CAAC;;;OAAA;IAED,sBAAI,mDAAc;aAAlB;YACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,OAAO,kBAAkB,CAAC;aAC3B;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACnC,OAAO,mBAAmB,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAC7C,CAAC;;;OAAA;IAED,kEAAkE;IAClE,yEAAyE;IACzE,yEAAyE;IACzE,gCAAgC;IAChC,0CAAQ,GAAR,UAAS,KAAK;;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QACrB,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,OAAO,CAAA,KAAA,IAAI,CAAC,eAAe,CAAA,CAAC,QAAQ,0BAAC,KAAK,UAAK,IAAI,IAAE;SACtD;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,kCAAgC,KAAK,CAAC,EAAE,oBACpD,KAAK,CAAC,IAAI,mCAAgC,CAAC,CAAC;SACjD;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,mCAAiC,KAAK,CAAC,EAAE,oBACrD,KAAK,CAAC,IAAI,wCAAqC,CAAC,CAAC;SACtD;QACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,MAAM,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,oEAAoE;IACpE,uEAAuE;IACvE,4EAA4E;IAC5E,gCAAgC;IAChC,6CAAW,GAAX,UAAY,MAAM;QAChB,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAChF;QACD,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,IAAI;gBACF,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aACjD;YAAC,OAAO,CAAC,EAAE;gBACV,uEAAuE;gBACvE,sEAAsE;gBACtE,2EAA2E;gBAC3E,uBAAuB;gBACvB,+DAA+D;aAChE;SACF;aAAM;YACG,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;YACzB,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YACD,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACnD;SACF;IACH,CAAC;IAED,4CAAU,GAAV;QACE,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,UAAU,EAAE;YACvD,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;SAC1C;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,iDAAe,GAAf,UAAgB,SAAS;QAAzB,iBAgBC;QAhB0B,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAChC,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC/C,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;gBACnD,OAAA,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC;YAA/C,CAA+C,CAAC,CAAC;SACpD;aAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,8BAAC,OAAO,UAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,8EAA8E;IAC9E,wEAAwE;IACxE,2BAA2B;IAC3B,uCAAK,GAAL;QACE,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IAED,6EAA6E;IAC7E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,0EAA0E;IAC1E,oCAAoC;IACpC,8CAAY,GAAZ;QAAA,iBAyCC;QAzCY,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAClB,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC;gBACjF,0EAA0E;gBAC1E,yEAAyE;gBACzE,wDAAwD;gBACxD,KAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;gBAClC,OAAO,KAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;gBACZ,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAEhC,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,IAAI,2BAA2B,CAAC;oBACrC,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC7E,CAAC,CAAC;YACL,CAAC,EAAE,UAAA,KAAK;gBACN,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM;gBACvD,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,IAAI,2BAA2B,CAAC;oBACrC,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC7E,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,8BAAC,OAAO,UAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,6CAAW,GAAX;QAAA,iBA2CC;QA3CW,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACX,IAAA,KAAA,OAAqB,IAAI,IAAA,EAAxB,IAAI,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAQ,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QAEnC,IAAI,WAAW,EAAE,EAAE;YACjB,4EAA4E;YAC5E,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;gBACnC,IAAI;oBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa;wBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;wBACzD,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;iBAClC;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;YAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;gBACnC,IAAI;oBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa;wBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;wBACzD,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;iBAClC;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;SACF;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK;YAClE,0FAA0F;YAC1F,6DAA6D;YAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,IAAI,2BAA2B,CAAC;gBACrC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aAC5E,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;YACpC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,mDAAiB,GAAjB,UAAkB,KAAK,EAAE,eAAe;QACtC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACvD,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACnF,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,qDAAmB,GAAnB;QAAoB,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnB,IAAA,KAAA,OAA4B,IAAI,IAAA,EAA/B,WAAW,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAQ,CAAC;QAEvC,uFAAuF;QACvF,+DAA+D;QAC/D,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7D,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;SACvC;QAED,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;YACpC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,sDAAoB,GAApB;QAAqB,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACpB,IAAA,KAAA,OAA4B,IAAI,IAAA,EAA/B,WAAW,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAQ,CAAC;QAEvC,wFAAwF;QACxF,wEAAwE;QACxE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAEtC,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;YACpC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IACH,8BAAC;AAAD,CAAC,AA7TD,CAAsC,WAAW,GA6ThD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,uBAAuB,CAAC,SAAS,EACjC,iBAAiB,CAAC,CAAC;AAErB,2EAA2E;AAC3E,yFAAyF;AACzF,+EAA+E;AAC/E,yEAAyE;AACzE,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,WAAW;IACxD,SAAS,oBAAoB,CAAC,KAAK;QACjC,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C;aAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC5C;IACH,CAAC;IAED,SAAS,sBAAsB;QAC7B,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC1C;aAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC3C;IACH,CAAC;IAED,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC;IACzG,IAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC;IAC1G,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC;IAC3E,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IACnF,IAAI,OAAO,CAAC;IAEZ,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjE,OAAO,GAAG,eAAe,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KACxD;SAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QACvC,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;YACrG,qDAAqD;YACrD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAc,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,yBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SAC9H;QAED,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,2EAA2E;QAC3E,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,oBAAoB,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7E,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SAC7C;QACD,IAAM,sBAAsB,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1C,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE5B,iEAAiE;QACjE,IAAI,cAAc,CAAC,cAAc,KAAK,sBAAsB,EAAE;YAC5D,OAAO,CAAC,IAAI,CAAC,cAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAA/D,CAA+D,CAAC,CAAC;SACrF;KAEF;SAAM,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;QAC1C,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,EAAE;YACvD,qDAAqD;YACrD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAmB,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,yBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACtI;aAAM;YACL,2BAA2B;YAC3B,sBAAsB,EAAE,CAAC;YAEzB,8EAA8E;YAC9E,gFAAgF;YAChF,iFAAiF;YACjF,kDAAkD;YAClD,cAAc,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACjF,cAAc,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAE9E,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,cAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAA/D,CAA+D,CAAC,CAAC;SACrF;KACF;IAED,OAAO,OAAO,IAAI,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7F,CAAC;AAED,SAAS,eAAe,CAAC,cAAc,EAAE,MAAM;IAC7C,iCAAiC;IACjC,IAAM,iBAAiB,GAAG,cAAc,CAAC,kBAAkB,CAAC;IAC5D,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;QAChF,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACzC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,IAAI,CAAC;QACN,0EAA0E;QAC1E,yEAAyE;QACzE,wDAAwD;QACxD,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC9C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,IAAI;IACvD,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAC,EAAiB;YAAf,gBAAa,EAAb,QAAQ,mBAAG,EAAE,KAAA;QACrD,IAAA,KAAe,QAAQ,MAAb,EAAV,KAAK,mBAAG,EAAE,KAAA,CAAc;QAChC,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,MAAM,CAAC,WAAW;IACzB,IAAI,WAAW,YAAY,2BAA2B,EAAE;QACtD,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,OAAO,WAAW,CAAC,YAAY,CAAC;SACjC;KACF;IACD,OAAO,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC;AAChD,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,0BAA0B;IACjC,OAAO,mBAAmB,IAAI,cAAc,CAAC,SAAS;WACjD,CAAC,CAAC,mBAAmB,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,eAAe;IAC1C,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;IACrD,IAAI,0BAA0B,EAAE,IAAI,mBAAmB,IAAI,eAAe,EAAE;QAC1E,eAAe,CAAC,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC;KACvE;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,WAAW;IAClC,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,EAAE;QACnD,KAAK,EAAE,WAAW,CAAC,cAAc,KAAK,KAAK;YACzC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,WAAW,CAAC,cAAc;KAC/B,CAAC,CAAC;IACH,IAAI,0BAA0B,EAAE,EAAE;QAChC,4EAA4E;QAC5E,EAAE;QACF,iEAAiE;QACjE,EAAE;QACF,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,EAAE;YACtD,KAAK,EAAE,WAAW,CAAC,iBAAiB,KAAK,KAAK;gBAC5C,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,WAAW,CAAC,iBAAiB;SAClC,CAAC,CAAC;KACJ;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,qBAAqB,CAAC,SAAS,EAAE,aAAa,EAAE,GAAG;IAC1D,OAAO,SAAS,KAAK,SAAS;QAC5B,CAAC,CAAC,gCAAgC,CAAC,aAAa,EAAE,GAAG,CAAC;QACtD,CAAC,CAAC,0BAA0B,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC"}