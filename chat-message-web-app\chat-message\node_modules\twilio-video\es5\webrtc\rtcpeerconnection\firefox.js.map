{"version": 3, "file": "firefox.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/firefox.js"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,4BAA4B,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;AACzE,IAAkC,mBAAmB,GAAK,OAAO,CAAC,aAAa,CAAC,iCAA3B,CAA4B;AACnF,IAAA,KAAsE,OAAO,CAAC,SAAS,CAAC,EAAtF,eAAe,qBAAA,EAAE,cAAc,oBAAA,EAAE,aAAa,mBAAA,EAAE,eAAe,qBAAuB,CAAC;AAE/F,+EAA+E;AAC/E,8EAA8E;AAC9E,eAAe;AACf,EAAE;AACF,4EAA4E;AAC5E,mEAAmE;AACnE,qBAAqB;AACrB,EAAE;AACF,6EAA6E;AAC7E,EAAE;AACF,4EAA4E;AAC5E,OAAO;AACP,EAAE;AACF,yDAAyD;AACzD,EAAE;AACF,sEAAsE;AACtE,qEAAqE;AACrE,EAAE;AACF,2DAA2D;AAC3D,EAAE;AACF;IAAuC,4CAAW;IAChD,kCAAY,aAAa;QAAzB,YACE,iBAAO,SA2DR;QAzDC,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAE7C,sBAAsB;QACtB,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,4BAA4B,EAAE;gBAC5B,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;aACjB;YAED,yEAAyE;YACzE,wEAAwE;YACxE,0DAA0D;YAC1D,kEAAkE;YAClE,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC;oBACrB,GAAG,EAAE,EAAE;oBACP,IAAI,EAAE,EAAE;iBACT,CAAC;aACH;SACF,CAAC,CAAC;QAEH,IAAI,sBAAsB,CAAC;QAE3B,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YAC9D,IAAI,CAAC,KAAI,CAAC,YAAY,IAAI,KAAI,CAAC,cAAc,KAAK,sBAAsB,EAAE;gBACxE,sBAAsB,GAAG,KAAI,CAAC,cAAc,CAAC;gBAE7C,gEAAgE;gBAChE,iDAAiD;gBACjD,qEAAqE;gBACrE,yDAAyD;gBACzD,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,aAAa,OAAlB,KAAI,2BAAkB,IAAI,KAA1B,CAA2B,CAAC,CAAC;iBAC/C;qBAAM;oBACL,KAAI,CAAC,aAAa,OAAlB,KAAI,2BAAkB,IAAI,IAAE;iBAC7B;aACF;QACH,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IACrE,CAAC;IAED,sBAAI,uDAAiB;aAArB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC9E,CAAC;;;OAAA;IAED,sBAAI,sDAAgB;aAApB;YACE,OAAO,wCAAwC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC5H,CAAC;;;OAAA;IAED,sBAAI,oDAAc;aAAlB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QACzE,CAAC;;;OAAA;IAED,+CAAY,GAAZ;QAAA,iBAWC;QAXY,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QAClB,IAAI,OAAO,CAAC;QAEZ,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM;YACvD,+BAA+B,CAAC,KAAI,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,wCAAwC,CAAC,MAAM,EAAE,KAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU;YAClC,CAAC,CAAC,aAAa,8BAAC,OAAO,UAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,0EAA0E;IAC1E,+EAA+E;IAC/E,6EAA6E;IAC7E,gEAAgE;IAChE,2EAA2E;IAC3E,yEAAyE;IACzE,oCAAoC;IACpC,8CAAW,GAAX;QAAA,iBAuBC;QAvBW,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACX,IAAA,KAAA,OAAqB,IAAI,IAAA,EAAxB,IAAI,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAQ,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,cAAc,KAAK,kBAAkB;YAC5C,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,KAAK,kBAAkB,CAAC;YACzD,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAzB,CAAyB,CAAC,CAAC;SAClE;aAAM;YACL,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACrD;QAED,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,UAAA,KAAK;YAC1B,OAAO,IAAI,4BAA4B,CAAC;gBACtC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,mBAAmB,CAAC,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aACzD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;YACpC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,oEAAoE;IACpE,8EAA8E;IAC9E,4EAA4E;IAC5E,yCAAyC;IACzC,sDAAmB,GAAnB;;QAAoB,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnB,IAAA,KAAA,OAAyB,IAAI,CAAA,EAA5B,WAAW,QAAA,EAAK,IAAI,cAAQ,CAAC;QACpC,IAAI,OAAO,CAAC;QAEZ,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,cAAc,KAAK,kBAAkB,EAAE;YAC9F,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAC;SAC1F;QAED,IAAI,OAAO,EAAE;YACX,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;gBACpB,CAAC,CAAC,aAAa,8BAAC,OAAO,UAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;SACb;QAED,OAAO,CAAA,KAAA,IAAI,CAAC,eAAe,CAAA,CAAC,mBAAmB,oCAAI,IAAI,IAAE;IAC3D,CAAC;IAED,+EAA+E;IAC/E,0EAA0E;IAC1E,+FAA+F;IAC/F,6EAA6E;IAC7E,6EAA6E;IAC7E,mBAAmB;IACnB,EAAE;IACF,8EAA8E;IAC9E,yEAAyE;IACzE,4EAA4E;IAC5E,sBAAsB;IACtB,uDAAoB,GAApB;QAAA,iBAsBC;QAtBoB,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACpB,IAAA,KAAA,OAAyB,IAAI,CAAA,EAA5B,WAAW,QAAA,EAAK,IAAI,cAAQ,CAAC;QAEpC,IAAI,OAAO,CAAC;QAEZ,IAAI,WAAW,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC9D,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC,CAAC;aAC5F;iBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;gBACvC,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,cAAM,OAAA,KAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAtD,CAAsD,CAAC,CAAC;aAC/F;SACF;QAED,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;SAClE;QAED,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,cAAM,OAAA,+BAA+B,CAAC,KAAI,EAAE,WAAW,EAAE,IAAI,CAAC,EAAxD,CAAwD,CAAC,CAAC;QAEvF,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC,aAAa,8BAAC,OAAO,UAAK,IAAI,IAChC,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,+EAA+E;IAC/E,0EAA0E;IAC1E,6EAA6E;IAC7E,wCAAK,GAAL;QACE,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IACH,+BAAC;AAAD,CAAC,AAzLD,CAAuC,WAAW,GAyLjD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,wBAAwB,CAAC,SAAS,EAClC,iBAAiB,CAAC,CAAC;AAErB,SAAS,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,cAAc;IACrD,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IACnF,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;IACnC,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,IAAI,4BAA4B,CAAC;QAC1F,IAAI,EAAE,UAAU;KACjB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;QAClC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,UAAA,KAAK;QACN,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,+BAA+B,CAAC,cAAc,EAAE,WAAW,EAAE,MAAM;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,QAAQ;IACR,IAAI,cAAc,CAAC,4BAA4B,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QAC/E,OAAO;KACR;IAED,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IAED,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,cAAc,CAAC,4BAA4B,GAAG,MAAM,CAAC,CAAC,CAAC;QACrD,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,QAAQ;KAClB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AACzB,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAS,wCAAwC,CAAC,WAAW,EAAE,QAAQ;IACrE,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAAE;QAC5D,OAAO,IAAI,4BAA4B,CAAC;YACtC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,GAAG,QAAQ,CAAC;SACvE,CAAC,CAAC;KACJ;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,wBAAwB,CAAC"}