{"version": 3, "file": "safari.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/rtcpeerconnection/safari.js"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,IAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAM,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AACjC,IAAA,KAAiF,OAAO,CAAC,aAAa,CAAC,EAArG,YAAY,kBAAA,EAAE,0BAA0B,gCAAA,EAAE,gCAAgC,sCAA2B,CAAC;AACxG,IAAA,KAAuD,OAAO,CAAC,SAAS,CAAC,EAAvE,eAAe,qBAAA,EAAE,cAAc,oBAAA,EAAE,eAAe,qBAAuB,CAAC;AAEhF,IAAM,aAAa,GAAG,YAAY,EAAE,KAAK,SAAS,CAAC;AAEnD,IAAM,qBAAqB,GAAG,aAAa;IACzC,CAAC,CAAC,gCAAgC;IAClC,CAAC,CAAC,0BAA0B,CAAC;AAE/B;IAAsC,2CAAW;IAC/C,iCAAY,aAAa;QAAzB,YACE,iBAAO,SAqFR;QAnFC,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,CAAC;QACpC,cAAc,CAAC,KAAI,EAAE,0BAA0B,CAAC,CAAC;QACjD,cAAc,CAAC,KAAI,EAAE,sBAAsB,CAAC,CAAC;QAC7C,cAAc,CAAC,KAAI,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAE5D,MAAM,CAAC,gBAAgB,CAAC,KAAI,EAAE;YAC5B,qBAAqB,EAAE;gBACrB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,cAAc;aACtB;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,mBAAmB,EAAE;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI,KAAK,EAAE;aACnB;YACD,cAAc,EAAE;gBACd,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,QAAQ,EAAE,IAAI;aACf;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,aAAa,EAAE,UAAA,KAAK;YAClD,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/B,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,0BAA0B,EAAE;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YAClE,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,OAAO;aACR;YACD,KAAI,CAAC,aAAa,OAAlB,KAAI,2BAAkB,IAAI,IAAE;QAC9B,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YAC9D,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,OAAO;aACR;YACD,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC9C,KAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,KAAI,CAAC,kBAAkB,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;gBACzD,KAAI,CAAC,aAAa,OAAlB,KAAI,2BAAkB,IAAI,IAAE;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,sGAAsG;QACtG,iFAAiF;QACjF,yFAAyF;QACzF,2EAA2E;QAC3E,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAA,KAAK;YAC5C,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAI,EAAE,cAAc,CAAC,CAAC;;IAErE,CAAC;IAED,sBAAI,qDAAgB;aAApB;YACE,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAC1E,CAAC;;;OAAA;IAED,sBAAI,uDAAkB;aAAtB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QAC7E,CAAC;;;OAAA;IAED,sBAAI,sDAAiB;aAArB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC9E,CAAC;;;OAAA;IAED,sBAAI,sDAAiB;aAArB;YACE,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC5E,CAAC;;;OAAA;IAED,sBAAI,mDAAc;aAAlB;YACE,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,OAAO,QAAQ,CAAC;aACjB;iBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAClC,OAAO,kBAAkB,CAAC;aAC3B;iBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACnC,OAAO,mBAAmB,CAAC;aAC5B;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAC7C,CAAC;;;OAAA;IAED,iDAAe,GAAf,UAAgB,SAAS;QAAzB,iBAKC;QAJC,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAmB,EAAE;YAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,EAA/C,CAA+C,CAAC,CAAC;SAC1G;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAED,6CAAW,GAAX,UAAY,OAAO;QAAnB,iBAqCC;QApCC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAErC,4EAA4E;QAC5E,mCAAmC;QACnC,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;YAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;YACnC,IAAI;gBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa;oBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;oBACzD,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aAClC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;YAC5H,OAAO,OAAO,CAAC,mBAAmB,CAAC;YACnC,IAAI;gBACF,IAAI,CAAC,iBAAiB,GAAG,aAAa;oBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;oBACzD,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aAClC;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK;YACzD,+EAA+E;YAC/E,wEAAwE;YACxE,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,IAAI,qBAAqB,CAAC;gBAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,KAAK,CAAC,GAAG,CAAC;aAC3D,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8CAAY,GAAZ,UAAa,OAAO;QAApB,iBAgCC;QA/BC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC;gBAC9E,KAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;gBAClC,OAAO,KAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;gBACZ,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAEhC,2FAA2F;gBAC3F,6DAA6D;gBAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAEtC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;oBAC/C,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;iBAC5D,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACd,CAAC,EAAE,UAAA,KAAK;gBACN,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,MAAM,KAAK,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;YAC3D,2FAA2F;YAC3F,6DAA6D;YAC7D,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAEtC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;gBAC/C,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,GAAG,EAAE,qBAAqB,CAAC,KAAI,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;aAC5D,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mDAAiB,GAAjB,UAAkB,KAAK,EAAE,eAAe;QACtC,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACnF,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,6CAAW,GAAX,UAAY,MAAM;QAChB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,qDAAmB,GAAnB,UAAoB,WAAW;QAC7B,uFAAuF;QACvF,+DAA+D;QAC/D,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC7D,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;SACvC;QACD,OAAO,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACjD,CAAC;IAED,sDAAoB,GAApB,UAAqB,WAAW;QAC9B,wFAAwF;QACxF,wEAAwE;QACxE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,uCAAK,GAAL;QAAA,iBAUC;QATC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,UAAU,CAAC;YACT,KAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;YAC1D,KAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IACH,8BAAC;AAAD,CAAC,AA3OD,CAAsC,WAAW,GA2OhD;AAED,eAAe,CACb,iBAAiB,CAAC,SAAS,EAC3B,uBAAuB,CAAC,SAAS,EACjC,iBAAiB,CAAC,CAAC;AAErB,SAAS,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,WAAW;IACxD,SAAS,oBAAoB,CAAC,KAAK;QACjC,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C;aAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC5C;IACH,CAAC;IAED,SAAS,sBAAsB;QAC7B,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC1C;aAAM;YACL,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC3C;IACH,CAAC;IAED,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC;IACzG,IAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC;IAC1G,IAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC;IAC3E,IAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,sBAAsB,CAAC;IAEnF,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjE,OAAO,eAAe,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KACrD;SAAM,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;QACvC,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,IAAI,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE;YACrG,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAc,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,kCACrD,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,oBAAoB,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7E,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SAC7C;QACD,IAAM,sBAAsB,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAElC,iEAAiE;QACjE,IAAI,cAAc,CAAC,cAAc,KAAK,sBAAsB,EAAE;YAC5D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,cAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAA/D,CAA+D,CAAC,CAAC;SACtG;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;SAAM,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;QAC1C,IAAI,cAAc,CAAC,cAAc,KAAK,iBAAiB,EAAE;YACvD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gCAC5B,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,yBAAmB,cAAc,CAAC,cAAgB,CAAC,CAAC,CAAC;SACpF;QACD,sBAAsB,EAAE,CAAC;QAEzB,8EAA8E;QAC9E,+EAA+E;QAC/E,+EAA+E;QAC/E,oDAAoD;QACpD,cAAc,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACjF,cAAc,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9E,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,cAAM,OAAA,cAAc,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAA/D,CAA+D,CAAC,CAAC;KACtG;IAED,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,eAAe,CAAC,cAAc,EAAE,MAAM;IAC7C,IAAM,iBAAiB,GAAG,cAAc,CAAC,kBAAkB,CAAC;IAC5D,OAAO,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;QAChF,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACzC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,EAA3C,CAA2C,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;GAMG;AACH,SAAS,2BAA2B,CAAC,cAAc,EAAE,IAAI;IACvD,OAAO,CAAC,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAC,EAAiB;YAAf,gBAAa,EAAb,QAAQ,mBAAG,EAAE,KAAA;QACrD,IAAA,KAAe,QAAQ,MAAb,EAAV,KAAK,mBAAG,EAAE,KAAA,CAAc;QAChC,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,WAAW;IAClC,OAAO,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE;QAC1C,iBAAiB,EAAE;YACjB,KAAK,EAAE,WAAW,CAAC,iBAAiB,KAAK,KAAK;gBAC5C,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,WAAW,CAAC,iBAAiB;SAClC;QACD,cAAc,EAAE;YACd,KAAK,EAAE,WAAW,CAAC,cAAc,KAAK,KAAK;gBACzC,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,WAAW,CAAC,cAAc;SAC/B;KACF,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC"}