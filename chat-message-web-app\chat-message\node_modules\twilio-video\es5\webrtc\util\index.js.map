{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/util/index.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;AAEb;;;GAGG;AACH,SAAS,KAAK;IACZ,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,QAAQ,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QAC7C,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;IAC3B,CAAC,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU;IACzD,IAAI,UAAU,IAAI,OAAO,EAAE;QACzB,gCAAgC;QAChC,OAAO;KACR;SAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QACzC,6DAA6D;QAC7D,OAAO;KACR;IAGD,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI;QACF,IAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACrE,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;KACzC;IAAC,OAAO,KAAK,EAAE;QACd,gCAAgC;KACjC;IAED,0EAA0E;IAC1E,yFAAyF;IACzF,sEAAsE;IACtE,mFAAmF;IACnF,8FAA8F;IAC9F,iBAAiB;IACjB,IAAI,UAAU,EAAE;QACd,OAAO;KACR;IAED,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,uEAAuE;QACvE,mDAAmD;KACpD;IAED,IAAI,IAAI,KAAK,UAAU,EAAE;QACvB,6BAA6B;QAC7B,OAAO;KACR;IAED,2BAA2B;IAC3B,OAAO,CAAC,UAAU,CAAC,GAAG;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;IACjE,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM;IAC9C,KAAK,IAAM,UAAU,IAAI,MAAM,EAAE;QAC/B,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;KACrD;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAAC,KAAK,EAAE,KAAK;IAC9B,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAExE,IAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;IAE7B,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;QAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACtB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;GAKG;AACH,SAAS,OAAO,CAAC,IAAI,EAAE,KAAK;IAC1B,IAAM,SAAS,GAAG,IAAI,YAAY,GAAG,IAAI,IAAI,YAAY,GAAG;QAC1D,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QAC3B,CAAC,CAAC,IAAI,CAAC;IAET,OAAO,SAAS,CAAC,MAAM,CAAC,UAAC,SAAS,EAAE,IAAI,IAAK,OAAA,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAA7B,CAA6B,EAAE,EAAE,CAAC,CAAC;AAClF,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY;IACnB,OAAO,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,QAAQ;QAChF,CAAC,CAAC,SAAS,CAAC,SAAS;QACrB,CAAC,CAAC,IAAI,CAAC;AACX,CAAC;AAED;;;;GAIG;AACH,SAAS,YAAY,CAAC,SAAS;IAC7B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAClC,OAAO,QAAQ,CAAC;KACjB;IACD,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACnC,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC7C,OAAO,QAAQ,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,SAAS;IACpC,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,IAAM,MAAM,GAAG;QACb,MAAM,EAAE,cAAc;QACtB,OAAO,EAAE,eAAe;QACxB,MAAM,EAAE,SAAS;KAClB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAE3B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI,CAAC;KACb;IACD,IAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAI,MAAM,gBAAa,CAAC,CAAC;IAC5C,IAAA,KAAA,OAAc,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAA,EAArC,KAAK,QAAgC,CAAC;IAEjD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IACD,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC9C,OAAO;QACL,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC/C,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAAC,SAAS;IAC5B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;QACpC,SAAS,GAAG,YAAY,EAAE,CAAC;KAC5B;IACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,YAAY,EAAE,KAAK,QAAQ,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACvG,CAAC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI;IAClC,IAAI,eAAe,GAAG,IAAI,CAAC;IAC3B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,EAAE;QACzC,GAAG,EAAE;YACH,OAAO,eAAe,CAAC;QACzB,CAAC;QACD,GAAG,EAAE,UAAS,WAAW;YACvB,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;aACjD;YAED,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;gBACrC,eAAe,GAAG,WAAW,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;aAC9C;iBAAM;gBACL,eAAe,GAAG,IAAI,CAAC;aACxB;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS;IAClD,OAAO,SAAS;QACd,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC;QACpC,CAAC,CAAC,OAAO,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ;IACf,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,CAAC;QAC9D,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,IAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM;IAC9C,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAA,YAAY;QACrD,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY;IAC1D,IAAI,YAAY,IAAI,OAAO,EAAE;QAC3B,mCAAmC;QACnC,OAAO;KACR;SAAM,IAAI,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;QAC3C,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE;YAC3C,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CACrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EACrB;YAAC,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YAAK,OAAA,OAAO,CAAC,aAAa,OAArB,OAAO,2BAAkB,IAAI;QAA7B,CAA8B,CAC5C,CAAC;QAEF,OAAO;KACR;IAED,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE;QAC3C,UAAU,EAAE,IAAI;QAChB,GAAG,EAAE;YACH,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO;IACd,OAAO,OAAO,SAAS,KAAK,QAAQ;WAC/B,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ;WAC1C,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,KAAK,UAAU;WACzD,OAAO,iBAAiB,KAAK,UAAU,CAAC;AAC/C,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,IAAI;IACpC,IAAI,OAAO,YAAY,KAAK,WAAW;WAClC,OAAO,YAAY,CAAC,eAAe,KAAK,UAAU,EAAE;QACvD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAC5B,YAAY;aACT,eAAe,CAAC,IAAI,CAAC;aACrB,MAAM;aACN,GAAG,CAAC,UAAC,EAAY;gBAAV,QAAQ,cAAA;YAAO,OAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;QAApC,CAAoC,CAAC,CAC/D,CAAC,CAAC;KACJ;IACD,IAAI,OAAO,iBAAiB,KAAK,WAAW;WACvC,OAAO,iBAAiB,CAAC,SAAS,KAAK,WAAW;WAClD,OAAO,iBAAiB,CAAC,SAAS,CAAC,cAAc,KAAK,UAAU;WAChE,OAAO,iBAAiB,CAAC,SAAS,CAAC,KAAK,KAAK,UAAU;WACvD,OAAO,iBAAiB,CAAC,SAAS,CAAC,WAAW,KAAK,UAAU,EAAE;QAClE,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;KACnC;IACD,IAAM,EAAE,GAAG,IAAI,iBAAiB,EAAE,CAAC;IACnC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACxB,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAC,EAAO;YAAL,GAAG,SAAA;QACjC,EAAE,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;aAChD,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAnD,CAAmD,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE;QACD,EAAE,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,GAAG,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,qEAAqE;AACrE,IAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AAElC;;;;;GAKG;AACH,SAAS,gBAAgB,CAAC,KAAK,EAAE,IAAI;IACnC,IAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,MAAM,EAAE;QACV,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KACzD;IACD,OAAO,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;QAC/C,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,0BAA0B;IACjC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC1B,CAAC;AAED;;;;;GAKG;AAEH,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;AAC1C,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1B,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACpC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAClD,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;AAClC,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC;AACxC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACtC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;AAC1C,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC"}