{"version": 3, "file": "sdp.js", "sourceRoot": "", "sources": ["../../../lib/webrtc/util/sdp.js"], "names": [], "mappings": "AAAA,kDAAkD;AAElD,YAAY,CAAC;AAEP,IAAA,KAA4B,OAAO,CAAC,IAAI,CAAC,EAAvC,OAAO,aAAA,EAAE,YAAY,kBAAkB,CAAC;AAEhD,+EAA+E;AAC/E,oDAAoD;AACpD,IAAI,uBAAuB,GAAG,IAAI,CAAC;AAEnC;;;GAGG;AACH,SAAS,8BAA8B;IACrC,IAAI,OAAO,uBAAuB,KAAK,SAAS,EAAE;QAChD,OAAO,uBAAuB,CAAC;KAChC;IACD,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;QAC5C,uBAAuB,GAAG,KAAK,CAAC;QAChC,OAAO,uBAAuB,CAAC;KAChC;IACD,IAAI;QACF,kCAAkC;QAClC,IAAI,iBAAiB,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/C,uBAAuB,GAAG,KAAK,CAAC;KACjC;IAAC,OAAO,CAAC,EAAE;QACV,uBAAuB,GAAG,IAAI,CAAC;KAChC;IACD,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED,qEAAqE;AACrE,oDAAoD;AACpD,IAAI,eAAe,GAAG,IAAI,CAAC;AAE3B;;GAEG;AACH,SAAS,0BAA0B;IACjC,eAAe,GAAG,IAAI,CAAC;AACzB,CAAC;AAED;;;GAGG;AACH,SAAS,yBAAyB;IAChC,IAAI,CAAC,eAAe,EAAE;QACpB,IAAI,OAAO,iBAAiB,KAAK,WAAW;eACvC,gBAAgB,IAAI,iBAAiB,CAAC,SAAS,EAAE;YACpD,IAAM,EAAE,GAAG,IAAI,iBAAiB,EAAE,CAAC;YACnC,IAAI;gBACF,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3B,eAAe,GAAG,SAAS,CAAC;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,eAAe,GAAG,OAAO,CAAC;aAC3B;YACD,EAAE,CAAC,KAAK,EAAE,CAAC;SACZ;aAAM;YACL,eAAe,GAAG,OAAO,CAAC;SAC3B;KACF;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACH,SAAS,kBAAkB,CAAC,YAAY;IACtC,IAAI,CAAC,YAAY,IAAI,CAAC,8BAA8B,EAAE,EAAE;QACtD,OAAO,yBAAyB,EAAE,CAAC;KACpC;IACD,OAAO;QACL,QAAQ,EAAE,OAAO;QACjB,cAAc,EAAE,SAAS;KAC1B,CAAC,YAAY,CAAC,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,SAAS,kBAAkB;IACzB,OAAO,OAAO,iBAAiB,KAAK,WAAW;WAC1C,kBAAkB,IAAI,iBAAiB,CAAC,SAAS;QACpD,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,OAAO,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAS,YAAY,CAAC,YAAY;IAChC,OAAO;QACL,MAAM,EAAE,kBAAkB,CAAC,YAAY,CAAC;QACxC,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,kBAAkB,EAAE;KAC7B,CAAC,YAAY,EAAE,CAAC,IAAI,IAAI,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CAAC,OAAO,EAAE,KAAK;IAChC,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7D,OAAO,OAAO,CAAC,MAAM,CAAC,UAAC,OAAO,EAAE,IAAI;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9C,OAAO,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IACjD,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,OAAO,EAAE,GAAG;IAC/B,OAAO,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,GAAG;IAC3B,OAAO,WAAW,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;AAC9D,CAAC;AAED;;;;GAIG;AACH,SAAS,sBAAsB,CAAC,GAAG;IACjC,OAAO,WAAW,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CAAC,GAAG,EAAE,OAAO;IACjC,IAAM,OAAO,GAAG,mCAAiC,OAAO,QAAK,CAAC;IAC9D,OAAO,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;GAMG;AACH,SAAS,gBAAgB,CAAC,GAAG,EAAE,IAAW,EAAE,SAAgB;IAA7B,qBAAA,EAAA,WAAW;IAAE,0BAAA,EAAA,gBAAgB;IAC1D,OAAO,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,YAAY,IAAI,OAAA,OAAK,YAAc,EAAnB,CAAmB,CAAC,CAAC,MAAM,CAAC,UAAA,YAAY;QAC9F,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,OAAK,IAAM,EAAE,IAAI,CAAC,CAAC;QAClD,IAAM,gBAAgB,GAAG,IAAI,MAAM,CAAC,OAAK,SAAW,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,YAAY;IACxC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,GAAG,EAAE,OAAO;IACvC,IAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAE5C,IAAM,cAAc,GAAG,IAAI,MAAM,CAAC,oBAAkB,OAAO,QAAK,EAAE,IAAI,CAAC,CAAC;IACxE,IAAM,qBAAqB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,YAAY,IAAI,OAAA,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,EAAlC,CAAkC,CAAC,CAAC;IAEvG,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE,GAAG;IACpD,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAC,CAAC;AACjG,CAAC;AAED;;;;GAIG;AACH,SAAS,uBAAuB,CAAC,GAAG;IAClC,OAAO,kBAAkB,CAAC,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;AAClE,CAAC;AAED;;;;GAIG;AACH,SAAS,6BAA6B,CAAC,GAAG;IACxC,OAAO,kBAAkB,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,GAAG,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,qBAAqB,CAAC,kBAAkB,EAAE,eAAe,EAAE,GAAG;IACrE,IAAM,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACnD,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;IAErC,mDAAmD;IACnD,kBAAkB,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,OAAO;QACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACjC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpC,OAAO;SACR;QACD,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1D,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,QAAQ,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,CAAC;YAC1B,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,kBAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACzC,IAAM,OAAO,GAAG,aAAW,OAAO,WAAQ,CAAC;YAC3C,IAAM,WAAW,GAAG,YAAU,OAAO,QAAK,CAAC;YAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,wDAAwD;IACxD,IAAM,OAAO,GAAG,8BAA8B,CAAC;IAC/C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3D,OAAO,CAAC,OAAO,CAAC,UAAA,IAAI;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,IAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,OAAO;YAC9C,IAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAChD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;QACrC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,0BAA0B,CAAC,eAAe,EAAE,GAAG;IACtD,OAAO,qBAAqB,CAAC,uBAAuB,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,gCAAgC,CAAC,eAAe,EAAE,GAAG;IAC5D,OAAO,qBAAqB,CAAC,6BAA6B,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;AACpF,CAAC;AAED,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;AACpC,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,OAAO,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;AACxD,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;AACtC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAClD,OAAO,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AAChE,OAAO,CAAC,gCAAgC,GAAG,gCAAgC,CAAC"}