{"tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "source": {"includePattern": ".+\\.(j|t)s(doc)?$", "excludePattern": "(^|\\/|\\\\)_"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-babel"], "babel": {"extensions": ["ts"], "babelrc": false, "presets": [["@babel/preset-env", {"targets": {"node": true}}], "@babel/preset-typescript"], "plugins": ["@babel/proposal-class-properties", "@babel/proposal-object-rest-spread"]}, "templates": {"cleverLinks": false, "monospaceLinks": false}}