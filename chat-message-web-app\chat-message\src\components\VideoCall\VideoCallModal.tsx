"use client";

import React, { useState, useEffect, useRef } from 'react';
import { XMarkIcon, VideoCameraIcon, VideoCameraSlashIcon, MicrophoneIcon, PhoneXMarkIcon } from '@heroicons/react/24/outline';
import { twilioVideoService } from '@/services/twilioVideoService';
import { VideoCallRoom, VideoCallParticipant, VideoCallSettings } from '@/types/video';
import { RemoteVideoTrack, RemoteAudioTrack } from 'twilio-video';
import toast from 'react-hot-toast';

interface VideoCallModalProps {
    isOpen: boolean;
    onClose: () => void;
    conversationId: string;
    conversationName: string;
    initialSettings?: VideoCallSettings;
}

export const VideoCallModal: React.FC<VideoCallModalProps> = ({
    isOpen,
    onClose,
    conversationId,
    conversationName,
    initialSettings = { video: true, audio: true, screenShare: false }
}) => {
    const [room, setRoom] = useState<VideoCallRoom | null>(null);
    const [participants, setParticipants] = useState<VideoCallParticipant[]>([]);
    const [settings, setSettings] = useState<VideoCallSettings>(initialSettings);
    const [isConnecting, setIsConnecting] = useState(false);
    const [callDuration, setCallDuration] = useState(0);

    const localVideoRef = useRef<HTMLVideoElement>(null);
    const remoteVideoRefs = useRef<Map<string, HTMLVideoElement>>(new Map());
    const callStartTime = useRef<Date | null>(null);
    const durationInterval = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        if (isOpen) {
            initializeVideoCall();
        } else {
            cleanup();
        }

        return () => cleanup();
    }, [isOpen]);

    useEffect(() => {
        // Initialize Twilio service callbacks
        twilioVideoService.initialize({
            onParticipantConnected: handleParticipantConnected,
            onParticipantDisconnected: handleParticipantDisconnected,
            onRoomConnected: handleRoomConnected,
            onRoomDisconnected: handleRoomDisconnected,
            onTrackSubscribed: handleTrackSubscribed,
            onTrackUnsubscribed: handleTrackUnsubscribed
        });
    }, []);

    const initializeVideoCall = async () => {
        setIsConnecting(true);
        try {
            const roomData = await twilioVideoService.joinRoom(conversationId, settings);
            setRoom(roomData);

            // Attach local video track
            const localVideoTrack = twilioVideoService.getLocalVideoTrack();
            if (localVideoTrack && localVideoRef.current) {
                localVideoTrack.attach(localVideoRef.current);
            }

            // Start call duration timer
            callStartTime.current = new Date();
            durationInterval.current = setInterval(() => {
                if (callStartTime.current) {
                    const duration = Math.floor((Date.now() - callStartTime.current.getTime()) / 1000);
                    setCallDuration(duration);
                }
            }, 1000);

            toast.success('Connected to video call');
        } catch (error) {
            console.error('Failed to join video call:', error);
            toast.error('Failed to join video call');
            onClose();
        } finally {
            setIsConnecting(false);
        }
    };

    const cleanup = async () => {
        if (durationInterval.current) {
            clearInterval(durationInterval.current);
            durationInterval.current = null;
        }

        callStartTime.current = null;
        setCallDuration(0);

        await twilioVideoService.leaveRoom();
        setRoom(null);
        setParticipants([]);

        // Clean up video elements
        remoteVideoRefs.current.clear();
    };

    const handleParticipantConnected = (participant: VideoCallParticipant) => {
        setParticipants(prev => [...prev, participant]);
        toast.success(`${participant.username} joined the call`);
    };

    const handleParticipantDisconnected = (participantId: string) => {
        setParticipants(prev => {
            const participant = prev.find(p => p.id === participantId);
            if (participant) {
                toast.success(`${participant.username} left the call`);
            }
            return prev.filter(p => p.id !== participantId);
        });

        // Clean up video element
        remoteVideoRefs.current.delete(participantId);
    };

    const handleRoomConnected = (roomData: VideoCallRoom) => {
        setRoom(roomData);
    };

    const handleRoomDisconnected = (roomData: VideoCallRoom) => {
        setRoom(null);
        toast.success('Call ended');
        onClose();
    };

    const handleTrackSubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => {
        if (track.kind === 'video') {
            const videoElement = remoteVideoRefs.current.get(participant.id);
            if (videoElement) {
                track.attach(videoElement);
            }
        }
    };

    const handleTrackUnsubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => {
        track.detach();
    };

    const toggleVideo = async () => {
        try {
            const isVideoEnabled = await twilioVideoService.toggleVideo();
            setSettings(prev => ({ ...prev, video: isVideoEnabled }));

            // Update local video display
            const localVideoTrack = twilioVideoService.getLocalVideoTrack();
            if (localVideoTrack && localVideoRef.current) {
                localVideoTrack.attach(localVideoRef.current);
            } else if (localVideoRef.current) {
                localVideoRef.current.srcObject = null;
            }
        } catch (error) {
            console.error('Failed to toggle video:', error);
            toast.error('Failed to toggle video');
        }
    };

    const toggleAudio = async () => {
        try {
            const isAudioEnabled = await twilioVideoService.toggleAudio();
            setSettings(prev => ({ ...prev, audio: isAudioEnabled }));
        } catch (error) {
            console.error('Failed to toggle audio:', error);
            toast.error('Failed to toggle audio');
        }
    };

    const endCall = async () => {
        try {
            await cleanup();
            onClose();
        } catch (error) {
            console.error('Failed to end call:', error);
            toast.error('Failed to end call');
        }
    };

    const formatDuration = (seconds: number): string => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
            <div className="bg-gray-900 rounded-lg shadow-2xl w-full h-full max-w-6xl max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700">
                    <div className="flex items-center space-x-3">
                        <h2 className="text-xl font-semibold text-white">{conversationName}</h2>
                        {room && (
                            <span className="text-sm text-gray-400">
                                {formatDuration(callDuration)}
                            </span>
                        )}
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white transition-colors"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Video Grid */}
                <div className="flex-1 p-4">
                    {isConnecting ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                                <p className="text-white">Connecting to call...</p>
                            </div>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 h-full">
                            {/* Local Video */}
                            <div className="relative bg-gray-800 rounded-lg overflow-hidden">
                                <video
                                    ref={localVideoRef}
                                    autoPlay
                                    muted
                                    playsInline
                                    className="w-full h-full object-cover"
                                />
                                <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                                    You
                                </div>
                                {!settings.video && (
                                    <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
                                        <VideoCameraSlashIcon className="h-12 w-12 text-gray-400" />
                                    </div>
                                )}
                            </div>

                            {/* Remote Videos */}
                            {participants.map((participant) => (
                                <div key={participant.id} className="relative bg-gray-800 rounded-lg overflow-hidden">
                                    <video
                                        ref={(el) => {
                                            if (el) {
                                                remoteVideoRefs.current.set(participant.id, el);
                                            }
                                        }}
                                        autoPlay
                                        playsInline
                                        className="w-full h-full object-cover"
                                    />
                                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                                        {participant.username}
                                    </div>
                                    {!participant.isVideoEnabled && (
                                        <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
                                            <VideoCameraSlashIcon className="h-12 w-12 text-gray-400" />
                                        </div>
                                    )}
                                    {participant.isMuted && (
                                        <div className="absolute top-2 right-2 bg-red-500 rounded-full p-1">
                                            <MicrophoneIcon className="h-4 w-4 text-white opacity-50" />
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Controls */}
                <div className="flex items-center justify-center space-x-4 p-4 border-t border-gray-700">
                    <button
                        onClick={toggleAudio}
                        className={`p-3 rounded-full transition-colors ${settings.audio
                            ? 'bg-gray-600 hover:bg-gray-500 text-white'
                            : 'bg-red-500 hover:bg-red-600 text-white'
                            }`}
                        title={settings.audio ? 'Mute' : 'Unmute'}
                    >
                        {settings.audio ? (
                            <MicrophoneIcon className="h-6 w-6" />
                        ) : (
                            <MicrophoneIcon className="h-6 w-6 opacity-50" />
                        )}
                    </button>

                    <button
                        onClick={toggleVideo}
                        className={`p-3 rounded-full transition-colors ${settings.video
                            ? 'bg-gray-600 hover:bg-gray-500 text-white'
                            : 'bg-red-500 hover:bg-red-600 text-white'
                            }`}
                        title={settings.video ? 'Turn off camera' : 'Turn on camera'}
                    >
                        {settings.video ? (
                            <VideoCameraIcon className="h-6 w-6" />
                        ) : (
                            <VideoCameraSlashIcon className="h-6 w-6" />
                        )}
                    </button>

                    <button
                        onClick={endCall}
                        className="p-3 rounded-full bg-red-500 hover:bg-red-600 text-white transition-colors"
                        title="End call"
                    >
                        <PhoneXMarkIcon className="h-6 w-6" />
                    </button>
                </div>
            </div>
        </div>
    );
};
